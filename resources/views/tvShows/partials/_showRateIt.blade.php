<div id="rateIt" class="hidden rateIt">
    <div class="rate-mobile">
        <h4> {{ $tvShow->title }}</h4>
        @if( isset($tvShow->mainImage->filename)   )
          <img src="{!! unstageAsset($tvShow->mainImage->filename) !!}" class="rate-mobile--img"/>
        @else
        @endif
    </div>
        <div  >
                <div id="clear-vote" data-url="{{ route('neptune.tvShowRatings.destroy', $tvShow->id) }}"
                     class="  "><i class="fa fa-times"></i></div>
                <div class="play-rate__stars">
                    <input type="hidden" class="play-rating"
                           @if(  !empty(auth('users')->user()) && auth('users')->user()->getTvShowRating($tvShow->id))
                           value="{{auth('users')->user()->getTvShowRating($tvShow->id)}}"
                           @endif
                           data-tvShowId="{{$tvShow->id}}"
                           data-url="{{ route('neptune.tvShowRatings.store') }}"
                           data-stop="10"
                           data-filled="fa fa-star"
                           data-empty="fa fa-star-o"/>
                </div>
    </div>
</div>