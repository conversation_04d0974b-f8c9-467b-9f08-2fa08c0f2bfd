@if(count($data['main_page_reviews']) > 0)
    <div class=" layout-page__section">
        <div class="row">
            <div class="col-md-6">
                <h5 class=" user-review__title">Κριτικές κοινού</h5>
            </div>
            <div class="col-md-6 text-right">
                <a href="{{route('tvShows.showUserReviews', [$tvShow->slug])}}" class="homepage-carousel__link">Όλες οι κριτικές <i class="fa fa-angle-right"></i></a>
            </div>
        </div>
        <div class="play-reviews">
        @forelse($data['main_page_reviews'] as $user)
                @include('tvShows.partials._userReview')
        @empty
        @endforelse
        </div>
        <div class="user-comment__actions user-comment__actions--small">
            <a rel="nofollow" href="{{route('neptune.tvShowReviews.create', $tvShow->slug)}}" class="write-critic"
               @if(!auth('users')->user())
               data-status="login"
                    @endif
            >Η κριτική σου <i class="fa fa-angle-right"></i></a>
        </div>
    </div>
@endif