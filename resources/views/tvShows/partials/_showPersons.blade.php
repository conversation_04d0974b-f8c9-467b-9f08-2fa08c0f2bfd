<div class="  play-persons ">
    <div class="layout-page__section">
        <div class="collapse-title collapsed collapse-title__hidden  visible-xs  " data-toggle="collapse"
             data-target="#play-persons">
            Συντελεστές
        </div>

        <div id="play-persons" class="collapse collapse-padding  ">
            @if(count($tvShow->directors))
                <div class=" layout-page__sectionSmall  ">
                    <h4 class="play-persons__title">Σκηνοθεσία</h4>
                    <div class="css-columns-2">
                        @foreach($tvShow->directors as $key => $director)
                            <div class="play-actors do-not-break">
                                <div class="table table-width-full ">
                                    <div class="table-cell table-cell--middle table-cell--image">
                                    @if($director->profilable)
                                        <a href="{!! route('people.show', ['slug' => $director->slug, 'ref_' => \App\Components\UrlReferrals::getReferral('person_details', 'play_details_people_list_img')]) !!}">
                                        @if (isset($director->mainImage))
                                            <div class="circle circle-medium circle-hover"
                                                 style="background-size:cover; background-position:center; background-image:url('{!! asset(Croppa::url($director->mainImage->filename,200,null)) !!}')"></div>
                                        @else
                                            <div class="circle circle-medium circle-hover"></div>
                                        @endif
                                        </a>
                                    @else
                                        @if (isset($director->mainImage))
                                            <div class="circle circle-medium circle-hover"
                                                 style="background-size:cover; background-position:center; background-image:url('{!! asset(Croppa::url($director->mainImage->filename,200,null)) !!}')"></div>
                                        @else
                                            <div class="circle circle-medium circle-hover"></div>
                                        @endif
                                    @endif
                                    </div>
                                    <div class="table-cell table-cell--middle table-cell-full">
                                    @if($director->profilable)
                                        <a href="{!! route('people.show', ['slug' => $director->slug, 'ref_' => \App\Components\UrlReferrals::getReferral('person_details', 'play_details_people_list_img')]) !!}">
                                            <h5 class="play-actors__name hover-underline">{{$director->fullName}} </h5>
                                        </a>
                                    @else
                                        <h5 class="play-actors__name">{{$director->fullName}} </h5>
                                    @endif
                                    </div>
                                    <div class="table-cell table-cell--middle text-right">
                                        @include('components.buttons.followPeopleMin', ['person' => $director])
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
            @if(count($tvShow->actors))
                <div class="layout-page__sectionSmall">
                    <h4 class="play-persons__title">Ηθοποιοί</h4>
                    <div class="css-columns-2">
                        @foreach($tvShow->actors as $actor)
                            <div class="play-actors do-not-break">
                                <div class="table">
                                    <div class="table-cell table-cell--middle table-cell--image">
                                    @if($actor->profilable)
                                        <a href="{!! route('people.show', ['slug' => $actor->slug, 'ref_' => \App\Components\UrlReferrals::getReferral('person_details', 'play_details_people_list_img')]) !!}">
                                        @if (isset($actor->mainImage))
                                            <div class="circle circle-medium circle-hover"
                                                 style="  background-size:cover; background-position:center; background-image:url('{!! asset(Croppa::url($actor->mainImage->filename,200,null)) !!}')"></div>
                                        @else
                                            <div class="circle circle-medium circle-hover"></div>
                                        @endif
                                        </a>
                                    @else
                                        @if (isset($actor->mainImage))
                                            <div class="circle circle-medium circle-hover"
                                                 style="  background-size:cover; background-position:center; background-image:url('{!! asset(Croppa::url($actor->mainImage->filename,200,null)) !!}')"></div>
                                        @else
                                            <div class="circle circle-medium circle-hover"></div>
                                        @endif
                                    @endif
                                    </div>
                                    <div class="table-cell  table-cell--middle table-cell-full">
                                    @if($actor->profilable)
                                        <a href="{!! route('people.show', ['slug' => $actor->slug, 'ref_' => \App\Components\UrlReferrals::getReferral('person_details', 'play_details_people_list_img')]) !!}">
                                            <h5 class="play-actors__name hover-underline">{!! $actor->fullName !!}</h5>
                                        </a>
                                    @else
                                        <h5 class="play-actors__name">{!! $actor->fullName !!}</h5>
                                    @endif
                                    @if($actor->pivot->character)
                                        <h6 class="play-actors__role">{!! $actor->pivot->character !!}</h6>
                                    @endif
                                    </div>
                                    <div class="table-cell table-cell--middle text-right">
                                        @include('components.buttons.followPeopleMin', ['person' => $actor])
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
            <div class="css-columns-2 layout-page__sectionSmall">
                @foreach ($tvShow->roles as $role)
                    @if($role->tvShowPeople->all())
                        <div class="play-sintelestes do-not-break">
                            <h6 class="play-sintelestes__title">{!! $role->description !!}</h6>
                            @foreach ($role->tvShowPeople as $person)
                                @if($person->profilable)
                                    <h5 class="play-sintelestes__person">
                                        {!! link_to_route('people.show', $person->fullName, ['slug' => $person->slug, 'ref_' => \App\Components\UrlReferrals::getReferral('person_details', 'play_details_people_list_text')]) !!}
                                    </h5>
                                @else
                                    <h5 class="play-sintelestes__person">
                                        {!! $person->fullName !!}
                                    </h5>
                                @endif
                            @endforeach
                        </div>
                    @endif
                @endforeach
            </div>
        </div>
    </div>
</div>
