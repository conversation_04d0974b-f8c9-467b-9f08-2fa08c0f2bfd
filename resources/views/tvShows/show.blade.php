@extends('components.layout')
@section('header')
@stop

@section('content')
    @include('tvShows.partials._showHero')
    <div class=" js-play-body">
        <div class="container  no-gutters">
            <div class="row no-gutters  ">
                <div class="col-md-12">
                    <div class="row  no-gutters ">
                        <div class="col-md-9">
{{--                            <livewire:serve-ads lazy/>--}}
                            <div class="layout-page__content layout-page ">
                                @if(count($tvShow->images) >= 1)
                                    <div class=" layout-page__section">
                                        <div class="row">
                                            <div class="col-xs-6">
                                                <h5 class=" user-review__title">Φωτογραφίες</h5>
                                            </div>
                                            <div class="col-xs-6 text-right">
                                                <a href="{{route('tvShows.showMedia', [$tvShow->slug])}}"
                                                   class="homepage-carousel__link">Όλες οι
                                                    Φωτογραφίες <i class="fa fa-angle-right"></i></a>
                                            </div>
                                        </div>
                                        @include('tvShows.partials._showMedia')
                                    </div>
                                @endif
                                @if($tvShow->videos()->count() >= 1)
                                    <div class=" layout-page__section">
                                        @include('tvShows.partials._showVideos')
                                    </div>
                                @endif
                                @include('tvShows.partials._showInfo')
                                @include('tvShows.partials._showPersons')
                                @include('tvShows.partials._showUserReview')
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="layout-page__sidebar">
                                @include('tvShows.partials._showSidebar')
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop
@section('footer')
    @parent

    {!! Minify::javascript(
    array( '/js/frontend/jquery.theatricPlay.js',
    '/js/image-lightbox/imagelightbox.min.js',
    '/js/frontend/jquery.initImageLighbox.js',
    '/js/frontend/jquery.tvShowRating.js',
     '/js/frontend/jquery.playReview.js'
    ))->withFullUrl() !!}
@stop
