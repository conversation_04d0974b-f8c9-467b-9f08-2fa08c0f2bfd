<div id="rateIt" class="hidden rateIt">
    <div class="rate-mobile">
        <h4> {{ $endeavour->title }}</h4>
        @if( isset($endeavour->mainImage->filename)   )
          <img src="{!! unstageAsset($endeavour->mainImage->filename) !!}" class="rate-mobile--img"/>
        @else
        @endif
    </div>
        <div  >
                <div id="clear-vote" data-url="{{ route('neptune.endeavourRatings.destroy', $endeavour->id) }}"
                     class="  "><i class="fa fa-times"></i></div>
                <div class="play-rate__stars">
                    <input type="hidden" class="endeavour-rating"
                           @if(  !empty(auth('users')->user()) && auth('users')->user()->getEndeavourRating($endeavour->id))
                           value="{{auth('users')->user()->getEndeavourRating($endeavour->id)}}"
                           @endif
                           data-endeavourId="{{$endeavour->id}}"
                           data-url="{{ route('neptune.endeavourRatings.store') }}"
                           data-stop="10"
                           data-filled="fa fa-star"
                           data-empty="fa fa-star-o"/>
                </div>
    </div>
</div>
