@if($endeavour->articles()->count() > 0)
    <div class="layout-page__section ">
        <div class="row">
            <div class="col-xs-12">
                <h5 class=" person-section__title">
                    <a href="{{ route('endeavours.showInfo', $endeavour->slug) }}" class="person-section__link"> Δημοσιεύματα
                        <span>{{$endeavour->articles()->count()}}</span>
                        <i class="fa fa-angle-right"></i>
                    </a>
                </h5>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
            <div class="row no-gutter auto-clear">
            @foreach($endeavour->articles()->take(4)->get() as $key => $interview)
            <div class="col-xs-6 col-lg-3 col-ms-6 col-md-6 col-sm-6 col-xl-3">
                <div class="play-grid_ @if($key > 1) play-grid--margin @endif">
                    @if(isset($interview->mainImage))
                        <a class="p_trending" href="{{ route('articles.show', ['slug' => $interview->slug]) }}">
                            <div class="play-grid__image playPhoto b-lazy"
                                 data-src="{!! asset(Croppa::url($interview->mainImage->filename,400,null)) !!}"
                            >
                            </div>
                        </a>
                    @endif
                    <div class="play-grid__details">
                        <h3 class="play-grid__title tickets-button__alt ">
                            <a href="{{ route('articles.show', ['slug' => $interview->slug]) }}">{{ $interview->title }}</a>
                        </h3>
                    </div>
                </div>
            </div>
            @endforeach
            </div>
            </div>
        </div>
    </div>
@endif
