@extends('components.layout')
@section('header')
@stop
@section('content')

    @include('endeavours.partials._showProfileMin', ['page_title' => $data['page_title']])

    <div class="container    ">
        <div class="row  ">
            <div class="col-md-12">
                <div class="row   no-gutters">
                    <div class="col-md-9">
                        @include('endeavours.partials._showInterviews')
                        @include('endeavours.partials._showArticles')
{{--                        @include('plays.partials._showUserposts')--}}
                    </div>
                    <div class="col-md-3">
                        <div class="layout-page__sidebar">
                            @include('endeavours.partials._showSidebarMenu')
{{--                            <div class="layout-sidebar__section-nopr-nobb">--}}
{{--                                @include('people.partials._showAdBannerOne')--}}
{{--                            </div>--}}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop
@section('footer')
    @parent
    {!! Minify::javascript(array('/js/image-lightbox/imagelightbox.min.js', '/js/frontend/jquery.initImageLighbox.js'))->withFullUrl() !!}
@stop
