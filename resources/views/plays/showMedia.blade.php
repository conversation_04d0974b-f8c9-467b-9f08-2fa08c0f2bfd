@extends('components.layout')
@section('header')
@stop
@section('content')

    @include('plays.partials._showProfileMin', ['page_title' => $data['page_title']])

    <div class="container    ">
        <div class="row  ">
            <div class="col-md-12">
                <div class="row   no-gutters">
                    <div class="col-md-9">
                        <div class="layout-page__content layout-page__section ">
{{--                            <livewire:serve-ads lazy/>--}}
                            {{--<div>{{ $data['page_subheading'] }}</div>--}}
                            @include('plays.partials._showMedia')
                            <br/>
                            <a name="videos"></a>
                            @include('plays.partials._showVideos')
                        </div>
                        @if ($play->synopsis)
                            <div class="layout-page__content layout-page__section ">
                            <h2 class="play-persons__title">{!! trans('plays.show_media.synopsis_title') !!}</h2>
                                {{ $play->synopsis }}
                            </div>
                        @endif
                    </div>
                    <div class="col-md-3  ">
                        <div class="layout-page__sidebar  ">
                            @include('plays.partials._showSidebarMenu')
                            <div class="layout-sidebar__section-nopr-nobb">
                                @include('people.partials._showAdBannerOne')
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop
@section('footer')
    @parent
    {!! Minify::javascript(array('/js/image-lightbox/imagelightbox.min.js', '/js/frontend/jquery.initImageLighbox.js'))->withFullUrl() !!}
@stop
