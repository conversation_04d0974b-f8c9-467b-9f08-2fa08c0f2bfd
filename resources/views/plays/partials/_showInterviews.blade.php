@if( $play->interviews()->count() > 0)
    <div class="layout-page__section ">
        <div class="interviews">
            <div class=" " style="margin-bottom:5px;" >
                <strong>{{ trans('people.show_bio.articles_text') }} ({{$play->interviews()->count()}})</strong>
            </div>
            @forelse($play->interviews()->get() as $article)
                <div class="interview-listItem  ">
                    <h4 class=" hover-underline"><a href="{{ route('articles.show', ['slug' => $article->slug]) }}"   >{!! $article->title !!}</a> </h4>
                    @if($article->author)
                        <div class="interview-listItem__author">από {!! $article->author->fullName !!}</div>
                    @endif
                </div>
            @empty
            @endforelse
        </div>
    </div>
@endif