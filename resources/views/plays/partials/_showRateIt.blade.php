<div id="rateIt" class="hidden rateIt">
    <div class="rate-mobile">
        <h4> {{ $play->title }}</h4>
        @if( isset($play->mainImage->filename)   )
          <img src="{!! unstageAsset($play->mainImage->filename) !!}" class="rate-mobile--img"/>
        @else
        @endif
    </div>
        <div  >
                <div id="clear-vote" data-url="{{ route('neptune.ratings.destroy', $play->id) }}"
                     class="  "><i class="fa fa-times"></i></div>
                <div class="play-rate__stars">
                    <input type="hidden" class="play-rating"
                           @if(  !empty(auth('users')->user()) && auth('users')->user()->getRating($play->id))
                           value="{{auth('users')->user()->getRating($play->id)}}"
                           @endif
                           data-playId="{{$play->id}}"
                           data-url="{{ route('neptune.ratings.store') }}"
                           data-stop="10"
                           data-filled="fa fa-star"
                           data-empty="fa fa-star-o"/>
                </div>
    </div>
</div>