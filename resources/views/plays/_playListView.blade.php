<div class="table margin-bottom play-listing">
    @if(count($play->mainImage))
        <div class="table-cell relative play-listing-image" style="background-image:url('{!! unstageAsset($play->mainImage->filename) !!}');">
            @if(count($play->genres))
                <div class="image-label lato">
                    @foreach($play->genres as $genre)
{{--                        {!! link_to_route('genres.plays.index', $genre->name, $genre->slug) !!}--}}
                        {{ $genre->name }}
                    @endforeach
                </div>
            @endif
        </div>
    @endif
    <div class="table-cell bck white padding padding-left-medium padding-right-large">
        <h3 class=" padding-none margin-none ">{!! link_to_route('plays.show', $play->title, $play->slug) !!} {!! $play->year !!}</h3>

        @if($play->theatre)
            <div class="text theme small margin-bottom lato">{!! link_to_route('theatres.show', $play->theatre->name, $play->theatre->slug) !!} </div>
        @endif

        @if($play->synopsis)
            <p class="text grey margin-bottom small lato">{{ Str::limit($play->synopsis, 150, '...') }} </p>
        @endif

        @if($play->end_date !='')
            <p><span class="text small lato theme">Μέχρι: {!! $play->end_date !!}</span></p>
        @endif

        <p class="border-top padding-top-small text grey small">

            @foreach($play->actors as $actor)
                {!! link_to_route('people.show', $actor->fullName, $actor->slug) !!}
                @if($actor != last($play->actors))
                    ,
                @endif
            @endforeach
        </p>
    </div>
</div>


<div style="display:none;">
    {{--Genres associated with theatric play--}}
    <h4>Genres</h4>
    @foreach($play->genres as $genre)
{{--        {!! link_to_route('genres.plays.index', $genre->name, $genre->slug) !!}--}}
        {{ $genre->name }}

    @endforeach

    {{--Main image of theatric play--}}
    @if (isset($play->mainImage))
        <h4>Main Image</h4>
        <a href="{!! route('plays.show', $play->slug) !!}">
            <img src="{!! unstageAsset($play->mainImage->filename) !!}" style="width:100px;height:100px"/>
        </a>
    @endif

    {{--Theatric play title--}}
    <h3>{!! link_to_route('plays.show', $play->title, $play->slug) !!}</h3>

    {{--Theatric play synopsis--}}
    <p>{!! $play->synopsis !!}</p>

    {{--List of Directors--}}
    <h4>Skinothetis</h4>
    @foreach($play->directors as $director)
        {{--Director Full name--}}
        {!! link_to_route('people.show', $director->fullName, $director->slug) !!} <br>
    @endforeach


    {{--List of actors--}}
    <h4>Ithopoioi</h4>
    @foreach($play->actors as $actor)
        {{--Actor Full name--}}
        {!! link_to_route('people.show', $actor->fullName, $actor->slug) !!} <br>
        {{--Main image of each actor--}}
        @if(isset($actor->mainImage->filename))
            <a href="{!! route('people.show', $actor->slug) !!}">
                <img src="{!! unstageAsset($actor->mainImage->filename)!!}" style="width:100px;height:100px"/>
            </a>
        @else
            No image yet
        @endif
    @endforeach
</div>
