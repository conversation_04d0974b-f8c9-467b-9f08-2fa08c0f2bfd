@extends('components.layout')

@section('content')

    <div class="festivals pages-header">

        <div class="container  ">
            <div class="row  ">
                <div class="col-md-10 col-md-offset-1   ">
                    <div class="festivals_header">
                        <h1 class="festivals__title">{!! trans('festivals.index.page_title') !!}</h1>
                        <h4 class="festivals__subtitle">{!! trans('festivals.index.page_subtitle') !!}</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="container ">
        <?php /**/ $year = '' /**/ ?>
        @foreach($festivals as $festival)
            @if($year == '' || $year!=$festival->year)
                <div class="row ">
                    <div class="col-md-10 col-md-offset-1 col-sm-12 col-sm-offset-0">
                        <h2 class="theater-archive__year">{{$festival->year}}</h2>
                    </div>
                </div>
            @endif
            <div class="row ">
                <div class="col-md-10 col-md-offset-1 col-sm-12 col-sm-offset-0">
                    <div class="festival-item">
                        <div class="row ">
                            <div class="col-sm-6  ">
                                <h3 class="festival-item__title">{!! link_to_route('festivals.show',$festival->title . ' (' . $festival->year . ')', $festival->slug) !!}</h3>
                            </div>
                            <div class="col-sm-6 text-right  mobile-text-left  ">
                                @if($festival->start_date && $festival->end_date)
                                    <span class=" festival-item__date  ">
                                    {{ $festival->start_date }}
                                        -
                                        {{ $festival->end_date }}
                                </span>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php /**/ $year = $festival->year /**/ ?>
        @endforeach
        <div class="row    ">
            <div class="col-md-12   text-center  ">
                {!! $festivals->links() !!}
            </div>
        </div>
    </div>
    </div>
@stop