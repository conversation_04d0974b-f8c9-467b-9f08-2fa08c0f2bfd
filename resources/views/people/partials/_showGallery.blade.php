@if(count($person->allImages) >= 2)
    <div class="   layout-sidebar__section">
        <div class="person-gallery">
            @foreach($person->allImages as $key => $image)
                <a href="{!! asset(Croppa::url($image->filename,800,null)) !!}"
                   @if($key!=1 &&  count($person->allImages) >=2)  class="hidden" @endif
                   data-imagelightbox="d">
                    <img class="b-lazy person-gallery__photo"
                         src="{!! asset(Croppa::url($image->filename,200,null)) !!}"
                         @if($image->play)
                         alt="{!! $image->play->title !!}"
                            @endif
                    />
                </a>
            @endforeach
        </div>
        <div class="trivia__actions">
            <a href="{!! route('people.showBio', [$person->slug]) !!}#trivia">Όλες οι {{count($person->allImages)}}
                φωτογραφίες</a>
        </div>
    </div>
@else(count($person->allImages) == 1)
@foreach($person->allImages as $key => $image)
    <a href="{!! asset(Croppa::url($image->filename,800,null)) !!}"
       class="hidden"
       data-imagelightbox="d">
        <img class="b-lazy person-gallery__photo"
             src="{!! asset(Croppa::url($image->filename,200,null)) !!}"
             @if($image->play)
             alt="{!! $image->play->title !!}"
                @endif
        />
    </a>
@endforeach
@endif
