<!DOCTYPE html>
<html lang="el">
<head prefix="og: http://ogp.me/ns# fb: http://ogp.me/ns/fb#">
    @include('scripts.googleTagManagerHead')

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, user-scalable=no">
    {!! SEO::generate() !!}

    <link rel="apple-touch-icon" sizes="180x180" href="{{unstageAsset('apple-touch-icon.png')}}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{unstageAsset('favicon-32x32.png')}}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{unstageAsset('favicon-16x16.png')}}">
    <link rel="manifest" href="{{unstageAsset('site.webmanifest')}}">
    <link rel="mask-icon" href="{{unstageAsset('safari-pinned-tab.svg')}}" color="#020202">
    <link rel="shortcut icon" href="{{unstageAsset('favicon.ico')}}">
    <meta name="msapplication-TileColor" content="#000000">
    <meta name="theme-color" content="#ffffff">

    <meta property="fb:app_id" content="155195791749213"/>

    <script type="text/javascript">
        document.documentElement.addEventListener('touchstart', function (event) {
            if (event.touches.length > 1) {
                event.preventDefault();
            }
        }, false);
        var APP_URL = {!! json_encode(url('/')) !!};
    </script>
    <meta name="GOOGLE_MAPS_KEY" content="{{ env('GOOGLE_MAPS_KEY') }}"/>
    <meta name="csrf-token" content="{{ csrf_token() }}"/>
{{--    <link type="text/css" rel="stylesheet" href="//fast.fonts.net/cssapi/94558f8e-7253-4455-8273-d0d3cb5bc016.css"/>--}}
    <style type="text/css">
        @font-face{
            font-family:"PF BeauSans W15 Thin";
            src:url("{{unstageAsset('fonts/beauSans/60199343-7165-4b7f-af69-51b627c674e9.woff2')}}") format("woff2"),url("{{unstageAsset('fonts/beauSans/49a39b78-0c6b-47f3-b523-29d3bf87ba46.woff')}}") format("woff");
        }
        @font-face{
            font-family:"PF BeauSans W15 Light Italic";
            src:url("{{unstageAsset('fonts/beauSans/11c786f4-5430-4a87-a17b-5c83d9f723a0.woff2')}}") format("woff2"),url("{{unstageAsset('fonts/beauSans/1e38a76d-ea25-4b25-b59a-9c7fd4b45277.woff')}}") format("woff");
        }
        @font-face{
            font-family:"PF BeauSans W15 Regular";
            src:url("{{unstageAsset('fonts/beauSans/ec0c1f62-070e-4bc5-84df-7789bcd43741.woff2')}}") format("woff2"),url("{{unstageAsset('fonts/beauSans/1f12527b-1f04-4883-868a-d7d37026b62f.woff')}}") format("woff");
        }
        @font-face{
            font-family:"PF BeauSans W15 SemiBold";
            src:url("{{unstageAsset('fonts/beauSans/ba5050a2-a1f2-47ed-9e93-80df0ca0bab5.woff2')}}") format("woff2"),url("{{unstageAsset('fonts/beauSans/759e5929-a05c-43d4-8e2c-28977a5e7834.woff')}}") format("woff");
        }
        @font-face{
            font-family:"PF BeauSans W15 Bold";
            src:url("{{unstageAsset('fonts/beauSans/bdfab7a1-9e07-43bd-89be-8d0f034dee17.woff2')}}") format("woff2"),url("{{unstageAsset('fonts/beauSans/4f32efdd-87f5-4abf-ae4e-df0758fe1452.woff')}}") format("woff");
        }
    </style>
    <script type="text/javascript">
        var MTIProjectId='94558f8e-7253-4455-8273-d0d3cb5bc016';
        (function() {
            var mtiTracking = document.createElement('script');
            mtiTracking.type='text/javascript';
            mtiTracking.async='true';
            mtiTracking.src='/fonts/mtiFontTrackingCode.js';
            (document.getElementsByTagName('head')[0]||document.getElementsByTagName('body')[0]).appendChild( mtiTracking );
        })();
    </script>
    @vite('resources/css/app.css')
            <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
    {!! Minify::javascript(
    array(
    '/js/frontend/html5shiv.min.js',
    '/js/frontend/respond.min.js',
    )) !!}
    <![endif]-->
@yield('header')
</head>
<body class="bg-white ">
@include('scripts.googleTagManagerBody')
@yield('content')
@include('components.messages.flash_tailwind')

@livewireScripts
@vite('resources/js/app.js')


@if (App::environment('local'))
    @include('ads.responsive-helper')
@endif

@yield('footer')
</body>
</html>
