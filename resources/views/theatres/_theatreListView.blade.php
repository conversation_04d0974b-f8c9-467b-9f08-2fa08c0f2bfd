<div class="container  margin-top ">
    <div class="   bck white" >
        @forelse ($theatres as $theatre)
        <div class="row      padding-small padding-left-medium margin-none " style="border-bottom:1px dotted #aaa">
            <div class="col-md-6   "   >
                <h3 class="margin-none text bold">{!! link_to_route('theatres.show', $theatre->name, $theatre->slug) !!}</h3>
                <p class="text grey georgia">{!! $theatre->full_address !!}</p>
            </div>
            <div class="col-md-6   "   >
                @forelse($theatre->getOngoingTheatricPlays() as $theatricPlay)
                <h5 class="text h6 bold theme margin-none">{!! link_to_route('theatricPlays.show', $theatricPlay->title, $theatricPlay->slug) !!}</h5>
                @forelse($theatricPlay->genres as $genre)
                {!! link_to_route('genres.theatricPlays.index', $genre->name, $genre->slug) !!}
                @empty
                @endforelse
                @empty
                @endforelse
            </div>
        </div>
        @empty
        <p>No theatres yet</p>
        @endforelse
    </div>
    <div class="row">
        <div class="col-md-8">
        </div>
    </div>
</div>