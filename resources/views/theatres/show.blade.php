@extends('components.layout')
@section('header')
@stop

@section('content')
    <div class="pages-header ">
        <div class="container container-narrow">
            <div class="row">
                <div class="col-sm-6  ">
                    <h1 class="theater-header__name">{!! $theatre->name !!}

                        <div class="theater-header__address">
                            @if(request('scope') != 'archive')
                                {{$theatre->area}}
                            @else
                                Αρχείο παραστάσεων
                            @endif
                        </div>
                    </h1>
                    @admincan('manage_theatres')
                    <a href="{{ url('/uranus/theatres', [$theatre->id, 'edit']) }}">Επεξεργασία</a>
                    @endadmincan
                </div>
                <div class="col-sm-6 ">
                    <ul class="nav nav-tabs search-page__nav  navbar-right ">
                        @if($theatre->followable)
                            <li>
                            @include('components.buttons.followTheaterMin')
                            </li>
                        @endif
                        <li @if(request('scope') != 'archive')class="active"@endif>
                            <a href="{{ route('theatres.show', $theatre->slug) }}">Τρέχοντα
                                {{ $ongoingComingSoonCount }}</a>
                        </li>
                        <li @if(request('scope') == 'archive')class="active"@endif>
                            <a href="{{ route('theatres.show',['slug' => $theatre->slug, 'scope' => 'archive']) }}">Αρχείο
                                {{ $totalPlays }}</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    @if(request('scope') != 'archive')
        <div class="   ">
            <div class="container  container-narrow">
                <div class="row">
                    @include('theatres._showOngoingComingSoonPlays')
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="schools__description">{!! $theatre->description !!}</div>
                        <div class="theater-info">
                            <div class="row ">
                                <div class="col-sm-6   ">
                                    <div class=" theater-info__inner   ">
                                        @if($theatre)
                                            <h3 class="theater-info__name">
                                                {!! $theatre->name !!}
                                            </h3>
                                        @endif
                                        <h5 class=" theater-info__address">
                                            {{ $theatre->fullAddress }}
                                        </h5>
                                        @if(isset($theatre->telephone ) && $theatre->telephone != '')
                                            <i class="fa fa-phone"></i> {!! $theatre->telephone !!}
                                            @if(isset($theatre->mobile_phone ) && $theatre->mobile_phone != '')
                                                {!! $theatre->mobile_phone !!}
                                            @endif
                                            <br><br>
                                        @endif
                                        @if($theatre->seats)
                                            <strong>{!! $theatre->seats !!}</strong>  Θέσεις<br><br>
                                        @endif
                                        @if(isset($theatre->website ) && $theatre->website != '' )
                                            <a href="{{ $theatre->website }}" class=" theater-socials" target="_blank">
                                                <i class="fa fa-external-link-square"></i> Ιστοσελίδα</a>

                                        @endif
                                        @if(isset($theatre->facebook ) && $theatre->facebook != '')
                                            <a href="{!! $theatre->facebook !!}" target="_blank"
                                               class=" theater-socials">

                                                <i class="fa fa-facebook-square  "
                                                   aria-hidden="true"></i> facebook
                                            </a>

                                        @endif
                                        @if(isset($theatre->twitter ) && $theatre->twitter != '')
                                            <a href="{!! $theatre->twitter !!}" class=" theater-socials"
                                               target="_blank">
                                                <i class="fa fa-twitter-square "></i> twitter</a>
                                        @endif
                                    </div>
                                </div>
                                <div class="col-sm-6  ">
                                    @if(!empty($theatre->latitude) && !empty($theatre->longitude))
                                        <div id="map" class="theater-info__map " latitude="{{ $theatre->latitude }}"
                                             longitude="{{ $theatre->longitude }}"></div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @if(count($theatre->ongoingPlays) > 0 || count($theatre->comingSoonPlays) > 0)

                <div class="container container-narrow">
                    <div class="row no-gutters">
                        <div class="col-md-12">
                            <div class="theater-calendar table">
                                <div class="table-cell theater-arrow" id="timetable-arrow-left"><i
                                            class="fa fa-angle-left"></i>
                                </div>
                                <div class="table-cell">
                                    <div class="owl-carousel-timetable owl-carousel">
                                        @php
                                        $now = \Carbon\Carbon::now()->subDays(1);
                                        $end = \Carbon\Carbon::now()->addDays(9);
                                        $date = $now;
                                        @endphp
                                        @while ($now->diffInDays($end)<=10)
                                            @php
                                            $date = $date->addDay();
                                            @endphp
                                            <div class="timetable-date searchDate"
                                                 data-date="{{$date->format('Y-m-d')}}">{{ trans('calendar.daysShort.'.$date->format('w'))}}
                                                <br>{{$date->format('d/m')}}
                                            </div>
                                        @endwhile
                                    </div>
                                    <form id="findPlaysForm" action="{{ route('theatres.findPlays', $theatre->id) }}"
                                          method="get">
                                        {!! csrf_field() !!}
                                        <input type="hidden" id="singleDate" name="singleDate"/>
                                    </form>
                                </div>
                                <div class="table-cell theater-arrow" id="timetable-arrow-right"><i
                                            class="fa fa-angle-right"></i></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="container container-narrow">
                    <div id="plays_container" class="row">
                        @foreach($todayPlays as $play)
                            <div class="col-md-12">
                                <a href="{!! route('plays.show', ['slug' => $play->slug, 'ref_' => \App\Components\UrlReferrals::getReferral('play_details', 'theatre_details_ongoing_plays')]) !!}">
                                    <div class="theater-archive-play">
                                        <div class="table">
                                            <div class="table-cell table-cell--image ">
                                                <div class="theater-archive-play__image b-lazy"
                                                     data-src="{!! $play->mainImage?asset(Croppa::url($play->mainImage->filename,800,null)):null !!}"></div>
                                            </div>
                                            <div class="table-cell table-cell--top table-cell-full">
                                                <div class="theater-archive-play__details">
                                                    <h3 class="   ">  {{$play->title}} </h3>
                                                    @if(count($play->genres) > 0)
                                                        <div class="theater-archive-play__genre ">
                                                            @foreach($play->genres as $genre)
                                                                <span class="comma-seperated  ">{{ $genre->name }}</span>
                                                            @endforeach
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif

        </div>
    @else
        @include('theatres._showPlaysArchive')
    @endif

@stop

@section('footer')
    @parent
    @if($theatre->latitude && $theatre->longitude)
        <script src="{!! unstageAsset('js/frontend/jquery.initializeGmap.js') !!}"></script>
        <script src="https://maps.googleapis.com/maps/api/js?key={{env('GOOGLE_MAPS_KEY')}}&callback=initializeMap"></script>
    @endif
    <script type="text/javascript">
        $('.owl-carousel-slider').owlCarousel({
            loop: false,
            margin: 0,
            nav: true,
            lazyLoad: true,
            items: 1,
            navText: ''
        });

        $(document).ready(function () {
            var owl = $('.owl-carousel-timetable').owlCarousel({
                loop: false,
                margin: 0,
                nav: false,
                items: 3,
                lazyLoad: true,
                loop: false,
                dots: false,
                navText: '',
                responsive: {
                    350: {
                        items: 4,
                        nav: false,
                    },
                    480: {
                        items: 5,
                        nav: false,
                    },
                    600: {
                        items: 7,
                        nav: false,
                    },
                    900: {
                        items: 9,
                        nav: false,
                    },
                }
            });

            $('#timetable-arrow-left').click(function () {
                owl.trigger('prev.owl.carousel');
            });
            $('#timetable-arrow-right').click(function () {
                owl.trigger('next.owl.carousel');
            });

            $('.searchDate').click(function () {
                $('#plays_container').html('<div class="plays-loading"><i class="fa fa-spinner fa-spin"></i></div>');
                // grab form data to formulate the ajax call
                var singleDate = $(this).data('date');
                $('.searchDate').css('background-color', 'transparent');
                $(this).css('background-color', '#f5f5f5');
                $('#singleDate').val(singleDate);

                var form = $('#findPlaysForm');
                var method = form.find('input[name="_method"]').val() || 'POST';
                var token = $('input[name="_token"]').val();
                var url = form.prop('action');

                $.ajax({
                            type: 'GET',
                            url: url,
                            headers: {
                                "x-csrf-token": token
                            },
                            data: form.serialize(),
                            dataType: 'json'
                        })
                        .fail(function () {
                        })
                        .done(function (response) {
                            if (response.status == 'ok') {
                                $('#plays_container').html(response.data);
                            }
                        });

                e.preventDefault();
            });
        });
    </script>

@stop
