<div class="col-md-12  ">
    <div class="owl-carousel-slider  owl-carousel theater-slider ">
        @foreach($theatre->ongoingPlays as $play)
            <a href="{!! route('plays.show', ['slug' => $play->slug, 'ref_' => \App\Components\UrlReferrals::getReferral('play_details', 'theatre_details_ongoing_plays')]) !!}">
                <div class="theater-carousel table"
                     style="   background-image:url('{!! $play->mainImage?asset(Croppa::url($play->mainImage->filename,800,null)):null !!}'); ">
                    <div class="table-cell">
                        <div class=" theater-carousel__info">
                            <h2 class="theater-carousel__title">
                                {{$play->title}}
                            </h2>
                            <div class="theater-carousel__synopsis ">
                                @if($play->synopsis)
                                    <div class="  hidden-xs  ">{{ Str::limit($play->synopsis, $limit = 220, $end = '...') }} </div>
                                @endif
                                <div class="theater-carousel__dates ">
                                    @if(isset($play->start_date))
                                        <strong>{!! $play->start_date !!}</strong>
                                    @endif
                                    @if(isset($play->end_date))
                                        - <strong>{!! $play->end_date !!}</strong>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </a>
        @endforeach
        @foreach($theatre->comingSoonPlays as $play)
                <a href="{!! route('plays.show', ['slug' => $play->slug, 'ref_' => \App\Components\UrlReferrals::getReferral('play_details', 'theatre_details_ongoing_plays')]) !!}">
                    <div class="theater-carousel table"
                         style="   background-image:url('{!! $play->mainImage?asset(Croppa::url($play->mainImage->filename,800,null)):null !!}'); ">
                        <div class="table-cell">
                            <div class=" theater-carousel__info">
                                <h2 class="theater-carousel__title">
                                    {{$play->title}}
                                </h2>
                                <div class="theater-carousel__synopsis ">
                                    @if($play->synopsis)
                                        <div class="">{{ Str::limit($play->synopsis, $limit = 220, $end = '...') }} </div>
                                    @endif
                                    <div class="theater-carousel__dates ">
                                        @if(isset($play->start_date))
                                              <strong>{!! $play->start_date !!}</strong>
                                        @endif
                                        @if(isset($play->end_date))
                                            - <strong>{!! $play->end_date !!}</strong>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
        @endforeach
        @foreach($theatre->endeavours as $endeavour)
                <a href="{!! route('endeavours.show', $endeavour->slug) !!}">
                    <div class="theater-carousel table"
                         style=" background-image:url('{!! $endeavour->mainImage?asset(Croppa::url($endeavour->mainImage->filename,800,null)):null !!}'); ">
                        <div class="table-cell">
                            <div class=" theater-carousel__info">
                                <h2 class="theater-carousel__title">
                                    {{$endeavour->title}}
                                </h2>
                                <div class="theater-carousel__synopsis ">
                                    @if($endeavour->synopsis)
                                        <div class="">{{ Str::limit($endeavour->synopsis, $limit = 220, $end = '...') }} </div>
                                    @endif
                                    <div class="theater-carousel__dates ">
                                        @if(isset($endeavour->start_date))
                                              <strong>{!! $endeavour->start_date !!}</strong>
                                        @endif
                                        @if(isset($endeavour->end_date))
                                            - <strong>{!! $endeavour->end_date !!}</strong>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
        @endforeach
    </div>
</div>
