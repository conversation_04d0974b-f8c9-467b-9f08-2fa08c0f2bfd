@extends('search.layout')

@section('results')
    <div class="row">
        <div class="col-md-7">
            <div class="">
                @if($results->totalPlays)
                    <h3 class="search-results__title">Παραστάσεις</h3>
                    @foreach($results->plays as $play)
                        <a href="{!! route('plays.show', ['slug' => $play->slug, 'ref_' => \App\Components\UrlReferrals::getReferral('play_details', 'search_results_all')]) !!}">
                            <div class="media   search-results-play">
                                <div class="media-left">
                                    <div class="media-object search-results-play__photo b-lazy"
                                         data-src="{{ $play->mainImage? asset(Croppa::url($play->mainImage->filename,200,null)):null }}">
                                    </div>
                                </div>
                                <div class="media-body">
                                    <h4 class="search-results-play__title ">{{$play->title}} ({{$play->year}})</h4>
                                    @if($play->theatre)
                                        <h6 class="search-results-play__theater">
                                            <div class="  ">
                                                {{ $play->theatre->name }}
                                            </div>
                                        </h6>
                                    @endif
                                    @if($play->isOngoing() && $play->start_date && $play->end_date)
                                        <div class="search-results-play__dates">
                                            από {!! $play->start_date !!} μέχρι {!! $play->end_date !!}
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </a>
                    @endforeach
                @else
                    <div class="bck white padding padding-left padding-right">Δε βρέθηκαν παραστάσεις</div>
                @endif
                <div class="text-center">{{ $results->plays->render() }}</div>
            </div>
        </div>
    </div>
@stop

