@extends('search.layout')

@section('results')
    <div class="row">
        <div class="col-md-7">
            <div class="">
                @if($results->totalTheatres)
                    <h3 class="search-results__title">Θέατρα</h3>
                    @foreach($results->theatres as $theatre)
                        <div class="search-results-theater ">
                            <div class="table table-width-full hover-underline">
                                <div class="table-cell table-cell--middle table-cell--image ">
                                    <a href="{!! route('theatres.show', $theatre->slug) !!}">
                                        <div>
                                            <h5 class="search-results-theater__name">{!! $theatre->name!!}</h5>
                                        </div>
                                        <p class=" search-results-theater__info">{!! $theatre->address !!}</p>
                                    </a>
                                </div>
                                <div class="table-cell table-cell--middle text-right">
                                    @include('components.buttons.followTheaterMin')
                                </div>
                            </div>
                        </div>
                    @endforeach
                @else
                    <div>Δε βρέθηκαν θέατρα</div>
                @endif
                <div class="text-center">{{ $results->theatres->render() }}</div>
            </div>
        </div>
    </div>
@stop

