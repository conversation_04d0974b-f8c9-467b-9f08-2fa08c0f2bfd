@extends('components.layout')
@section('header')
@stop

@section('content')
    @include('movies.partials._showHero')
    <div class=" js-play-body">
        <div class="container  no-gutters">
            <div class="row no-gutters  ">
                <div class="col-md-12">

                    <div class="row  no-gutters ">
                        <div class="col-md-9">
                            <div class="layout-page__content layout-page ">
                                @if($movie->showableStreamings()->exists())
                                    @include('plays.partials._showStreamButton', ['model' => $movie])
                                @endif
{{--                                    <livewire:serve-ads lazy/>--}}
                                @if(count($movie->images) >= 1)
                                    <div class=" layout-page__section">
                                        <div class="row">
                                            <div class="col-xs-6">
                                                <h5 class=" user-review__title">Φωτογραφίες</h5>
                                            </div>
                                            <div class="col-xs-6 text-right">
                                                <a href="{{route('movies.showMedia', [$movie->slug])}}"
                                                   class="homepage-carousel__link">Όλες οι
                                                    Φωτογραφίες <i class="fa fa-angle-right"></i></a>
                                            </div>
                                        </div>
                                        @include('movies.partials._showMedia')
                                    </div>
                                @endif
                                @if($movie->videos()->count() >= 1)
                                    <div class=" layout-page__section">
                                        @include('movies.partials._showVideos')
                                    </div>
                                @endif
                                @include('movies.partials._showInfo')
                                @include('movies.partials._showPersons')
                                @include('movies.partials._showUserReview')
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="layout-page__sidebar">
                                @include('movies.partials._showSidebar')
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop
@section('footer')
    @parent

    {!! Minify::javascript(array( '/js/frontend/jquery.theatricPlay.js', '/js/image-lightbox/imagelightbox.min.js', '/js/frontend/jquery.initImageLighbox.js', '/js/frontend/jquery.movieRating.js','/js/frontend/jquery.playReview.js' ))->withFullUrl() !!}
@stop
