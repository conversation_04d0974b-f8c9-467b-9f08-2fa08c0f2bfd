<div id="rateIt" class="rateIt--review">
    <div class="play-rate__stars">
        <input type="hidden" class="play-rating no-notify" name="rating"
               @if(  !empty(auth('users')->user()) && auth('users')->user()->getMovieRating($movie->id))
               value="{{auth('users')->user()->getMovieRating($movie->id)}}"
               @endif
               data-movieId="{{$movie->id}}"
               data-url="{{ route('neptune.movieRatings.store') }}"
               data-stop="10"
               data-filled="fa fa-star"
               data-empty="fa fa-star-o"/>
    </div>
    <span class="  userCritic__rating">
                                    <span id="my-rating" class="rate-number">
                                        <?php $logged_user_rating = auth('users')->user()->getMovieRating($movie->id); ?>
                                        @if($logged_user_rating > 0)
                                            {{ $logged_user_rating }}
                                        @else
                                            -
                                        @endif
                                    </span>
                                </span>
</div>