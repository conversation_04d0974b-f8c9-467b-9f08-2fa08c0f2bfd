<div class="play-grid ">
    <a href="{{route('onDemand.show', $streaming->slug)}}">
        @if( $streaming->plays->first()->mainImage()->first())
            <img class="play-grid__image b-lazy_ owl-lazy b-lazy-promise_"
                 data-src="@if($streaming->plays->first()->mainImage()->first())    {!! unstageAsset($streaming->plays->first()->mainImage()->first()->filename)!!} @endif"
                 data-src-retina="@if($streaming->plays->first()->mainImage()->first())    {!! unstageAsset($streaming->plays->first()->mainImage()->first()->filename)!!} @endif"
            />
        @endif
        <div class="play-grid__details play-grid__relative">
            <h3 class="play-grid__title">
                {{$streaming->plays->first()->title}}
            </h3>
            <div class="play-grid__theater">
                @if($streaming->plays()->first()->theatre)
                    <span class="vertical-seperator">
                        {{$streaming->plays()->first()->theatre->name}}
                    </span>
                @endif
                @if(isset($streaming->plays->first()->year) && (int)$streaming->plays->first()->year>0)
                    <span class="vertical-seperator">
                        {{ $streaming->plays->first()->year }}
                    </span>
                @endif
                    @if($streaming->duration !='')
                        <span class="vertical-seperator">
                        {{$streaming->duration}}'
                    </span>
                    @endif
            </div>
        </div>
    </a>
</div>
