<div class="play-grid ">
    <a href="{{ route('onDemand.show', ['slug' => $streaming->slug]) }}">
        <div class="streamCarouselPlaceholder">
            <div class="play-grid__image owl-lazy b-lazy-promise"
                 data-src="{!! $model->mainImage()->first() ? asset(Croppa::url($model->mainImage()->first()->filename,800,null)):'' !!}">
            </div>
        </div>
        <div class="play-grid__details play-grid__relative">
            <h3 class="play-grid__title">
                {{ $streaming->title }}
            </h3>
            @include('onDemand.common._requiresPurchases')
            @include('onDemand.common._playInfo')
        </div>
    </a>
</div>
