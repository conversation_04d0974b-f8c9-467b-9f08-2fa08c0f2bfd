<h4 class="play-grid__theater">
    @if($model instanceOf \App\Models\Movie)
        <span class="vertical-seperator">
            Ταινία
        </span>
    @endif
    @if($model->relationLoaded('genres'))
        @if($model->genres()->exists())
            <span class="vertical-seperator">
                <span class="comma-seperated text">
                @foreach($model->genres()->get() as $genre)
                    <span>{{ $genre->name }}</span>
                @endforeach
                </span>
            </span>
        @endif
    @elseif($model->relationLoaded('filmGenres'))
        @if($model->filmGenres()->exists())
            <span class="vertical-seperator">
                <span class="comma-seperated text">
                @foreach($model->filmGenres()->get() as $genre)
                    <span>{{ $genre->name }}</span>
                @endforeach
                </span>
            </span>
        @endif
    @endif
    @if($model->relationLoaded('theatre'))
        @if($model->theatre()->exists())
            <span class="vertical-seperator">
                {{ $model->theatre()->first()->name }}
            </span>
        @endif
    @endif
    @if($model->year)
        <span class="vertical-seperator">
            {{$model->year}}
        </span>
    @endif
    @if($streaming->duration)
        <span class="vertical-seperator">
            {{$streaming->duration}}'
        </span>
    @endif
</h4>
