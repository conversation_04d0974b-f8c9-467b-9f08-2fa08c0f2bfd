<div class="-mt-10 pt-10" id="contact">
<div class="relative isolate bg-white px-6 py-24 sm:py-24 lg:px-8">
    <svg
        class="absolute inset-0 -z-10 h-full w-full stroke-gray-200 [mask-image:radial-gradient(100%_100%_at_top_right,white,transparent)]"
        aria-hidden="true">
        <defs>
            <pattern id="83fd4e5a-9d52-42fc-97b6-718e5d7ee527" width="200" height="200" x="50%" y="-64"
                     patternUnits="userSpaceOnUse">
                <path d="M100 200V.5M.5 .5H200" fill="none"/>
            </pattern>
        </defs>
        <svg x="50%" y="-64" class="overflow-visible fill-gray-50">
            <path d="M-100.5 0h201v201h-201Z M699.5 0h201v201h-201Z M499.5 400h201v201h-201Z M299.5 800h201v201h-201Z"
                  stroke-width="0"/>
        </svg>
        <rect width="100%" height="100%" stroke-width="0" fill="url(#83fd4e5a-9d52-42fc-97b6-718e5d7ee527)"/>
    </svg>
    <div class="mx-auto max-w-xl lg:max-w-4xl">
        <h2 class="text-3xl font-semibold tracking-tight md:text-4xl">Νέες συνεργασίες</h2>
        <div class="mt-4 flex flex-col gap-10 lg:mt-12 lg:flex-row lg:gap-y-20">
            <form action="{{ route('collaboration.handle') }}" method="post" class="lg:flex-auto">
                {!! csrf_field() !!}
                <div class="grid grid-cols-1 gap-x-8 gap-y-6 sm:grid-cols-2">
                    <div class="col-span-2 sm:col-span-1">
                        <label for="name"
                               class="block text-sm font-semibold leading-6 text-gray-900">{!! trans('contact.name') !!} *</label>
                        <div class="mt-2.5">
                            <input required  type="text" name="name" id="first-name" autocomplete="name" value="{{ old('name') }}"
                                   class="block w-full rounded-md border-0 py-2 placeholder:text-gray-400 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 px-3.5 focus:ring-2 focus:ring-inset focus:ring-orange-400 sm:text-sm sm:leading-6">
                        </div>
                        <span class="text-sm text-red-500">{!! $errors->first('name') !!}</span>
                    </div>
                    <div class="col-span-2 sm:col-span-1">
                        <label for="email"
                               class="block text-sm font-semibold leading-6 text-gray-900">{!! trans('contact.email') !!} *</label>
                        <div class="mt-2.5">
                            <input required type="email" name="email" id="email" autocomplete="email" value="{{ old('email') }}"
                                   class="block w-full rounded-md border-0 py-2 placeholder:text-gray-400 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 px-3.5 focus:ring-2 focus:ring-inset focus:ring-orange-400 sm:text-sm sm:leading-6">
                        </div>
                        <span class="text-sm text-red-500">{!! $errors->first('email') !!}</span>
                    </div>

                    <div class="col-span-2">
                        <label for="phone" class="block text-sm font-semibold leading-6 text-gray-900">{!! trans('contact.phone') !!} *</label>
                        <div class="mt-2.5">
                            <input required type="text" name="phone" id="phone" value="{{ old('phone') }}"
                                   class="block w-full rounded-md border-0 py-2 placeholder:text-gray-400 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 px-3.5 focus:ring-2 focus:ring-inset focus:ring-orange-400 sm:text-sm sm:leading-6">
                        </div>
                        <span class="text-sm text-red-500">{!! $errors->first('phone') !!}</span>
                    </div>
                    <div class="col-span-2">
                        <label for="comment"
                               class="block text-sm font-semibold leading-6 text-gray-900">{!! trans('contact.comment') !!} *</label>
                        <div class="mt-2.5">
                            <textarea required id="comment" name="comment" rows="4"
                                      class="block w-full rounded-md border-0 px-3.5 py-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400
                                       focus:ring-2 focus:ring-inset focus:ring-orange-400 sm:text-sm sm:leading-6"></textarea>
                        </div>
                        <span class="text-sm text-red-500">{!! $errors->first('comment') !!}</span>
                    </div>
                </div>
                <div class="form-group">
                    {!! NoCaptcha::renderJs(App::getLocale()) !!}
                    {!! NoCaptcha::display() !!}
                    <span class="text-sm text-red-500">{!! $errors->first('g-recaptcha-response') !!}</span>
                </div>
                <div class="mt-4">
                    <button type="submit"
                            class="block w-full rounded-md bg-gradient-to-r from-orange-400 to-orange-500 hover:from-orange-400/90  hover:to-orange-600/90  transition
                             px-3.5 py-2.5 text-center   font-semibold text-white shadow-sm
                             focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-orange-600">
                        {!! trans('contact.submit') !!}
                    </button>
                </div>
            </form>
            <div class="order-first lg:order-last lg:mt-6 lg:w-80 lg:flex-none">
                <figure class="lg:mt-10">
                    <blockquote class="text-lg font-semibold leading-8 text-gray-900">
                        <p>Επικοινωνήστε μαζί μας για να σας προτείνουμε τον καλύτερο τρόπο να διαφημιστείτε στο unstage.gr </p>
                    </blockquote>
                </figure>
            </div>
        </div>
    </div>
</div>
</div>
