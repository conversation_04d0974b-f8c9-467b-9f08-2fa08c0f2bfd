body {
    background-color: #f9f9f9;
    color: #0F2B43;
    font-family: 'Noir W05 Light', Arial, Helvetica Neue, Helvetica, sans-serif;
    /*font-family:'Noir W05 Regular, Arial, Helvetica Neue, Helvetica, sans-serif;';*/
    /*font-family:'Noir W05 Medium, Arial, Helvetica Neue, Helvetica, sans-serif;';*/
    /*font-family:'Noir W05 Semi Bold, Arial, Helvetica Neue, Helvetica, sans-serif;';*/
    /*font-family:'Noir W05 Light, Arial, Helvetica Neue, Helvetica, sans-serif;';*/
    /*font-family:'Noir W05 Bold, Arial, Helvetica Neue, Helvetica, sans-serif;';*/
}

a {
    color: #0F2B43;
    text-decoration: none;
    background-color: transparent;
}

a:hover {
    color: #0F2B43;
}

#menu-toggle {
    cursor: pointer;
}

.form-control:focus {
    color: #495057;
    background-color: #fff;
    border-color: #96C8DA;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0);
}

.proHomeSelect {
    margin-bottom: 10px;
    margin-top: 30px;
    font-size: 14px;
}

.table {
    display: table;
    font-size: 14px;
    margin: 0;
}

.table-row {
    display: table-row;
}

.table-cell {
    display: table-cell;
    padding: 10px 20px;
    border-bottom: 1px solid #eee;
    vertical-align: center;
    vertical-align: top;
}

.table-cell:first-child {
    padding-left: 0;
}

.table-cell:last-child {
    padding-right: 0;
}

.table-cell--full {
    width: 100%
}

.table-row:last-child .table-cell {
    border: 0;
}

.table-cell a {
    color: #007bff
}

.galleryImage {
    width: 110px;
    height: 110px;
    object-fit: cover;
    object-position: center;
}
.moveHandle {
    opacity: 0.3;
    cursor: pointer
}

.moveHandle:hover {
    opacity: 1
}

.actionIcon {
    opacity: 0.6;
    cursor: pointer;
    width: 20px;
    color: #0F2B43
}

.actionIcon:hover {
    opacity: 1;
    color: #007bff;
}

.actionIcon--danger:hover {
    color: #dc3545;
}

.actionIcon1 {
    cursor: pointer;
    width: 18px;
    display: block;
    margin-bottom:-6px;
}

.statusIcon {
    width: 16px;
    color: #0F2B43
}

.menuIcon {
    width: 20px;
    margin-right: 5px;
    opacity: 0.8
}

.uploadedAvatar {
    width: 100px;
    height: 100px;
    object-fit: cover;
    object-position: center;
}

h1 {
    font-size: 24px;
    font-family: 'Noir W05 Medium', Arial, Helvetica Neue, Helvetica, sans-serif;
}

.subheader {
    margin-bottom: 20px;
}

.mainContent {
    padding: 30px 30px;
}

.pageCont, .proCard {
    background-color: #fff;
    padding: 25px 25px;
    -webkit-box-shadow: 0 1px 10px rgba(0, 0, 0, .05);
    box-shadow: 0 1px 10px rgba(0, 0, 0, .05);
    margin-bottom: 10px;
}

@media (max-width: 767px) {
    .mainContent {
        padding: 30px 0px;
    }

    .pageCont, .proCard {
        padding: 15px 15px;
    }
}

.proCard h2 {
    font-size: 18px;
    margin-bottom: 20px;
}

.proCard label {
    font-size: 14px;
}

.menu-item {
    padding: 12px 20px;
    font-size: 14px;
    color: #888788;
    border-left: 4px solid transparent;
}

.menu-item.active {
    border-left: 4px solid #4DA6FF;
    color: #323232;
}

.menu-item:hover {
    text-decoration: none;
    color: #323232;
    border-left: 4px solid #4DA6FF
}

.menu-item.active .menuIcon, .menu-item:hover .menuIcon {
    opacity: 1
}

.sidebar-user {
    background-color: #fff;
    font-size: 14px;
    border-bottom: 1px solid #eaeaea;
}

.sidebar-user:last-child {
    border-bottom: 0px;
}

.sidebar-user a {
    text-decoration: none !important;
    font-size: 13px;
}

.sidebar-user .item-padded {
    padding: 15px 20px;
}

.sidebar-user img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    object-position: center;
    margin-right: 10px;
}

#wrapper {
    overflow-x: hidden;
}

#sidebar-wrapper {
    min-height: 100vh;
    margin-left: -15rem;
    -webkit-transition: margin .25s ease-out;
    -moz-transition: margin .25s ease-out;
    -o-transition: margin .25s ease-out;
    transition: margin .25s ease-out;
    background-color: #fff;
    -webkit-box-shadow: 0 1px 10px rgba(0, 0, 0, .05);
    box-shadow: 0 1px 10px rgba(0, 0, 0, .05);
}

.sidebar-heading {
    padding: 0.875rem 1.25rem;
    font-size: 1.2rem;
    background-color: #072B43
}

.sidebar-heading img {
    width: 110px;
}

#sidebar-wrapper .list-group {
    width: 15rem;
}

#page-content-wrapper {
    /*min-width: 100vw;*/
    width: 100%
}

#wrapper.toggled #sidebar-wrapper {
    margin-left: 0;
}

@media (min-width: 768px) {
    #sidebar-wrapper {
        margin-left: 0;
    }

    #page-content-wrapper {
        min-width: 0;
        width: 100%;
    }

    #wrapper.toggled #sidebar-wrapper {
        margin-left: -15rem;
    }
}

.nav-pills .nav-link {
    border-radius: 0rem;
    font-size: 14px;
    padding: 0.5rem 0rem;
    margin: 0 10px;
    color: #888788;
    letter-spacing:0.05em
}

.nav-pills .nav-link.active, .nav-pills .show > .nav-link, .nav-pills .nav-link:hover {
    color: #007bff;
    background-color: transparent;
    border-bottom: 2px solid #007bff;
}

.btn {
    padding: 0.375rem 1.75rem;
    font-size: 0.9rem;
    border-radius: 0.05rem;
}

.note-editor.note-frame {
    border: 1px solid rgba(0, 0, 0, 0.125);
}


/*
* === PAGINATION ===
*/
.pagination {
    margin-top: 10px;
    justify-content: center;
}

.page-link {
    position: relative;
    display: block;
    padding: 0;
    width: 40px;
    height: 40px;
    margin-left: 5px;
    line-height: 40px;
    text-align: center;
    color: #000;
    font-size: 13px;
    background-color: transparent;
    border: 1px solid transparent;
    border-radius: 50%;
}

.page-link svg {
    width: 15px;
    margin-top: -5px;
}

.page-link:hover {
    z-index: 2;
    color: #000;
    text-decoration: none;
    background-color: #e9ecef;
    border-color: #e9ecef;
}

.page-item.active .page-link {
    z-index: 1;
    color: #fff;
    background-color: #007bff;
    border-color: transparent
}

.page-item:first-child .page-link {
    margin-left: 0;
    border-top-left-radius: 50%;
    border-bottom-left-radius: 50%;
}

.page-item:last-child .page-link {
    border-top-right-radius: 50%;
    border-bottom-right-radius: 50%;
}

.page-item.disabled .page-link {
    color: #6c757d;
    pointer-events: none;
    cursor: auto;
    background-color: transparent;
    border-color: transparent;
}

.page-link:focus {
    z-index: 2;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0);
}


.dropify-wrapper {
    position: relative;
    display: block;
    width: 100%;
    max-width: 100%;
    height: 200px;
    padding: 5px 10px;
    overflow: hidden;
    font-family: Roboto, sans-serif;
    font-size: 14px;
    line-height: 22px;
    color: #76838f;
    text-align: center;
    cursor: pointer;
    background-color: #fff;
    background-image: none;
    border: 2px solid #e4eaec;
    margin-bottom: 30px;
}

.dropify-wrapper:hover {
    background-image: -webkit-linear-gradient(135deg, #f6f6f6 25%, transparent 25%, transparent 50%, #f6f6f6 50%, #f6f6f6 75%, transparent 75%, transparent);
    background-image: -o-linear-gradient(135deg, #f6f6f6 25%, transparent 25%, transparent 50%, #f6f6f6 50%, #f6f6f6 75%, transparent 75%, transparent);
    background-image: linear-gradient(-45deg, #f6f6f6 25%, transparent 25%, transparent 50%, #f6f6f6 50%, #f6f6f6 75%, transparent 75%, transparent);
    -webkit-background-size: 30px 30px;
    background-size: 30px 30px;
    -webkit-animation: stripes 2s linear infinite;
    -o-animation: stripes 2s linear infinite;
    animation: stripes 2s linear infinite;
}

 .dropify-message {
    position: relative;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    transform: translateY(-50%);
}

@-webkit-keyframes stripes {
    from {
        background-position: 0 0
    }
    to {
        background-position: 60px 30px
    }
}

@-o-keyframes stripes {
    from {
        background-position: 0 0
    }
    to {
        background-position: 60px 30px
    }
}

@keyframes stripes {
    from {
        background-position: 0 0
    }
    to {
        background-position: 60px 30px
    }
}

.dtp > .dtp-content > .dtp-date-view > header.dtp-header {
    background: #2d70e6;
}

.dtp div.dtp-date, .dtp div.dtp-time {
    background: #327CFE;
    color: #fff;
}

.dtp table.dtp-picker-days tr > td > a.selected {
    background: #327CFE;
    color: #fff;
}

.dtp .p10 > a {
    color: rgba(255, 255, 255, 0.78);
}

.input-error {
    width: 100%;
    margin-top: .25rem;
    font-size: 80%;
    color: #dc3545;
}

.has-error label {
    color: #dc3545;
}

.has-error input {
    border-color: #dc3545;
}

.ui.dropdown .item a {
    text-decoration: none !important;
    font-size: 13px;
}

.ui.dropdown > .text img, .ui.dropdown > .text .image, .ui.dropdown .menu > .item .image, .ui.dropdown .menu > .item img {
    display: inline-block;
    vertical-align: top;
    width: 30px;
    height: 30px;
    margin-top: -0.5em;
    margin-bottom: -0.5em;
}

.ui.dropdown > .text > .icon, .ui.dropdown > .text > .label, .ui.dropdown > .text > .flag, .ui.dropdown > .text img, .ui.dropdown > .text .image, .ui.dropdown .menu > .item > .icon, .ui.dropdown .menu > .item > .label, .ui.dropdown .menu > .item > .flag, .ui.dropdown .menu > .item > .image, .ui.dropdown .menu > .item img {
    margin-left: 0em;
    float: none;
    margin-right: 0.78571429rem;
}

.alert {
    font-size: 14px;
}

.alert .close {
    color: #fff;
    text-shadow: none;
    font-size: 2rem;
}

.stat {

}

.stat_number {
    font-size: 30px;
    font-family: 'Noir W05 Regular', Arial, Helvetica Neue, Helvetica, sans-serif;
}

.stat_number img {
    width: 26px;
    margin-top: -6px;
}

.stat_text {
    font-size: 16px;
    line-height: 1.4
}

.stat_icon {
    margin-bottom: 10px
}

.stat_icon svg {
    width: 40px;
    height: 40px;
}

.stat_icon--success svg {
    stroke: #95C632
}

.stat_icon--alert svg {
    stroke: #C63200;
}

.stat_link a {
    font-size: 12px;
    color: #007bff

}

.featherSmall a {
    display: block;
}

.noDecoration {
    text-decoration: none !important
}

.explainText {
    margin-top: 10px;
    font-size: 15px;
}
.explainText a{
    color:#007bff
}
label + .explainForm{
    margin-top:-7px;
}
.explainForm{
    margin-bottom: 10px;
    font-size: 14px;
    color:#999;
}
.uiselect {
    width: 100%
}

.personSelected {
    padding: 15px 10px;
    width: 100%;
    display: block;
}

.dropdownChevron {
    float: right;
    margin-right: 0;
    margin-top: 6px;
    width: 20px;
}

.ui.dropdown .menu > a.item {
    font-size: 14px;
}

.dropzone {
    min-height:auto;
    border: 0px;
    background: transparent;
    padding: 0;
}
/*.dropify-wrapper{*/
/*    min-height:170px;*/
/*    height:auto*/
/*}*/
.dropzone .dz-preview .dz-image {
    border-radius: 10px;
    overflow: hidden;
    width: 120px;
    height: 120px;
    position: relative;
    display: block;
    z-index: 10;
}
.dz-image img{
    width:100%;
    object-fit: contain;
}
.dropzone .dz-message {
    text-align: center;
    margin: 0;
}
.dropify-wrapper {
    height: auto;
    padding: 0;
    overflow: visible;
}
.dropify-message {
    position: relative;
     top: 0;
     -webkit-transform: none;
    -ms-transform: none;
    -o-transform: none;
     transform: none;
}
.dropzone .dz-preview.dz-image-preview {
    background: transparent;
}
.caption{
    font-size:14px;
}
.captionSaved {
    border: 1px solid #2cc56f;
    position:relative;
}
.captionCont{
    position:relative;
}
.captionSaved + .captionCont::before {
    content: url('/proAssets/img/check-circle.svg');
    width: 20px;
    height:20px;
    position: absolute;
    top: -49px;
    right: 8px;
}
.dropzone .dz-preview .dz-error-message {
    border-radius: 8px;
    font-size: 11px;
    top: 150px;
    line-height: 1.3;
}
.dropzone .dz-preview .dz-success-mark, .dropzone .dz-preview .dz-error-mark {
    margin-top: -37px;
}
@media (max-width: 991px) {
    .galleryImage {
        width: 60px;
        height: 60px;
    }
    .table-cell {
        padding: 10px 5px;
    }
    .pageCont, .proCard {
        padding: 15px 15px;
        margin-bottom: 10px;
    }
    .caption {
        font-size: 13px;
    }
}
.ui.multiple.dropdown > .label {
    font-size: 14px;
    color: #fff;
    background-color: #007bff;
    border-radius:4px;
}
.select2-container--default .select2-selection--multiple .select2-selection__choice {
    font-size: 14px;
    color: #fff;
    background-color: #007bff;
    border-radius:4px;
    border:0;
    padding: 0.35714286em 0.78571429em;
    margin: 0.14285714rem 0.28571429rem 0.14285714rem 0em;

}
.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    color: #fff;
    margin-left: 7px;
    float: right;
    font-size:22px;
    font-style: normal;
    line-height:1;
}
.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
    color: #fff;
    opacity:0.7
}
.selectize-control.multi .selectize-input>div {
    background: #007bff;
    color: #fff;
    font-size:14px;
    border: 0px solid #007bff;
    padding: 0 10px;
    letter-spacing: 0.04em;
}
.selectize-control.plugin-remove_button .item .remove {
    padding: 0px 5px;
    border-left: 1px solid transparent;
    border-radius: 0 2px 2px 0;
    font-size:18px;
}
.selectize-input.input-active,
.selectize-input.input-active:hover,
.selectize-control.multi .selectize-input.focus {
    background: #fff !important;
    outline: 0 !important;
    outline: thin dotted \9 !important;
    border-color: #96C8DA!important;
    -webkit-box-shadow: 0px 2px 3px 0px rgba(34, 36, 38, 0.15)!important;
    box-shadow: 0px 2px 3px 0px rgba(34, 36, 38, 0.15)!important;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,0), 0 0 8px rgba(82,168,236,0) !important;
    -moz-box-shadow: inset 0 1px 1px rgba(0,0,0,0), 0 0 8px rgba(82,168,236,0) !important;
    box-shadow: inset 0 1px 1px rgba(0,0,0,0), 0 0 8px rgba(82,168,236,0) !important;
}
.choices__list--multiple .choices__item {
    display: inline-block;
    vertical-align: middle;
    border-radius: 4px;
    padding: 3px 10px;
    font-size: 14px;
    font-weight: 500;
    margin-right: 3.75px;
    margin-bottom: 3.75px;
    background-color: #007bff;
    border: 1px solid #007bff;
    color: #ffffff;
    word-break: break-all;
    box-sizing: border-box;
}
.choices[data-type*='select-multiple'] .choices__button, .choices[data-type*='text'] .choices__button {
    position: relative;
    display: inline-block;
    margin-top: 0;
    margin-right: -4px;
    margin-bottom: 0;
    margin-left: 8px;
    padding-left: 16px;
    border-left: 1px solid #007bff;
    background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjEiIGhlaWdodD0iMjEiIHZpZXdCb3g9IjAgMCAyMSAyMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSIjRkZGIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0yLjU5Mi4wNDRsMTguMzY0IDE4LjM2NC0yLjU0OCAyLjU0OEwuMDQ0IDIuNTkyeiIvPjxwYXRoIGQ9Ik0wIDE4LjM2NEwxOC4zNjQgMGwyLjU0OCAyLjU0OEwyLjU0OCAyMC45MTJ6Ii8+PC9nPjwvc3ZnPg==);
    background-size: 8px;
    width: 8px;
    line-height: 1;
    opacity: 0.75;
    border-radius: 0;
}
.toast-error {
    background-color: #B7342E;
}
.toast-error .toast-message{
    color:#fff;
}
.toast-success {
    background-color: #007BFF;
}
.toast-success .toast-message{
    color:#fff;
}
#toast-container > .toast {
    background-image: none !important;
}
[aria-live="polite"] > .toast-success:not(.toast-just-text):before {
    content: "";
}
[aria-live="polite"] > .toast-success:not(.toast-just-text), [aria-live="polite"] > .toast-info:not(.toast-just-text), [aria-live="polite"] > .toast-warning:not(.toast-just-text), [aria-live="polite"] > .toast-danger:not(.toast-just-text) {
    padding-left: 20px;
}
.nav-plays{
    margin-top:10px;
}
