$(document).ready(function() {

    // Handle translation suggestion for all fields
    $('[id^="translate_"]').click(function(e) {
        e.preventDefault();

        var fieldName = $(this).attr('id').replace('translate_', '');
        var url = $(this).data('url');
        var playId = window.location.pathname.split('/').slice(-2, -1)[0];

        // Show loading state
        $(this).html('<i class="fa fa-spinner fa-spin"></i> Μετάφραση...');
        $(this).prop('disabled', true);

        $.ajax({
            url: url,
            type: 'POST',
            data: {
                play_id: playId,
                field: fieldName,
                _token: $('input[name="_token"]').val()
            },
            success: function(response) {
                if (response.status) {
                    $('#' + fieldName + '_en').val(response.translation);

                    $(".page-content").prepend('<div class="alert alert-success" role="alert">' +
                        '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                        '<span aria-hidden="true">&times;</span></button>' + response.message + '</div>');
                } else {
                    $(".page-content").prepend('<div class="alert alert-danger" role="alert">' +
                        '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                        '<span aria-hidden="true">&times;</span></button>' + response.message + '</div>');
                }
            },
            error: function() {
                $(".page-content").prepend('<div class="alert alert-danger" role="alert">' +
                    '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                    '<span aria-hidden="true">&times;</span></button>Σφάλμα κατά τη μετάφραση</div>');
            },
            complete: function() {
                // Restore button state
                $('#translate_' + fieldName).html('Πρότεινε μετάφρασιν');
                $('#translate_' + fieldName).prop('disabled', false);
            }
        });
    });

});