a.text-action {
  color: #a3afb7;
}
a.text-action,
a.text-action:hover,
a.text-action:focus {
  text-decoration: none;
}
a.text-action:hover,
a.text-action:focus {
  color: #ccd5db;
}
a.text-action .icon + span {
  margin-left: 3px;
}
a.text-like {
  color: #a3afb7 !important;
}
a.text-like,
a.text-like:hover,
a.text-like:focus {
  text-decoration: none;
}
a.text-like.active,
a.text-like:hover,
a.text-like:focus {
  color: #f96868 !important;
}
.text-action + .text-action {
  margin-left: 6px;
}
.img-bordered {
  padding: 3px;
  border: 1px solid #e4eaec;
}
.img-bordered-primary {
  border-color: #62a8ea !important;
}
.img-bordered-purple {
  border-color: #7c51d1 !important;
}
.img-bordered-red {
  border-color: #e9595b !important;
}
.img-bordered-green {
  border-color: #7dd3ae !important;
}
.img-bordered-orange {
  border-color: #ec9940 !important;
}
h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
  text-shadow: rgba(0, 0, 0, .15) 0 0 1px;
}
h1 .icon:first-child,
h2 .icon:first-child,
h3 .icon:first-child,
h4 .icon:first-child,
h5 .icon:first-child,
h6 .icon:first-child,
.h1 .icon:first-child,
.h2 .icon:first-child,
.h3 .icon:first-child,
.h4 .icon:first-child,
.h5 .icon:first-child,
.h6 .icon:first-child {
  margin-right: .5em;
}
mark,
.mark {
  color: #fff;
  border-radius: 2px;
}
.drop-cap {
  float: left;
  padding: 5px;
  margin-right: 5px;
  font-family: Georgia;
  font-size: 60px;
  line-height: 50px;
  color: #263238;
}
.drop-cap-reversed {
  color: #fff;
  background-color: #263238;
}
.list-icons {
  padding-left: 10px;
  margin-left: 0;
  list-style: none;
}
.list-icons > li {
  margin-top: 6px;
}
.list-icons > li:first-child {
  margin-top: 0;
}
.list-icons > li i {
  float: left;
  width: 1em;
  margin: 0 6px 0 0;
}
.text-primary {
  color: #62a8ea;
}
a.text-primary:hover,
a.text-primary:focus {
  color: #358fe4;
}
.text-success {
  color: #46be8a;
}
a.text-success:hover,
a.text-success:focus {
  color: #369b6f;
}
.text-info {
  color: #57c7d4;
}
a.text-info:hover,
a.text-info:focus {
  color: #33b6c5;
}
.text-warning {
  color: #f2a654;
}
a.text-warning:hover,
a.text-warning:focus {
  color: #ee8d25;
}
.text-danger {
  color: #f96868;
}
a.text-danger:hover,
a.text-danger:focus {
  color: #f73737;
}
blockquote {
  font-size: 20px;
  color: #526069;
  border-left-width: 2px;
}
blockquote footer,
blockquote small,
blockquote .small {
  font-size: 14px;
}
.blockquote-reverse {
  border-right-width: 2px;
}
.blockquote {
  padding: 15px 20px;
  border-left-width: 4px;
  border-radius: 3px;
}
.blockquote.blockquote-reverse {
  border-right-width: 4px;
}
.blockquote-success {
  background-color: rgba(70, 190, 138, .1);
  border-color: #46be8a;
}
.blockquote-info {
  background-color: rgba(87, 199, 212, .1);
  border-color: #57c7d4;
}
.blockquote-warning {
  background-color: rgba(242, 166, 84, .1);
  border-color: #f2a654;
}
.blockquote-danger {
  background-color: rgba(249, 104, 104, .1);
  border-color: #f96868;
}
code {
  border: 1px solid #bcd8f1;
}
.container {
  max-width: 100%;
}
@media (min-width: 1600px) {
  .container {
    width: 1310px;
  }
}
@media (min-width: 1600px) {
  .col-xlg-1, .col-xlg-2, .col-xlg-3, .col-xlg-4, .col-xlg-5, .col-xlg-6, .col-xlg-7, .col-xlg-8, .col-xlg-9, .col-xlg-10, .col-xlg-11, .col-xlg-12 {
    float: left;
  }
  .col-xlg-12 {
    width: 100%;
  }
  .col-xlg-11 {
    width: 91.66666667%;
  }
  .col-xlg-10 {
    width: 83.33333333%;
  }
  .col-xlg-9 {
    width: 75%;
  }
  .col-xlg-8 {
    width: 66.66666667%;
  }
  .col-xlg-7 {
    width: 58.33333333%;
  }
  .col-xlg-6 {
    width: 50%;
  }
  .col-xlg-5 {
    width: 41.66666667%;
  }
  .col-xlg-4 {
    width: 33.33333333%;
  }
  .col-xlg-3 {
    width: 25%;
  }
  .col-xlg-2 {
    width: 16.66666667%;
  }
  .col-xlg-1 {
    width: 8.33333333%;
  }
  .col-xlg-pull-12 {
    right: 100%;
  }
  .col-xlg-pull-11 {
    right: 91.66666667%;
  }
  .col-xlg-pull-10 {
    right: 83.33333333%;
  }
  .col-xlg-pull-9 {
    right: 75%;
  }
  .col-xlg-pull-8 {
    right: 66.66666667%;
  }
  .col-xlg-pull-7 {
    right: 58.33333333%;
  }
  .col-xlg-pull-6 {
    right: 50%;
  }
  .col-xlg-pull-5 {
    right: 41.66666667%;
  }
  .col-xlg-pull-4 {
    right: 33.33333333%;
  }
  .col-xlg-pull-3 {
    right: 25%;
  }
  .col-xlg-pull-2 {
    right: 16.66666667%;
  }
  .col-xlg-pull-1 {
    right: 8.33333333%;
  }
  .col-xlg-pull-0 {
    right: auto;
  }
  .col-xlg-push-12 {
    left: 100%;
  }
  .col-xlg-push-11 {
    left: 91.66666667%;
  }
  .col-xlg-push-10 {
    left: 83.33333333%;
  }
  .col-xlg-push-9 {
    left: 75%;
  }
  .col-xlg-push-8 {
    left: 66.66666667%;
  }
  .col-xlg-push-7 {
    left: 58.33333333%;
  }
  .col-xlg-push-6 {
    left: 50%;
  }
  .col-xlg-push-5 {
    left: 41.66666667%;
  }
  .col-xlg-push-4 {
    left: 33.33333333%;
  }
  .col-xlg-push-3 {
    left: 25%;
  }
  .col-xlg-push-2 {
    left: 16.66666667%;
  }
  .col-xlg-push-1 {
    left: 8.33333333%;
  }
  .col-xlg-push-0 {
    left: auto;
  }
  .col-xlg-offset-12 {
    margin-left: 100%;
  }
  .col-xlg-offset-11 {
    margin-left: 91.66666667%;
  }
  .col-xlg-offset-10 {
    margin-left: 83.33333333%;
  }
  .col-xlg-offset-9 {
    margin-left: 75%;
  }
  .col-xlg-offset-8 {
    margin-left: 66.66666667%;
  }
  .col-xlg-offset-7 {
    margin-left: 58.33333333%;
  }
  .col-xlg-offset-6 {
    margin-left: 50%;
  }
  .col-xlg-offset-5 {
    margin-left: 41.66666667%;
  }
  .col-xlg-offset-4 {
    margin-left: 33.33333333%;
  }
  .col-xlg-offset-3 {
    margin-left: 25%;
  }
  .col-xlg-offset-2 {
    margin-left: 16.66666667%;
  }
  .col-xlg-offset-1 {
    margin-left: 8.33333333%;
  }
  .col-xlg-offset-0 {
    margin-left: 0;
  }
}
.col-xlg-1, .col-xlg-2, .col-xlg-3, .col-xlg-4, .col-xlg-5, .col-xlg-6, .col-xlg-7, .col-xlg-8, .col-xlg-9, .col-xlg-10, .col-xlg-11, .col-xlg-12 {
  position: relative;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px;
}
.row.no-space {
  margin-right: 0;
  margin-left: 0;
}
.row.no-space > [class*="col-"] {
  padding-right: 0;
  padding-left: 0;
}
.row-lg {
  margin-right: -25px;
  margin-left: -25px;
}
.row-lg > .col-xs-1, .row-lg > .col-sm-1, .row-lg > .col-md-1, .row-lg > .col-lg-1, .row-lg > .col-xlg-1, .row-lg > .col-xs-2, .row-lg > .col-sm-2, .row-lg > .col-md-2, .row-lg > .col-lg-2, .row-lg > .col-xlg-2, .row-lg > .col-xs-3, .row-lg > .col-sm-3, .row-lg > .col-md-3, .row-lg > .col-lg-3, .row-lg > .col-xlg-3, .row-lg > .col-xs-4, .row-lg > .col-sm-4, .row-lg > .col-md-4, .row-lg > .col-lg-4, .row-lg > .col-xlg-4, .row-lg > .col-xs-5, .row-lg > .col-sm-5, .row-lg > .col-md-5, .row-lg > .col-lg-5, .row-lg > .col-xlg-5, .row-lg > .col-xs-6, .row-lg > .col-sm-6, .row-lg > .col-md-6, .row-lg > .col-lg-6, .row-lg > .col-xlg-6, .row-lg > .col-xs-7, .row-lg > .col-sm-7, .row-lg > .col-md-7, .row-lg > .col-lg-7, .row-lg > .col-xlg-7, .row-lg > .col-xs-8, .row-lg > .col-sm-8, .row-lg > .col-md-8, .row-lg > .col-lg-8, .row-lg > .col-xlg-8, .row-lg > .col-xs-9, .row-lg > .col-sm-9, .row-lg > .col-md-9, .row-lg > .col-lg-9, .row-lg > .col-xlg-9, .row-lg > .col-xs-10, .row-lg > .col-sm-10, .row-lg > .col-md-10, .row-lg > .col-lg-10, .row-lg > .col-xlg-10, .row-lg > .col-xs-11, .row-lg > .col-sm-11, .row-lg > .col-md-11, .row-lg > .col-lg-11, .row-lg > .col-xlg-11, .row-lg > .col-xs-12, .row-lg > .col-sm-12, .row-lg > .col-md-12, .row-lg > .col-lg-12, .row-lg > .col-xlg-12 {
  padding-right: 25px;
  padding-left: 25px;
}
.table {
  color: #76838f;
}
.table > thead > tr > th,
.table > tfoot > tr > th {
  font-weight: 400;
  color: #526069;
}
.table > thead > tr > th {
  border-bottom: 1px solid #e4eaec;
}
.table > tbody + tbody {
  border-top: 1px solid #e4eaec;
}
.table a {
  text-decoration: underline;
}
.table th > .checkbox-custom:only-child,
.table td > .checkbox-custom:only-child {
  margin-top: 0;
  margin-bottom: 0;
  text-align: center;
}
.table .success,
.table .warning,
.table .danger,
.table .info {
  color: #fff;
}
.table .success a,
.table .warning a,
.table .danger a,
.table .info a {
  color: #fff;
}
.table .cell-30 {
  width: 30px;
}
.table .cell-40 {
  width: 40px;
}
.table .cell-50 {
  width: 50px;
}
.table .cell-60 {
  width: 60px;
}
.table .cell-80 {
  width: 80px;
}
.table .cell-100 {
  width: 100px;
}
.table .cell-120 {
  width: 120px;
}
.table .cell-130 {
  width: 130px;
}
.table .cell-150 {
  width: 150px;
}
.table .cell-180 {
  width: 180px;
}
.table .cell-200 {
  width: 200px;
}
.table .cell-250 {
  width: 250px;
}
.table .cell-300 {
  width: 300px;
}
.table-primary thead tr,
.table-success thead tr,
.table-info thead tr,
.table-warning thead tr,
.table-danger thead tr,
.table-dark thead tr {
  color: #fff;
}
.table-default thead tr {
  background: #f3f7f9;
}
.table-primary thead tr {
  background: #62a8ea;
}
.table-success thead tr {
  background: #46be8a;
}
.table-info thead tr {
  background: #57c7d4;
}
.table-warning thead tr {
  background: #f2a654;
}
.table-danger thead tr {
  background: #f96868;
}
.table-dark thead tr {
  background: #526069;
}
.table-gray thead tr {
  color: #526069;
  background: #ccd5db;
}
.table-bordered > thead > tr > th,
.table-bordered > thead > tr > td {
  border-bottom-width: 1px;
}
.table-bordered > thead:first-child > tr:first-child > th {
  border: 1px solid #e4eaec;
}
.table-section + tbody {
  display: none;
}
.table-section-arrow {
  position: relative;
  display: inline-block;
  font-family: 'Web Icons';
  font-style: normal;
  font-weight: normal;
  text-align: center;
  -webkit-transition: -webkit-transform .15s;
       -o-transition:      -o-transform .15s;
          transition:         transform .15s;
  -webkit-transform: translate(0, 0);
      -ms-transform: translate(0, 0);
       -o-transform: translate(0, 0);
          transform: translate(0, 0);

  text-rendering: auto;
  speak: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.table-section-arrow:before {
  content: '\f181';
}
.table-section.active tr {
  background-color: #f3f7f9;
}
.table-section.active + tbody {
  display: table-row-group;
}
.table-section.active .table-section-arrow {
  -webkit-transform: rotate(-180deg);
      -ms-transform: rotate(-180deg);
       -o-transform: rotate(-180deg);
          transform: rotate(-180deg);
}
.form-control {
  -webkit-box-shadow: none;
          box-shadow: none;
  -webkit-transition: -webkit-box-shadow .25s linear, border .25s linear, color .25s linear, background-color .25s linear;
       -o-transition:         box-shadow .25s linear, border .25s linear, color .25s linear, background-color .25s linear;
          transition:         box-shadow .25s linear, border .25s linear, color .25s linear, background-color .25s linear;
}
.form-control:not(select) {
  -webkit-appearance: none;
}
.has-success .help-block,
.has-success .control-label,
.has-success .radio,
.has-success .checkbox,
.has-success .radio-inline,
.has-success .checkbox-inline,
.has-success.radio label,
.has-success.checkbox label,
.has-success.radio-inline label,
.has-success.checkbox-inline label {
  color: #46be8a;
}
.has-success .form-control {
  border-color: #46be8a;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
          box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
}
.has-success .form-control:focus {
  border-color: #369b6f;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px #91d9ba;
          box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px #91d9ba;
}
.has-success .input-group-addon {
  color: #46be8a;
  background-color: #fff;
  border-color: #46be8a;
}
.has-success .form-control-feedback {
  color: #46be8a;
}
.has-success .form-control {
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .05);
          box-shadow: inset 0 1px 1px rgba(0, 0, 0, .05);
}
.has-success .form-control:focus {
  border-color: #46be8a;
  outline: 0;
  -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(70, 190, 138, .6);
          box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(70, 190, 138, .6);
}
.has-success .form-control.focus,
.has-success .form-control:focus {
  border-color: #46be8a;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.has-warning .help-block,
.has-warning .control-label,
.has-warning .radio,
.has-warning .checkbox,
.has-warning .radio-inline,
.has-warning .checkbox-inline,
.has-warning.radio label,
.has-warning.checkbox label,
.has-warning.radio-inline label,
.has-warning.checkbox-inline label {
  color: #f2a654;
}
.has-warning .form-control {
  border-color: #f2a654;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
          box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
}
.has-warning .form-control:focus {
  border-color: #ee8d25;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px #f9d7b3;
          box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px #f9d7b3;
}
.has-warning .input-group-addon {
  color: #f2a654;
  background-color: #fff;
  border-color: #f2a654;
}
.has-warning .form-control-feedback {
  color: #f2a654;
}
.has-warning .form-control {
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .05);
          box-shadow: inset 0 1px 1px rgba(0, 0, 0, .05);
}
.has-warning .form-control:focus {
  border-color: #f2a654;
  outline: 0;
  -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(242, 166, 84, .6);
          box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(242, 166, 84, .6);
}
.has-warning .form-control.focus,
.has-warning .form-control:focus {
  border-color: #f2a654;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.has-error .help-block,
.has-error .control-label,
.has-error .radio,
.has-error .checkbox,
.has-error .radio-inline,
.has-error .checkbox-inline,
.has-error.radio label,
.has-error.checkbox label,
.has-error.radio-inline label,
.has-error.checkbox-inline label {
  color: #f96868;
}
.has-error .form-control {
  border-color: #f96868;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
          box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
}
.has-error .form-control:focus {
  border-color: #f73737;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px #fdcaca;
          box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px #fdcaca;
}
.has-error .input-group-addon {
  color: #f96868;
  background-color: #fff;
  border-color: #f96868;
}
.has-error .form-control-feedback {
  color: #f96868;
}
.has-error .form-control {
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .05);
          box-shadow: inset 0 1px 1px rgba(0, 0, 0, .05);
}
.has-error .form-control:focus {
  border-color: #f96868;
  outline: 0;
  -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(249, 104, 104, .6);
          box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(249, 104, 104, .6);
}
.has-error .form-control.focus,
.has-error .form-control:focus {
  border-color: #f96868;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.form-group.has-feedback.no-label .form-control-feedback {
  top: 0;
}
.form-group.has-feedback.left-feedback .form-control-feedback {
  right: auto;
  left: 0;
}
.form-group.has-feedback.left-feedback .form-control {
  padding-right: 13px;
  padding-left: 50px;
}
.form-control.square {
  border-radius: 0;
}
.form-control.round {
  border-radius: 200px;
}
textarea.form-control.no-resize {
  resize: none;
}
.input-group-file input[type="text"] {
  background-color: #fff;
}
.input-group-file .btn-file {
  position: relative;
  overflow: hidden;
}
.input-group-file .btn-file.btn-outline {
  border: 1px solid #e4eaec;
  border-left: none;
}
.input-group-file .btn-file.btn-outline:hover {
  border-left: none;
}
.input-group-file .btn-file > .icon {
  margin: 0 3px;
}
.input-group-file .btn-file input[type="file"] {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  min-width: 100%;
  min-height: 100%;
  text-align: 0;
  cursor: pointer;
  opacity: 0;
}
.help-block {
  margin-top: 7px;
  margin-bottom: 8px;
}
.help-block > .icon {
  margin: 0 5px;
}
.input-search-close {
  color: #000;
  text-shadow: none;
  filter: alpha(opacity=20);
  opacity: .2;
}
.input-search-close.icon {
  font-size: inherit;
  line-height: inherit;
}
.input-search-close:hover,
.input-search-close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
  filter: alpha(opacity=50);
  opacity: .5;
}
button.input-search-close {
  -webkit-appearance: none;
  padding: 0;
  cursor: pointer;
  background: transparent;
  border: 0;
}
.input-search {
  position: relative;
}
.input-search .form-control {
  border-radius: 200px;
}
.input-search .input-search-icon,
.input-search .input-search-close {
  position: absolute;
  top: 50%;
  z-index: 1;
  width: 36px;
  -webkit-transform: translateY(-50%);
      -ms-transform: translateY(-50%);
       -o-transform: translateY(-50%);
          transform: translateY(-50%);
}
.input-search .input-search-close {
  right: 8px;
}
.input-search .input-search-icon + .form-control {
  padding-left: 43.5px;
}
.input-search .input-search-icon {
  left: 8px;
  font-size: 16px;
  color: #a3afb7;
  text-align: center;
  pointer-events: none;
}
.input-search-btn + .form-control {
  padding-right: 50px;
}
.input-search-btn {
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  padding: 0 10px;
  background: transparent;
  border: none;
  border-radius: 0 200px 200px 0;
}
.input-search-btn > .icon {
  margin: 0 3px;
}
.input-search-dark .input-search-icon {
  color: #76838f;
}
.input-search-dark .form-control {
  background: #f3f7f9;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.input-search-dark .form-control:focus {
  background-color: transparent;
}
.form-inline .form-group {
  margin-right: 20px;
}
.form-inline .form-group:last-child {
  margin-right: 0;
}
.form-inline .control-label {
  margin-right: 5px;
}
@media (max-width: 767px) {
  .form-inline .form-group {
    margin-right: 0;
  }
}
/*@btn-floating-xs-padding:                10px;*/
/*@btn-floating-sm-padding:                13px;*/
/*@btn-floating-lg-padding:                15px;*/
.btn {
  padding: 6px 15px;
  font-size: 14px;
  line-height: 1.57142857;
  border-radius: 3px;
  -webkit-transition: border .2s linear, color .2s linear, width .2s linear, background-color .2s linear;
       -o-transition: border .2s linear, color .2s linear, width .2s linear, background-color .2s linear;
          transition: border .2s linear, color .2s linear, width .2s linear, background-color .2s linear;

  -webkit-font-smoothing: subpixel-antialiased;
}
.btn:focus,
.btn:active:focus,
.btn.active:focus {
  outline: 0;
}
.btn:active,
.btn.active {
  -webkit-box-shadow: none;
          box-shadow: none;
}
.btn .icon {
  width: 1em;
  margin: -1px 3px 0;
  line-height: inherit;
  text-align: center;
}
.btn-block {
  white-space: normal;
}
.btn-outline.btn-default {
  color: #76838f;
  background-color: transparent;
}
.btn-outline.btn-default:hover,
.btn-outline.btn-default:focus,
.btn-outline.btn-default:active,
.btn-outline.btn-default.active,
.open > .dropdown-toggle.btn-outline.btn-default {
  color: #76838f;
  background-color: rgba(118, 131, 143, .1);
  border-color: #e4eaec;
}
.btn-outline.btn-default:hover .badge,
.btn-outline.btn-default:focus .badge,
.btn-outline.btn-default:active .badge,
.btn-outline.btn-default.active .badge,
.open > .dropdown-toggle.btn-outline.btn-default .badge {
  color: #76838f;
  background-color: #76838f;
}
.btn-outline.btn-primary {
  color: #62a8ea;
  background-color: transparent;
}
.btn-outline.btn-primary:hover,
.btn-outline.btn-primary:focus,
.btn-outline.btn-primary:active,
.btn-outline.btn-primary.active,
.open > .dropdown-toggle.btn-outline.btn-primary {
  color: #fff;
  background-color: #62a8ea;
  border-color: #62a8ea;
}
.btn-outline.btn-primary:hover .badge,
.btn-outline.btn-primary:focus .badge,
.btn-outline.btn-primary:active .badge,
.btn-outline.btn-primary.active .badge,
.open > .dropdown-toggle.btn-outline.btn-primary .badge {
  color: #62a8ea;
  background-color: #fff;
}
.btn-outline.btn-success {
  color: #46be8a;
  background-color: transparent;
}
.btn-outline.btn-success:hover,
.btn-outline.btn-success:focus,
.btn-outline.btn-success:active,
.btn-outline.btn-success.active,
.open > .dropdown-toggle.btn-outline.btn-success {
  color: #fff;
  background-color: #46be8a;
  border-color: #46be8a;
}
.btn-outline.btn-success:hover .badge,
.btn-outline.btn-success:focus .badge,
.btn-outline.btn-success:active .badge,
.btn-outline.btn-success.active .badge,
.open > .dropdown-toggle.btn-outline.btn-success .badge {
  color: #46be8a;
  background-color: #fff;
}
.btn-outline.btn-info {
  color: #57c7d4;
  background-color: transparent;
}
.btn-outline.btn-info:hover,
.btn-outline.btn-info:focus,
.btn-outline.btn-info:active,
.btn-outline.btn-info.active,
.open > .dropdown-toggle.btn-outline.btn-info {
  color: #fff;
  background-color: #57c7d4;
  border-color: #57c7d4;
}
.btn-outline.btn-info:hover .badge,
.btn-outline.btn-info:focus .badge,
.btn-outline.btn-info:active .badge,
.btn-outline.btn-info.active .badge,
.open > .dropdown-toggle.btn-outline.btn-info .badge {
  color: #57c7d4;
  background-color: #fff;
}
.btn-outline.btn-warning {
  color: #f2a654;
  background-color: transparent;
}
.btn-outline.btn-warning:hover,
.btn-outline.btn-warning:focus,
.btn-outline.btn-warning:active,
.btn-outline.btn-warning.active,
.open > .dropdown-toggle.btn-outline.btn-warning {
  color: #fff;
  background-color: #f2a654;
  border-color: #f2a654;
}
.btn-outline.btn-warning:hover .badge,
.btn-outline.btn-warning:focus .badge,
.btn-outline.btn-warning:active .badge,
.btn-outline.btn-warning.active .badge,
.open > .dropdown-toggle.btn-outline.btn-warning .badge {
  color: #f2a654;
  background-color: #fff;
}
.btn-outline.btn-danger {
  color: #f96868;
  background-color: transparent;
}
.btn-outline.btn-danger:hover,
.btn-outline.btn-danger:focus,
.btn-outline.btn-danger:active,
.btn-outline.btn-danger.active,
.open > .dropdown-toggle.btn-outline.btn-danger {
  color: #fff;
  background-color: #f96868;
  border-color: #f96868;
}
.btn-outline.btn-danger:hover .badge,
.btn-outline.btn-danger:focus .badge,
.btn-outline.btn-danger:active .badge,
.btn-outline.btn-danger.active .badge,
.open > .dropdown-toggle.btn-outline.btn-danger .badge {
  color: #f96868;
  background-color: #fff;
}
.btn-outline.btn-dark {
  color: #526069;
  background-color: transparent;
}
.btn-outline.btn-dark:hover,
.btn-outline.btn-dark:focus,
.btn-outline.btn-dark:active,
.btn-outline.btn-dark.active,
.open > .dropdown-toggle.btn-outline.btn-dark {
  color: #fff;
  background-color: #526069;
  border-color: #526069;
}
.btn-outline.btn-dark:hover .badge,
.btn-outline.btn-dark:focus .badge,
.btn-outline.btn-dark:active .badge,
.btn-outline.btn-dark.active .badge,
.open > .dropdown-toggle.btn-outline.btn-dark .badge {
  color: #526069;
  background-color: #fff;
}
.btn-outline.btn-inverse {
  color: #fff;
  background-color: transparent;
}
.btn-outline.btn-inverse:hover,
.btn-outline.btn-inverse:focus,
.btn-outline.btn-inverse:active,
.btn-outline.btn-inverse.active,
.open > .dropdown-toggle.btn-outline.btn-inverse {
  color: #76838f;
  background-color: #fff;
  border-color: #fff;
}
.btn-outline.btn-inverse:hover .badge,
.btn-outline.btn-inverse:focus .badge,
.btn-outline.btn-inverse:active .badge,
.btn-outline.btn-inverse.active .badge,
.open > .dropdown-toggle.btn-outline.btn-inverse .badge {
  color: #fff;
  background-color: #76838f;
}
.btn-lg {
  padding: 10px 18px;
  font-size: 18px;
  line-height: 1.3333333;
  border-radius: 4px;
}
.btn-sm {
  padding: 6px 13px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-xs {
  padding: 1px 5px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-squared {
  border-radius: 0;
}
.btn-round {
  border-radius: 1000px;
}
.btn-default:hover,
.btn-default:focus,
.btn-default.focus {
  background-color: #f3f7f9;
  border-color: #f3f7f9;
}
.btn-default:active,
.btn-default.active,
.open > .dropdown-toggle.btn-default {
  background-color: #ccd5db;
  border-color: #ccd5db;
}
.btn-default:active:hover,
.btn-default.active:hover,
.open > .dropdown-toggle.btn-default:hover,
.btn-default:active:focus,
.btn-default.active:focus,
.open > .dropdown-toggle.btn-default:focus,
.btn-default:active.focus,
.btn-default.active.focus,
.open > .dropdown-toggle.btn-default.focus {
  background-color: #ccd5db;
  border-color: #ccd5db;
}
.btn-default.disabled,
.btn-default[disabled],
fieldset[disabled] .btn-default,
.btn-default.disabled:hover,
.btn-default[disabled]:hover,
fieldset[disabled] .btn-default:hover,
.btn-default.disabled:focus,
.btn-default[disabled]:focus,
fieldset[disabled] .btn-default:focus,
.btn-default.disabled.focus,
.btn-default[disabled].focus,
fieldset[disabled] .btn-default.focus,
.btn-default.disabled:active,
.btn-default[disabled]:active,
fieldset[disabled] .btn-default:active,
.btn-default.disabled.active,
.btn-default[disabled].active,
fieldset[disabled] .btn-default.active {
  color: #76838f;
  background-color: #f3f7f9;
  border-color: #f3f7f9;
}
.btn-default.btn-up:before {
  border-bottom-color: #e4eaec;
}
.btn-default.btn-up:hover:before,
.btn-default.btn-up:focus:before {
  border-bottom-color: #f3f7f9;
}
.btn-default.btn-up:active:before,
.btn-default.btn-up.active:before,
.open > .dropdown-toggle.btn-default.btn-up:before {
  border-bottom-color: #ccd5db;
}
.btn-default.btn-right:before {
  border-left-color: #e4eaec;
}
.btn-default.btn-right:hover:before,
.btn-default.btn-right:focus:before {
  border-left-color: #f3f7f9;
}
.btn-default.btn-right:active:before,
.btn-default.btn-right.active:before,
.open > .dropdown-toggle.btn-default.btn-right:before {
  border-left-color: #ccd5db;
}
.btn-default.btn-bottom:before {
  border-top-color: #e4eaec;
}
.btn-default.btn-bottom:hover:before,
.btn-default.btn-bottom:focus:before {
  border-top-color: #f3f7f9;
}
.btn-default.btn-bottom:active:before,
.btn-default.btn-bottom.active:before,
.open > .dropdown-toggle.btn-default.btn-bottom:before {
  border-top-color: #ccd5db;
}
.btn-default.btn-left:before {
  border-right-color: #e4eaec;
}
.btn-default.btn-left:hover:before,
.btn-default.btn-left:focus:before {
  border-right-color: #f3f7f9;
}
.btn-default.btn-left:active:before,
.btn-default.btn-left.active:before,
.open > .dropdown-toggle.btn-default.btn-left:before {
  border-right-color: #ccd5db;
}
.btn-primary:hover,
.btn-primary:focus,
.btn-primary.focus {
  background-color: #89bceb;
  border-color: #89bceb;
}
.btn-primary:active,
.btn-primary.active,
.open > .dropdown-toggle.btn-primary {
  background-color: #4e97d9;
  border-color: #4e97d9;
}
.btn-primary:active:hover,
.btn-primary.active:hover,
.open > .dropdown-toggle.btn-primary:hover,
.btn-primary:active:focus,
.btn-primary.active:focus,
.open > .dropdown-toggle.btn-primary:focus,
.btn-primary:active.focus,
.btn-primary.active.focus,
.open > .dropdown-toggle.btn-primary.focus {
  background-color: #4e97d9;
  border-color: #4e97d9;
}
.btn-primary.disabled,
.btn-primary[disabled],
fieldset[disabled] .btn-primary,
.btn-primary.disabled:hover,
.btn-primary[disabled]:hover,
fieldset[disabled] .btn-primary:hover,
.btn-primary.disabled:focus,
.btn-primary[disabled]:focus,
fieldset[disabled] .btn-primary:focus,
.btn-primary.disabled.focus,
.btn-primary[disabled].focus,
fieldset[disabled] .btn-primary.focus,
.btn-primary.disabled:active,
.btn-primary[disabled]:active,
fieldset[disabled] .btn-primary:active,
.btn-primary.disabled.active,
.btn-primary[disabled].active,
fieldset[disabled] .btn-primary.active {
  color: #fff;
  background-color: #a2caee;
  border-color: #a2caee;
}
.btn-primary.btn-up:before {
  border-bottom-color: #62a8ea;
}
.btn-primary.btn-up:hover:before,
.btn-primary.btn-up:focus:before {
  border-bottom-color: #89bceb;
}
.btn-primary.btn-up:active:before,
.btn-primary.btn-up.active:before,
.open > .dropdown-toggle.btn-primary.btn-up:before {
  border-bottom-color: #4e97d9;
}
.btn-primary.btn-right:before {
  border-left-color: #62a8ea;
}
.btn-primary.btn-right:hover:before,
.btn-primary.btn-right:focus:before {
  border-left-color: #89bceb;
}
.btn-primary.btn-right:active:before,
.btn-primary.btn-right.active:before,
.open > .dropdown-toggle.btn-primary.btn-right:before {
  border-left-color: #4e97d9;
}
.btn-primary.btn-bottom:before {
  border-top-color: #62a8ea;
}
.btn-primary.btn-bottom:hover:before,
.btn-primary.btn-bottom:focus:before {
  border-top-color: #89bceb;
}
.btn-primary.btn-bottom:active:before,
.btn-primary.btn-bottom.active:before,
.open > .dropdown-toggle.btn-primary.btn-bottom:before {
  border-top-color: #4e97d9;
}
.btn-primary.btn-left:before {
  border-right-color: #62a8ea;
}
.btn-primary.btn-left:hover:before,
.btn-primary.btn-left:focus:before {
  border-right-color: #89bceb;
}
.btn-primary.btn-left:active:before,
.btn-primary.btn-left.active:before,
.open > .dropdown-toggle.btn-primary.btn-left:before {
  border-right-color: #4e97d9;
}
.btn-success:hover,
.btn-success:focus,
.btn-success.focus {
  background-color: #5cd29d;
  border-color: #5cd29d;
}
.btn-success:active,
.btn-success.active,
.open > .dropdown-toggle.btn-success {
  background-color: #36ab7a;
  border-color: #36ab7a;
}
.btn-success:active:hover,
.btn-success.active:hover,
.open > .dropdown-toggle.btn-success:hover,
.btn-success:active:focus,
.btn-success.active:focus,
.open > .dropdown-toggle.btn-success:focus,
.btn-success:active.focus,
.btn-success.active.focus,
.open > .dropdown-toggle.btn-success.focus {
  background-color: #36ab7a;
  border-color: #36ab7a;
}
.btn-success.disabled,
.btn-success[disabled],
fieldset[disabled] .btn-success,
.btn-success.disabled:hover,
.btn-success[disabled]:hover,
fieldset[disabled] .btn-success:hover,
.btn-success.disabled:focus,
.btn-success[disabled]:focus,
fieldset[disabled] .btn-success:focus,
.btn-success.disabled.focus,
.btn-success[disabled].focus,
fieldset[disabled] .btn-success.focus,
.btn-success.disabled:active,
.btn-success[disabled]:active,
fieldset[disabled] .btn-success:active,
.btn-success.disabled.active,
.btn-success[disabled].active,
fieldset[disabled] .btn-success.active {
  color: #fff;
  background-color: #7dd3ae;
  border-color: #7dd3ae;
}
.btn-success.btn-up:before {
  border-bottom-color: #46be8a;
}
.btn-success.btn-up:hover:before,
.btn-success.btn-up:focus:before {
  border-bottom-color: #5cd29d;
}
.btn-success.btn-up:active:before,
.btn-success.btn-up.active:before,
.open > .dropdown-toggle.btn-success.btn-up:before {
  border-bottom-color: #36ab7a;
}
.btn-success.btn-right:before {
  border-left-color: #46be8a;
}
.btn-success.btn-right:hover:before,
.btn-success.btn-right:focus:before {
  border-left-color: #5cd29d;
}
.btn-success.btn-right:active:before,
.btn-success.btn-right.active:before,
.open > .dropdown-toggle.btn-success.btn-right:before {
  border-left-color: #36ab7a;
}
.btn-success.btn-bottom:before {
  border-top-color: #46be8a;
}
.btn-success.btn-bottom:hover:before,
.btn-success.btn-bottom:focus:before {
  border-top-color: #5cd29d;
}
.btn-success.btn-bottom:active:before,
.btn-success.btn-bottom.active:before,
.open > .dropdown-toggle.btn-success.btn-bottom:before {
  border-top-color: #36ab7a;
}
.btn-success.btn-left:before {
  border-right-color: #46be8a;
}
.btn-success.btn-left:hover:before,
.btn-success.btn-left:focus:before {
  border-right-color: #5cd29d;
}
.btn-success.btn-left:active:before,
.btn-success.btn-left.active:before,
.open > .dropdown-toggle.btn-success.btn-left:before {
  border-right-color: #36ab7a;
}
.btn-info:hover,
.btn-info:focus,
.btn-info.focus {
  background-color: #77d6e1;
  border-color: #77d6e1;
}
.btn-info:active,
.btn-info.active,
.open > .dropdown-toggle.btn-info {
  background-color: #47b8c6;
  border-color: #47b8c6;
}
.btn-info:active:hover,
.btn-info.active:hover,
.open > .dropdown-toggle.btn-info:hover,
.btn-info:active:focus,
.btn-info.active:focus,
.open > .dropdown-toggle.btn-info:focus,
.btn-info:active.focus,
.btn-info.active.focus,
.open > .dropdown-toggle.btn-info.focus {
  background-color: #47b8c6;
  border-color: #47b8c6;
}
.btn-info.disabled,
.btn-info[disabled],
fieldset[disabled] .btn-info,
.btn-info.disabled:hover,
.btn-info[disabled]:hover,
fieldset[disabled] .btn-info:hover,
.btn-info.disabled:focus,
.btn-info[disabled]:focus,
fieldset[disabled] .btn-info:focus,
.btn-info.disabled.focus,
.btn-info[disabled].focus,
fieldset[disabled] .btn-info.focus,
.btn-info.disabled:active,
.btn-info[disabled]:active,
fieldset[disabled] .btn-info:active,
.btn-info.disabled.active,
.btn-info[disabled].active,
fieldset[disabled] .btn-info.active {
  color: #fff;
  background-color: #9ae1e9;
  border-color: #9ae1e9;
}
.btn-info.btn-up:before {
  border-bottom-color: #57c7d4;
}
.btn-info.btn-up:hover:before,
.btn-info.btn-up:focus:before {
  border-bottom-color: #77d6e1;
}
.btn-info.btn-up:active:before,
.btn-info.btn-up.active:before,
.open > .dropdown-toggle.btn-info.btn-up:before {
  border-bottom-color: #47b8c6;
}
.btn-info.btn-right:before {
  border-left-color: #57c7d4;
}
.btn-info.btn-right:hover:before,
.btn-info.btn-right:focus:before {
  border-left-color: #77d6e1;
}
.btn-info.btn-right:active:before,
.btn-info.btn-right.active:before,
.open > .dropdown-toggle.btn-info.btn-right:before {
  border-left-color: #47b8c6;
}
.btn-info.btn-bottom:before {
  border-top-color: #57c7d4;
}
.btn-info.btn-bottom:hover:before,
.btn-info.btn-bottom:focus:before {
  border-top-color: #77d6e1;
}
.btn-info.btn-bottom:active:before,
.btn-info.btn-bottom.active:before,
.open > .dropdown-toggle.btn-info.btn-bottom:before {
  border-top-color: #47b8c6;
}
.btn-info.btn-left:before {
  border-right-color: #57c7d4;
}
.btn-info.btn-left:hover:before,
.btn-info.btn-left:focus:before {
  border-right-color: #77d6e1;
}
.btn-info.btn-left:active:before,
.btn-info.btn-left.active:before,
.open > .dropdown-toggle.btn-info.btn-left:before {
  border-right-color: #47b8c6;
}
.btn-warning:hover,
.btn-warning:focus,
.btn-warning.focus {
  background-color: #f4b066;
  border-color: #f4b066;
}
.btn-warning:active,
.btn-warning.active,
.open > .dropdown-toggle.btn-warning {
  background-color: #ec9940;
  border-color: #ec9940;
}
.btn-warning:active:hover,
.btn-warning.active:hover,
.open > .dropdown-toggle.btn-warning:hover,
.btn-warning:active:focus,
.btn-warning.active:focus,
.open > .dropdown-toggle.btn-warning:focus,
.btn-warning:active.focus,
.btn-warning.active.focus,
.open > .dropdown-toggle.btn-warning.focus {
  background-color: #ec9940;
  border-color: #ec9940;
}
.btn-warning.disabled,
.btn-warning[disabled],
fieldset[disabled] .btn-warning,
.btn-warning.disabled:hover,
.btn-warning[disabled]:hover,
fieldset[disabled] .btn-warning:hover,
.btn-warning.disabled:focus,
.btn-warning[disabled]:focus,
fieldset[disabled] .btn-warning:focus,
.btn-warning.disabled.focus,
.btn-warning[disabled].focus,
fieldset[disabled] .btn-warning.focus,
.btn-warning.disabled:active,
.btn-warning[disabled]:active,
fieldset[disabled] .btn-warning:active,
.btn-warning.disabled.active,
.btn-warning[disabled].active,
fieldset[disabled] .btn-warning.active {
  color: #fff;
  background-color: #f6be80;
  border-color: #f6be80;
}
.btn-warning.btn-up:before {
  border-bottom-color: #f2a654;
}
.btn-warning.btn-up:hover:before,
.btn-warning.btn-up:focus:before {
  border-bottom-color: #f4b066;
}
.btn-warning.btn-up:active:before,
.btn-warning.btn-up.active:before,
.open > .dropdown-toggle.btn-warning.btn-up:before {
  border-bottom-color: #ec9940;
}
.btn-warning.btn-right:before {
  border-left-color: #f2a654;
}
.btn-warning.btn-right:hover:before,
.btn-warning.btn-right:focus:before {
  border-left-color: #f4b066;
}
.btn-warning.btn-right:active:before,
.btn-warning.btn-right.active:before,
.open > .dropdown-toggle.btn-warning.btn-right:before {
  border-left-color: #ec9940;
}
.btn-warning.btn-bottom:before {
  border-top-color: #f2a654;
}
.btn-warning.btn-bottom:hover:before,
.btn-warning.btn-bottom:focus:before {
  border-top-color: #f4b066;
}
.btn-warning.btn-bottom:active:before,
.btn-warning.btn-bottom.active:before,
.open > .dropdown-toggle.btn-warning.btn-bottom:before {
  border-top-color: #ec9940;
}
.btn-warning.btn-left:before {
  border-right-color: #f2a654;
}
.btn-warning.btn-left:hover:before,
.btn-warning.btn-left:focus:before {
  border-right-color: #f4b066;
}
.btn-warning.btn-left:active:before,
.btn-warning.btn-left.active:before,
.open > .dropdown-toggle.btn-warning.btn-left:before {
  border-right-color: #ec9940;
}
.btn-danger:hover,
.btn-danger:focus,
.btn-danger.focus {
  background-color: #fa7a7a;
  border-color: #fa7a7a;
}
.btn-danger:active,
.btn-danger.active,
.open > .dropdown-toggle.btn-danger {
  background-color: #e9595b;
  border-color: #e9595b;
}
.btn-danger:active:hover,
.btn-danger.active:hover,
.open > .dropdown-toggle.btn-danger:hover,
.btn-danger:active:focus,
.btn-danger.active:focus,
.open > .dropdown-toggle.btn-danger:focus,
.btn-danger:active.focus,
.btn-danger.active.focus,
.open > .dropdown-toggle.btn-danger.focus {
  background-color: #e9595b;
  border-color: #e9595b;
}
.btn-danger.disabled,
.btn-danger[disabled],
fieldset[disabled] .btn-danger,
.btn-danger.disabled:hover,
.btn-danger[disabled]:hover,
fieldset[disabled] .btn-danger:hover,
.btn-danger.disabled:focus,
.btn-danger[disabled]:focus,
fieldset[disabled] .btn-danger:focus,
.btn-danger.disabled.focus,
.btn-danger[disabled].focus,
fieldset[disabled] .btn-danger.focus,
.btn-danger.disabled:active,
.btn-danger[disabled]:active,
fieldset[disabled] .btn-danger:active,
.btn-danger.disabled.active,
.btn-danger[disabled].active,
fieldset[disabled] .btn-danger.active {
  color: #fff;
  background-color: #fa9898;
  border-color: #fa9898;
}
.btn-danger.btn-up:before {
  border-bottom-color: #f96868;
}
.btn-danger.btn-up:hover:before,
.btn-danger.btn-up:focus:before {
  border-bottom-color: #fa7a7a;
}
.btn-danger.btn-up:active:before,
.btn-danger.btn-up.active:before,
.open > .dropdown-toggle.btn-danger.btn-up:before {
  border-bottom-color: #e9595b;
}
.btn-danger.btn-right:before {
  border-left-color: #f96868;
}
.btn-danger.btn-right:hover:before,
.btn-danger.btn-right:focus:before {
  border-left-color: #fa7a7a;
}
.btn-danger.btn-right:active:before,
.btn-danger.btn-right.active:before,
.open > .dropdown-toggle.btn-danger.btn-right:before {
  border-left-color: #e9595b;
}
.btn-danger.btn-bottom:before {
  border-top-color: #f96868;
}
.btn-danger.btn-bottom:hover:before,
.btn-danger.btn-bottom:focus:before {
  border-top-color: #fa7a7a;
}
.btn-danger.btn-bottom:active:before,
.btn-danger.btn-bottom.active:before,
.open > .dropdown-toggle.btn-danger.btn-bottom:before {
  border-top-color: #e9595b;
}
.btn-danger.btn-left:before {
  border-right-color: #f96868;
}
.btn-danger.btn-left:hover:before,
.btn-danger.btn-left:focus:before {
  border-right-color: #fa7a7a;
}
.btn-danger.btn-left:active:before,
.btn-danger.btn-left.active:before,
.open > .dropdown-toggle.btn-danger.btn-left:before {
  border-right-color: #e9595b;
}
.btn-inverse {
  color: #76838f;
  background-color: #fff;
  border-color: #e4eaec;
}
.btn-inverse:focus,
.btn-inverse.focus {
  color: #76838f;
  background-color: #e6e6e6;
  border-color: #99b0b7;
}
.btn-inverse:hover {
  color: #76838f;
  background-color: #e6e6e6;
  border-color: #c0ced3;
}
.btn-inverse:active,
.btn-inverse.active,
.open > .dropdown-toggle.btn-inverse {
  color: #76838f;
  background-color: #e6e6e6;
  border-color: #c0ced3;
}
.btn-inverse:active:hover,
.btn-inverse.active:hover,
.open > .dropdown-toggle.btn-inverse:hover,
.btn-inverse:active:focus,
.btn-inverse.active:focus,
.open > .dropdown-toggle.btn-inverse:focus,
.btn-inverse:active.focus,
.btn-inverse.active.focus,
.open > .dropdown-toggle.btn-inverse.focus {
  color: #76838f;
  background-color: #d4d4d4;
  border-color: #99b0b7;
}
.btn-inverse:active,
.btn-inverse.active,
.open > .dropdown-toggle.btn-inverse {
  background-image: none;
}
.btn-inverse.disabled:hover,
.btn-inverse[disabled]:hover,
fieldset[disabled] .btn-inverse:hover,
.btn-inverse.disabled:focus,
.btn-inverse[disabled]:focus,
fieldset[disabled] .btn-inverse:focus,
.btn-inverse.disabled.focus,
.btn-inverse[disabled].focus,
fieldset[disabled] .btn-inverse.focus {
  background-color: #fff;
  border-color: #e4eaec;
}
.btn-inverse .badge {
  color: #fff;
  background-color: #76838f;
}
.btn-inverse:hover,
.btn-inverse:focus,
.btn-inverse.focus {
  background-color: #fff;
  border-color: #f3f7f9;
}
.btn-inverse:active,
.btn-inverse.active,
.open > .dropdown-toggle.btn-inverse {
  background-color: #fff;
  border-color: #ccd5db;
}
.btn-inverse:active:hover,
.btn-inverse.active:hover,
.open > .dropdown-toggle.btn-inverse:hover,
.btn-inverse:active:focus,
.btn-inverse.active:focus,
.open > .dropdown-toggle.btn-inverse:focus,
.btn-inverse:active.focus,
.btn-inverse.active.focus,
.open > .dropdown-toggle.btn-inverse.focus {
  background-color: #fff;
  border-color: #ccd5db;
}
.btn-inverse.disabled,
.btn-inverse[disabled],
fieldset[disabled] .btn-inverse,
.btn-inverse.disabled:hover,
.btn-inverse[disabled]:hover,
fieldset[disabled] .btn-inverse:hover,
.btn-inverse.disabled:focus,
.btn-inverse[disabled]:focus,
fieldset[disabled] .btn-inverse:focus,
.btn-inverse.disabled.focus,
.btn-inverse[disabled].focus,
fieldset[disabled] .btn-inverse.focus,
.btn-inverse.disabled:active,
.btn-inverse[disabled]:active,
fieldset[disabled] .btn-inverse:active,
.btn-inverse.disabled.active,
.btn-inverse[disabled].active,
fieldset[disabled] .btn-inverse.active {
  color: #ccd5db;
  background-color: #fff;
  border-color: #a3afb7;
}
.btn-inverse.btn-up:before {
  border-bottom-color: #fff;
}
.btn-inverse.btn-up:hover:before,
.btn-inverse.btn-up:focus:before {
  border-bottom-color: #fff;
}
.btn-inverse.btn-up:active:before,
.btn-inverse.btn-up.active:before,
.open > .dropdown-toggle.btn-inverse.btn-up:before {
  border-bottom-color: #fff;
}
.btn-inverse.btn-right:before {
  border-left-color: #fff;
}
.btn-inverse.btn-right:hover:before,
.btn-inverse.btn-right:focus:before {
  border-left-color: #fff;
}
.btn-inverse.btn-right:active:before,
.btn-inverse.btn-right.active:before,
.open > .dropdown-toggle.btn-inverse.btn-right:before {
  border-left-color: #fff;
}
.btn-inverse.btn-bottom:before {
  border-top-color: #fff;
}
.btn-inverse.btn-bottom:hover:before,
.btn-inverse.btn-bottom:focus:before {
  border-top-color: #fff;
}
.btn-inverse.btn-bottom:active:before,
.btn-inverse.btn-bottom.active:before,
.open > .dropdown-toggle.btn-inverse.btn-bottom:before {
  border-top-color: #fff;
}
.btn-inverse.btn-left:before {
  border-right-color: #fff;
}
.btn-inverse.btn-left:hover:before,
.btn-inverse.btn-left:focus:before {
  border-right-color: #fff;
}
.btn-inverse.btn-left:active:before,
.btn-inverse.btn-left.active:before,
.open > .dropdown-toggle.btn-inverse.btn-left:before {
  border-right-color: #fff;
}
.btn-dark {
  color: #fff;
  background-color: #526069;
  border-color: #526069;
}
.btn-dark:focus,
.btn-dark.focus {
  color: #fff;
  background-color: #3c464c;
  border-color: #1a1f21;
}
.btn-dark:hover {
  color: #fff;
  background-color: #3c464c;
  border-color: #374147;
}
.btn-dark:active,
.btn-dark.active,
.open > .dropdown-toggle.btn-dark {
  color: #fff;
  background-color: #3c464c;
  border-color: #374147;
}
.btn-dark:active:hover,
.btn-dark.active:hover,
.open > .dropdown-toggle.btn-dark:hover,
.btn-dark:active:focus,
.btn-dark.active:focus,
.open > .dropdown-toggle.btn-dark:focus,
.btn-dark:active.focus,
.btn-dark.active.focus,
.open > .dropdown-toggle.btn-dark.focus {
  color: #fff;
  background-color: #2c3338;
  border-color: #1a1f21;
}
.btn-dark:active,
.btn-dark.active,
.open > .dropdown-toggle.btn-dark {
  background-image: none;
}
.btn-dark.disabled:hover,
.btn-dark[disabled]:hover,
fieldset[disabled] .btn-dark:hover,
.btn-dark.disabled:focus,
.btn-dark[disabled]:focus,
fieldset[disabled] .btn-dark:focus,
.btn-dark.disabled.focus,
.btn-dark[disabled].focus,
fieldset[disabled] .btn-dark.focus {
  background-color: #526069;
  border-color: #526069;
}
.btn-dark .badge {
  color: #526069;
  background-color: #fff;
}
.btn-dark:hover,
.btn-dark:focus,
.btn-dark.focus {
  background-color: #76838f;
  border-color: #76838f;
}
.btn-dark:active,
.btn-dark.active,
.open > .dropdown-toggle.btn-dark {
  background-color: #37474f;
  border-color: #37474f;
}
.btn-dark:active:hover,
.btn-dark.active:hover,
.open > .dropdown-toggle.btn-dark:hover,
.btn-dark:active:focus,
.btn-dark.active:focus,
.open > .dropdown-toggle.btn-dark:focus,
.btn-dark:active.focus,
.btn-dark.active.focus,
.open > .dropdown-toggle.btn-dark.focus {
  background-color: #37474f;
  border-color: #37474f;
}
.btn-dark.disabled,
.btn-dark[disabled],
fieldset[disabled] .btn-dark,
.btn-dark.disabled:hover,
.btn-dark[disabled]:hover,
fieldset[disabled] .btn-dark:hover,
.btn-dark.disabled:focus,
.btn-dark[disabled]:focus,
fieldset[disabled] .btn-dark:focus,
.btn-dark.disabled.focus,
.btn-dark[disabled].focus,
fieldset[disabled] .btn-dark.focus,
.btn-dark.disabled:active,
.btn-dark[disabled]:active,
fieldset[disabled] .btn-dark:active,
.btn-dark.disabled.active,
.btn-dark[disabled].active,
fieldset[disabled] .btn-dark.active {
  color: #fff;
  background-color: #a3afb7;
  border-color: #a3afb7;
}
.btn-dark.btn-up:before {
  border-bottom-color: #526069;
}
.btn-dark.btn-up:hover:before,
.btn-dark.btn-up:focus:before {
  border-bottom-color: #76838f;
}
.btn-dark.btn-up:active:before,
.btn-dark.btn-up.active:before,
.open > .dropdown-toggle.btn-dark.btn-up:before {
  border-bottom-color: #37474f;
}
.btn-dark.btn-right:before {
  border-left-color: #526069;
}
.btn-dark.btn-right:hover:before,
.btn-dark.btn-right:focus:before {
  border-left-color: #76838f;
}
.btn-dark.btn-right:active:before,
.btn-dark.btn-right.active:before,
.open > .dropdown-toggle.btn-dark.btn-right:before {
  border-left-color: #37474f;
}
.btn-dark.btn-bottom:before {
  border-top-color: #526069;
}
.btn-dark.btn-bottom:hover:before,
.btn-dark.btn-bottom:focus:before {
  border-top-color: #76838f;
}
.btn-dark.btn-bottom:active:before,
.btn-dark.btn-bottom.active:before,
.open > .dropdown-toggle.btn-dark.btn-bottom:before {
  border-top-color: #37474f;
}
.btn-dark.btn-left:before {
  border-right-color: #526069;
}
.btn-dark.btn-left:hover:before,
.btn-dark.btn-left:focus:before {
  border-right-color: #76838f;
}
.btn-dark.btn-left:active:before,
.btn-dark.btn-left.active:before,
.open > .dropdown-toggle.btn-dark.btn-left:before {
  border-right-color: #37474f;
}
.btn-dark:hover,
.btn-dark:focus {
  color: #fff;
}
.btn-dark:active,
.btn-dark.active,
.open > .dropdown-toggle.btn-dark {
  color: #fff;
}
.btn-dark.btn-flat {
  color: #526069;
}
.btn-flat {
  background: none;
  border: none;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.btn-flat.disabled {
  color: #a3afb7;
}
.btn-icon,
.btn.icon {
  padding: 10px;
  line-height: 1em;
}
.btn-icon.btn-xs,
.btn.icon.btn-xs {
  padding: 4px;
  font-size: 12px;
}
.btn-icon.btn-sm,
.btn.icon.btn-sm {
  padding: 8px;
  font-size: 14px;
}
.btn-icon.btn-lg,
.btn.icon.btn-lg {
  padding: 12px;
  font-size: 20px;
}
.btn-icon.disabled,
.btn.icon.disabled {
  color: #a3afb7;
}
.btn-icon .icon {
  margin: -1px 0 0;
}
.btn-raised {
  -webkit-box-shadow: 0 0 2px rgba(0, 0, 0, .18), 0 2px 4px rgba(0, 0, 0, .21);
          box-shadow: 0 0 2px rgba(0, 0, 0, .18), 0 2px 4px rgba(0, 0, 0, .21);
  -webkit-transition: -webkit-box-shadow .25s cubic-bezier(.4, 0, .2, 1);
       -o-transition:         box-shadow .25s cubic-bezier(.4, 0, .2, 1);
          transition:         box-shadow .25s cubic-bezier(.4, 0, .2, 1);
}
.btn-raised:hover,
.btn-raised:active,
.btn-raised.active,
.open > .dropdown-toggle.btn-raised {
  -webkit-box-shadow: 0 0 3px rgba(0, 0, 0, .15), 0 3px 6px rgba(0, 0, 0, .2);
          box-shadow: 0 0 3px rgba(0, 0, 0, .15), 0 3px 6px rgba(0, 0, 0, .2);
}
.btn-raised.disabled,
.btn-raised[disabled],
fieldset[disabled] .btn-raised {
  -webkit-box-shadow: none;
          box-shadow: none;
}
.btn-floating {
  width: 56px;
  height: 56px;
  padding: 0;
  margin: 0;
  font-size: 24px;
  text-align: center;
  border-radius: 100%;
  -webkit-box-shadow: 0 6px 10px rgba(0, 0, 0, .15);
          box-shadow: 0 6px 10px rgba(0, 0, 0, .15);
}
.btn-floating.btn-xs {
  width: 30px;
  height: 30px;
  padding: 0;
  font-size: 13px;
}
.btn-floating.btn-sm {
  width: 40px;
  height: 40px;
  padding: 0;
  font-size: 15px;
}
.btn-floating.btn-lg {
  width: 70px;
  height: 70px;
  padding: 0;
  font-size: 30px;
}
.btn-floating i {
  position: relative;
  top: 0;
}
.btn-animate {
  position: relative;
  overflow: hidden;
}
.btn-animate span {
  display: block;
  width: 100%;
  height: 100%;
  -webkit-transform: translate(0px, 0px);
      -ms-transform: translate(0px, 0px);
       -o-transform: translate(0px, 0px);
          transform: translate(0px, 0px);
}
.btn-animate-side {
  padding: 8px 28px;
}
.btn-animate-side span {
  -webkit-transition: -webkit-transform .2s ease-out 0s;
       -o-transition:      -o-transform .2s ease-out 0s;
          transition:         transform .2s ease-out 0s;
}
.btn-animate-side span > .icon {
  position: absolute;
  top: 50%;
  left: 0;
  display: block;
  opacity: 0;
  -webkit-transition: opacity .2s ease-out 0s;
       -o-transition: opacity .2s ease-out 0s;
          transition: opacity .2s ease-out 0s;
  -webkit-transform: translate(-20px, -50%);
      -ms-transform: translate(-20px, -50%);
       -o-transform: translate(-20px, -50%);
          transform: translate(-20px, -50%);
}
.btn-animate-side:hover span {
  -webkit-transform: translate(10px, 0px);
      -ms-transform: translate(10px, 0px);
       -o-transform: translate(10px, 0px);
          transform: translate(10px, 0px);
}
.btn-animate-side:hover span > .icon {
  opacity: 1;
}
.btn-animate-side.btn-xs {
  padding: 3px 14px;
}
.btn-animate-side.btn-xs span > .icon {
  left: 5px;
}
.btn-animate-side.btn-xs:hover span {
  -webkit-transform: translate(8px, 0px);
      -ms-transform: translate(8px, 0px);
       -o-transform: translate(8px, 0px);
          transform: translate(8px, 0px);
}
.btn-animate-side.btn-sm {
  padding: 6px 22px;
}
.btn-animate-side.btn-sm span > .icon {
  left: 3px;
}
.btn-animate-side.btn-sm:hover span {
  -webkit-transform: translate(8px, 0px);
      -ms-transform: translate(8px, 0px);
       -o-transform: translate(8px, 0px);
          transform: translate(8px, 0px);
}
.btn-animate-side.btn-lg {
  padding: 10px 33px;
}
.btn-animate-side.btn-lg span > .icon {
  left: -6px;
}
.btn-animate-side.btn-lg:hover span {
  -webkit-transform: translate(14px, 0px);
      -ms-transform: translate(14px, 0px);
       -o-transform: translate(14px, 0px);
          transform: translate(14px, 0px);
}
.btn-animate-vertical span {
  -webkit-transition: all .2s ease-out 0s;
       -o-transition: all .2s ease-out 0s;
          transition: all .2s ease-out 0s;
}
.btn-animate-vertical span > .icon {
  position: absolute;
  top: -2px;
  left: 50%;
  display: block;
  font-size: 24px;
  -webkit-transform: translate(-50%, -100%);
      -ms-transform: translate(-50%, -100%);
       -o-transform: translate(-50%, -100%);
          transform: translate(-50%, -100%);
}
.btn-animate-vertical:hover span {
  -webkit-transform: translate(0, 150%);
      -ms-transform: translate(0, 150%);
       -o-transform: translate(0, 150%);
          transform: translate(0, 150%);
}
.btn-animate-vertical.btn-xs span > .icon {
  top: -5px;
  font-size: 18px;
}
.btn-animate-vertical.btn-sm span > .icon {
  top: -3px;
  font-size: 21px;
}
.btn-animate-vertical.btn-lg span > .icon {
  font-size: 37px;
}
.btn-labeled {
  padding: 0;
  padding-right: 8px;
}
.btn-labeled .btn-label {
  padding: 6px 8px;
  margin-right: 5px;
}
.btn-labeled.btn-xs {
  padding-right: 5px;
}
.btn-labeled.btn-xs .btn-label {
  padding: 1px 4px;
  margin-right: 2px;
}
.btn-labeled.btn-sm {
  padding-right: 13px;
}
.btn-labeled.btn-sm .btn-label {
  padding: 6px 6px;
  margin-right: 10px;
}
.btn-labeled.btn-lg {
  padding-right: 14px;
}
.btn-labeled.btn-lg .btn-label {
  padding: 10px 14px;
  margin-right: 11px;
}
.btn-labeled.btn-block {
  text-align: left;
}
.btn-label {
  display: inline-block;
  background-color: rgba(0, 0, 0, .15);
  border-radius: 3px 0 0 3px;
}
.btn-pill-left {
  border-radius: 500px 0 0 500px;
}
.btn-pill-right {
  border-radius: 0 500px 500px 0;
}
.btn-direction {
  position: relative;
}
.btn-direction:before {
  position: absolute;
  line-height: 0;
  content: '';
  border: 8px solid transparent;
}
.btn-up:before {
  top: -16px;
  left: 50%;
  margin-left: -8px;
  border-bottom-color: #e4eaec;
}
.btn-right:before {
  top: 50%;
  right: -16px;
  margin-top: -8px;
  border-left-color: #e4eaec;
}
.btn-bottom:before {
  bottom: -16px;
  left: 50%;
  margin-left: -8px;
  border-top-color: #e4eaec;
}
.btn-left:before {
  top: 50%;
  left: -16px;
  margin-top: -8px;
  border-right-color: #e4eaec;
}
.btn-pure,
.btn-pure:hover,
.btn-pure:focus,
.btn-pure:active,
.btn-pure.active,
.open > .dropdown-toggle.btn-pure,
.btn-pure[disabled],
fieldset[disabled] .btn-pure {
  background-color: transparent;
  border-color: transparent;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.btn-pure:hover,
.btn-pure:hover:hover,
.btn-pure:focus:hover,
.btn-pure:active:hover,
.btn-pure.active:hover,
.open > .dropdown-toggle.btn-pure:hover,
.btn-pure[disabled]:hover,
fieldset[disabled] .btn-pure:hover,
.btn-pure:focus,
.btn-pure:hover:focus,
.btn-pure:focus:focus,
.btn-pure:active:focus,
.btn-pure.active:focus,
.open > .dropdown-toggle.btn-pure:focus,
.btn-pure[disabled]:focus,
fieldset[disabled] .btn-pure:focus,
.btn-pure.focus,
.btn-pure:hover.focus,
.btn-pure:focus.focus,
.btn-pure:active.focus,
.btn-pure.active.focus,
.open > .dropdown-toggle.btn-pure.focus,
.btn-pure[disabled].focus,
fieldset[disabled] .btn-pure.focus {
  background-color: transparent;
  border-color: transparent;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.btn-pure.btn-default {
  color: #a3afb7;
}
.btn-pure.btn-default:hover,
.btn-pure.btn-default:focus,
.btn-pure.btn-default:active,
.btn-pure.btn-default.active,
.open > .dropdown-toggle.btn-pure.btn-default {
  color: #ccd5db;
}
.btn-pure.btn-default:hover:hover,
.btn-pure.btn-default:focus:hover,
.btn-pure.btn-default:active:hover,
.btn-pure.btn-default.active:hover,
.open > .dropdown-toggle.btn-pure.btn-default:hover,
.btn-pure.btn-default:hover:focus,
.btn-pure.btn-default:focus:focus,
.btn-pure.btn-default:active:focus,
.btn-pure.btn-default.active:focus,
.open > .dropdown-toggle.btn-pure.btn-default:focus,
.btn-pure.btn-default:hover.focus,
.btn-pure.btn-default:focus.focus,
.btn-pure.btn-default:active.focus,
.btn-pure.btn-default.active.focus,
.open > .dropdown-toggle.btn-pure.btn-default.focus {
  color: #ccd5db;
}
.btn-pure.btn-default:hover .badge,
.btn-pure.btn-default:focus .badge,
.btn-pure.btn-default:active .badge,
.btn-pure.btn-default.active .badge,
.open > .dropdown-toggle.btn-pure.btn-default .badge {
  color: #ccd5db;
}
.btn-pure.btn-primary {
  color: #62a8ea;
}
.btn-pure.btn-primary:hover,
.btn-pure.btn-primary:focus,
.btn-pure.btn-primary:active,
.btn-pure.btn-primary.active,
.open > .dropdown-toggle.btn-pure.btn-primary {
  color: #a2caee;
}
.btn-pure.btn-primary:hover:hover,
.btn-pure.btn-primary:focus:hover,
.btn-pure.btn-primary:active:hover,
.btn-pure.btn-primary.active:hover,
.open > .dropdown-toggle.btn-pure.btn-primary:hover,
.btn-pure.btn-primary:hover:focus,
.btn-pure.btn-primary:focus:focus,
.btn-pure.btn-primary:active:focus,
.btn-pure.btn-primary.active:focus,
.open > .dropdown-toggle.btn-pure.btn-primary:focus,
.btn-pure.btn-primary:hover.focus,
.btn-pure.btn-primary:focus.focus,
.btn-pure.btn-primary:active.focus,
.btn-pure.btn-primary.active.focus,
.open > .dropdown-toggle.btn-pure.btn-primary.focus {
  color: #a2caee;
}
.btn-pure.btn-primary:hover .badge,
.btn-pure.btn-primary:focus .badge,
.btn-pure.btn-primary:active .badge,
.btn-pure.btn-primary.active .badge,
.open > .dropdown-toggle.btn-pure.btn-primary .badge {
  color: #a2caee;
}
.btn-pure.btn-success {
  color: #46be8a;
}
.btn-pure.btn-success:hover,
.btn-pure.btn-success:focus,
.btn-pure.btn-success:active,
.btn-pure.btn-success.active,
.open > .dropdown-toggle.btn-pure.btn-success {
  color: #7dd3ae;
}
.btn-pure.btn-success:hover:hover,
.btn-pure.btn-success:focus:hover,
.btn-pure.btn-success:active:hover,
.btn-pure.btn-success.active:hover,
.open > .dropdown-toggle.btn-pure.btn-success:hover,
.btn-pure.btn-success:hover:focus,
.btn-pure.btn-success:focus:focus,
.btn-pure.btn-success:active:focus,
.btn-pure.btn-success.active:focus,
.open > .dropdown-toggle.btn-pure.btn-success:focus,
.btn-pure.btn-success:hover.focus,
.btn-pure.btn-success:focus.focus,
.btn-pure.btn-success:active.focus,
.btn-pure.btn-success.active.focus,
.open > .dropdown-toggle.btn-pure.btn-success.focus {
  color: #7dd3ae;
}
.btn-pure.btn-success:hover .badge,
.btn-pure.btn-success:focus .badge,
.btn-pure.btn-success:active .badge,
.btn-pure.btn-success.active .badge,
.open > .dropdown-toggle.btn-pure.btn-success .badge {
  color: #7dd3ae;
}
.btn-pure.btn-info {
  color: #57c7d4;
}
.btn-pure.btn-info:hover,
.btn-pure.btn-info:focus,
.btn-pure.btn-info:active,
.btn-pure.btn-info.active,
.open > .dropdown-toggle.btn-pure.btn-info {
  color: #9ae1e9;
}
.btn-pure.btn-info:hover:hover,
.btn-pure.btn-info:focus:hover,
.btn-pure.btn-info:active:hover,
.btn-pure.btn-info.active:hover,
.open > .dropdown-toggle.btn-pure.btn-info:hover,
.btn-pure.btn-info:hover:focus,
.btn-pure.btn-info:focus:focus,
.btn-pure.btn-info:active:focus,
.btn-pure.btn-info.active:focus,
.open > .dropdown-toggle.btn-pure.btn-info:focus,
.btn-pure.btn-info:hover.focus,
.btn-pure.btn-info:focus.focus,
.btn-pure.btn-info:active.focus,
.btn-pure.btn-info.active.focus,
.open > .dropdown-toggle.btn-pure.btn-info.focus {
  color: #9ae1e9;
}
.btn-pure.btn-info:hover .badge,
.btn-pure.btn-info:focus .badge,
.btn-pure.btn-info:active .badge,
.btn-pure.btn-info.active .badge,
.open > .dropdown-toggle.btn-pure.btn-info .badge {
  color: #9ae1e9;
}
.btn-pure.btn-warning {
  color: #f2a654;
}
.btn-pure.btn-warning:hover,
.btn-pure.btn-warning:focus,
.btn-pure.btn-warning:active,
.btn-pure.btn-warning.active,
.open > .dropdown-toggle.btn-pure.btn-warning {
  color: #f6be80;
}
.btn-pure.btn-warning:hover:hover,
.btn-pure.btn-warning:focus:hover,
.btn-pure.btn-warning:active:hover,
.btn-pure.btn-warning.active:hover,
.open > .dropdown-toggle.btn-pure.btn-warning:hover,
.btn-pure.btn-warning:hover:focus,
.btn-pure.btn-warning:focus:focus,
.btn-pure.btn-warning:active:focus,
.btn-pure.btn-warning.active:focus,
.open > .dropdown-toggle.btn-pure.btn-warning:focus,
.btn-pure.btn-warning:hover.focus,
.btn-pure.btn-warning:focus.focus,
.btn-pure.btn-warning:active.focus,
.btn-pure.btn-warning.active.focus,
.open > .dropdown-toggle.btn-pure.btn-warning.focus {
  color: #f6be80;
}
.btn-pure.btn-warning:hover .badge,
.btn-pure.btn-warning:focus .badge,
.btn-pure.btn-warning:active .badge,
.btn-pure.btn-warning.active .badge,
.open > .dropdown-toggle.btn-pure.btn-warning .badge {
  color: #f6be80;
}
.btn-pure.btn-danger {
  color: #f96868;
}
.btn-pure.btn-danger:hover,
.btn-pure.btn-danger:focus,
.btn-pure.btn-danger:active,
.btn-pure.btn-danger.active,
.open > .dropdown-toggle.btn-pure.btn-danger {
  color: #fa9898;
}
.btn-pure.btn-danger:hover:hover,
.btn-pure.btn-danger:focus:hover,
.btn-pure.btn-danger:active:hover,
.btn-pure.btn-danger.active:hover,
.open > .dropdown-toggle.btn-pure.btn-danger:hover,
.btn-pure.btn-danger:hover:focus,
.btn-pure.btn-danger:focus:focus,
.btn-pure.btn-danger:active:focus,
.btn-pure.btn-danger.active:focus,
.open > .dropdown-toggle.btn-pure.btn-danger:focus,
.btn-pure.btn-danger:hover.focus,
.btn-pure.btn-danger:focus.focus,
.btn-pure.btn-danger:active.focus,
.btn-pure.btn-danger.active.focus,
.open > .dropdown-toggle.btn-pure.btn-danger.focus {
  color: #fa9898;
}
.btn-pure.btn-danger:hover .badge,
.btn-pure.btn-danger:focus .badge,
.btn-pure.btn-danger:active .badge,
.btn-pure.btn-danger.active .badge,
.open > .dropdown-toggle.btn-pure.btn-danger .badge {
  color: #fa9898;
}
.btn-pure.btn-dark {
  color: #526069;
}
.btn-pure.btn-dark:hover,
.btn-pure.btn-dark:focus,
.btn-pure.btn-dark:active,
.btn-pure.btn-dark.active,
.open > .dropdown-toggle.btn-pure.btn-dark {
  color: #76838f;
}
.btn-pure.btn-dark:hover:hover,
.btn-pure.btn-dark:focus:hover,
.btn-pure.btn-dark:active:hover,
.btn-pure.btn-dark.active:hover,
.open > .dropdown-toggle.btn-pure.btn-dark:hover,
.btn-pure.btn-dark:hover:focus,
.btn-pure.btn-dark:focus:focus,
.btn-pure.btn-dark:active:focus,
.btn-pure.btn-dark.active:focus,
.open > .dropdown-toggle.btn-pure.btn-dark:focus,
.btn-pure.btn-dark:hover.focus,
.btn-pure.btn-dark:focus.focus,
.btn-pure.btn-dark:active.focus,
.btn-pure.btn-dark.active.focus,
.open > .dropdown-toggle.btn-pure.btn-dark.focus {
  color: #76838f;
}
.btn-pure.btn-dark:hover .badge,
.btn-pure.btn-dark:focus .badge,
.btn-pure.btn-dark:active .badge,
.btn-pure.btn-dark.active .badge,
.open > .dropdown-toggle.btn-pure.btn-dark .badge {
  color: #76838f;
}
.btn-pure.btn-inverse {
  color: #fff;
}
.btn-pure.btn-inverse:hover,
.btn-pure.btn-inverse:focus,
.btn-pure.btn-inverse:active,
.btn-pure.btn-inverse.active,
.open > .dropdown-toggle.btn-pure.btn-inverse {
  color: #fff;
}
.btn-pure.btn-inverse:hover:hover,
.btn-pure.btn-inverse:focus:hover,
.btn-pure.btn-inverse:active:hover,
.btn-pure.btn-inverse.active:hover,
.open > .dropdown-toggle.btn-pure.btn-inverse:hover,
.btn-pure.btn-inverse:hover:focus,
.btn-pure.btn-inverse:focus:focus,
.btn-pure.btn-inverse:active:focus,
.btn-pure.btn-inverse.active:focus,
.open > .dropdown-toggle.btn-pure.btn-inverse:focus,
.btn-pure.btn-inverse:hover.focus,
.btn-pure.btn-inverse:focus.focus,
.btn-pure.btn-inverse:active.focus,
.btn-pure.btn-inverse.active.focus,
.open > .dropdown-toggle.btn-pure.btn-inverse.focus {
  color: #fff;
}
.btn-pure.btn-inverse:hover .badge,
.btn-pure.btn-inverse:focus .badge,
.btn-pure.btn-inverse:active .badge,
.btn-pure.btn-inverse.active .badge,
.open > .dropdown-toggle.btn-pure.btn-inverse .badge {
  color: #fff;
}
.caret {
  border-top: 4px solid;
  -webkit-transition: .25s;
       -o-transition: .25s;
          transition: .25s;
  -webkit-transform: scale(1.001);
      -ms-transform: scale(1.001);
       -o-transform: scale(1.001);
          transform: scale(1.001);
}
.btn-group .btn + .dropdown-toggle .caret {
  margin-left: 0;
}
.dropdown-toggle.btn .caret {
  margin-left: .3em;
}
.dropdown-toggle.btn.btn-xs .caret {
  margin-left: 0;
}
.btn-group > .btn + .dropdown-toggle {
  padding-right: .8em;
  padding-left: .8em;
}
.dropdown-menu {
  margin-top: 5px;
  -webkit-box-shadow: 0 3px 12px rgba(0, 0, 0, .05);
          box-shadow: 0 3px 12px rgba(0, 0, 0, .05);
  -webkit-transition: .25s;
       -o-transition: .25s;
          transition: .25s;
}
.dropdown-menu .divider {
  margin: 6px 0;
}
.dropdown-menu > li {
  padding: 0 5px;
  margin: 2px 0;
}
.dropdown-menu > li > a {
  padding: 8px 15px;
  border-radius: 3px;
  -webkit-transition: background-color .25s;
       -o-transition: background-color .25s;
          transition: background-color .25s;
}
.dropdown-menu li .icon:first-child,
.dropdown-menu li > a .icon:first-child {
  width: 1em;
  margin-right: .5em;
  text-align: center;
}
.dropdown-menu.bullet {
  margin-top: 12px;
}
.dropdown-menu.bullet:before,
.dropdown-menu.bullet:after {
  position: absolute;
  left: 10px;
  display: inline-block;
  width: 0;
  height: 0;
  content: '';
  border: 7px solid transparent;
  border-top-width: 0;
}
.dropdown-menu.bullet:before {
  top: -7px;
  border-bottom-color: #e4eaec;
}
.dropdown-menu.bullet:after {
  top: -6px;
  border-bottom-color: #fff;
}
.dropdown-menu-right.bullet:before,
.dropdown-menu-right.bullet:after {
  right: 10px;
  left: auto;
}
.dropdown-menu.animate {
  overflow: hidden;
}
.dropdown-menu.animate > li {
  -webkit-animation-name: slide-left;
       -o-animation-name: slide-left;
          animation-name: slide-left;
  -webkit-animation-duration: .5s;
       -o-animation-duration: .5s;
          animation-duration: .5s;

  -webkit-animation-fill-mode: both;
       -o-animation-fill-mode: both;
          animation-fill-mode: both;
}
.dropdown-menu.animate > li:nth-child(1) {
  -webkit-animation-delay: .02s;
       -o-animation-delay: .02s;
          animation-delay: .02s;
}
.dropdown-menu.animate > li:nth-child(2) {
  -webkit-animation-delay: .04s;
       -o-animation-delay: .04s;
          animation-delay: .04s;
}
.dropdown-menu.animate > li:nth-child(3) {
  -webkit-animation-delay: .06s;
       -o-animation-delay: .06s;
          animation-delay: .06s;
}
.dropdown-menu.animate > li:nth-child(4) {
  -webkit-animation-delay: .08s;
       -o-animation-delay: .08s;
          animation-delay: .08s;
}
.dropdown-menu.animate > li:nth-child(5) {
  -webkit-animation-delay: .1s;
       -o-animation-delay: .1s;
          animation-delay: .1s;
}
.dropdown-menu.animate > li:nth-child(6) {
  -webkit-animation-delay: .12s;
       -o-animation-delay: .12s;
          animation-delay: .12s;
}
.dropdown-menu.animate > li:nth-child(7) {
  -webkit-animation-delay: .14s;
       -o-animation-delay: .14s;
          animation-delay: .14s;
}
.dropdown-menu.animate > li:nth-child(8) {
  -webkit-animation-delay: .16s;
       -o-animation-delay: .16s;
          animation-delay: .16s;
}
.dropdown-menu.animate > li:nth-child(9) {
  -webkit-animation-delay: .18s;
       -o-animation-delay: .18s;
          animation-delay: .18s;
}
.dropdown-menu.animate > li:nth-child(10) {
  -webkit-animation-delay: .2s;
       -o-animation-delay: .2s;
          animation-delay: .2s;
}
.dropdown-menu.animate > li.divider {
  -webkit-animation-name: none;
       -o-animation-name: none;
          animation-name: none;
}
.dropdown-menu.animate.animate-reverse > li:nth-last-child(1) {
  -webkit-animation-delay: .02s;
       -o-animation-delay: .02s;
          animation-delay: .02s;
}
.dropdown-menu.animate.animate-reverse > li:nth-last-child(2) {
  -webkit-animation-delay: .04s;
       -o-animation-delay: .04s;
          animation-delay: .04s;
}
.dropdown-menu.animate.animate-reverse > li:nth-last-child(3) {
  -webkit-animation-delay: .06s;
       -o-animation-delay: .06s;
          animation-delay: .06s;
}
.dropdown-menu.animate.animate-reverse > li:nth-last-child(4) {
  -webkit-animation-delay: .08s;
       -o-animation-delay: .08s;
          animation-delay: .08s;
}
.dropdown-menu.animate.animate-reverse > li:nth-last-child(5) {
  -webkit-animation-delay: .1s;
       -o-animation-delay: .1s;
          animation-delay: .1s;
}
.dropdown-menu.animate.animate-reverse > li:nth-last-child(6) {
  -webkit-animation-delay: .12s;
       -o-animation-delay: .12s;
          animation-delay: .12s;
}
.dropdown-menu.animate.animate-reverse > li:nth-last-child(7) {
  -webkit-animation-delay: .14s;
       -o-animation-delay: .14s;
          animation-delay: .14s;
}
.dropdown-menu.animate.animate-reverse > li:nth-last-child(8) {
  -webkit-animation-delay: .16s;
       -o-animation-delay: .16s;
          animation-delay: .16s;
}
.dropdown-menu.animate.animate-reverse > li:nth-last-child(9) {
  -webkit-animation-delay: .18s;
       -o-animation-delay: .18s;
          animation-delay: .18s;
}
.dropdown-menu.animate.animate-reverse > li:nth-last-child(10) {
  -webkit-animation-delay: .2s;
       -o-animation-delay: .2s;
          animation-delay: .2s;
}
.dropup .dropdown-menu,
.navbar-fixed-bottom .dropdown .dropdown-menu {
  margin-bottom: 6px;
  -webkit-box-shadow: 0 -3px 12px rgba(0, 0, 0, .05);
          box-shadow: 0 -3px 12px rgba(0, 0, 0, .05);
}
.dropup .dropdown-menu.bullet,
.navbar-fixed-bottom .dropdown .dropdown-menu.bullet {
  margin-bottom: 12px;
}
.dropup .dropdown-menu.bullet:before,
.navbar-fixed-bottom .dropdown .dropdown-menu.bullet:before,
.dropup .dropdown-menu.bullet:after,
.navbar-fixed-bottom .dropdown .dropdown-menu.bullet:after {
  top: auto;
  border-top-width: 7px;
  border-bottom-width: 0;
}
.dropup .dropdown-menu.bullet:before,
.navbar-fixed-bottom .dropdown .dropdown-menu.bullet:before {
  bottom: -7px;
  border-top-color: #e4eaec;
}
.dropup .dropdown-menu.bullet:after,
.navbar-fixed-bottom .dropdown .dropdown-menu.bullet:after {
  bottom: -6px;
  border-top-color: #fff;
}
.dropdown-menu > .dropdown-header {
  padding: 8px 20px 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: default;
}
.dropdown-menu > .dropdown-submenu {
  position: relative;
}
.dropdown-menu > .dropdown-submenu > a {
  position: relative;
}
.dropdown-menu > .dropdown-submenu > a:after {
  position: absolute;
  right: 10px;
  display: inline-block;
  width: 0;
  height: 0;
  margin-top: 6px;
  vertical-align: middle;
  content: '';
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
  border-left: 4px dashed;
}
.dropdown-menu > .dropdown-submenu .dropdown-menu {
  left: 100%;
  margin: 0;
}
.dropdown-menu > .dropdown-submenu.dropdown-menu-left .dropdown-menu {
  left: -100%;
}
.dropdown-menu > .dropdown-submenu:hover .dropdown-menu {
  display: block;
}
.dropdown .dropdown-submenu .dropdown-menu {
  top: 0;
}
.dropup .dropdown-submenu .dropdown-menu {
  bottom: 0;
}
.dropdown-menu-media {
  width: 360px;
  padding-top: 0;
  padding-bottom: 0;
}
.dropdown-menu-media > li {
  padding: 0;
  margin: 0;
}
.dropdown-menu-media .dropdown-menu-header {
  position: relative;
  padding: 20px 20px;
  background-color: #fff;
  border-bottom: 1px solid #e4eaec;
}
.dropdown-menu-media .dropdown-menu-header > h3,
.dropdown-menu-media .dropdown-menu-header > h4,
.dropdown-menu-media .dropdown-menu-header > h5 {
  margin: 0;
}
.dropdown-menu-media .dropdown-menu-header .badge,
.dropdown-menu-media .dropdown-menu-header .label {
  position: absolute;
  top: 50%;
  right: 20px;
  -webkit-transform: translateY(-50%);
      -ms-transform: translateY(-50%);
       -o-transform: translateY(-50%);
          transform: translateY(-50%);
}
.dropdown-menu-media .list-group {
  max-height: 270px;
  margin: 0;
  font-size: 12px;
  border-radius: 0;
}
.dropdown-menu-media .list-group-item {
  padding: 0 20px;
  border: none;
  border-radius: 0 !important;
}
.dropdown-menu-media .list-group-item .media {
  padding: 15px 0;
  border-top: 1px solid #e4eaec;
}
.dropdown-menu-media .list-group-item:first-child .media {
  border-top: none;
}
.dropdown-menu-media > .dropdown-menu-footer {
  background-color: #f3f7f9;
  border-top: 1px solid #e4eaec;
}
.dropdown-menu-media > .dropdown-menu-footer > a {
  padding: 15px 20px !important;
  color: #a3afb7 !important;
}
.dropdown-menu-media > .dropdown-menu-footer > a:hover {
  color: #89bceb !important;
  background-color: transparent !important;
}
.dropdown-menu-media > .dropdown-menu-footer > .dropdown-menu-footer-btn {
  position: absolute;
  right: 0;
}
.dropdown-menu-media > .dropdown-menu-footer > .dropdown-menu-footer-btn:hover {
  color: #89bceb !important;
  background-color: transparent !important;
}
.dropdown-menu-primary > .active > a,
.dropdown-menu-primary > .active > a:hover,
.dropdown-menu-primary > .active > a:focus {
  color: #fff;
  background-color: #62a8ea;
}
.dropdown-menu-success > .active > a,
.dropdown-menu-success > .active > a:hover,
.dropdown-menu-success > .active > a:focus {
  color: #fff;
  background-color: #46be8a;
}
.dropdown-menu-info > .active > a,
.dropdown-menu-info > .active > a:hover,
.dropdown-menu-info > .active > a:focus {
  color: #fff;
  background-color: #57c7d4;
}
.dropdown-menu-warning > .active > a,
.dropdown-menu-warning > .active > a:hover,
.dropdown-menu-warning > .active > a:focus {
  color: #fff;
  background-color: #f2a654;
}
.dropdown-menu-danger > .active > a,
.dropdown-menu-danger > .active > a:hover,
.dropdown-menu-danger > .active > a:focus {
  color: #fff;
  background-color: #f96868;
}
.dropdown-menu-dark > .active > a,
.dropdown-menu-dark > .active > a:hover,
.dropdown-menu-dark > .active > a:focus {
  color: #fff;
  background-color: #526069;
}
.btn-group.open .dropdown-toggle {
  -webkit-box-shadow: inset 0 1px 3px rgba(0, 0, 0, .05);
          box-shadow: inset 0 1px 3px rgba(0, 0, 0, .05);
}
.btn-group:focus .dropdown-toggle {
  -webkit-transition: .25s;
       -o-transition: .25s;
          transition: .25s;
}
.input-group-addon {
  -webkit-transition: border .25s linear, color .25s linear, background-color .25s linear;
       -o-transition: border .25s linear, color .25s linear, background-color .25s linear;
          transition: border .25s linear, color .25s linear, background-color .25s linear;
}
.input-group-btn .btn {
  padding: 6px 10px;
}
.input-group-btn .btn > .icon {
  vertical-align: bottom;
}
.input-group-btn .dropdown-toggle.btn .caret {
  margin-left: 2px;
}
.input-group-btn:last-child > .btn,
.input-group-btn:last-child > .btn-group {
  z-index: 1;
}
.nav > li > a {
  overflow: hidden;
}
.nav > li > a:focus {
  outline: none;
}
.nav > li > a .close {
  display: inline-block;
  margin-left: 10px;
}
.nav .open > a,
.nav .open > a:hover,
.nav .open > a:focus {
  border-color: transparent;
}
.nav-quick {
  padding: 0;
  margin-right: 0;
  margin-bottom: 22px;
  margin-left: 0;
  background-color: #fff;
  border-radius: 3px;
  -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, .05);
          box-shadow: 0 1px 1px rgba(0, 0, 0, .05);
}
.nav-quick li {
  position: relative;
  display: block;
  padding: 0;
  text-align: center;
  list-style: none;
}
.nav-quick a {
  display: block;
  padding: 16px 0;
  color: #76838f;
}
.nav-quick a .icon {
  display: block;
  margin-bottom: .2em;
  font-size: 32px;
}
.nav-quick a:hover {
  text-decoration: none;
  background-color: #f3f7f9;
}
.nav-quick .label,
.nav-quick .badge {
  position: absolute;
  top: 0;
  right: 0;
}
.nav-quick-sm a {
  padding: 12px 0;
}
.nav-quick-sm a .icon {
  font-size: 24px;
}
.nav-quick-lg a {
  padding: 22px 0;
}
.nav-quick-lg a .icon {
  font-size: 40px;
}
.nav-quick-bordered {
  border-top: 1px solid #e4eaec;
  border-left: 1px solid #e4eaec;
}
.nav-quick-bordered li {
  border-right: 1px solid #e4eaec;
  border-bottom: 1px solid #e4eaec;
}
.nav-tabs > li > a {
  padding: 10px 20px;
  color: #76838f;
  -webkit-transition: .25s;
       -o-transition: .25s;
          transition: .25s;
}
.nav-tabs > li > a > .icon {
  margin-right: .5em;
  line-height: 1;
}
.nav-tabs > li.active > a,
.nav-tabs > li.active > a:hover,
.nav-tabs > li.active > a:focus {
  color: #fff;
  background-color: #62a8ea;
  border-color: transparent;
  border-bottom-color: #62a8ea;
}
.nav-tabs.nav-justified > li > a {
  border-radius: 4px 4px 0 0;
}
.nav-tabs.nav-justified > li.active > a,
.nav-tabs.nav-justified > li.active > a:hover,
.nav-tabs.nav-justified > li.active > a:focus {
  border-color: transparent;
  border-bottom-color: #62a8ea;
}
.nav-tabs.nav-tabs-bottom {
  border-top: 1px solid #e4eaec;
  border-bottom: none;
}
.nav-tabs.nav-tabs-bottom > li {
  margin-top: -1px;
  margin-bottom: 0;
}
.nav-tabs.nav-tabs-bottom > li > a {
  border-radius: 0 0 4px 4px;
}
.nav-tabs.nav-tabs-bottom > li > a:hover,
.nav-tabs.nav-tabs-bottom > li > a:focus {
  border-top-color: #e4eaec;
  border-bottom-color: transparent;
}
.nav-tabs.nav-tabs-bottom.nav-justified {
  border-top: none;
}
.nav-tabs.nav-tabs-bottom.nav-justified > li > a {
  border-top-color: #e4eaec;
  border-bottom-color: transparent;
}
.nav-tabs.nav-tabs-bottom.nav-justified > li.active > a,
.nav-tabs.nav-tabs-bottom.nav-justified > li.active > a:hover,
.nav-tabs.nav-tabs-bottom.nav-justified > li.active > a:focus {
  border-top: 1px solid #62a8ea;
}
.nav-tabs-reverse > li {
  float: right;
}
.nav-tabs-reverse > li > a {
  margin-right: 0;
  margin-left: 2px;
}
.nav-tabs-solid {
  border-bottom-color: #f3f7f9;
}
.nav-tabs-solid > li > a:hover {
  border-color: transparent;
}
.nav-tabs-solid > li.active > a,
.nav-tabs-solid > li.active > a:hover,
.nav-tabs-solid > li.active > a:focus {
  color: #76838f;
  background-color: #f3f7f9;
  border-color: transparent;
}
.nav-tabs-solid ~ .tab-content {
  padding: 20px;
  background-color: #f3f7f9;
}
.nav-tabs-solid.nav-justified > li > a {
  border: none;
}
.nav-tabs-solid.nav-justified > li.active > a,
.nav-tabs-solid.nav-justified > li.active > a:hover,
.nav-tabs-solid.nav-justified > li.active > a:focus {
  border: none;
}
.nav-tabs-solid.nav-tabs-bottom > li.active > a,
.nav-tabs-solid.nav-tabs-bottom > li.active > a:hover,
.nav-tabs-solid.nav-tabs-bottom > li.active > a:focus {
  border: none;
}
.nav-tabs-line > li > a {
  padding: 10px 20px;
  border-bottom: 2px solid transparent;
}
.nav-tabs-line > li > a:hover,
.nav-tabs-line > li > a:focus {
  background-color: transparent;
}
.nav-tabs-line > li > a:hover {
  border-bottom-color: #ccd5db;
}
.nav-tabs-line > li.active > a,
.nav-tabs-line > li.active > a:hover,
.nav-tabs-line > li.active > a:focus {
  color: #62a8ea;
  background-color: transparent;
  border-bottom: 2px solid #62a8ea;
}
.nav-tabs-line .open > a,
.nav-tabs-line .open > a:hover,
.nav-tabs-line .open > a:focus {
  border-color: transparent;
  border-bottom-color: #ccd5db;
}
.nav-tabs-line.nav-tabs-bottom > li > a {
  border-top: 2px solid transparent;
  border-bottom: none;
}
.nav-tabs-line.nav-tabs-bottom > li > a:hover {
  border-top-color: #ccd5db;
  border-bottom-color: transparent;
}
.nav-tabs-line.nav-tabs-bottom > li.active > a,
.nav-tabs-line.nav-tabs-bottom > li.active > a:hover,
.nav-tabs-line.nav-tabs-bottom > li.active > a:focus {
  border-top: 2px solid #62a8ea;
  border-bottom: none;
}
.nav-tabs-line.nav-justified > li > a {
  border-bottom: 2px solid #e4eaec;
}
.nav-tabs-line.nav-justified > li > a:hover {
  border-bottom-color: #ccd5db;
}
.nav-tabs-line.nav-justified > li.active > a,
.nav-tabs-line.nav-justified > li.active > a:hover,
.nav-tabs-line.nav-justified > li.active > a:focus {
  border-color: transparent;
  border-bottom: 2px solid #62a8ea;
}
.nav-tabs-line.nav-justified.nav-tabs-bottom {
  border-top: none;
}
.nav-tabs-line.nav-justified.nav-tabs-bottom > li > a {
  border-top: 2px solid #e4eaec;
  border-bottom: none;
}
.nav-tabs-line.nav-justified.nav-tabs-bottom > li > a:hover {
  border-top-color: #ccd5db;
}
.nav-tabs-line.nav-justified.nav-tabs-bottom > li.active > a,
.nav-tabs-line.nav-justified.nav-tabs-bottom > li.active > a:hover,
.nav-tabs-line.nav-justified.nav-tabs-bottom > li.active > a:focus {
  border-top-color: #62a8ea;
  border-bottom: none;
}
.nav-tabs-vertical:before,
.nav-tabs-vertical:after {
  display: table;
  content: " ";
}
.nav-tabs-vertical:after {
  clear: both;
}
.nav-tabs-vertical .nav-tabs {
  float: left;
  border-right: 1px solid #e4eaec;
  border-bottom: none;
}
.nav-tabs-vertical .nav-tabs > li {
  float: none;
  margin-right: -1px;
  margin-bottom: 0;
}
.nav-tabs-vertical .nav-tabs > li > a {
  padding: 10px 20px;
  margin-right: 0;
  margin-bottom: 2px;
  border-radius: 4px 0 0 4px;
}
.nav-tabs-vertical .nav-tabs > li > a:hover {
  border-right-color: #e4eaec;
  border-bottom-color: transparent;
}
.nav-tabs-vertical .nav-tabs > li.active > a,
.nav-tabs-vertical .nav-tabs > li.active > a:focus,
.nav-tabs-vertical .nav-tabs > li.active > a:hover {
  border-right-color: #62a8ea;
}
.nav-tabs-vertical .nav-tabs-reverse {
  float: right;
  border-right: none;
  border-left: 1px solid #e4eaec;
}
.nav-tabs-vertical .nav-tabs-reverse > li {
  margin-right: 0;
  margin-left: -1px;
}
.nav-tabs-vertical .nav-tabs-reverse > li > a {
  margin-left: 0;
  border-radius: 0 4px 4px 0;
}
.nav-tabs-vertical .nav-tabs-reverse > li > a:hover {
  border-right-color: transparent;
  border-left-color: #e4eaec;
}
.nav-tabs-vertical .nav-tabs-reverse > li.active > a,
.nav-tabs-vertical .nav-tabs-reverse > li.active > a:focus,
.nav-tabs-vertical .nav-tabs-reverse > li.active > a:hover {
  border-left-color: #62a8ea;
}
.nav-tabs-vertical .nav-tabs-solid {
  border-right-color: #f3f7f9;
}
.nav-tabs-vertical .nav-tabs-solid > li > a:hover {
  border-color: transparent;
}
.nav-tabs-vertical .nav-tabs-solid > li.active > a,
.nav-tabs-vertical .nav-tabs-solid > li.active > a:focus,
.nav-tabs-vertical .nav-tabs-solid > li.active > a:hover {
  border-color: transparent;
}
.nav-tabs-vertical .nav-tabs-solid + .tab-content {
  padding: 20px;
}
.nav-tabs-vertical .nav-tabs-solid.nav-tabs-reverse {
  border-left-color: #f3f7f9;
}
.nav-tabs-vertical .nav-tabs-line > li > a {
  border-right: 2px solid transparent;
  border-bottom: none;
}
.nav-tabs-vertical .nav-tabs-line > li > a:hover {
  border-right-color: #ccd5db;
}
.nav-tabs-vertical .nav-tabs-line > li.active > a,
.nav-tabs-vertical .nav-tabs-line > li.active > a:hover,
.nav-tabs-vertical .nav-tabs-line > li.active > a:focus {
  border-right: 2px solid #62a8ea;
  border-bottom: none;
}
.nav-tabs-vertical .nav-tabs-line.nav-tabs-reverse > li > a {
  border-right-width: 1px;
  border-left: 2px solid transparent;
}
.nav-tabs-vertical .nav-tabs-line.nav-tabs-reverse > li > a:hover {
  border-color: transparent;
  border-left-color: #ccd5db;
}
.nav-tabs-vertical .nav-tabs-line.nav-tabs-reverse > li.active > a,
.nav-tabs-vertical .nav-tabs-line.nav-tabs-reverse > li.active > a:hover,
.nav-tabs-vertical .nav-tabs-line.nav-tabs-reverse > li.active > a:focus {
  border-right: 1px solid transparent;
  border-left: 2px solid #62a8ea;
}
.nav-tabs-vertical .tab-content {
  overflow: hidden;
}
.nav-tabs-inverse .nav-tabs-solid {
  border-bottom-color: #fff;
}
.nav-tabs-inverse .nav-tabs-solid > li.active > a,
.nav-tabs-inverse .nav-tabs-solid > li.active > a:hover,
.nav-tabs-inverse .nav-tabs-solid > li.active > a:focus {
  color: #76838f;
  background-color: #fff;
}
.nav-tabs-inverse.nav-tabs-vertical .nav-tabs-solid {
  border-right-color: #fff;
}
.nav-tabs-inverse.nav-tabs-vertical .nav-tabs-solid.nav-tabs-reverse {
  border-left-color: #fff;
}
.nav-tabs-inverse .tab-content {
  background: #fff;
}
.nav-tabs-animate .tab-content {
  overflow: hidden;
}
.nav-tabs-lg > li > a {
  padding: 12px 20px;
  font-size: 18px;
  line-height: 1.3333333;
}
.nav-tabs-sm > li > a {
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
}
.navbar-toggle {
  height: 66px;
  padding: 22px 15px;
  margin-top: 16px;
  margin-top: 0;
  margin-bottom: 16px;
  margin-bottom: 0;
  line-height: 22px;
  background: transparent !important;
  -webkit-transition: color .25s linear;
       -o-transition: color .25s linear;
          transition: color .25s linear;
}
.navbar-toggle .icon {
  margin-top: -1px;
}
.navbar-toggle:hover {
  background: transparent !important;
}
.navbar-toggle-left {
  float: left;
  margin-right: 0;
  margin-left: 15px;
}
.navbar {
  border: none;
  -webkit-box-shadow: 0 2px 4px rgba(0, 0, 0, .08);
          box-shadow: 0 2px 4px rgba(0, 0, 0, .08);
}
.navbar-fixed-top,
.navbar-fixed-bottom {
  width: 100%;
}
@media (min-width: 768px) {
  .hidden-float {
    display: block;
  }
}
@media (max-width: 767px) {
  .hidden-float {
    display: none !important;
  }
}
.navbar-brand {
  padding: 22px 20px;
  font-weight: 500;
}
.navbar-brand > .navbar-brand-logo {
  display: inline-block;
}
.navbar-brand-logo {
  height: 32px;
  margin-top: -5px;
}
.navbar-brand-text {
  margin-left: 6px;
}
@media (max-width: 767px) {
  .navbar-brand-center {
    position: absolute;
    left: 50%;
    -webkit-transform: translate(-50%, 0);
        -ms-transform: translate(-50%, 0);
         -o-transform: translate(-50%, 0);
            transform: translate(-50%, 0);
  }
}
@media (min-width: 768px) {
  .navbar-mega .container,
  .navbar-mega .container-fluid {
    position: relative;
  }
}
.navbar-mega .dropdown-menu {
  left: auto;
}
.navbar-mega .dropdown-mega {
  position: static;
}
.navbar-mega .mega-content {
  padding: 20px 30px;
}
.navbar-mega .mega-menu {
  min-width: 150px;
  max-width: 100%;
}
.navbar-mega .mega-menu > ul {
  padding-left: 0;
}
.navbar-mega .mega-menu .list-icons {
  margin-bottom: 6px;
}
.navbar-mega .dropdown.dropdown-fw .dropdown-menu {
  right: 5px;
  left: 5px;
}
@media (max-width: 767px) {
  .navbar-mega .dropdown.dropdown-fw .dropdown-menu {
    right: 0;
    left: 0;
  }
}
.navbar-nav > li > a.navbar-avatar,
.navbar-toolbar > li > a.navbar-avatar {
  padding-top: 18px;
  padding-bottom: 18px;
}
@media (max-width: 767px) {
  .navbar-nav > li > a.navbar-avatar {
    padding-top: 6px;
    padding-bottom: 6px;
  }
}
.navbar-avatar .avatar {
  width: 30px;
}
.navbar-form .icon {
  font-size: 16px;
  color: rgba(55, 71, 79, .4);
}
.navbar-form .form-control {
  background-color: #f3f7f9;
  border: none;
  border-radius: 38px;
}
@media (min-width: 768px) {
  .navbar-search.collapse {
    display: block !important;
    height: auto !important;
    overflow: visible !important;
    visibility: visible !important;
  }
}
@media (max-width: 767px) {
  .navbar-search {
    padding-right: 15px;
    padding-left: 15px;
  }
}
@media (max-width: 767px) {
  .navbar-search .navbar-form {
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: none;
  }
}
.container > .navbar-search,
.container-fluid > .navbar-search {
  margin-right: -15px;
  margin-left: -15px;
}
@media (min-width: 768px) {
  .container > .navbar-search,
  .container-fluid > .navbar-search {
    margin-right: 0;
    margin-left: 0;
  }
}
.navbar-search-overlap {
  position: absolute !important;
  top: 0;
  right: 0;
  left: 0;
  background-color: #fff;
}
.navbar-search-overlap .form-group,
.navbar-search-overlap .form-control {
  display: block !important;
  margin: 0;
}
.navbar-search-overlap .form-control {
  height: 66px !important;
  background-color: transparent !important;
  border-radius: 0;
}
.navbar-search-overlap .form-control:focus {
  border-color: transparent;
}
.navbar-collapse-toolbar.in {
  overflow-y: visible;
}
.navbar-toolbar {
  float: left;
}
.navbar-toolbar:before,
.navbar-toolbar:after {
  display: table;
  content: " ";
}
.navbar-toolbar:after {
  clear: both;
}
.navbar-toolbar > li {
  float: left;
}
.navbar-toolbar > li:before,
.navbar-toolbar > li:after {
  display: table;
  content: " ";
}
.navbar-toolbar > li:after {
  clear: both;
}
.navbar-toolbar > li > a {
  padding-top: 22px;
  padding-bottom: 22px;
  line-height: 22px;
}
.navbar-toolbar .dropdown-menu {
  -webkit-transform-origin: 100% 0;
      -ms-transform-origin: 100% 0;
       -o-transform-origin: 100% 0;
          transform-origin: 100% 0;
  -webkit-animation-duration: .3s;
       -o-animation-duration: .3s;
          animation-duration: .3s;
}
@media (max-width: 767px) {
  .navbar-toolbar .dropdown-menu:not(.dropdown-menu-media) {
    max-height: 400px;
    overflow-x: hidden;
    overflow-y: scroll;
    -webkit-overflow-scrolling: touch;
    -webkit-transform: translate3d(0, 0, 0);
        -ms-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
  }
  .navbar-toolbar .open {
    position: static;
  }
  .navbar-toolbar .open .dropdown-menu {
    right: 0;
    left: 0;
    float: none;
    width: auto;
    margin-top: 0;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }
}
@media (max-width: 767px) and (max-device-width: 480px) and (orientation: landscape) {
  .navbar-toolbar .dropdown-menu:not(.dropdown-menu-media) {
    max-height: 200px;
  }
}
@media (max-width: 767px) {
  .navbar-toolbar-left {
    float: left !important;
  }
  .navbar-toolbar-right {
    float: right !important;
  }
}
.icon-fullscreen {
  font-family: "Web Icons";
}
.icon-fullscreen:before {
  content: "\f11d";
}
.icon-fullscreen.active:before {
  content: "\f11e";
}
.icon-menubar {
  font-family: "Web Icons";
}
.icon-menubar:before {
  content: "\f119";
}
.icon-menubar.active:before {
  content: "\f119";
}
.navbar-default .navbar-toolbar > li > a {
  color: #76838f;
}
.navbar-default .navbar-toolbar > li > a:hover,
.navbar-default .navbar-toolbar > li > a:focus {
  color: #526069;
  background-color: rgba(243, 247, 249, .3);
}
.navbar-default .navbar-toolbar > .active > a,
.navbar-default .navbar-toolbar > .active > a:hover,
.navbar-default .navbar-toolbar > .active > a:focus {
  color: #526069;
  background-color: rgba(243, 247, 249, .6);
}
.navbar-default .navbar-toolbar > .disabled > a,
.navbar-default .navbar-toolbar > .disabled > a:hover,
.navbar-default .navbar-toolbar > .disabled > a:focus {
  color: #ccd5db;
  background-color: transparent;
}
.navbar-default .navbar-toggle {
  color: #76838f;
}
.navbar-default .navbar-toolbar > .open > a,
.navbar-default .navbar-toolbar > .open > a:hover,
.navbar-default .navbar-toolbar > .open > a:focus {
  color: #526069;
  background-color: rgba(243, 247, 249, .6);
}
.navbar-inverse .navbar-toolbar > li > a {
  color: #fff;
}
.navbar-inverse .navbar-toolbar > li > a:hover,
.navbar-inverse .navbar-toolbar > li > a:focus {
  color: #fff;
  background-color: rgba(0, 0, 0, .1);
}
.navbar-inverse .navbar-toolbar > .active > a,
.navbar-inverse .navbar-toolbar > .active > a:hover,
.navbar-inverse .navbar-toolbar > .active > a:focus {
  color: #fff;
  background-color: rgba(0, 0, 0, .1);
}
.navbar-inverse .navbar-toolbar > .disabled > a,
.navbar-inverse .navbar-toolbar > .disabled > a:hover,
.navbar-inverse .navbar-toolbar > .disabled > a:focus {
  color: #fff;
  background-color: transparent;
}
.navbar-inverse .navbar-toggle {
  color: #fff;
}
.navbar-inverse .navbar-toolbar > .open > a,
.navbar-inverse .navbar-toolbar > .open > a:hover,
.navbar-inverse .navbar-toolbar > .open > a:focus {
  color: #fff;
  background-color: rgba(0, 0, 0, .1);
}
.breadcrumb {
  margin-bottom: 10px;
}
.breadcrumb li + li:before {
  padding: 0 5px;
}
.breadcrumb li .icon {
  text-decoration: none;
}
.breadcrumb li .icon:before {
  margin-right: 10px;
}
.breadcrumb-arrow > li + li:before {
  content: "\00bb\00a0";
}
.pagination li > a,
.pagination li > span {
  padding: 9px 15px;
  -webkit-transition: background .2s ease-out, border-color 0s ease-out, color .2s ease-out;
       -o-transition: background .2s ease-out, border-color 0s ease-out, color .2s ease-out;
          transition: background .2s ease-out, border-color 0s ease-out, color .2s ease-out;
}
.pagination li > a:hover,
.pagination li > span:hover,
.pagination li > a:focus,
.pagination li > span:focus {
  -webkit-transition: background .2s ease-out, border-color .2s ease-out, color .2s ease-out;
       -o-transition: background .2s ease-out, border-color .2s ease-out, color .2s ease-out;
          transition: background .2s ease-out, border-color .2s ease-out, color .2s ease-out;
}
.pagination li .icon {
  margin-top: -1px;
}
.pagination > .disabled > span,
.pagination > .disabled > span:hover,
.pagination > .disabled > span:focus,
.pagination > .disabled > a,
.pagination > .disabled > a:hover,
.pagination > .disabled > a:focus {
  color: #ccd5db;
  cursor: not-allowed;
  background-color: transparent;
  border-color: #e4eaec;
}
.pagination-gap > li > a {
  margin: 0 5px;
  border-radius: 5px;
}
.pagination-gap > li > a:hover {
  background-color: transparent;
  border-color: #62a8ea;
}
.pagination-gap > li:first-child > a,
.pagination-gap > li:last-child > a {
  border-radius: 5px;
}
.pagination-no-border > li > a {
  border: none;
}
.pagination-lg > li > a,
.pagination-lg > li > span {
  padding: 10px 17px;
  font-size: 16px;
  line-height: 1.3333333;
}
.pagination-lg > li:first-child > a,
.pagination-lg > li:first-child > span {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}
.pagination-lg > li:last-child > a,
.pagination-lg > li:last-child > span {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
.pagination-sm > li > a,
.pagination-sm > li > span {
  padding: 6px 11px;
  font-size: 14px;
  line-height: 1.5;
}
.pagination-sm > li:first-child > a,
.pagination-sm > li:first-child > span {
  border-top-left-radius: 2px;
  border-bottom-left-radius: 2px;
}
.pagination-sm > li:last-child > a,
.pagination-sm > li:last-child > span {
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
}
.pager li > a,
.pager li > span {
  padding: 10px 20px;
  color: #76838f;
  -webkit-transition: all .2s ease;
       -o-transition: all .2s ease;
          transition: all .2s ease;
}
.pager li > a:hover,
.pager li > a:focus {
  color: #62a8ea;
}
.pager li .icon {
  margin-top: -1px;
}
.pager li > a:hover,
.pager li > a:focus {
  border-color: #62a8ea;
}
.pager .disabled > a,
.pager .disabled > a:hover,
.pager .disabled > a:focus,
.pager .disabled > span {
  border-color: #e4eaec;
}
.pager-round li > a,
.pager-round li > span {
  border-radius: 1000px;
}
.label {
  padding: .25em .6em .25em;
  font-weight: 300;
  border-radius: .3em;
}
.label.label-outline {
  color: #f3f7f9;
  background-color: transparent;
  border-color: #f3f7f9;
}
.label-outline {
  border: 1px solid transparent;
}
.label-round {
  border-radius: 1em;
}
.label-default {
  color: #76838f;
  background-color: #e4eaec;
}
.label-default[href]:hover,
.label-default[href]:focus {
  background-color: #f3f7f9;
}
.label-default.label-outline {
  color: #e4eaec;
  background-color: transparent;
  border-color: #e4eaec;
}
.label-default[href]:hover,
.label-default[href]:focus {
  color: #a3afb7;
}
.label-default.label-outline {
  color: #76838f;
}
.label-primary {
  background-color: #62a8ea;
}
.label-primary[href]:hover,
.label-primary[href]:focus {
  background-color: #89bceb;
}
.label-primary.label-outline {
  color: #62a8ea;
  background-color: transparent;
  border-color: #62a8ea;
}
.label-success {
  background-color: #46be8a;
}
.label-success[href]:hover,
.label-success[href]:focus {
  background-color: #5cd29d;
}
.label-success.label-outline {
  color: #46be8a;
  background-color: transparent;
  border-color: #46be8a;
}
.label-info {
  background-color: #57c7d4;
}
.label-info[href]:hover,
.label-info[href]:focus {
  background-color: #77d6e1;
}
.label-info.label-outline {
  color: #57c7d4;
  background-color: transparent;
  border-color: #57c7d4;
}
.label-warning {
  background-color: #f2a654;
}
.label-warning[href]:hover,
.label-warning[href]:focus {
  background-color: #f4b066;
}
.label-warning.label-outline {
  color: #f2a654;
  background-color: transparent;
  border-color: #f2a654;
}
.label-danger {
  background-color: #f96868;
}
.label-danger[href]:hover,
.label-danger[href]:focus {
  background-color: #fa7a7a;
}
.label-danger.label-outline {
  color: #f96868;
  background-color: transparent;
  border-color: #f96868;
}
.label-dark {
  background-color: #526069;
}
.label-dark[href]:hover,
.label-dark[href]:focus {
  background-color: #76838f;
}
.label-dark.label-outline {
  color: #526069;
  background-color: transparent;
  border-color: #526069;
}
.label-lg {
  font-size: 16px;
}
.label-sm {
  padding: .1em .5em .1em;
  font-size: 10px;
}
.badge {
  padding: 3px 6px;
}
.btn .badge {
  top: 0;
}
.badge.up {
  position: relative;
  top: -10px;
  margin: 0 -.8em;
  border-radius: 15px;
}
.badge.badge-absolute {
  position: absolute;
  top: -8px;
  right: -10px;
  z-index: 5;
}
.badge-radius {
  border-radius: 3px;
}
.badge-primary {
  color: #fff;
  background-color: #62a8ea;
}
.badge-primary[href]:hover,
.badge-primary[href]:focus {
  color: #fff;
  background-color: #358fe4;
}
.list-group-item.active > .badge-primary,
.nav-pills > .active > a > .badge-primary {
  color: #fff;
  background-color: #62a8ea;
}
.badge-success {
  color: #fff;
  background-color: #46be8a;
}
.badge-success[href]:hover,
.badge-success[href]:focus {
  color: #fff;
  background-color: #369b6f;
}
.list-group-item.active > .badge-success,
.nav-pills > .active > a > .badge-success {
  color: #fff;
  background-color: #46be8a;
}
.badge-info {
  color: #fff;
  background-color: #57c7d4;
}
.badge-info[href]:hover,
.badge-info[href]:focus {
  color: #fff;
  background-color: #33b6c5;
}
.list-group-item.active > .badge-info,
.nav-pills > .active > a > .badge-info {
  color: #fff;
  background-color: #57c7d4;
}
.badge-warning {
  color: #fff;
  background-color: #f2a654;
}
.badge-warning[href]:hover,
.badge-warning[href]:focus {
  color: #fff;
  background-color: #ee8d25;
}
.list-group-item.active > .badge-warning,
.nav-pills > .active > a > .badge-warning {
  color: #fff;
  background-color: #f2a654;
}
.badge-danger {
  color: #fff;
  background-color: #f96868;
}
.badge-danger[href]:hover,
.badge-danger[href]:focus {
  color: #fff;
  background-color: #f73737;
}
.list-group-item.active > .badge-danger,
.nav-pills > .active > a > .badge-danger {
  color: #fff;
  background-color: #f96868;
}
.badge-dark {
  color: #fff;
  background-color: #526069;
}
.badge-dark[href]:hover,
.badge-dark[href]:focus {
  color: #fff;
  background-color: #3c464c;
}
.list-group-item.active > .badge-dark,
.nav-pills > .active > a > .badge-dark {
  color: #fff;
  background-color: #526069;
}
.badge-lg {
  padding: 5px 9px 8px;
  font-size: 16px;
}
.badge-sm {
  padding: 2px 5px;
  font-size: 10px;
}
.jumbotron {
  padding: 0;
  border-radius: 3px;
}
.jumbotron > .jumbotron-photo img {
  width: 100%;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}
.jumbotron-contents {
  padding: 20px;
}
.jumbotron .carousel,
.jumbotron .carousel-inner,
.jumbotron .carousel-inner > .item.active img {
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}
.jumbotron .carousel-inner > .item > a > img,
.jumbotron .carousel-inner > .item > img {
  width: 100%;
}
.jumbotron h1,
.jumbotron .h1 {
  font-size: 28px;
}
.jumbotron h2,
.jumbotron .h2 {
  font-size: 24px;
}
@media screen and (min-width: 768px) {
  .jumbotron,
  .container .jumbotron {
    padding: 0;
  }
  .jumbotron h1,
  .jumbotron .h1 {
    font-size: 28px;
  }
}
.thumbnail {
  padding: 0;
  border: none;
  -webkit-transition: all .25s ease-in-out;
       -o-transition: all .25s ease-in-out;
          transition: all .25s ease-in-out;
}
.thumbnail .caption {
  position: relative;
  display: block;
  padding-right: 0;
  padding-left: 0;
}
.alert {
  padding-right: 20px;
  padding-left: 20px;
}
.alert ul {
  padding-left: 13px;
}
.alert ul li {
  padding-left: 7px;
}
.panel > .alert {
  margin: 0;
}
.alert-alt {
  color: #76838f;
  background-color: rgba(243, 247, 249, .8);
  border: none;
  border-left: 3px solid transparent;
}
.alert-alt a,
.alert-alt .alert-link {
  text-decoration: none;
}
.alert-dismissible {
  padding-right: 40px;
}
.alert-dismissible .close {
  top: 0;
  text-decoration: none;
  opacity: .6;
}
.alert-dismissible .close:hover,
.alert-dismissible .close:focus {
  opacity: 1;
}
.alert-dismissible.alert-alt .close {
  color: #a3afb7;
  opacity: .6;
}
.alert-dismissible.alert-alt .close:hover,
.alert-dismissible.alert-alt .close:focus {
  color: #a3afb7;
  opacity: 1;
}
.alert-icon {
  position: relative;
  padding-left: 45px;
}
.alert-icon > .icon {
  position: absolute;
  top: 18px;
  left: 20px;
  width: 1em;
  font-size: 16px;
  text-align: center;
}
.alert-avatar {
  position: relative;
  padding-top: 20px;
  padding-bottom: 20px;
  padding-left: 75px;
}
.alert-avatar > .avatar {
  position: absolute;
  top: 12px;
  left: 20px;
}
.page-alert .alert-wrap {
  max-height: 0;
  padding: 0;
  margin: 0;
  overflow: hidden;
  -webkit-transition: max-height .7s linear 0s;
       -o-transition: max-height .7s linear 0s;
          transition: max-height .7s linear 0s;
}
.page-alert .alert-wrap.in {
  max-height: 500px;
  -webkit-transition: max-height 1s linear 0s;
       -o-transition: max-height 1s linear 0s;
          transition: max-height 1s linear 0s;
}
.page-alert .alert-wrap .alert {
  margin: 0;
  text-align: left;
  border-radius: 0;
}
.alert-primary {
  color: #62a8ea;
  background-color: rgba(232, 241, 248, .8);
  border-color: #e8f1f8;
}
.alert-primary hr {
  border-top-color: #d4e5f2;
}
.alert-primary .alert-link {
  color: #358fe4;
}
.alert-primary .close {
  color: #62a8ea;
}
.alert-primary .close:hover,
.alert-primary .close:focus {
  color: #62a8ea;
}
.alert-primary .alert-link {
  color: #4e97d9;
}
.alert-alt.alert-primary {
  border-color: #62a8ea;
}
.alert-alt.alert-primary a,
.alert-alt.alert-primary .alert-link {
  color: #62a8ea;
}
.alert-success .alert-link {
  color: #36ab7a;
}
.alert-alt.alert-success {
  border-color: #46be8a;
}
.alert-alt.alert-success a,
.alert-alt.alert-success .alert-link {
  color: #46be8a;
}
.alert-info .alert-link {
  color: #47b8c6;
}
.alert-alt.alert-info {
  border-color: #57c7d4;
}
.alert-alt.alert-info a,
.alert-alt.alert-info .alert-link {
  color: #57c7d4;
}
.alert-warning .alert-link {
  color: #ec9940;
}
.alert-alt.alert-warning {
  border-color: #f2a654;
}
.alert-alt.alert-warning a,
.alert-alt.alert-warning .alert-link {
  color: #f2a654;
}
.alert-danger .alert-link {
  color: #e9595b;
}
.alert-alt.alert-danger {
  border-color: #f96868;
}
.alert-alt.alert-danger a,
.alert-alt.alert-danger .alert-link {
  color: #f96868;
}
.alert-social {
  position: relative;
  padding-left: 65px;
}
.alert-social > .icon {
  position: absolute;
  top: 12px;
  bottom: 0;
  left: 20px;
  width: 1em;
  font-size: 30px;
  text-align: center;
}
.alert-facebook {
  color: #fff;
  background-color: #3b5998;
  border-color: #3b5998;
}
.alert-facebook hr {
  border-top-color: #344e86;
}
.alert-facebook .alert-link {
  color: #e6e6e6;
}
.alert-facebook .close {
  color: #fff;
}
.alert-facebook .close:hover,
.alert-facebook .close:focus {
  color: #fff;
}
.alert-facebook .alert-link {
  font-weight: 500;
  color: #fff;
}
.alert-twitter {
  color: #fff;
  background-color: #55acee;
  border-color: #55acee;
}
.alert-twitter hr {
  border-top-color: #3ea1ec;
}
.alert-twitter .alert-link {
  color: #e6e6e6;
}
.alert-twitter .close {
  color: #fff;
}
.alert-twitter .close:hover,
.alert-twitter .close:focus {
  color: #fff;
}
.alert-twitter .alert-link {
  font-weight: 500;
  color: #fff;
}
.alert-google-plus {
  color: #fff;
  background-color: #dd4b39;
  border-color: #dd4b39;
}
.alert-google-plus hr {
  border-top-color: #d73925;
}
.alert-google-plus .alert-link {
  color: #e6e6e6;
}
.alert-google-plus .close {
  color: #fff;
}
.alert-google-plus .close:hover,
.alert-google-plus .close:focus {
  color: #fff;
}
.alert-google-plus .alert-link {
  font-weight: 500;
  color: #fff;
}
.alert-linkedin {
  color: #fff;
  background-color: #0976b4;
  border-color: #0976b4;
}
.alert-linkedin hr {
  border-top-color: #08669c;
}
.alert-linkedin .alert-link {
  color: #e6e6e6;
}
.alert-linkedin .close {
  color: #fff;
}
.alert-linkedin .close:hover,
.alert-linkedin .close:focus {
  color: #fff;
}
.alert-linkedin .alert-link {
  font-weight: 500;
  color: #fff;
}
.alert-flickr {
  color: #fff;
  background-color: #ff0084;
  border-color: #ff0084;
}
.alert-flickr hr {
  border-top-color: #e60077;
}
.alert-flickr .alert-link {
  color: #e6e6e6;
}
.alert-flickr .close {
  color: #fff;
}
.alert-flickr .close:hover,
.alert-flickr .close:focus {
  color: #fff;
}
.alert-flickr .alert-link {
  font-weight: 500;
  color: #fff;
}
.alert-tumblr {
  color: #fff;
  background-color: #35465c;
  border-color: #35465c;
}
.alert-tumblr hr {
  border-top-color: #2c3a4c;
}
.alert-tumblr .alert-link {
  color: #e6e6e6;
}
.alert-tumblr .close {
  color: #fff;
}
.alert-tumblr .close:hover,
.alert-tumblr .close:focus {
  color: #fff;
}
.alert-tumblr .alert-link {
  font-weight: 500;
  color: #fff;
}
.alert-github {
  color: #fff;
  background-color: #4183c4;
  border-color: #4183c4;
}
.alert-github hr {
  border-top-color: #3876b4;
}
.alert-github .alert-link {
  color: #e6e6e6;
}
.alert-github .close {
  color: #fff;
}
.alert-github .close:hover,
.alert-github .close:focus {
  color: #fff;
}
.alert-github .alert-link {
  font-weight: 500;
  color: #fff;
}
.alert-dribbble {
  color: #fff;
  background-color: #c32361;
  border-color: #c32361;
}
.alert-dribbble hr {
  border-top-color: #ad1f56;
}
.alert-dribbble .alert-link {
  color: #e6e6e6;
}
.alert-dribbble .close {
  color: #fff;
}
.alert-dribbble .close:hover,
.alert-dribbble .close:focus {
  color: #fff;
}
.alert-dribbble .alert-link {
  font-weight: 500;
  color: #fff;
}
.alert-youtube {
  color: #fff;
  background-color: #b31217;
  border-color: #b31217;
}
.alert-youtube hr {
  border-top-color: #9c1014;
}
.alert-youtube .alert-link {
  color: #e6e6e6;
}
.alert-youtube .close {
  color: #fff;
}
.alert-youtube .close:hover,
.alert-youtube .close:focus {
  color: #fff;
}
.alert-youtube .alert-link {
  font-weight: 500;
  color: #fff;
}
.alert.dark .alert-link {
  font-weight: 500;
  color: #fff !important;
}
.alert.dark .alert-left-border {
  border: none;
  border-left: 3px solid transparent;
}
.alert.dark.alert-dismissible.alert-alt .close {
  color: #fff;
}
.alert.dark.alert-dismissible.alert-alt .close:hover,
.alert.dark.alert-dismissible.alert-alt .close:focus {
  color: #fff;
}
.alert.dark.alert-primary {
  color: #fff;
  background-color: #62a8ea;
  border-color: #62a8ea;
}
.alert.dark.alert-primary hr {
  border-top-color: #4c9ce7;
}
.alert.dark.alert-primary .alert-link {
  color: #e6e6e6;
}
.alert.dark.alert-primary .close {
  color: #fff;
}
.alert.dark.alert-primary .close:hover,
.alert.dark.alert-primary .close:focus {
  color: #fff;
}
.alert-alt.alert.dark.alert-primary {
  border-color: #2771b4;
}
.alert-alt.alert.dark.alert-primary a,
.alert-alt.alert.dark.alert-primary .alert-link {
  color: #fff;
}
.alert.dark.alert-success {
  color: #fff;
  background-color: #46be8a;
  border-color: #46be8a;
}
.alert.dark.alert-success hr {
  border-top-color: #3dae7d;
}
.alert.dark.alert-success .alert-link {
  color: #e6e6e6;
}
.alert.dark.alert-success .close {
  color: #fff;
}
.alert.dark.alert-success .close:hover,
.alert.dark.alert-success .close:focus {
  color: #fff;
}
.alert-alt.alert.dark.alert-success {
  border-color: #247151;
}
.alert-alt.alert.dark.alert-success a,
.alert-alt.alert.dark.alert-success .alert-link {
  color: #fff;
}
.alert.dark.alert-info {
  color: #fff;
  background-color: #57c7d4;
  border-color: #57c7d4;
}
.alert.dark.alert-info hr {
  border-top-color: #43c0cf;
}
.alert.dark.alert-info .alert-link {
  color: #e6e6e6;
}
.alert.dark.alert-info .close {
  color: #fff;
}
.alert.dark.alert-info .close:hover,
.alert.dark.alert-info .close:focus {
  color: #fff;
}
.alert-alt.alert.dark.alert-info {
  border-color: #2e8893;
}
.alert-alt.alert.dark.alert-info a,
.alert-alt.alert.dark.alert-info .alert-link {
  color: #fff;
}
.alert.dark.alert-warning {
  color: #fff;
  background-color: #f2a654;
  border-color: #f2a654;
}
.alert.dark.alert-warning hr {
  border-top-color: #f09a3c;
}
.alert.dark.alert-warning .alert-link {
  color: #e6e6e6;
}
.alert.dark.alert-warning .close {
  color: #fff;
}
.alert.dark.alert-warning .close:hover,
.alert.dark.alert-warning .close:focus {
  color: #fff;
}
.alert-alt.alert.dark.alert-warning {
  border-color: #cb7314;
}
.alert-alt.alert.dark.alert-warning a,
.alert-alt.alert.dark.alert-warning .alert-link {
  color: #fff;
}
.alert.dark.alert-danger {
  color: #fff;
  background-color: #f96868;
  border-color: #f96868;
}
.alert.dark.alert-danger hr {
  border-top-color: #f84f4f;
}
.alert.dark.alert-danger .alert-link {
  color: #e6e6e6;
}
.alert.dark.alert-danger .close {
  color: #fff;
}
.alert.dark.alert-danger .close:hover,
.alert.dark.alert-danger .close:focus {
  color: #fff;
}
.alert-alt.alert.dark.alert-danger {
  border-color: #d91d1f;
}
.alert-alt.alert.dark.alert-danger a,
.alert-alt.alert.dark.alert-danger .alert-link {
  color: #fff;
}
.progress {
  height: 15px;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.progress-bar {
  line-height: 15px;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.progress-square {
  border-radius: 0;
}
.progress-circle {
  border-radius: 1000px;
}
.progress-vertical {
  position: relative;
  display: inline-block;
  width: 15px;
  height: 250px;
  min-height: 250px;
  margin-right: 30px;
  margin-bottom: 0;
}
.progress-vertical .progress-bar {
  width: 100%;
}
.progress-bar-indicating.active {
  position: relative;
  -webkit-animation: none;
       -o-animation: none;
          animation: none;
}
.progress-bar-indicating.active:before {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  content: '';
  background-color: #fff;
  border-radius: inherit;
  opacity: 0;
  -webkit-animation: progress-active 3s ease 0s infinite;
       -o-animation: progress-active 3s ease 0s infinite;
          animation: progress-active 3s ease 0s infinite;
}
.progress-vertical .progress-bar-indicating.active:before {
  -webkit-animation-name: progress-vertical-active;
       -o-animation-name: progress-vertical-active;
          animation-name: progress-vertical-active;
}
.progress-skill {
  position: relative;
}
.progress-skill .progress-bar > span {
  position: absolute;
  top: 0;
  right: 10px;
  color: #526069;
}
.progress-lg {
  height: 22px;
}
.progress-lg.progress-vertical {
  width: 25px;
}
.progress-lg .progress-bar {
  line-height: 22px;
}
.progress-sm {
  height: 10px;
}
.progress-sm.progress-vertical {
  width: 10px;
}
.progress-sm .progress-bar {
  line-height: 10px;
}
.progress-xs {
  height: 4px;
  border-radius: 1px;
}
.progress-xs.progress-vertical {
  width: 4px;
}
.progress-xs .progress-bar {
  line-height: 4px;
}
.contextual-progress {
  margin: 20px 0;
}
.contextual-progress .progress-title {
  float: left;
}
.contextual-progress .progress-label {
  position: relative;
  float: right;
}
.contextual-progress .progress {
  height: 2px;
  margin: 5px 0;
}
@-webkit-keyframes progress-active {
  0% {
    width: 0;
    opacity: .4;
  }
  100% {
    width: 100%;
    opacity: 0;
  }
}
@-o-keyframes progress-active {
  0% {
    width: 0;
    opacity: .4;
  }
  100% {
    width: 100%;
    opacity: 0;
  }
}
@keyframes progress-active {
  0% {
    width: 0;
    opacity: .4;
  }
  100% {
    width: 100%;
    opacity: 0;
  }
}
@-webkit-keyframes progress-vertical-active {
  0% {
    top: 0;
    opacity: 0;
  }
  100% {
    top: 175px;
    opacity: .4;
  }
}
@-o-keyframes progress-vertical-active {
  0% {
    top: 0;
    opacity: 0;
  }
  100% {
    top: 175px;
    opacity: .4;
  }
}
@keyframes progress-vertical-active {
  0% {
    top: 0;
    opacity: 0;
  }
  100% {
    top: 175px;
    opacity: .4;
  }
}
.media-object {
  width: 120px;
}
.media-left,
.media > .pull-left {
  padding-right: 20px;
}
.media-right,
.media > .pull-right {
  padding-left: 20px;
}
.media-body {
  overflow: auto;
}
.media .media {
  padding-bottom: 0;
  border-bottom: none;
}
.media-meta {
  margin-bottom: 3px;
  font-size: 12px;
  color: #526069;
}
.media-lg .media-object {
  width: 160px;
}
.media-lg .media {
  margin-left: -110px;
}
.media-sm .media-object {
  width: 80px;
}
.media-sm .media {
  margin-left: -70px;
}
.media-xs .media-object {
  width: 60px;
}
.media-xs .media {
  margin-left: -60px;
}
@media screen and (min-width: 768px) {
  .media-body {
    overflow: hidden;
  }
  .media .media {
    margin-left: 0;
  }
}
.list-group .media {
  padding: 2px 0;
  border-bottom: 0;
}
.list-group .media .pull-left,
.list-group .media .media-left {
  padding-right: 20px;
}
.list-group .media .pull-right,
.list-group .media .media-right {
  padding-left: 20px;
}
.list-group .media .pull-right .status {
  margin-top: 15px;
  margin-right: 5px;
}
.list-group .media .media-heading {
  font-size: 14px;
}
.list-group-full > .list-group-item {
  padding-right: 0;
  padding-left: 0;
}
a.list-group-item {
  border-radius: 3px;
}
a.list-group-item.disabled,
a.list-group-item.disabled:hover,
a.list-group-item.disabled:focus {
  color: #ccd5db;
  background-color: #f3f7f9;
}
a.list-group-item.active,
a.list-group-item.active:hover,
a.list-group-item.active:focus {
  color: #fff;
  background-color: #62a8ea;
}
.list-group-item .icon {
  margin-right: 10px;
}
.list-group.bg-inherit {
  border-radius: 3px;
}
.list-group.bg-inherit .list-group-item {
  background-color: transparent;
  border-bottom-color: rgba(0, 0, 0, .075);
}
.list-group.bg-inherit .list-group-item:last-child {
  border-bottom-color: transparent;
}
.list-group.bg-inherit .list-group-item:hover {
  background-color: rgba(0, 0, 0, .075);
  border-color: transparent;
}
.list-group-bordered .list-group-item {
  border-color: #e4eaec;
}
.list-group-bordered .list-group-item.active,
.list-group-bordered .list-group-item.active:hover,
.list-group-bordered .list-group-item.active:focus {
  color: #fff;
  background-color: #4e97d9;
  border-color: #4e97d9;
}
.list-group-dividered .list-group-item {
  border-top-color: #e4eaec;
}
.list-group-dividered .list-group-item.active:hover {
  border-top-color: #e4eaec;
}
.list-group-dividered .list-group-item:last-child {
  border-bottom-color: #e4eaec;
}
.list-group-dividered .list-group-item:first-child {
  border-top-color: transparent;
}
.list-group-dividered .list-group-item:first-child.active:hover {
  border-top-color: transparent;
}
.list-group-gap .list-group-item {
  margin-bottom: 2px;
  border-radius: 3px;
}
.list-group-full .list-group-item {
  padding-right: 0;
  padding-left: 0;
}
.list-group-item-dark {
  color: #fff;
  background-color: #526069;
}
a.list-group-item-dark,
button.list-group-item-dark {
  color: #fff;
}
a.list-group-item-dark .list-group-item-heading,
button.list-group-item-dark .list-group-item-heading {
  color: inherit;
}
a.list-group-item-dark:hover,
button.list-group-item-dark:hover,
a.list-group-item-dark:focus,
button.list-group-item-dark:focus {
  color: #fff;
  background-color: #47535b;
}
a.list-group-item-dark.active,
button.list-group-item-dark.active,
a.list-group-item-dark.active:hover,
button.list-group-item-dark.active:hover,
a.list-group-item-dark.active:focus,
button.list-group-item-dark.active:focus {
  color: #fff;
  background-color: #fff;
  border-color: #fff;
}
.panel {
  position: relative;
  margin-bottom: 30px;
  border-width: 0;
}
.panel > .nav-tabs-vertical .nav-tabs {
  margin-left: -1px;
}
.panel > .nav-tabs-vertical .nav-tabs > li > a {
  border-left: none;
  border-radius: 0;
}
.panel > .nav-tabs-vertical .nav-tabs.nav-tabs-reverse {
  margin-right: -1px;
}
.panel > .nav-tabs-vertical .nav-tabs.nav-tabs-reverse > li > a {
  border-right: none;
  border-radius: 0;
}
.panel:hover .panel-actions .show-on-hover {
  display: inline-block;
}
.panel .panel-actions .show-on-hover {
  display: none;
}
.panel.is-fullscreen {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 9999;
  width: 100%;
  margin-bottom: 0;
  border-radius: 0;
}
.panel.is-fullscreen .panel-loading {
  border-radius: 0;
}
.panel.is-fullscreen .panel-actions [data-toggle=collapse] {
  display: none;
}
.panel.is-fullscreen .panel-body {
  max-height: 100%;
  overflow: auto;
}
.panel.is-close {
  display: none;
}
.panel.is-collapse .panel-body {
  display: none;
  height: 0;
}
.panel > .alert {
  padding-right: 30px;
  padding-left: 30px;
}
.panel > .alert-dismissible {
  padding-right: 50px;
}
@media screen and (max-width: 480px) {
  .panel > .alert {
    padding-right: 20px;
    padding-left: 20px;
  }
  .panel > .alert-dismissible {
    padding-right: 40px;
  }
}
.panel > .table > tr > td:first-child,
.panel > .table-responsive > .table > tr > td:first-child,
.panel > .table > thead > tr > td:first-child,
.panel > .table-responsive > .table > thead > tr > td:first-child,
.panel > .table > tbody > tr > td:first-child,
.panel > .table-responsive > .table > tbody > tr > td:first-child,
.panel > .table > tfoot > tr > td:first-child,
.panel > .table-responsive > .table > tfoot > tr > td:first-child,
.panel > .table > tr > th:first-child,
.panel > .table-responsive > .table > tr > th:first-child,
.panel > .table > thead > tr > th:first-child,
.panel > .table-responsive > .table > thead > tr > th:first-child,
.panel > .table > tbody > tr > th:first-child,
.panel > .table-responsive > .table > tbody > tr > th:first-child,
.panel > .table > tfoot > tr > th:first-child,
.panel > .table-responsive > .table > tfoot > tr > th:first-child {
  padding-left: 30px;
}
@media screen and (max-width: 480px) {
  .panel > .table > tr > td:first-child,
  .panel > .table-responsive > .table > tr > td:first-child,
  .panel > .table > thead > tr > td:first-child,
  .panel > .table-responsive > .table > thead > tr > td:first-child,
  .panel > .table > tbody > tr > td:first-child,
  .panel > .table-responsive > .table > tbody > tr > td:first-child,
  .panel > .table > tfoot > tr > td:first-child,
  .panel > .table-responsive > .table > tfoot > tr > td:first-child,
  .panel > .table > tr > th:first-child,
  .panel > .table-responsive > .table > tr > th:first-child,
  .panel > .table > thead > tr > th:first-child,
  .panel > .table-responsive > .table > thead > tr > th:first-child,
  .panel > .table > tbody > tr > th:first-child,
  .panel > .table-responsive > .table > tbody > tr > th:first-child,
  .panel > .table > tfoot > tr > th:first-child,
  .panel > .table-responsive > .table > tfoot > tr > th:first-child {
    padding-left: 20px;
  }
}
.panel > .table > tr > td:last-child,
.panel > .table-responsive > .table > tr > td:last-child,
.panel > .table > thead > tr > td:last-child,
.panel > .table-responsive > .table > thead > tr > td:last-child,
.panel > .table > tbody > tr > td:last-child,
.panel > .table-responsive > .table > tbody > tr > td:last-child,
.panel > .table > tfoot > tr > td:last-child,
.panel > .table-responsive > .table > tfoot > tr > td:last-child,
.panel > .table > tr > th:last-child,
.panel > .table-responsive > .table > tr > th:last-child,
.panel > .table > thead > tr > th:last-child,
.panel > .table-responsive > .table > thead > tr > th:last-child,
.panel > .table > tbody > tr > th:last-child,
.panel > .table-responsive > .table > tbody > tr > th:last-child,
.panel > .table > tfoot > tr > th:last-child,
.panel > .table-responsive > .table > tfoot > tr > th:last-child {
  padding-right: 30px;
}
@media screen and (max-width: 480px) {
  .panel > .table > tr > td:last-child,
  .panel > .table-responsive > .table > tr > td:last-child,
  .panel > .table > thead > tr > td:last-child,
  .panel > .table-responsive > .table > thead > tr > td:last-child,
  .panel > .table > tbody > tr > td:last-child,
  .panel > .table-responsive > .table > tbody > tr > td:last-child,
  .panel > .table > tfoot > tr > td:last-child,
  .panel > .table-responsive > .table > tfoot > tr > td:last-child,
  .panel > .table > tr > th:last-child,
  .panel > .table-responsive > .table > tr > th:last-child,
  .panel > .table > thead > tr > th:last-child,
  .panel > .table-responsive > .table > thead > tr > th:last-child,
  .panel > .table > tbody > tr > th:last-child,
  .panel > .table-responsive > .table > tbody > tr > th:last-child,
  .panel > .table > tfoot > tr > th:last-child,
  .panel > .table-responsive > .table > tfoot > tr > th:last-child {
    padding-right: 20px;
  }
}
.panel > .table > tbody:first-child > tr:first-child th,
.panel > .table > tbody:first-child > tr:first-child td {
  border-top: 1px solid #e4eaec;
}
.panel > .list-group > .list-group-item {
  padding-right: 30px;
  padding-left: 30px;
}
@media screen and (max-width: 480px) {
  .panel > .list-group > .list-group-item {
    padding-right: 20px;
    padding-left: 20px;
  }
}
.panel-content > .row {
  padding-right: 30px;
  padding-left: 30px;
}
.panel-content > .row > [class*="col-"] {
  padding-right: 30px;
  padding-left: 30px;
}
.panel-heading {
  position: relative;
  padding: 0;
  border-bottom: 1px solid transparent;
}
.panel-heading + .alert {
  border-radius: 0;
}
.panel-heading > .nav-tabs {
  border-bottom: none;
}
.panel-heading + .nav-tabs {
  margin-top: -10px;
}
.panel-body {
  position: relative;
}
.panel-heading + .panel-body {
  padding-top: 0;
}
.panel-body h1:first-child,
.panel-body h2:first-child,
.panel-body h3:first-child,
.panel-body h4:first-child,
.panel-body h5:first-child,
.panel-body h6:first-child,
.panel-body .h1:first-child,
.panel-body .h2:first-child,
.panel-body .h3:first-child,
.panel-body .h4:first-child,
.panel-body .h5:first-child,
.panel-body .h6:first-child {
  margin-top: 0;
}
.panel-body > *:last-child {
  margin-bottom: 0;
}
.panel-body > .list-group-dividered:only-child > .list-group-item:last-child {
  border-bottom-color: transparent;
}
.panel-footer {
  border-top: 1px solid transparent;
}
.table + .panel-footer {
  padding-top: 15px;
  border-color: #e4eaec;
}
.panel-title {
  display: block;
  padding: 20px 30px;
  font-size: 18px;
  color: #37474f;
}
.panel-title > .icon {
  margin-right: 10px;
}
.panel-title > .label {
  margin-left: 10px;
}
.panel-title small {
  color: #76838f;
}
.panel-desc {
  display: block;
  padding: 5px 0 0;
  margin: 0;
  font-size: 14px;
  color: #76838f;
}
.panel-actions {
  position: absolute;
  top: 50%;
  right: 30px;
  z-index: 1;
  margin: auto;
  -webkit-transform: translate(0%, -50%);
      -ms-transform: translate(0%, -50%);
       -o-transform: translate(0%, -50%);
          transform: translate(0%, -50%);
}
@media screen and (max-width: 480px) {
  .panel-actions {
    right: 20px;
  }
}
ul.panel-actions {
  list-style: none;
}
ul.panel-actions > li {
  display: inline-block;
  margin-left: 8px;
}
ul.panel-actions > li:first-child {
  margin-left: 0;
}
.panel-actions a {
  color: inherit;
}
.panel-actions a.dropdown-toggle {
  text-decoration: none;
}
.panel-actions .dropdown {
  display: inline-block;
}
.panel-actions .dropdown-toggle {
  display: inline-block;
}
.panel-actions .panel-action {
  display: inline-block;
  padding: 8px 10px;
  color: #a3afb7;
  text-decoration: none;
  cursor: pointer;
  background-color: transparent;
}
.panel-actions .panel-action:hover {
  color: #526069;
}
.panel-actions .panel-action:active {
  color: #526069;
}
.panel-actions .panel-action:focus {
  outline: none;
}
.panel-actions .progress {
  width: 100px;
  margin: 0;
}
.panel-actions .pagination {
  margin: 0;
}
.panel-toolbar {
  padding: 5px 15px;
  margin: 0;
  background-color: transparent;
  border-top: 1px solid #e4eaec;
  border-bottom: 1px solid #e4eaec;
}
.panel-bordered .panel-toolbar {
  border-top-color: transparent;
}
.panel-toolbar .btn {
  padding: 5px 10px;
  color: #a3afb7;
}
.panel-toolbar .btn.icon {
  width: 1em;
  text-align: center;
}
.panel-toolbar .btn:hover,
.panel-toolbar .btn:active,
.panel-toolbar .btn.active {
  color: #76838f;
}
.panel-loading {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 6;
  display: none;
  width: 100%;
  height: 100%;
  border-radius: 4px;
  opacity: .6;
}
.panel-loading .loader {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
      -ms-transform: translate(-50%, -50%);
       -o-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}
.panel > *:not(.panel-loading):not(.collapsing) {
  -webkit-transition: opacity .3s;
       -o-transition: opacity .3s;
          transition: opacity .3s;
}
.panel.is-loading > *:not(.panel-loading) {
  opacity: .3;
}
.panel.is-loading .panel-loading {
  display: block;
  opacity: 1;
}
.panel-footer-chart {
  padding: 0;
}
.panel-control {
  padding: 0;
  border: none;
  border-radius: 0;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.panel-body.scrollable-vertical {
  padding-right: 0 !important;
  padding-left: 0 !important;
}
.panel-body.scrollable-vertical > .scrollable-container > .scrollable-content {
  padding-right: 30px;
  padding-left: 30px;
}
@media screen and (max-width: 480px) {
  .panel-body.scrollable-vertical > .scrollable-container > .scrollable-content {
    padding-right: 20px;
    padding-left: 20px;
  }
}
.panel-body.scrollable-vertical > .scrollable-bar {
  height: -webkit-calc(100% - 30px);
  height:         calc(100% - 30px);
  margin-top: 0;
  margin-bottom: 30px;
  -webkit-transform: translateX(-26px);
      -ms-transform: translateX(-26px);
       -o-transform: translateX(-26px);
          transform: translateX(-26px);
}
.panel-bordered > .panel-body.scrollable-vertical > .scrollable-bar {
  height: -webkit-calc(100% - 60px);
  height:         calc(100% - 60px);
  margin-bottom: 30px;
}
.panel-body.scrollable-horizontal {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}
.panel-body.scrollable-horizontal > .scrollable-container > .scrollable-content {
  padding-top: 0;
  padding-bottom: 30px;
}
.panel-bordered > .panel-body.scrollable-horizontal > .scrollable-container > .scrollable-content {
  padding-top: 30px;
  padding-bottom: 30px;
}
.panel-body.scrollable-horizontal > .scrollable-bar {
  width: -webkit-calc(100% - 60px);
  width:         calc(100% - 60px);
  margin-right: 30px;
  margin-left: 0;
  -webkit-transform: translateY(-26px);
      -ms-transform: translateY(-26px);
       -o-transform: translateY(-26px);
          transform: translateY(-26px);
}
@media screen and (max-width: 480px) {
  .panel-body.scrollable-horizontal > .scrollable-bar {
    width: -webkit-calc(100% - 40px);
    width:         calc(100% - 40px);
    margin-right: 20px;
  }
}
.panel-bordered > .panel-body.scrollable-horizontal > .scrollable-bar {
  -webkit-transform: translateY(-26px);
      -ms-transform: translateY(-26px);
       -o-transform: translateY(-26px);
          transform: translateY(-26px);
}
.panel-bordered > .panel-heading {
  border-bottom: 1px solid #e4eaec;
}
.panel-bordered > .panel-heading > .panel-title {
  padding-bottom: 20px;
}
.panel-bordered > .panel-footer {
  padding-top: 15px;
  border-top: 1px solid #e4eaec;
}
.panel-bordered > .panel-body {
  padding-top: 30px;
}
.panel-bordered > .table > tbody:first-child > tr:first-child th,
.panel-bordered > .table > tbody:first-child > tr:first-child td {
  border-top: 0;
}
.panel.is-dragging {
  opacity: .8;
}
.panel.is-dragging {
  cursor: move;
}
.panel.panel-transparent {
  background: transparent;
  border-color: transparent;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.panel.panel-transparent > .panel-heading,
.panel.panel-transparent > .panel-footer {
  border-color: transparent;
}
.panel-dark {
  border-color: #526069;
}
.panel-dark > .panel-heading {
  color: #fff;
  background-color: #526069;
  border-color: #526069;
}
.panel-dark > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #526069;
}
.panel-dark > .panel-heading .badge {
  color: #526069;
  background-color: #fff;
}
.panel-dark > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #526069;
}
.panel-primary,
.panel-info,
.panel-success,
.panel-warning,
.panel-danger,
.panel-dark {
  border: none;
}
.panel-primary .panel-heading,
.panel-info .panel-heading,
.panel-success .panel-heading,
.panel-warning .panel-heading,
.panel-danger .panel-heading,
.panel-dark .panel-heading {
  border: none;
}
.panel-primary .panel-title,
.panel-info .panel-title,
.panel-success .panel-title,
.panel-warning .panel-title,
.panel-danger .panel-title,
.panel-dark .panel-title {
  color: #fff;
}
.panel-primary .panel-action,
.panel-info .panel-action,
.panel-success .panel-action,
.panel-warning .panel-action,
.panel-danger .panel-action,
.panel-dark .panel-action {
  color: #fff;
}
@media screen and (max-width: 480px) {
  .panel-actions {
    right: 20px;
  }
  .panel-actions .progress {
    min-width: 80px;
  }
  .panel-actions .show-on-hover {
    display: none;
  }
  .panel-title,
  .panel-body,
  .panel-footer {
    padding-right: 20px;
    padding-left: 20px;
  }
}
.well {
  padding: 20px;
}
.well-lg {
  padding: 24px;
}
.well-sm {
  padding: 12px;
}
.well {
  -webkit-box-shadow: inset 0 0 1px rgba(0, 0, 0, .02);
          box-shadow: inset 0 0 1px rgba(0, 0, 0, .02);
}
.well-primary {
  color: #fff;
  background-color: #62a8ea;
}
.well-success {
  color: #fff;
  background-color: #46be8a;
}
.well-info {
  color: #fff;
  background-color: #57c7d4;
}
.well-warning {
  color: #fff;
  background-color: #f2a654;
}
.well-danger {
  color: #fff;
  background-color: #f96868;
}
.close.icon {
  font-size: inherit;
}
body {
  font-weight: 300;
}
th {
  font-weight: 400;
}
b,
strong {
  font-weight: 500;
}
optgroup {
  font-weight: 500;
}
h1 small,
h2 small,
h3 small,
h4 small,
h5 small,
h6 small,
.h1 small,
.h2 small,
.h3 small,
.h4 small,
.h5 small,
.h6 small,
h1 .small,
h2 .small,
h3 .small,
h4 .small,
h5 .small,
h6 .small,
.h1 .small,
.h2 .small,
.h3 .small,
.h4 .small,
.h5 .small,
.h6 .small {
  font-weight: 300;
}
.lead {
  font-weight: 100;
}
dt {
  font-weight: 500;
}
kbd kbd {
  font-weight: 500;
}
label {
  font-weight: 300;
}
.radio label,
.checkbox label {
  font-weight: 300;
}
.radio-inline,
.checkbox-inline {
  font-weight: 300;
}
.btn-link {
  font-weight: 300;
}
.dropdown-menu > li > a {
  font-weight: 100;
}
.input-group-addon {
  font-weight: 300;
}
.label {
  font-weight: 500;
}
.popover {
  font-weight: 300;
}
.tooltip {
  font-weight: 300;
}
.modal-content {
  border: none;
  border-radius: 4px;
  -webkit-box-shadow: 0 2px 12px rgba(0, 0, 0, .2);
          box-shadow: 0 2px 12px rgba(0, 0, 0, .2);
}
.modal-header {
  padding: 15px 20px;
  border-bottom: none;
}
.modal-header .close {
  margin-top: 1px;
}
.modal-body {
  padding: 20px;
}
.modal-footer {
  padding: 6px 20px 20px;
  border-top: none;
}
.modal-top {
  margin: 0 auto;
}
.modal-center {
  display: -ms-flexbox;
  display: -webkit-flex;
  display: -webkit-box;
  display:         flex;
  height: 100%;
  margin: 0 auto;

  -webkit-justify-content: center;
  -ms-flex-pack: center;
  -webkit-box-pack: center;
          justify-content: center;
  -webkit-flex-flow: column nowrap;
      -ms-flex-flow: column nowrap;
          flex-flow: column nowrap;
  -webkit-align-content: stretch;
  -ms-flex-line-pack: center;
          align-content: center;
}
.modal-bottom {
  display: -ms-flexbox;
  display: -webkit-flex;
  display: -webkit-box;
  display:         flex;
  height: 100%;
  margin: 0 auto;

  -webkit-justify-content: flex-end;
  -ms-flex-pack: end;
  -webkit-box-pack: end;
          justify-content: flex-end;
  -webkit-flex-flow: column nowrap;
      -ms-flex-flow: column nowrap;
          flex-flow: column nowrap;
  -webkit-align-content: stretch;
  -ms-flex-line-pack: center;
          align-content: center;
}
.modal-sidebar {
  position: absolute;
  right: 0;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: -webkit-box;
  display:         flex;
  height: 100%;
  margin: 0;
  background-color: #fff;

  -webkit-justify-content: center;
  -ms-flex-pack: center;
  -webkit-box-pack: center;
          justify-content: center;
  -webkit-flex-flow: column nowrap;
      -ms-flex-flow: column nowrap;
          flex-flow: column nowrap;
  -webkit-align-content: stretch;
  -ms-flex-line-pack: center;
          align-content: center;
}
.modal-sidebar .modal-content {
  background-color: transparent;
  border-radius: 0;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.modal-sidebar .modal-header {
  border-bottom: none;
}
.modal-sidebar .modal-footer {
  border-top: none;
}
.modal-sidebar button.close {
  position: fixed;
  top: 20px;
  right: 20px;
}
.modal.fade .modal-dialog.modal-sidebar {
  -webkit-transform: translate(25%, 0px);
      -ms-transform: translate(25%, 0px);
       -o-transform: translate(25%, 0px);
          transform: translate(25%, 0px);
}
.modal.in .modal-dialog.modal-sidebar {
  -webkit-transform: translate(0px, 0px);
      -ms-transform: translate(0px, 0px);
       -o-transform: translate(0px, 0px);
          transform: translate(0px, 0px);
}
.modal-fill-in {
  background-color: transparent;
}
.modal-fill-in.in {
  background-color: rgba(255, 255, 255, .95);
  opacity: 1;
}
.modal-fill-in .modal-dialog {
  display: -ms-flexbox;
  display: -webkit-flex;
  display: -webkit-box;
  display:         flex;
  width: 100%;
  height: 100%;
  margin: 0 auto;

  -webkit-justify-content: center;
  -ms-flex-pack: center;
  -webkit-box-pack: center;
          justify-content: center;
  -webkit-flex-flow: column nowrap;
      -ms-flex-flow: column nowrap;
          flex-flow: column nowrap;
  -webkit-align-content: stretch;
  -ms-flex-line-pack: center;
          align-content: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  -webkit-box-align: center;
          align-items: center;
}
@media (min-width: 768px) {
  .modal-fill-in .modal-dialog > * {
    width: 600px;
  }
  .modal-fill-in .modal-dialog.modal-sm > * {
    width: 300px;
  }
  .modal-fill-in .modal-dialog button.close {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1;
    filter: alpha(opacity=50);
    opacity: .5;
    -webkit-transform: translate(0%, 0%);
        -ms-transform: translate(0%, 0%);
         -o-transform: translate(0%, 0%);
            transform: translate(0%, 0%);
  }
}
@media (min-width: 992px) {
  .modal-fill-in .modal-dialog.modal-lg > * {
    width: 900px;
  }
}
.modal-fill-in .modal-content {
  background-color: transparent;
  border-radius: 0;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.modal-fill-in .modal-header {
  border-bottom: none;
}
.modal-fill-in .modal-footer {
  border-top: none;
}
.modal-primary .modal-header {
  background-color: #62a8ea;
  border-radius: 4px 4px 0 0;
}
.modal-primary .modal-header * {
  color: #fff;
}
.modal-primary .modal-header .close {
  opacity: .6;
}
.modal-success .modal-header {
  background-color: #46be8a;
  border-radius: 4px 4px 0 0;
}
.modal-success .modal-header * {
  color: #fff;
}
.modal-success .modal-header .close {
  opacity: .6;
}
.modal-info .modal-header {
  background-color: #57c7d4;
  border-radius: 4px 4px 0 0;
}
.modal-info .modal-header * {
  color: #fff;
}
.modal-info .modal-header .close {
  opacity: .6;
}
.modal-warning .modal-header {
  background-color: #f2a654;
  border-radius: 4px 4px 0 0;
}
.modal-warning .modal-header * {
  color: #fff;
}
.modal-warning .modal-header .close {
  opacity: .6;
}
.modal-danger .modal-header {
  background-color: #f96868;
  border-radius: 4px 4px 0 0;
}
.modal-danger .modal-header * {
  color: #fff;
}
.modal-danger .modal-header .close {
  opacity: .6;
}
.modal.modal-fade-in-scale-up .modal-dialog {
  opacity: 0;
  -webkit-transition: all .3s ease 0s;
       -o-transition: all .3s ease 0s;
          transition: all .3s ease 0s;
  -webkit-transform: scale(.7);
      -ms-transform: scale(.7);
       -o-transform: scale(.7);
          transform: scale(.7);
}
.modal.modal-fade-in-scale-up.in .modal-dialog {
  opacity: 1;
  -webkit-transform: scale(1);
      -ms-transform: scale(1);
       -o-transform: scale(1);
          transform: scale(1);
}
.modal.modal-slide-in-right .modal-dialog {
  opacity: 0;
  -webkit-transition: all .3s cubic-bezier(.25, .5, .5, .9) 0s;
       -o-transition: all .3s cubic-bezier(.25, .5, .5, .9) 0s;
          transition: all .3s cubic-bezier(.25, .5, .5, .9) 0s;
  -webkit-transform: translate(20%, 0%);
      -ms-transform: translate(20%, 0%);
       -o-transform: translate(20%, 0%);
          transform: translate(20%, 0%);
}
.modal.modal-slide-in-right.in .modal-dialog {
  opacity: 1;
  -webkit-transform: translate(0px, 0px);
      -ms-transform: translate(0px, 0px);
       -o-transform: translate(0px, 0px);
          transform: translate(0px, 0px);
}
.modal.modal-slide-from-bottom .modal-dialog {
  opacity: 0;
  -webkit-transition: all .3s ease 0s;
       -o-transition: all .3s ease 0s;
          transition: all .3s ease 0s;
  -webkit-transform: translate(0%, 20%);
      -ms-transform: translate(0%, 20%);
       -o-transform: translate(0%, 20%);
          transform: translate(0%, 20%);
}
.modal.modal-slide-from-bottom.in .modal-dialog {
  opacity: 1;
  -webkit-transform: translate(0px, 0px);
      -ms-transform: translate(0px, 0px);
       -o-transform: translate(0px, 0px);
          transform: translate(0px, 0px);
}
.modal.modal-newspaper .modal-dialog {
  opacity: 0;
  -webkit-transition: all .5s ease 0s;
       -o-transition: all .5s ease 0s;
          transition: all .5s ease 0s;
  -webkit-transform: scale(0) rotate(720deg);
      -ms-transform: scale(0) rotate(720deg);
       -o-transform: scale(0) rotate(720deg);
          transform: scale(0) rotate(720deg);
}
.modal.modal-newspaper.in .modal-dialog {
  opacity: 1;
  -webkit-transform: scale(1) rotate(0deg);
      -ms-transform: scale(1) rotate(0deg);
       -o-transform: scale(1) rotate(0deg);
          transform: scale(1) rotate(0deg);
}
.modal.modal-fall {
  -webkit-perspective: 1300px;
          perspective: 1300px;
}
.modal.modal-fall .modal-dialog {
  opacity: 0;
  -webkit-transform: translateZ(600px) rotateX(20deg);
      -ms-transform: translateZ(600px) rotateX(20deg);
          transform: translateZ(600px) rotateX(20deg);

      -ms-transform-style: preserve-3d;
  -webkit-transform-style: preserve-3d;
          transform-style: preserve-3d;
}
.modal.modal-fall.in .modal-dialog {
  opacity: 1;
  -webkit-transition: all .3s ease-in 0s;
       -o-transition: all .3s ease-in 0s;
          transition: all .3s ease-in 0s;
  -webkit-transform: translateZ(0px) rotateX(0deg);
      -ms-transform: translateZ(0px) rotateX(0deg);
          transform: translateZ(0px) rotateX(0deg);
}
.modal.modal-side-fall {
  -webkit-perspective: 1300px;
          perspective: 1300px;
}
.modal.modal-side-fall .modal-dialog {
  -webkit-transform: translate(30%) translateZ(600px) rotate(10deg);
      -ms-transform: translate(30%) translateZ(600px) rotate(10deg);
          transform: translate(30%) translateZ(600px) rotate(10deg);

      -ms-transform-style: preserve-3d;
  -webkit-transform-style: preserve-3d;
          transform-style: preserve-3d;
}
.modal.modal-side-fall.in .modal-dialog {
  -webkit-transition: all .3s ease-in 0s;
       -o-transition: all .3s ease-in 0s;
          transition: all .3s ease-in 0s;
  -webkit-transform: translate(0%) translateZ(0px) rotate(0deg);
      -ms-transform: translate(0%) translateZ(0px) rotate(0deg);
          transform: translate(0%) translateZ(0px) rotate(0deg);
}
.modal.modal-3d-flip-horizontal {
  -webkit-perspective: 1300px;
          perspective: 1300px;
}
.modal.modal-3d-flip-horizontal .modal-dialog {
  -webkit-transition: all .3s ease 0s;
       -o-transition: all .3s ease 0s;
          transition: all .3s ease 0s;
  -webkit-transform: rotateY(-70deg);
      -ms-transform: rotateY(-70deg);
       -o-transform: rotateY(-70deg);
          transform: rotateY(-70deg);

      -ms-transform-style: preserve-3d;
  -webkit-transform-style: preserve-3d;
          transform-style: preserve-3d;
}
.modal.modal-3d-flip-horizontal.in .modal-dialog {
  -webkit-transform: rotateY(0deg);
      -ms-transform: rotateY(0deg);
       -o-transform: rotateY(0deg);
          transform: rotateY(0deg);
}
.modal.modal-3d-flip-vertical {
  -webkit-perspective: 1300px;
          perspective: 1300px;
}
.modal.modal-3d-flip-vertical .modal-dialog {
  -webkit-transition: all .3s ease 0s;
       -o-transition: all .3s ease 0s;
          transition: all .3s ease 0s;
  -webkit-transform: rotateX(-70deg);
      -ms-transform: rotateX(-70deg);
       -o-transform: rotateX(-70deg);
          transform: rotateX(-70deg);

      -ms-transform-style: preserve-3d;
  -webkit-transform-style: preserve-3d;
          transform-style: preserve-3d;
}
.modal.modal-3d-flip-vertical.in .modal-dialog {
  -webkit-transform: rotateX(0deg);
      -ms-transform: rotateX(0deg);
       -o-transform: rotateX(0deg);
          transform: rotateX(0deg);
}
.modal.modal-3d-sign {
  -webkit-perspective: 1300px;
          perspective: 1300px;
}
.modal.modal-3d-sign .modal-dialog {
  -webkit-transition: all .3s ease 0s;
       -o-transition: all .3s ease 0s;
          transition: all .3s ease 0s;
  -webkit-transform: rotateX(-60deg);
      -ms-transform: rotateX(-60deg);
       -o-transform: rotateX(-60deg);
          transform: rotateX(-60deg);
  -webkit-transform-origin: 50% 0 0;
      -ms-transform-origin: 50% 0 0;
       -o-transform-origin: 50% 0 0;
          transform-origin: 50% 0 0;

      -ms-transform-style: preserve-3d;
  -webkit-transform-style: preserve-3d;
          transform-style: preserve-3d;
}
.modal.modal-3d-sign.in .modal-dialog {
  -webkit-transform: rotateX(0deg);
      -ms-transform: rotateX(0deg);
       -o-transform: rotateX(0deg);
          transform: rotateX(0deg);
}
.modal.modal-super-scaled .modal-dialog {
  opacity: 0;
  -webkit-transition: all .3s ease 0s;
       -o-transition: all .3s ease 0s;
          transition: all .3s ease 0s;
  -webkit-transform: scale(2);
      -ms-transform: scale(2);
       -o-transform: scale(2);
          transform: scale(2);
}
.modal.modal-super-scaled.in .modal-dialog {
  opacity: 1;
  -webkit-transform: scale(1);
      -ms-transform: scale(1);
       -o-transform: scale(1);
          transform: scale(1);
}
.modal.modal-just-me .modal-dialog {
  opacity: 0;
  -webkit-transition: all .3s ease 0s;
       -o-transition: all .3s ease 0s;
          transition: all .3s ease 0s;
  -webkit-transform: scale(.8);
      -ms-transform: scale(.8);
       -o-transform: scale(.8);
          transform: scale(.8);
}
.modal.modal-just-me .modal-backdrop {
  background-color: #fff;
}
.modal.modal-just-me.in {
  background: #fff;
}
.modal.modal-just-me.in .modal-dialog {
  opacity: 1;
  -webkit-transform: scale(1);
      -ms-transform: scale(1);
       -o-transform: scale(1);
          transform: scale(1);
}
.modal.modal-just-me.in .modal-backdrop {
  opacity: 1;
}
.modal.modal-3d-slit {
  -webkit-perspective: 1300px;
          perspective: 1300px;
}
.modal.modal-3d-slit .modal-dialog {
  opacity: 0;
  -webkit-transition: all .5s ease 0s;
       -o-transition: all .5s ease 0s;
          transition: all .5s ease 0s;
  -webkit-transform: translateZ(-3000px) rotateY(90deg);
      -ms-transform: translateZ(-3000px) rotateY(90deg);
          transform: translateZ(-3000px) rotateY(90deg);

      -ms-transform-style: preserve-3d;
  -webkit-transform-style: preserve-3d;
          transform-style: preserve-3d;
}
.modal.modal-3d-slit.in .modal-dialog {
  -webkit-animation-name: slit;
       -o-animation-name: slit;
          animation-name: slit;
  -webkit-animation-duration: .7s;
       -o-animation-duration: .7s;
          animation-duration: .7s;
  -webkit-animation-timing-function: ease-out;
       -o-animation-timing-function: ease-out;
          animation-timing-function: ease-out;

  -webkit-animation-fill-mode: forwards;
       -o-animation-fill-mode: forwards;
          animation-fill-mode: forwards;
}
.modal.modal-rotate-from-bottom {
  -webkit-perspective: 1300px;
          perspective: 1300px;
}
.modal.modal-rotate-from-bottom .modal-dialog {
  -webkit-transition: all .3s ease-out 0s;
       -o-transition: all .3s ease-out 0s;
          transition: all .3s ease-out 0s;
  -webkit-transform: translateY(100%) rotateX(90deg);
      -ms-transform: translateY(100%) rotateX(90deg);
          transform: translateY(100%) rotateX(90deg);
  -webkit-transform-origin: 0 100% 0;
      -ms-transform-origin: 0 100% 0;
       -o-transform-origin: 0 100% 0;
          transform-origin: 0 100% 0;

      -ms-transform-style: preserve-3d;
  -webkit-transform-style: preserve-3d;
          transform-style: preserve-3d;
}
.modal.modal-rotate-from-bottom.in .modal-dialog {
  -webkit-transform: translateY(0%) rotateX(0deg);
      -ms-transform: translateY(0%) rotateX(0deg);
          transform: translateY(0%) rotateX(0deg);
}
.modal.modal-rotate-from-left {
  -webkit-perspective: 1300px;
          perspective: 1300px;
}
.modal.modal-rotate-from-left .modal-dialog {
  -webkit-transition: all .3s ease-out 0s;
       -o-transition: all .3s ease-out 0s;
          transition: all .3s ease-out 0s;
  -webkit-transform: translateZ(100px) translateX(-30%) rotateY(90deg);
      -ms-transform: translateZ(100px) translateX(-30%) rotateY(90deg);
          transform: translateZ(100px) translateX(-30%) rotateY(90deg);
  -webkit-transform-origin: 0 100% 0;
      -ms-transform-origin: 0 100% 0;
       -o-transform-origin: 0 100% 0;
          transform-origin: 0 100% 0;

      -ms-transform-style: preserve-3d;
  -webkit-transform-style: preserve-3d;
          transform-style: preserve-3d;
}
.modal.modal-rotate-from-left.in .modal-dialog {
  -webkit-transform: translateZ(0px) translateX(0%) rotateY(0deg);
      -ms-transform: translateZ(0px) translateX(0%) rotateY(0deg);
          transform: translateZ(0px) translateX(0%) rotateY(0deg);
}
@-webkit-keyframes slit {
  50% {
    opacity: .5;
    -webkit-transform: translateZ(-250px) rotateY(89deg);
        -ms-transform: translateZ(-250px) rotateY(89deg);
            transform: translateZ(-250px) rotateY(89deg);
  }
  100% {
    opacity: 1;
    -webkit-transform: translateZ(0px) rotateY(0deg);
        -ms-transform: translateZ(0px) rotateY(0deg);
            transform: translateZ(0px) rotateY(0deg);
  }
}
@-o-keyframes slit {
  50% {
    opacity: .5;
    -webkit-transform: translateZ(-250px) rotateY(89deg);
        -ms-transform: translateZ(-250px) rotateY(89deg);
            transform: translateZ(-250px) rotateY(89deg);
  }
  100% {
    opacity: 1;
    -webkit-transform: translateZ(0px) rotateY(0deg);
        -ms-transform: translateZ(0px) rotateY(0deg);
            transform: translateZ(0px) rotateY(0deg);
  }
}
@keyframes slit {
  50% {
    opacity: .5;
    -webkit-transform: translateZ(-250px) rotateY(89deg);
        -ms-transform: translateZ(-250px) rotateY(89deg);
            transform: translateZ(-250px) rotateY(89deg);
  }
  100% {
    opacity: 1;
    -webkit-transform: translateZ(0px) rotateY(0deg);
        -ms-transform: translateZ(0px) rotateY(0deg);
            transform: translateZ(0px) rotateY(0deg);
  }
}
.tooltip-inner {
  padding: 6px 12px;
}
.tooltip-primary + .tooltip .tooltip-inner {
  color: #fff;
  background-color: #62a8ea;
}
.tooltip-primary + .tooltip.top .tooltip-arrow {
  border-top-color: #62a8ea;
}
.tooltip-primary + .tooltip.right .tooltip-arrow {
  border-right-color: #62a8ea;
}
.tooltip-primary + .tooltip.bottom .tooltip-arrow {
  border-bottom-color: #62a8ea;
}
.tooltip-primary + .tooltip.left .tooltip-arrow {
  border-left-color: #62a8ea;
}
.tooltip-success + .tooltip .tooltip-inner {
  color: #fff;
  background-color: #46be8a;
}
.tooltip-success + .tooltip.top .tooltip-arrow {
  border-top-color: #46be8a;
}
.tooltip-success + .tooltip.right .tooltip-arrow {
  border-right-color: #46be8a;
}
.tooltip-success + .tooltip.bottom .tooltip-arrow {
  border-bottom-color: #46be8a;
}
.tooltip-success + .tooltip.left .tooltip-arrow {
  border-left-color: #46be8a;
}
.tooltip-info + .tooltip .tooltip-inner {
  color: #fff;
  background-color: #57c7d4;
}
.tooltip-info + .tooltip.top .tooltip-arrow {
  border-top-color: #57c7d4;
}
.tooltip-info + .tooltip.right .tooltip-arrow {
  border-right-color: #57c7d4;
}
.tooltip-info + .tooltip.bottom .tooltip-arrow {
  border-bottom-color: #57c7d4;
}
.tooltip-info + .tooltip.left .tooltip-arrow {
  border-left-color: #57c7d4;
}
.tooltip-warning + .tooltip .tooltip-inner {
  color: #fff;
  background-color: #f2a654;
}
.tooltip-warning + .tooltip.top .tooltip-arrow {
  border-top-color: #f2a654;
}
.tooltip-warning + .tooltip.right .tooltip-arrow {
  border-right-color: #f2a654;
}
.tooltip-warning + .tooltip.bottom .tooltip-arrow {
  border-bottom-color: #f2a654;
}
.tooltip-warning + .tooltip.left .tooltip-arrow {
  border-left-color: #f2a654;
}
.tooltip-danger + .tooltip .tooltip-inner {
  color: #fff;
  background-color: #f96868;
}
.tooltip-danger + .tooltip.top .tooltip-arrow {
  border-top-color: #f96868;
}
.tooltip-danger + .tooltip.right .tooltip-arrow {
  border-right-color: #f96868;
}
.tooltip-danger + .tooltip.bottom .tooltip-arrow {
  border-bottom-color: #f96868;
}
.tooltip-danger + .tooltip.left .tooltip-arrow {
  border-left-color: #f96868;
}
.tooltip-rotate + .tooltip {
  opacity: 0;
  -webkit-animation: tooltip-rotate3d 1s ease .1s forwards;
       -o-animation: tooltip-rotate3d 1s ease .1s forwards;
          animation: tooltip-rotate3d 1s ease .1s forwards;
}
@-webkit-keyframes tooltip-rotate3d {
  0% {
    opacity: 0;
    -webkit-transform: rotate(15deg);
            transform: rotate(15deg);
  }
  100% {
    opacity: 1;
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
}
@-o-keyframes tooltip-rotate3d {
  0% {
    opacity: 0;
    -o-transform: rotate(15deg);
       transform: rotate(15deg);
  }
  100% {
    opacity: 1;
    -o-transform: rotate(0deg);
       transform: rotate(0deg);
  }
}
@keyframes tooltip-rotate3d {
  0% {
    opacity: 0;
    -webkit-transform: rotate(15deg);
         -o-transform: rotate(15deg);
            transform: rotate(15deg);
  }
  100% {
    opacity: 1;
    -webkit-transform: rotate(0deg);
         -o-transform: rotate(0deg);
            transform: rotate(0deg);
  }
}
.tooltip-scale + .tooltip {
  -webkit-animation: tooltip-scale3d 1s ease 0s forwards;
       -o-animation: tooltip-scale3d 1s ease 0s forwards;
          animation: tooltip-scale3d 1s ease 0s forwards;
}
@-webkit-keyframes tooltip-scale3d {
  0% {
    opacity: 0;
    -webkit-transform: scale3d(.7, .3, 1) translate(50%, 50%);
            transform: scale3d(.7, .3, 1) translate(50%, 50%);
  }
  100% {
    opacity: 1;
    -webkit-transform: scale3d(1, 1, 1) translate(50%, 50%);
            transform: scale3d(1, 1, 1) translate(50%, 50%);
  }
}
@-o-keyframes tooltip-scale3d {
  0% {
    opacity: 0;
    transform: scale3d(.7, .3, 1) translate(50%, 50%);
  }
  100% {
    opacity: 1;
    transform: scale3d(1, 1, 1) translate(50%, 50%);
  }
}
@keyframes tooltip-scale3d {
  0% {
    opacity: 0;
    -webkit-transform: scale3d(.7, .3, 1) translate(50%, 50%);
            transform: scale3d(.7, .3, 1) translate(50%, 50%);
  }
  100% {
    opacity: 1;
    -webkit-transform: scale3d(1, 1, 1) translate(50%, 50%);
            transform: scale3d(1, 1, 1) translate(50%, 50%);
  }
}
.popover {
  padding: 0;
  -webkit-box-shadow: 0 2px 6px rgba(0, 0, 0, .05);
          box-shadow: 0 2px 6px rgba(0, 0, 0, .05);
}
.popover.bottom > .arrow:after {
  border-bottom-color: #f3f7f9;
}
.popover-content {
  padding: 20px;
}
.popover-primary + .popover .popover-title {
  color: #fff;
  background-color: #62a8ea;
  border-color: #62a8ea;
}
.popover-primary + .popover.bottom .arrow {
  border-bottom-color: #62a8ea;
}
.popover-primary + .popover.bottom .arrow:after {
  border-bottom-color: #62a8ea;
}
.popover-success + .popover .popover-title {
  color: #fff;
  background-color: #46be8a;
  border-color: #46be8a;
}
.popover-success + .popover.bottom .arrow {
  border-bottom-color: #46be8a;
}
.popover-success + .popover.bottom .arrow:after {
  border-bottom-color: #46be8a;
}
.popover-info + .popover .popover-title {
  color: #fff;
  background-color: #57c7d4;
  border-color: #57c7d4;
}
.popover-info + .popover.bottom .arrow {
  border-bottom-color: #57c7d4;
}
.popover-info + .popover.bottom .arrow:after {
  border-bottom-color: #57c7d4;
}
.popover-warning + .popover .popover-title {
  color: #fff;
  background-color: #f2a654;
  border-color: #f2a654;
}
.popover-warning + .popover.bottom .arrow {
  border-bottom-color: #f2a654;
}
.popover-warning + .popover.bottom .arrow:after {
  border-bottom-color: #f2a654;
}
.popover-danger + .popover .popover-title {
  color: #fff;
  background-color: #f96868;
  border-color: #f96868;
}
.popover-danger + .popover.bottom .arrow {
  border-bottom-color: #f96868;
}
.popover-danger + .popover.bottom .arrow:after {
  border-bottom-color: #f96868;
}
.popover-rotate + .popover {
  opacity: 0;
  -webkit-animation: popover-rotate3d 1s ease .1s forwards;
       -o-animation: popover-rotate3d 1s ease .1s forwards;
          animation: popover-rotate3d 1s ease .1s forwards;
}
@-webkit-keyframes popover-rotate3d {
  0% {
    opacity: 0;
    -webkit-transform: rotate(15deg);
            transform: rotate(15deg);
  }
  100% {
    opacity: 1;
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
}
@-o-keyframes popover-rotate3d {
  0% {
    opacity: 0;
    -o-transform: rotate(15deg);
       transform: rotate(15deg);
  }
  100% {
    opacity: 1;
    -o-transform: rotate(0deg);
       transform: rotate(0deg);
  }
}
@keyframes popover-rotate3d {
  0% {
    opacity: 0;
    -webkit-transform: rotate(15deg);
         -o-transform: rotate(15deg);
            transform: rotate(15deg);
  }
  100% {
    opacity: 1;
    -webkit-transform: rotate(0deg);
         -o-transform: rotate(0deg);
            transform: rotate(0deg);
  }
}
.popover-scale + .popover {
  -webkit-animation: popover-scale3d 1s ease 0s forwards;
       -o-animation: popover-scale3d 1s ease 0s forwards;
          animation: popover-scale3d 1s ease 0s forwards;
}
@-webkit-keyframes popover-scale3d {
  0% {
    opacity: 0;
    -webkit-transform: scale3d(.7, .3, 1) translate(50%, 50%);
            transform: scale3d(.7, .3, 1) translate(50%, 50%);
  }
  100% {
    opacity: 1;
    -webkit-transform: scale3d(1, 1, 1) translate(50%, 50%);
            transform: scale3d(1, 1, 1) translate(50%, 50%);
  }
}
@-o-keyframes popover-scale3d {
  0% {
    opacity: 0;
    transform: scale3d(.7, .3, 1) translate(50%, 50%);
  }
  100% {
    opacity: 1;
    transform: scale3d(1, 1, 1) translate(50%, 50%);
  }
}
@keyframes popover-scale3d {
  0% {
    opacity: 0;
    -webkit-transform: scale3d(.7, .3, 1) translate(50%, 50%);
            transform: scale3d(.7, .3, 1) translate(50%, 50%);
  }
  100% {
    opacity: 1;
    -webkit-transform: scale3d(1, 1, 1) translate(50%, 50%);
            transform: scale3d(1, 1, 1) translate(50%, 50%);
  }
}
.carousel-control {
  min-width: 50px;
}
.carousel-control:hover,
.carousel-control:focus {
  filter: alpha(opacity=40);
  opacity: .4;
}
.carousel-control .icon {
  position: absolute;
  top: 50%;
  z-index: 5;
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-top: -8px;
}
.carousel-control.left .icon {
  left: 50%;
  margin-left: -8px;
}
.carousel-control.right .icon {
  right: 50%;
  margin-right: -8px;
}
.carousel-caption h1,
.carousel-caption h2,
.carousel-caption h3,
.carousel-caption h4,
.carousel-caption h5,
.carousel-caption h6 {
  color: inherit;
}
.carousel-indicators {
  margin-bottom: 0;
}
.carousel-indicators li {
  margin: 3px;
  background-color: rgba(255, 255, 255, .3);
  border: none;
}
.carousel-indicators .active {
  width: 10px;
  height: 10px;
  margin: 3px;
}
.carousel-indicators-scaleup li {
  border: none;
  -webkit-transition: -webkit-transform .3s ease 0s, background-color .3s ease 0s;
       -o-transition:      -o-transform .3s ease 0s, background-color .3s ease 0s;
          transition:         transform .3s ease 0s, background-color .3s ease 0s;
}
.carousel-indicators-scaleup .active {
  -webkit-transform: scale(1.5);
      -ms-transform: scale(1.5);
       -o-transform: scale(1.5);
          transform: scale(1.5);
}
.carousel-indicators-fillin li {
  background-color: transparent;
  -webkit-box-shadow: 0 0 0 2px #fff inset;
          box-shadow: 0 0 0 2px #fff inset;
  -webkit-transition: -webkit-box-shadow .3s ease 0s;
       -o-transition:         box-shadow .3s ease 0s;
          transition:         box-shadow .3s ease 0s;
}
.carousel-indicators-fillin .active {
  -webkit-box-shadow: 0 0 0 8px #fff inset;
          box-shadow: 0 0 0 8px #fff inset;
}
.carousel-indicators-fall li {
  position: relative;
  -webkit-transition: -webkit-transform .3s ease 0s, background-color .3s ease 0s;
       -o-transition:      -o-transform .3s ease 0s, background-color .3s ease 0s;
          transition:         transform .3s ease 0s, background-color .3s ease 0s;
}
.carousel-indicators-fall li:after {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  visibility: hidden;
  content: '';
  background-color: rgba(0, 0, 0, .3);
  border-radius: 50%;
  opacity: 0;
  -webkit-transition: opacity .3s ease 0s, visibility 0s ease .3s;
       -o-transition: opacity .3s ease 0s, visibility 0s ease .3s;
          transition: opacity .3s ease 0s, visibility 0s ease .3s;
  -webkit-transform: translate(0%, -200%);
      -ms-transform: translate(0%, -200%);
       -o-transform: translate(0%, -200%);
          transform: translate(0%, -200%);
}
.carousel-indicators-fall .active {
  background-color: transparent;
  -webkit-transform: translate(0, 200%);
      -ms-transform: translate(0, 200%);
       -o-transform: translate(0, 200%);
          transform: translate(0, 200%);
}
.carousel-indicators-fall .active:after {
  visibility: visible;
  opacity: 1;
  -webkit-transition: opacity .3s ease 0s;
       -o-transition: opacity .3s ease 0s;
          transition: opacity .3s ease 0s;
}
@media screen and (min-width: 768px) {
  .carousel-control .icon {
    width: 24px;
    height: 24px;
    margin-top: -12px;
    font-size: 24px;
  }
  .carousel-control.left .icon {
    margin-left: -12px;
  }
  .carousel-control.right .icon {
    margin-right: -12px;
  }
}
.vertical-align {
  font-size: 0;
}
.vertical-align:before {
  display: inline-block;
  height: 100%;
  vertical-align: middle;
  content: "";
}
.vertical-align-middle,
.vertical-align-bottom {
  display: inline-block;
  max-width: 100%;
  font-size: 14px;
}
.vertical-align-middle {
  vertical-align: middle;
}
.vertical-align-bottom {
  vertical-align: bottom;
}
.inline {
  display: inline !important;
}
.inline-block {
  display: inline-block !important;
}
.block {
  display: block !important;
}
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: normal;
  /* for IE */
  white-space: nowrap;
}
.text-break {
  -webkit-hyphens: auto;
     -moz-hyphens: auto;
          hyphens: auto;
  word-wrap: break-word;
  white-space: normal;

      -ms-hyphens: auto;
}
.text-nowrap {
  white-space: nowrap;
}
.text-top {
  vertical-align: top !important;
}
.text-middle {
  vertical-align: middle !important;
}
.text-bottom {
  vertical-align: bottom !important;
}
.text-left {
  text-align: left !important;
}
.text-right {
  text-align: right !important;
}
.text-center {
  text-align: center !important;
}
.text-justify {
  text-align: justify !important;
}
.text-lowercase {
  text-transform: lowercase !important;
}
.text-uppercase {
  text-transform: uppercase !important;
}
.text-capitalize {
  text-transform: capitalize !important;
}
.font-weight-unset {
  font-weight: unset !important;
}
.font-weight-100 {
  font-weight: 100 !important;
}
.font-weight-200 {
  font-weight: 200 !important;
}
.font-weight-300 {
  font-weight: 300 !important;
}
.font-weight-400 {
  font-weight: 400 !important;
}
.font-weight-500 {
  font-weight: 500 !important;
}
.font-weight-600 {
  font-weight: 600 !important;
}
.font-weight-700 {
  font-weight: 700 !important;
}
.font-weight-800 {
  font-weight: 800 !important;
}
.font-weight-900 {
  font-weight: 900 !important;
}
.font-weight-light {
  font-weight: 100 !important;
}
.font-weight-normal {
  font-weight: 300 !important;
}
.font-weight-medium {
  font-weight: 400 !important;
}
.font-weight-bold {
  font-weight: 500 !important;
}
.font-size-0 {
  font-size: 0 !important;
}
.font-size-10 {
  font-size: 10px !important;
}
.font-size-12 {
  font-size: 12px !important;
}
.font-size-14 {
  font-size: 14px !important;
}
.font-size-16 {
  font-size: 16px !important;
}
.font-size-18 {
  font-size: 18px !important;
}
.font-size-20 {
  font-size: 20px !important;
}
.font-size-24 {
  font-size: 24px !important;
}
.font-size-26 {
  font-size: 26px !important;
}
.font-size-30 {
  font-size: 30px !important;
}
.font-size-40 {
  font-size: 40px !important;
}
.font-size-50 {
  font-size: 50px !important;
}
.font-size-60 {
  font-size: 60px !important;
}
.font-size-70 {
  font-size: 70px !important;
}
.font-size-80 {
  font-size: 80px !important;
}
.visible-xlg {
  display: none !important;
}
.visible-xlg-block,
.visible-xlg-inline,
.visible-xlg-inline-block {
  display: none !important;
}
@media (min-width: 1600px) {
  .visible-xlg {
    display: block !important;
  }
  table.visible-xlg {
    display: table !important;
  }
  tr.visible-xlg {
    display: table-row !important;
  }
  th.visible-xlg,
  td.visible-xlg {
    display: table-cell !important;
  }
}
@media (min-width: 1600px) {
  .visible-xlg-block {
    display: block !important;
  }
}
@media (min-width: 1600px) {
  .visible-xlg-inline {
    display: inline !important;
  }
}
@media (min-width: 1200px) {
  .visible-xlg-inline-block {
    display: inline-block !important;
  }
}
@media (min-width: 1600px) {
  .hidden-xlg {
    display: none !important;
  }
}

/*# sourceMappingURL=data:application/json;base64,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 */
