/* fullcalendar Custom style
--------------------------------------------------------------------------------------------------*/
.fc td.fc-widget-header {
  padding-top: 20px;
  padding-bottom: 20px;
  font-size: 14px;
  text-transform: uppercase;
  border-width: 0;
}
.fc td.fc-widget-content {
  border-width: 1px 0 0;
}
.fc td.fc-day.fc-widget-content + .fc-widget-content {
  border-left-width: 1px;
}
.fc .fc-row {
  border-width: 0;
}
.fc table > thead > tr > th {
  font-weight: 100;
  border-width: 0;
}
.fc .fc-toolbar h2 {
  display: inline-block;
  font-size: 20px;
  vertical-align: sub;
}
.fc .fc-axis.fc-time {
  text-transform: uppercase;
}
.fc .fc-toolbar {
  position: relative;
}
.fc-button.fc-prev-button,
.fc-button.fc-next-button {
  background-color: transparent;
  background-image: none;
  border: 0 solid transparent;
  outline: none;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.fc-button.fc-month-button,
.fc-button.fc-agendaWeek-button,
.fc-button.fc-agendaDay-button {
  height: auto;
  padding: 8px 14px;
  font-size: 14px;
  text-transform: capitalize;
  background-color: #f6f8f8;
  background-image: none;
  border-color: #e4eaec;
  outline: none;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.fc-button.fc-state-active,
.fc-button.fc-state-hover {
  background-color: #fff;
}
.fc-unthemed .fc-today {
  background-color: #f4f7f8;
}
.fc-toolbar .fc-right {
  position: absolute;
  top: 0;
  right: 0;
}
.fc-agendaWeek-view .fc-event,
.fc-agendaDay-view .fc-event {
  border-radius: 3px;
}
.fc-row.fc-widget-header {
  border-color: transparent;
}
.calendar-container {
  overflow: hidden;
}
.panel-heading {
  padding-top: 10px;
  border-width: 0;
}
.panel-body {
  padding-top: 10px;
}
.calendar-list .list-group-item {
  cursor: pointer;
}
.fc-event {
  background-color: #62a8ea;
  border-color: #62a8ea;
  border-radius: 10px;
}
.fc-day-grid-event .fc-content {
  padding: 2px 10px;
  line-height: 1em;
}
.fc-highlight {
  background: #f3f7f9;
}
.bootstrap-touchspin .input-group-btn-vertical i {
  left: 3px;
}
@media (max-width: 991px) {
  .fc-toolbar {
    margin-bottom: 60px;
  }
  .fc-toolbar .fc-center {
    white-space: nowrap;
  }
  .fc-toolbar .fc-right {
    top: 50px;
  }
  .fc-button.fc-prev-button {
    padding-left: 0;
  }
  .fc-button.fc-next-button {
    padding-right: 0;
  }
}
