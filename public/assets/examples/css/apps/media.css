.app-media .page-content-actions .checkbox-custom {
  margin-bottom: 20px;
}
.app-media .page-content-actions .actions-inner {
  border-bottom: 1px solid #e4eaec;
}
.app-media .page-content-actions .btn-outline.btn-default {
  z-index: 2;
}
.app-media .blocks {
  margin-right: -5px;
  margin-left: -5px;
}
.app-media .blocks > li {
  padding-right: 5px;
  padding-left: 5px;
  margin-bottom: 0;
}
.app-media .time {
  color: #a3afb7;
}
.app-media .media-list {
  padding: 20px 30px;
  overflow: hidden;
}
.app-media .media-list .image-wrap > .image {
  max-width: 100%;
}
.app-media .media-list .media-item-actions {
  display: none;
}
.app-media .media-list .media-item {
  cursor: pointer;
}
.app-media .media-list.is-grid .media-item {
  position: relative;
  width: 100%;
  padding: 10px;
  margin-bottom: 10px;
  cursor: pointer;
  border: 1px solid transparent;
  border-radius: 4px;
}
.app-media .media-list.is-grid .media-item:hover,
.app-media .media-list.is-grid .media-item.active {
  background-color: #f3f7f9;
  border-color: #e4eaec;
}
.app-media .media-list.is-grid .media-item:hover .dropdown,
.app-media .media-list.is-grid .media-item.active .dropdown {
  display: block;
}
.app-media .media-list.is-grid .media-item .checkbox-custom {
  position: absolute;
  top: 9px;
  left: 34px;
  padding: 0;
  margin: 0;
}
.app-media .media-list.is-grid .media-item .image-wrap {
  margin-bottom: 10px;
}
.app-media .media-list.is-grid .media-item .dropdown {
  display: none;
  float: right;
}
.app-media .media-list.is-grid .media-item .dropdown.open .dropdown-toggle,
.app-media .media-list.is-grid .media-item .dropdown.open .dropdown-toggle:hover {
  color: #526069;
}
.app-media .media-list.is-grid .media-item .dropdown-toggle {
  color: #a3afb7;
}
.app-media .media-list.is-grid .media-item .dropdown-menu {
  -webkit-transform-origin: 100% 0;
      -ms-transform-origin: 100% 0;
       -o-transform-origin: 100% 0;
          transform-origin: 100% 0;
  -webkit-animation-duration: .3s;
       -o-animation-duration: .3s;
          animation-duration: .3s;
}
.app-media .media-list.is-grid .media-item .dropdown-menu .icon {
  margin-right: 10px;
}
.app-media .media-list.is-grid .media-item .dropdown-menu > li > a {
  padding: 3px 10px;
}
.app-media .media-list.is-list {
  padding-right: 0;
  padding-left: 0;
}
.app-media .media-list.is-list .blocks > li {
  width: 100%;
}
.app-media .media-list.is-list .media-item {
  position: relative;
  padding: 20px 30px;
  white-space: nowrap;
}
.app-media .media-list.is-list .media-item > div {
  display: inline-block;
}
.app-media .media-list.is-list .media-item:hover {
  background-color: #f3f7f9;
}
.app-media .media-list.is-list .media-item:hover .media-item-actions {
  display: block;
}
.app-media .media-list.is-list .media-item:after {
  position: absolute;
  bottom: 0;
  left: 30px;
  display: block;
  width: -webkit-calc(100% - 60px);
  width:         calc(100% - 60px);
  content: '';
  border-bottom: 1px solid #e4eaec;
}
.app-media .media-list.is-list .checkbox-custom {
  margin-right: 30px;
}
.app-media .media-list.is-list .image-wrap {
  width: 140px;
  height: 100px;
  margin-right: 20px;
  font-size: 0;
}
.app-media .media-list.is-list .image-wrap:before {
  display: inline-block;
  height: 100%;
  vertical-align: middle;
  content: "";
}
.app-media .media-list.is-list .info-wrap {
  vertical-align: top;
}
.app-media .media-list.is-list .info-wrap .title {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.app-media .media-list.is-list .dropdown {
  display: none;
}
.app-media .media-list.is-list .media-item-actions {
  margin-top: 15px;
}
.app-media .media-list.is-list .media-item-actions .btn-icon {
  margin-left: 1px;
  color: #a3afb7;
}
.app-media .slidePanel-header {
  width: 100%;
  height: 350px;
}
.app-media .slidePanel-header .slidePanel-actions {
  min-height: 46px;
}
.app-media .slidePanel .overlay-top {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.app-media .slidePanel .overlay-background {
  background-color: rgba(38, 50, 56, .6);
}
.app-media .slidePanel .media-header {
  position: relative;
  margin-bottom: 20px;
}
.app-media .slidePanel .media-header:before,
.app-media .slidePanel .media-header:after {
  display: table;
  content: " ";
}
.app-media .slidePanel .media-header:after {
  clear: both;
}
.app-media .slidePanel .media-header .time {
  line-height: 40px;
}
.app-media .slidePanel .media-header .share {
  display: inline-block;
  float: right;
}
.app-media .slidePanel .media-header .tags {
  display: inline-block;
}
.app-media .slidePanel .avatar {
  vertical-align: middle;
}
@media (max-width: 480px) {
  .app-media .is-list .info-wrap {
    display: block !important;
    padding-left: 60px;
  }
  .app-media .is-list .info-wrap:before,
  .app-media .is-list .info-wrap:after {
    display: table;
    content: " ";
  }
  .app-media .is-list .info-wrap:after {
    clear: both;
  }
  .app-media .is-list .media-item-actions {
    display: block;
    margin-top: 5px;
  }
  .app-media .page-header .page-header-actions {
    position: relative;
    top: 0;
    right: 0;
    margin-top: 20px;
    -webkit-transform: none;
        -ms-transform: none;
         -o-transform: none;
            transform: none;
  }
  .app-media .slidePanel .media-header .share {
    display: block;
    float: none;
    margin-top: 20px;
  }
}
