/*
* bootstrap-table - v1.8.1 - 2015-09-27
* https://github.com/wenzhixin/bootstrap-table
* Copyright (c) 2015 zhixin wen
* Licensed MIT License
*/
function alphanum(a,b){function c(a){for(var b,c,d=[],e=0,f=-1,g=0;b=(c=a.charAt(e++)).charCodeAt(0);){var h=46===b||b>=48&&57>=b;h!==g&&(d[++f]="",g=h),d[f]+=c}return d}var d=c(a),e=c(b);for(x=0;d[x]&&e[x];x++)if(d[x]!==e[x]){var f=Number(d[x]),g=Number(e[x]);return f==d[x]&&g==e[x]?f-g:d[x]>e[x]?1:-1}return d.length-e.length}!function(a){"use strict";var b=null,c=function(a){var b=arguments,c=!0,d=1;return a=a.replace(/%s/g,function(){var a=b[d++];return"undefined"==typeof a?(c=!1,""):a}),c?a:""},d=function(b,c,d,e){var f="";return a.each(b,function(a,b){return b[c]===e?(f=b[d],!1):!0}),f},e=function(b,c){var d=-1;return a.each(b,function(a,b){return b.field===c?(d=a,!1):!0}),d},f=function(b){var c,d,e,f=0,g=[];for(c=0;c<b[0].length;c++)f+=b[0][c].colspan||1;for(c=0;c<b.length;c++)for(g[c]=[],d=0;f>d;d++)g[c][d]=!1;for(c=0;c<b.length;c++)for(d=0;d<b[c].length;d++){var h=b[c][d],i=h.rowspan||1,j=h.colspan||1,k=a.inArray(!1,g[c]);for(1===j&&(h.fieldIndex=k,"undefined"==typeof h.field&&(h.field=k)),e=0;i>e;e++)g[c+e][k]=!0;for(e=0;j>e;e++)g[c][k+e]=!0}},g=function(){if(null===b){var c,d,e=a("<p/>").addClass("fixed-table-scroll-inner"),f=a("<div/>").addClass("fixed-table-scroll-outer");f.append(e),a("body").append(f),c=e[0].offsetWidth,f.css("overflow","scroll"),d=e[0].offsetWidth,c===d&&(d=f[0].clientWidth),f.remove(),b=c-d}return b},h=function(b,d,e,f){var g=d;if("string"==typeof d){var h=d.split(".");h.length>1?(g=window,a.each(h,function(a,b){g=g[b]})):g=window[d]}return"object"==typeof g?g:"function"==typeof g?g.apply(b,e):!g&&"string"==typeof d&&c.apply(this,[d].concat(e))?c.apply(this,[d].concat(e)):f},i=function(b,c,d){var e=Object.getOwnPropertyNames(b),f=Object.getOwnPropertyNames(c),g="";if(d&&e.length!==f.length)return!1;for(var h=0;h<e.length;h++)if(g=e[h],a.inArray(g,f)>-1&&b[g]!==c[g])return!1;return!0},j=function(a){return"string"==typeof a?a.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;"):a},k=function(b){var c=0;return b.children().each(function(){c<a(this).outerHeight(!0)&&(c=a(this).outerHeight(!0))}),c},l=function(a){for(var b in a){var c=b.split(/(?=[A-Z])/).join("-").toLowerCase();c!==b&&(a[c]=a[b],delete a[b])}return a},m=function(a,b){var c=a;if("string"!=typeof b||a.hasOwnProperty(b))return a[b];var d=b.split(".");for(var e in d)c=c[d[e]];return c},n=function(b,c){this.options=c,this.$el=a(b),this.$el_=this.$el.clone(),this.timeoutId_=0,this.timeoutFooter_=0,this.init()};n.DEFAULTS={classes:"table table-hover",locale:void 0,height:void 0,undefinedText:"-",sortName:void 0,sortOrder:"asc",striped:!1,columns:[[]],data:[],dataField:"rows",method:"get",url:void 0,ajax:void 0,cache:!0,contentType:"application/json",dataType:"json",ajaxOptions:{},queryParams:function(a){return a},queryParamsType:"limit",responseHandler:function(a){return a},pagination:!1,sidePagination:"client",totalRows:0,pageNumber:1,pageSize:10,pageList:[10,25,50,100],paginationHAlign:"right",paginationVAlign:"bottom",paginationDetailHAlign:"left",paginationFirstText:"&laquo;",paginationPreText:"&lsaquo;",paginationNextText:"&rsaquo;",paginationLastText:"&raquo;",search:!1,strictSearch:!1,searchAlign:"right",selectItemName:"btSelectItem",showHeader:!0,showFooter:!1,showColumns:!1,showPaginationSwitch:!1,showRefresh:!1,showToggle:!1,buttonsAlign:"right",smartDisplay:!0,minimumCountColumns:1,idField:void 0,uniqueId:void 0,cardView:!1,detailView:!1,detailFormatter:function(){return""},trimOnSearch:!0,clickToSelect:!1,singleSelect:!1,toolbar:void 0,toolbarAlign:"left",checkboxHeader:!0,sortable:!0,silentSort:!0,maintainSelected:!1,searchTimeOut:500,searchText:"",iconSize:void 0,iconsPrefix:"glyphicon",icons:{paginationSwitchDown:"glyphicon-collapse-down icon-chevron-down",paginationSwitchUp:"glyphicon-collapse-up icon-chevron-up",refresh:"glyphicon-refresh icon-refresh",toggle:"glyphicon-list-alt icon-list-alt",columns:"glyphicon-th icon-th",detailOpen:"glyphicon-plus icon-plus",detailClose:"glyphicon-minus icon-minus"},rowStyle:function(){return{}},rowAttributes:function(){return{}},onAll:function(){return!1},onClickCell:function(){return!1},onDblClickCell:function(){return!1},onClickRow:function(){return!1},onDblClickRow:function(){return!1},onSort:function(){return!1},onCheck:function(){return!1},onUncheck:function(){return!1},onCheckAll:function(){return!1},onUncheckAll:function(){return!1},onCheckSome:function(){return!1},onUncheckSome:function(){return!1},onLoadSuccess:function(){return!1},onLoadError:function(){return!1},onColumnSwitch:function(){return!1},onPageChange:function(){return!1},onSearch:function(){return!1},onToggle:function(){return!1},onPreBody:function(){return!1},onPostBody:function(){return!1},onPostHeader:function(){return!1},onExpandRow:function(){return!1},onCollapseRow:function(){return!1},onRefreshOptions:function(){return!1},onResetView:function(){return!1}},n.LOCALES=[],n.LOCALES["en-US"]=n.LOCALES.en={formatLoadingMessage:function(){return"Loading, please wait..."},formatRecordsPerPage:function(a){return c("%s records per page",a)},formatShowingRows:function(a,b,d){return c("Showing %s to %s of %s rows",a,b,d)},formatSearch:function(){return"Search"},formatNoMatches:function(){return"No matching records found"},formatPaginationSwitch:function(){return"Hide/Show pagination"},formatRefresh:function(){return"Refresh"},formatToggle:function(){return"Toggle"},formatColumns:function(){return"Columns"},formatAllRows:function(){return"All"}},a.extend(n.DEFAULTS,n.LOCALES["en-US"]),n.COLUMN_DEFAULTS={radio:!1,checkbox:!1,checkboxEnabled:!0,field:void 0,title:void 0,titleTooltip:void 0,"class":void 0,align:void 0,halign:void 0,falign:void 0,valign:void 0,width:void 0,sortable:!1,order:"asc",visible:!0,switchable:!0,clickToSelect:!0,formatter:void 0,footerFormatter:void 0,events:void 0,sorter:void 0,sortName:void 0,cellStyle:void 0,searchable:!0,searchFormatter:!0,cardVisible:!0},n.EVENTS={"all.bs.table":"onAll","click-cell.bs.table":"onClickCell","dbl-click-cell.bs.table":"onDblClickCell","click-row.bs.table":"onClickRow","dbl-click-row.bs.table":"onDblClickRow","sort.bs.table":"onSort","check.bs.table":"onCheck","uncheck.bs.table":"onUncheck","check-all.bs.table":"onCheckAll","uncheck-all.bs.table":"onUncheckAll","check-some.bs.table":"onCheckSome","uncheck-some.bs.table":"onUncheckSome","load-success.bs.table":"onLoadSuccess","load-error.bs.table":"onLoadError","column-switch.bs.table":"onColumnSwitch","page-change.bs.table":"onPageChange","search.bs.table":"onSearch","toggle.bs.table":"onToggle","pre-body.bs.table":"onPreBody","post-body.bs.table":"onPostBody","post-header.bs.table":"onPostHeader","expand-row.bs.table":"onExpandRow","collapse-row.bs.table":"onCollapseRow","refresh-options.bs.table":"onRefreshOptions","reset-view.bs.table":"onResetView"},n.prototype.init=function(){this.initLocale(),this.initContainer(),this.initTable(),this.initHeader(),this.initData(),this.initFooter(),this.initToolbar(),this.initPagination(),this.initBody(),this.initSearchText(),this.initServer()},n.prototype.initLocale=function(){if(this.options.locale){var b=this.options.locale.split(/-|_/);b[0].toLowerCase(),b[1]&&b[1].toUpperCase(),a.fn.bootstrapTable.locales[this.options.locale]?a.extend(this.options,a.fn.bootstrapTable.locales[this.options.locale]):a.fn.bootstrapTable.locales[b.join("-")]?a.extend(this.options,a.fn.bootstrapTable.locales[b.join("-")]):a.fn.bootstrapTable.locales[b[0]]&&a.extend(this.options,a.fn.bootstrapTable.locales[b[0]])}},n.prototype.initContainer=function(){this.$container=a(['<div class="bootstrap-table">','<div class="fixed-table-toolbar"></div>',"top"===this.options.paginationVAlign||"both"===this.options.paginationVAlign?'<div class="fixed-table-pagination" style="clear: both;"></div>':"",'<div class="fixed-table-container">','<div class="fixed-table-header"><table></table></div>','<div class="fixed-table-body">','<div class="fixed-table-loading">',this.options.formatLoadingMessage(),"</div>","</div>",'<div class="fixed-table-footer"><table><tr></tr></table></div>',"bottom"===this.options.paginationVAlign||"both"===this.options.paginationVAlign?'<div class="fixed-table-pagination"></div>':"","</div>","</div>"].join("")),this.$container.insertAfter(this.$el),this.$tableContainer=this.$container.find(".fixed-table-container"),this.$tableHeader=this.$container.find(".fixed-table-header"),this.$tableBody=this.$container.find(".fixed-table-body"),this.$tableLoading=this.$container.find(".fixed-table-loading"),this.$tableFooter=this.$container.find(".fixed-table-footer"),this.$toolbar=this.$container.find(".fixed-table-toolbar"),this.$pagination=this.$container.find(".fixed-table-pagination"),this.$tableBody.append(this.$el),this.$container.after('<div class="clearfix"></div>'),this.$el.addClass(this.options.classes),this.options.striped&&this.$el.addClass("table-striped"),-1!==a.inArray("table-no-bordered",this.options.classes.split(" "))&&this.$tableContainer.addClass("table-no-bordered")},n.prototype.initTable=function(){var b=this,c=[],d=[];this.$header=this.$el.find(">thead"),this.$header.length||(this.$header=a("<thead></thead>").appendTo(this.$el)),this.$header.find("tr").each(function(){var b=[];a(this).find("th").each(function(){b.push(a.extend({},{title:a(this).html(),"class":a(this).attr("class"),titleTooltip:a(this).attr("title"),rowspan:a(this).attr("rowspan")?+a(this).attr("rowspan"):void 0,colspan:a(this).attr("colspan")?+a(this).attr("colspan"):void 0},a(this).data()))}),c.push(b)}),a.isArray(this.options.columns[0])||(this.options.columns=[this.options.columns]),this.options.columns=a.extend(!0,[],c,this.options.columns),this.columns=[],f(this.options.columns),a.each(this.options.columns,function(c,d){a.each(d,function(d,e){e=a.extend({},n.COLUMN_DEFAULTS,e),"undefined"!=typeof e.fieldIndex&&(b.columns[e.fieldIndex]=e),b.options.columns[c][d]=e})}),this.options.data.length||(this.$el.find(">tbody>tr").each(function(){var c={};c._id=a(this).attr("id"),c._class=a(this).attr("class"),c._data=l(a(this).data()),a(this).find("td").each(function(d){var e=b.columns[d].field;c[e]=a(this).html(),c["_"+e+"_id"]=a(this).attr("id"),c["_"+e+"_class"]=a(this).attr("class"),c["_"+e+"_rowspan"]=a(this).attr("rowspan"),c["_"+e+"_title"]=a(this).attr("title"),c["_"+e+"_data"]=l(a(this).data())}),d.push(c)}),this.options.data=d)},n.prototype.initHeader=function(){var b=this,d={},e=[];this.header={fields:[],styles:[],classes:[],formatters:[],events:[],sorters:[],sortNames:[],cellStyles:[],searchables:[]},a.each(this.options.columns,function(f,g){e.push("<tr>"),0==f&&!b.options.cardView&&b.options.detailView&&e.push(c('<th class="detail" rowspan="%s"><div class="fht-cell"></div></th>',b.options.columns.length)),a.each(g,function(a,f){var g="",h="",i="",j="",k=c(' class="%s"',f["class"]),l=(b.options.sortOrder||f.order,"px"),m=f.width;if(void 0===f.width||b.options.cardView||"string"==typeof f.width&&-1!==f.width.indexOf("%")&&(l="%"),f.width&&"string"==typeof f.width&&(m=f.width.replace("%","").replace("px","")),h=c("text-align: %s; ",f.halign?f.halign:f.align),i=c("text-align: %s; ",f.align),j=c("vertical-align: %s; ",f.valign),j+=c("width: %s; ",!f.checkbox&&!f.radio||m?m?m+l:void 0:"36px"),"undefined"!=typeof f.fieldIndex){if(b.header.fields[f.fieldIndex]=f.field,b.header.styles[f.fieldIndex]=i+j,b.header.classes[f.fieldIndex]=k,b.header.formatters[f.fieldIndex]=f.formatter,b.header.events[f.fieldIndex]=f.events,b.header.sorters[f.fieldIndex]=f.sorter,b.header.sortNames[f.fieldIndex]=f.sortName,b.header.cellStyles[f.fieldIndex]=f.cellStyle,b.header.searchables[f.fieldIndex]=f.searchable,!f.visible)return;if(b.options.cardView&&!f.cardVisible)return;d[f.field]=f}e.push("<th"+c(' title="%s"',f.titleTooltip),f.checkbox||f.radio?c(' class="bs-checkbox %s"',f["class"]||""):k,c(' style="%s"',h+j),c(' rowspan="%s"',f.rowspan),c(' colspan="%s"',f.colspan),c(' data-field="%s"',f.field),"tabindex='0'",">"),e.push(c('<div class="th-inner %s">',b.options.sortable&&f.sortable?"sortable both":"")),g=f.title,f.checkbox&&(!b.options.singleSelect&&b.options.checkboxHeader&&(g='<input name="btSelectAll" type="checkbox" />'),b.header.stateField=f.field),f.radio&&(g="",b.header.stateField=f.field,b.options.singleSelect=!0),e.push(g),e.push("</div>"),e.push('<div class="fht-cell"></div>'),e.push("</div>"),e.push("</th>")}),e.push("</tr>")}),this.$header.html(e.join("")),this.$header.find("th[data-field]").each(function(){a(this).data(d[a(this).data("field")])}),this.$container.off("click",".th-inner").on("click",".th-inner",function(c){b.options.sortable&&a(this).parent().data().sortable&&b.onSort(c)}),this.$header.children().children().off("keypress").on("keypress",function(c){if(b.options.sortable&&a(this).data().sortable){var d=c.keyCode||c.which;13==d&&b.onSort(c)}}),!this.options.showHeader||this.options.cardView?(this.$header.hide(),this.$tableHeader.hide(),this.$tableLoading.css("top",0)):(this.$header.show(),this.$tableHeader.show(),this.$tableLoading.css("top",this.$header.outerHeight()+1),this.getCaret()),this.$selectAll=this.$header.find('[name="btSelectAll"]'),this.$container.off("click",'[name="btSelectAll"]').on("click",'[name="btSelectAll"]',function(){var c=a(this).prop("checked");b[c?"checkAll":"uncheckAll"]()})},n.prototype.initFooter=function(){!this.options.showFooter||this.options.cardView?this.$tableFooter.hide():this.$tableFooter.show()},n.prototype.initData=function(a,b){this.data="append"===b?this.data.concat(a):"prepend"===b?[].concat(a).concat(this.data):a||this.options.data,this.options.data="append"===b?this.options.data.concat(a):"prepend"===b?[].concat(a).concat(this.options.data):this.data,"server"!==this.options.sidePagination&&this.initSort()},n.prototype.initSort=function(){var b=this,c=this.options.sortName,d="desc"===this.options.sortOrder?-1:1,e=a.inArray(this.options.sortName,this.header.fields);-1!==e&&this.data.sort(function(f,g){b.header.sortNames[e]&&(c=b.header.sortNames[e]);var i=m(f,c),j=m(g,c),k=h(b.header,b.header.sorters[e],[i,j]);return void 0!==k?d*k:((void 0===i||null===i)&&(i=""),(void 0===j||null===j)&&(j=""),a.isNumeric(i)&&a.isNumeric(j)?(i=parseFloat(i),j=parseFloat(j),j>i?-1*d:d):i===j?0:("string"!=typeof i&&(i=i.toString()),-1===i.localeCompare(j)?-1*d:d))})},n.prototype.onSort=function(b){var c="keypress"===b.type?a(b.currentTarget):a(b.currentTarget).parent(),d=this.$header.find("th").eq(c.index());return this.$header.add(this.$header_).find("span.order").remove(),this.options.sortName===c.data("field")?this.options.sortOrder="asc"===this.options.sortOrder?"desc":"asc":(this.options.sortName=c.data("field"),this.options.sortOrder="asc"===c.data("order")?"desc":"asc"),this.trigger("sort",this.options.sortName,this.options.sortOrder),c.add(d).data("order",this.options.sortOrder),this.getCaret(),"server"===this.options.sidePagination?void this.initServer(this.options.silentSort):(this.initSort(),void this.initBody())},n.prototype.initToolbar=function(){var b,d,f=this,g=[],i=0,j=0;this.$toolbar.html(""),("string"==typeof this.options.toolbar||"object"==typeof this.options.toolbar)&&a(c('<div class="bars pull-%s"></div>',this.options.toolbarAlign)).appendTo(this.$toolbar).append(a(this.options.toolbar)),g=[c('<div class="columns columns-%s btn-group pull-%s">',this.options.buttonsAlign,this.options.buttonsAlign)],"string"==typeof this.options.icons&&(this.options.icons=h(null,this.options.icons)),this.options.showPaginationSwitch&&g.push(c('<button class="btn btn-default" type="button" name="paginationSwitch" title="%s">',this.options.formatPaginationSwitch()),c('<i class="%s %s"></i>',this.options.iconsPrefix,this.options.icons.paginationSwitchDown),"</button>"),this.options.showRefresh&&g.push(c('<button class="btn btn-default'+(void 0===this.options.iconSize?"":" btn-"+this.options.iconSize)+'" type="button" name="refresh" title="%s">',this.options.formatRefresh()),c('<i class="%s %s"></i>',this.options.iconsPrefix,this.options.icons.refresh),"</button>"),this.options.showToggle&&g.push(c('<button class="btn btn-default'+(void 0===this.options.iconSize?"":" btn-"+this.options.iconSize)+'" type="button" name="toggle" title="%s">',this.options.formatToggle()),c('<i class="%s %s"></i>',this.options.iconsPrefix,this.options.icons.toggle),"</button>"),this.options.showColumns&&(g.push(c('<div class="keep-open btn-group" title="%s">',this.options.formatColumns()),'<button type="button" class="btn btn-default'+(void 0==this.options.iconSize?"":" btn-"+this.options.iconSize)+' dropdown-toggle" data-toggle="dropdown">',c('<i class="%s %s"></i>',this.options.iconsPrefix,this.options.icons.columns),' <span class="caret"></span>',"</button>",'<ul class="dropdown-menu" role="menu">'),a.each(this.columns,function(a,b){if(!(b.radio||b.checkbox||f.options.cardView&&!b.cardVisible)){var d=b.visible?' checked="checked"':"";b.switchable&&(g.push(c('<li><label><input type="checkbox" data-field="%s" value="%s"%s> %s</label></li>',b.field,a,d,b.title)),j++)}}),g.push("</ul>","</div>")),g.push("</div>"),(this.showToolbar||g.length>2)&&this.$toolbar.append(g.join("")),this.options.showPaginationSwitch&&this.$toolbar.find('button[name="paginationSwitch"]').off("click").on("click",a.proxy(this.togglePagination,this)),this.options.showRefresh&&this.$toolbar.find('button[name="refresh"]').off("click").on("click",a.proxy(this.refresh,this)),this.options.showToggle&&this.$toolbar.find('button[name="toggle"]').off("click").on("click",function(){f.toggleView()}),this.options.showColumns&&(b=this.$toolbar.find(".keep-open"),j<=this.options.minimumCountColumns&&b.find("input").prop("disabled",!0),b.find("li").off("click").on("click",function(a){a.stopImmediatePropagation()}),b.find("input").off("click").on("click",function(){var b=a(this);f.toggleColumn(e(f.columns,a(this).data("field")),b.prop("checked"),!1),f.trigger("column-switch",a(this).data("field"),b.prop("checked"))})),this.options.search&&(g=[],g.push('<div class="pull-'+this.options.searchAlign+' search">',c('<input class="form-control'+(void 0===this.options.iconSize?"":" input-"+this.options.iconSize)+'" type="text" placeholder="%s">',this.options.formatSearch()),"</div>"),this.$toolbar.append(g.join("")),d=this.$toolbar.find(".search input"),d.off("keyup drop").on("keyup drop",function(a){clearTimeout(i),i=setTimeout(function(){f.onSearch(a)},f.options.searchTimeOut)}))},n.prototype.onSearch=function(b){var c=a.trim(a(b.currentTarget).val());this.options.trimOnSearch&&a(b.currentTarget).val()!==c&&a(b.currentTarget).val(c),c!==this.searchText&&(this.searchText=c,this.options.pageNumber=1,this.initSearch(),this.updatePagination(),this.trigger("search",c))},n.prototype.initSearch=function(){var b=this;if("server"!==this.options.sidePagination){var c=this.searchText&&this.searchText.toLowerCase(),d=a.isEmptyObject(this.filterColumns)?null:this.filterColumns;this.data=d?a.grep(this.options.data,function(a){for(var b in d)if(a[b]!==d[b])return!1;return!0}):this.options.data,this.data=c?a.grep(this.data,function(d,f){for(var g in d){g=a.isNumeric(g)?parseInt(g,10):g;var i=d[g],j=b.columns[e(b.columns,g)],k=a.inArray(g,b.header.fields);j&&j.searchFormatter&&(i=h(j,b.header.formatters[k],[i,d,f],i));var l=a.inArray(g,b.header.fields);if(-1!==l&&b.header.searchables[l]&&("string"==typeof i||"number"==typeof i))if(b.options.strictSearch){if((i+"").toLowerCase()===c)return!0}else if(-1!==(i+"").toLowerCase().indexOf(c))return!0}return!1}):this.data}},n.prototype.initPagination=function(){if(!this.options.pagination)return void this.$pagination.hide();this.$pagination.show();var b,d,e,f,g,h,i,j,k,l=this,m=[],n=!1,o=this.getData();if("server"!==this.options.sidePagination&&(this.options.totalRows=o.length),this.totalPages=0,this.options.totalRows){if(this.options.pageSize===this.options.formatAllRows())this.options.pageSize=this.options.totalRows,n=!0;else if(this.options.pageSize===this.options.totalRows){var p="string"==typeof this.options.pageList?this.options.pageList.replace("[","").replace("]","").replace(/ /g,"").toLowerCase().split(","):this.options.pageList;p.indexOf(this.options.formatAllRows().toLowerCase())>-1&&(n=!0)}this.totalPages=~~((this.options.totalRows-1)/this.options.pageSize)+1,this.options.totalPages=this.totalPages}this.totalPages>0&&this.options.pageNumber>this.totalPages&&(this.options.pageNumber=this.totalPages),this.pageFrom=(this.options.pageNumber-1)*this.options.pageSize+1,this.pageTo=this.options.pageNumber*this.options.pageSize,this.pageTo>this.options.totalRows&&(this.pageTo=this.options.totalRows),m.push('<div class="pull-'+this.options.paginationDetailHAlign+' pagination-detail">','<span class="pagination-info">',this.options.formatShowingRows(this.pageFrom,this.pageTo,this.options.totalRows),"</span>"),m.push('<span class="page-list">');var q=[c('<span class="btn-group %s">',"top"===this.options.paginationVAlign||"both"===this.options.paginationVAlign?"dropdown":"dropup"),'<button type="button" class="btn btn-default '+(void 0===this.options.iconSize?"":" btn-"+this.options.iconSize)+' dropdown-toggle" data-toggle="dropdown">','<span class="page-size">',n?this.options.formatAllRows():this.options.pageSize,"</span>",' <span class="caret"></span>',"</button>",'<ul class="dropdown-menu" role="menu">'],r=this.options.pageList;if("string"==typeof this.options.pageList){var s=this.options.pageList.replace("[","").replace("]","").replace(/ /g,"").split(",");r=[],a.each(s,function(a,b){r.push(b.toUpperCase()===l.options.formatAllRows().toUpperCase()?l.options.formatAllRows():+b)})}for(a.each(r,function(a,b){if(!l.options.smartDisplay||0===a||r[a-1]<=l.options.totalRows){var d;d=n?b===l.options.formatAllRows()?' class="active"':"":b===l.options.pageSize?' class="active"':"",q.push(c('<li%s><a href="javascript:void(0)">%s</a></li>',d,b))}}),q.push("</ul></span>"),m.push(this.options.formatRecordsPerPage(q.join(""))),m.push("</span>"),m.push("</div>",'<div class="pull-'+this.options.paginationHAlign+' pagination">','<ul class="pagination'+(void 0===this.options.iconSize?"":" pagination-"+this.options.iconSize)+'">','<li class="page-first"><a href="javascript:void(0)">'+this.options.paginationFirstText+"</a></li>",'<li class="page-pre"><a href="javascript:void(0)">'+this.options.paginationPreText+"</a></li>"),this.totalPages<5?(d=1,e=this.totalPages):(d=this.options.pageNumber-2,e=d+4,1>d&&(d=1,e=5),e>this.totalPages&&(e=this.totalPages,d=e-4)),b=d;e>=b;b++)m.push('<li class="page-number'+(b===this.options.pageNumber?" active":"")+'">','<a href="javascript:void(0)">',b,"</a>","</li>");m.push('<li class="page-next"><a href="javascript:void(0)">'+this.options.paginationNextText+"</a></li>",'<li class="page-last"><a href="javascript:void(0)">'+this.options.paginationLastText+"</a></li>","</ul>","</div>"),this.$pagination.html(m.join("")),f=this.$pagination.find(".page-list a"),g=this.$pagination.find(".page-first"),h=this.$pagination.find(".page-pre"),i=this.$pagination.find(".page-next"),j=this.$pagination.find(".page-last"),k=this.$pagination.find(".page-number"),this.options.pageNumber<=1&&(g.addClass("disabled"),h.addClass("disabled")),this.options.pageNumber>=this.totalPages&&(i.addClass("disabled"),j.addClass("disabled")),this.options.smartDisplay&&(this.totalPages<=1&&this.$pagination.find("div.pagination").hide(),(r.length<2||this.options.totalRows<=r[0])&&this.$pagination.find("span.page-list").hide(),this.$pagination[this.getData().length?"show":"hide"]()),n&&(this.options.pageSize=this.options.formatAllRows()),f.off("click").on("click",a.proxy(this.onPageListChange,this)),g.off("click").on("click",a.proxy(this.onPageFirst,this)),h.off("click").on("click",a.proxy(this.onPagePre,this)),i.off("click").on("click",a.proxy(this.onPageNext,this)),j.off("click").on("click",a.proxy(this.onPageLast,this)),k.off("click").on("click",a.proxy(this.onPageNumber,this))},n.prototype.updatePagination=function(b){b&&a(b.currentTarget).hasClass("disabled")||(this.options.maintainSelected||this.resetRows(),this.initPagination(),"server"===this.options.sidePagination?this.initServer():this.initBody(),this.trigger("page-change",this.options.pageNumber,this.options.pageSize))},n.prototype.onPageListChange=function(b){var c=a(b.currentTarget);c.parent().addClass("active").siblings().removeClass("active"),this.options.pageSize=c.text().toUpperCase()===this.options.formatAllRows().toUpperCase()?this.options.formatAllRows():+c.text(),this.$toolbar.find(".page-size").text(this.options.pageSize),this.updatePagination(b)},n.prototype.onPageFirst=function(a){this.options.pageNumber=1,this.updatePagination(a)},n.prototype.onPagePre=function(a){this.options.pageNumber--,this.updatePagination(a)},n.prototype.onPageNext=function(a){this.options.pageNumber++,this.updatePagination(a)},n.prototype.onPageLast=function(a){this.options.pageNumber=this.totalPages,this.updatePagination(a)},n.prototype.onPageNumber=function(b){this.options.pageNumber!==+a(b.currentTarget).text()&&(this.options.pageNumber=+a(b.currentTarget).text(),this.updatePagination(b))},n.prototype.initBody=function(b){var f=this,g=[],i=this.getData();this.trigger("pre-body",i),this.$body=this.$el.find(">tbody"),this.$body.length||(this.$body=a("<tbody></tbody>").appendTo(this.$el)),this.options.pagination&&"server"!==this.options.sidePagination||(this.pageFrom=1,this.pageTo=i.length);for(var k=this.pageFrom-1;k<this.pageTo;k++){var l,n=i[k],o={},p=[],q="",r={},s=[];if(o=h(this.options,this.options.rowStyle,[n,k],o),o&&o.css)for(l in o.css)p.push(l+": "+o.css[l]);if(r=h(this.options,this.options.rowAttributes,[n,k],r))for(l in r)s.push(c('%s="%s"',l,j(r[l])));n._data&&!a.isEmptyObject(n._data)&&a.each(n._data,function(a,b){"index"!==a&&(q+=c(' data-%s="%s"',a,b))}),g.push("<tr",c(" %s",s.join(" ")),c(' id="%s"',a.isArray(n)?void 0:n._id),c(' class="%s"',o.classes||(a.isArray(n)?void 0:n._class)),c(' data-index="%s"',k),c(' data-uniqueid="%s"',n[this.options.uniqueId]),c("%s",q),">"),this.options.cardView&&g.push(c('<td colspan="%s">',this.header.fields.length)),!this.options.cardView&&this.options.detailView&&g.push("<td>",'<a class="detail-icon" href="javascript:">',c('<i class="%s %s"></i>',this.options.iconsPrefix,this.options.icons.detailOpen),"</a>","</td>"),a.each(this.header.fields,function(b,i){var j="",l=m(n,i),q="",r={},s="",t=f.header.classes[b],u="",v="",w="",x=f.columns[e(f.columns,i)];if(x.visible){if(o=c('style="%s"',p.concat(f.header.styles[b]).join("; ")),l=h(x,f.header.formatters[b],[l,n,k],l),n["_"+i+"_id"]&&(s=c(' id="%s"',n["_"+i+"_id"])),n["_"+i+"_class"]&&(t=c(' class="%s"',n["_"+i+"_class"])),n["_"+i+"_rowspan"]&&(v=c(' rowspan="%s"',n["_"+i+"_rowspan"])),n["_"+i+"_title"]&&(w=c(' title="%s"',n["_"+i+"_title"])),r=h(f.header,f.header.cellStyles[b],[l,n,k],r),r.classes&&(t=c(' class="%s"',r.classes)),r.css){var y=[];for(var z in r.css)y.push(z+": "+r.css[z]);o=c('style="%s"',y.concat(f.header.styles[b]).join("; "))}n["_"+i+"_data"]&&!a.isEmptyObject(n["_"+i+"_data"])&&a.each(n["_"+i+"_data"],function(a,b){"index"!==a&&(u+=c(' data-%s="%s"',a,b))}),x.checkbox||x.radio?(q=x.checkbox?"checkbox":q,q=x.radio?"radio":q,j=[f.options.cardView?'<div class="card-view">':'<td class="bs-checkbox">',"<input"+c(' data-index="%s"',k)+c(' name="%s"',f.options.selectItemName)+c(' type="%s"',q)+c(' value="%s"',n[f.options.idField])+c(' checked="%s"',l===!0||l&&l.checked?"checked":void 0)+c(' disabled="%s"',!x.checkboxEnabled||l&&l.disabled?"disabled":void 0)+" />",f.header.formatters[b]&&"string"==typeof l?l:"",f.options.cardView?"</div>":"</td>"].join(""),n[f.header.stateField]=l===!0||l&&l.checked):(l="undefined"==typeof l||null===l?f.options.undefinedText:l,j=f.options.cardView?['<div class="card-view">',f.options.showHeader?c('<span class="title" %s>%s</span>',o,d(f.columns,"field","title",i)):"",c('<span class="value">%s</span>',l),"</div>"].join(""):[c("<td%s %s %s %s %s %s>",s,t,o,u,v,w),l,"</td>"].join(""),f.options.cardView&&f.options.smartDisplay&&""===l&&(j="")),g.push(j)}}),this.options.cardView&&g.push("</td>"),g.push("</tr>")}g.length||g.push('<tr class="no-records-found">',c('<td colspan="%s">%s</td>',this.$header.find("th").length,this.options.formatNoMatches()),"</tr>"),this.$body.html(g.join("")),b||this.scrollTo(0),this.$body.find("> tr[data-index] > td").off("click dblclick").on("click dblclick",function(b){var d=a(this),g=d.parent(),h=f.data[g.data("index")],i=d[0].cellIndex,j=f.header.fields[f.options.detailView&&!f.options.cardView?i-1:i],k=f.columns[e(f.columns,j)],l=m(h,j);if(!d.find(".detail-icon").length&&(f.trigger("click"===b.type?"click-cell":"dbl-click-cell",j,l,h,d),f.trigger("click"===b.type?"click-row":"dbl-click-row",h,g),"click"===b.type&&f.options.clickToSelect&&k.clickToSelect)){var n=g.find(c('[name="%s"]',f.options.selectItemName));n.length&&n[0].click()}}),this.$body.find("> tr[data-index] > td > .detail-icon").off("click").on("click",function(){var b=a(this),d=b.parent().parent(),e=d.data("index"),g=i[e];d.next().is("tr.detail-view")?(b.find("i").attr("class",c("%s %s",f.options.iconsPrefix,f.options.icons.detailOpen)),d.next().remove(),f.trigger("collapse-row",e,g)):(b.find("i").attr("class",c("%s %s",f.options.iconsPrefix,f.options.icons.detailClose)),d.after(c('<tr class="detail-view"><td colspan="%s">%s</td></tr>',d.find("td").length,h(f.options,f.options.detailFormatter,[e,g],""))),f.trigger("expand-row",e,g,d.next().find("td"))),f.resetView()}),this.$selectItem=this.$body.find(c('[name="%s"]',this.options.selectItemName)),this.$selectItem.off("click").on("click",function(b){b.stopImmediatePropagation();var c=a(this),d=c.prop("checked"),e=f.data[c.data("index")];f.options.maintainSelected&&a(this).is(":radio")&&a.each(f.options.data,function(a,b){b[f.header.stateField]=!1}),e[f.header.stateField]=d,f.options.singleSelect&&(f.$selectItem.not(this).each(function(){f.data[a(this).data("index")][f.header.stateField]=!1}),f.$selectItem.filter(":checked").not(this).prop("checked",!1)),f.updateSelected(),f.trigger(d?"check":"uncheck",e,c)}),a.each(this.header.events,function(b,c){if(c){"string"==typeof c&&(c=h(null,c));var d=f.header.fields[b],e=a.inArray(d,f.getVisibleFields());f.options.detailView&&!f.options.cardView&&(e+=1);for(var g in c)f.$body.find(">tr").each(function(){var b=a(this),h=b.find(f.options.cardView?".card-view":"td").eq(e),i=g.indexOf(" "),j=g.substring(0,i),k=g.substring(i+1),l=c[g];h.find(k).off(j).on(j,function(a){var c=b.data("index"),e=f.data[c],g=e[d];l.apply(this,[a,g,e,c])})})}}),this.updateSelected(),this.resetView(),this.trigger("post-body")},n.prototype.initServer=function(b,c){var d,e=this,f={},g={pageSize:this.options.pageSize===this.options.formatAllRows()?this.options.totalRows:this.options.pageSize,pageNumber:this.options.pageNumber,searchText:this.searchText,sortName:this.options.sortName,sortOrder:this.options.sortOrder};(this.options.url||this.options.ajax)&&("limit"===this.options.queryParamsType&&(g={search:g.searchText,sort:g.sortName,order:g.sortOrder},this.options.pagination&&(g.limit=this.options.pageSize===this.options.formatAllRows()?this.options.totalRows:this.options.pageSize,g.offset=this.options.pageSize===this.options.formatAllRows()?0:this.options.pageSize*(this.options.pageNumber-1))),a.isEmptyObject(this.filterColumnsPartial)||(g.filter=JSON.stringify(this.filterColumnsPartial,null)),f=h(this.options,this.options.queryParams,[g],f),a.extend(f,c||{}),f!==!1&&(b||this.$tableLoading.show(),d=a.extend({},h(null,this.options.ajaxOptions),{type:this.options.method,url:this.options.url,data:"application/json"===this.options.contentType&&"post"===this.options.method?JSON.stringify(f):f,cache:this.options.cache,contentType:this.options.contentType,dataType:this.options.dataType,success:function(a){a=h(e.options,e.options.responseHandler,[a],a),e.load(a),e.trigger("load-success",a)},error:function(a){
e.trigger("load-error",a.status,a)},complete:function(){b||e.$tableLoading.hide()}}),this.options.ajax?h(this,this.options.ajax,[d],null):a.ajax(d)))},n.prototype.initSearchText=function(){if(this.options.search&&""!==this.options.searchText){var a=this.$toolbar.find(".search input");a.val(this.options.searchText),this.onSearch({currentTarget:a})}},n.prototype.getCaret=function(){var b=this;a.each(this.$header.find("th"),function(c,d){a(d).find(".sortable").removeClass("desc asc").addClass(a(d).data("field")===b.options.sortName?b.options.sortOrder:"both")})},n.prototype.updateSelected=function(){var b=this.$selectItem.filter(":enabled").length&&this.$selectItem.filter(":enabled").length===this.$selectItem.filter(":enabled").filter(":checked").length;this.$selectAll.add(this.$selectAll_).prop("checked",b),this.$selectItem.each(function(){a(this).closest("tr")[a(this).prop("checked")?"addClass":"removeClass"]("selected")})},n.prototype.updateRows=function(){var b=this;this.$selectItem.each(function(){b.data[a(this).data("index")][b.header.stateField]=a(this).prop("checked")})},n.prototype.resetRows=function(){var b=this;a.each(this.data,function(a,c){b.$selectAll.prop("checked",!1),b.$selectItem.prop("checked",!1),b.header.stateField&&(c[b.header.stateField]=!1)})},n.prototype.trigger=function(b){var c=Array.prototype.slice.call(arguments,1);b+=".bs.table",this.options[n.EVENTS[b]].apply(this.options,c),this.$el.trigger(a.Event(b),c),this.options.onAll(b,c),this.$el.trigger(a.Event("all.bs.table"),[b,c])},n.prototype.resetHeader=function(){clearTimeout(this.timeoutId_),this.timeoutId_=setTimeout(a.proxy(this.fitHeader,this),this.$el.is(":hidden")?100:0)},n.prototype.fitHeader=function(){var b,d,e,f,h=this;if(h.$el.is(":hidden"))return void(h.timeoutId_=setTimeout(a.proxy(h.fitHeader,h),100));if(b=this.$tableBody.get(0),d=b.scrollWidth>b.clientWidth&&b.scrollHeight>b.clientHeight+this.$header.outerHeight()?g():0,this.$el.css("margin-top",-this.$header.outerHeight()),e=a(":focus"),e.length>0){var i=e.parents("th");if(i.length>0){var j=i.attr("data-field");if(void 0!==j){var k=this.$header.find("[data-field='"+j+"']");k.length>0&&k.find(":input").addClass("focus-temp")}}}this.$header_=this.$header.clone(!0,!0),this.$selectAll_=this.$header_.find('[name="btSelectAll"]'),this.$tableHeader.css({"margin-right":d}).find("table").css("width",this.$el.outerWidth()).html("").attr("class",this.$el.attr("class")).append(this.$header_),f=a(".focus-temp:visible:eq(0)"),f.length>0&&(f.focus(),this.$header.find(".focus-temp").removeClass("focus-temp")),this.$header.find("th[data-field]").each(function(){h.$header_.find(c('th[data-field="%s"]',a(this).data("field"))).data(a(this).data())});var l=this.getVisibleFields();this.$body.find(">tr:first-child:not(.no-records-found) > *").each(function(b){var d=a(this),e=b;h.options.detailView&&!h.options.cardView&&(0===b&&h.$header_.find("th.detail").find(".fht-cell").width(d.innerWidth()),e=b-1),h.$header_.find(c('th[data-field="%s"]',l[e])).find(".fht-cell").width(d.innerWidth())}),this.$tableBody.off("scroll").on("scroll",function(){h.$tableHeader.scrollLeft(a(this).scrollLeft()),h.options.showFooter&&!h.options.cardView&&h.$tableFooter.scrollLeft(a(this).scrollLeft())}),h.trigger("post-header")},n.prototype.resetFooter=function(){var b=this,d=b.getData(),e=[];this.options.showFooter&&!this.options.cardView&&(!this.options.cardView&&this.options.detailView&&e.push('<td><div class="th-inner">&nbsp;</div><div class="fht-cell"></div></td>'),a.each(this.columns,function(a,f){var g="",i="",j=c(' class="%s"',f["class"]);f.visible&&(!b.options.cardView||f.cardVisible)&&(g=c("text-align: %s; ",f.falign?f.falign:f.align),i=c("vertical-align: %s; ",f.valign),e.push("<td",j,c(' style="%s"',g+i),">"),e.push('<div class="th-inner">'),e.push(h(f,f.footerFormatter,[d],"&nbsp;")||"&nbsp;"),e.push("</div>"),e.push('<div class="fht-cell"></div>'),e.push("</div>"),e.push("</td>"))}),this.$tableFooter.find("tr").html(e.join("")),clearTimeout(this.timeoutFooter_),this.timeoutFooter_=setTimeout(a.proxy(this.fitFooter,this),this.$el.is(":hidden")?100:0))},n.prototype.fitFooter=function(){var b,c,d;return clearTimeout(this.timeoutFooter_),this.$el.is(":hidden")?void(this.timeoutFooter_=setTimeout(a.proxy(this.fitFooter,this),100)):(c=this.$el.css("width"),d=c>this.$tableBody.width()?g():0,this.$tableFooter.css({"margin-right":d}).find("table").css("width",c).attr("class",this.$el.attr("class")),b=this.$tableFooter.find("td"),void this.$body.find(">tr:first-child:not(.no-records-found) > *").each(function(c){var d=a(this);b.eq(c).find(".fht-cell").width(d.innerWidth())}))},n.prototype.toggleColumn=function(a,b,d){if(-1!==a&&(this.columns[a].visible=b,this.initHeader(),this.initSearch(),this.initPagination(),this.initBody(),this.options.showColumns)){var e=this.$toolbar.find(".keep-open input").prop("disabled",!1);d&&e.filter(c('[value="%s"]',a)).prop("checked",b),e.filter(":checked").length<=this.options.minimumCountColumns&&e.filter(":checked").prop("disabled",!0)}},n.prototype.toggleRow=function(a,b,d){-1!==a&&this.$body.find("undefined"!=typeof a?c('tr[data-index="%s"]',a):c('tr[data-uniqueid="%s"]',b))[d?"show":"hide"]()},n.prototype.getVisibleFields=function(){var b=this,c=[];return a.each(this.header.fields,function(a,d){var f=b.columns[e(b.columns,d)];f.visible&&c.push(d)}),c},n.prototype.resetView=function(a){var b=0;if(a&&a.height&&(this.options.height=a.height),this.$selectAll.prop("checked",this.$selectItem.length>0&&this.$selectItem.length===this.$selectItem.filter(":checked").length),this.options.height){var c=k(this.$toolbar),d=k(this.$pagination),e=this.options.height-c-d;this.$tableContainer.css("height",e+"px")}return this.options.cardView?(this.$el.css("margin-top","0"),void this.$tableContainer.css("padding-bottom","0")):(this.options.showHeader&&this.options.height?(this.$tableHeader.show(),this.resetHeader(),b+=this.$header.outerHeight()):(this.$tableHeader.hide(),this.trigger("post-header")),this.options.showFooter&&(this.resetFooter(),this.options.height&&(b+=this.$tableFooter.outerHeight()+1)),this.getCaret(),this.$tableContainer.css("padding-bottom",b+"px"),void this.trigger("reset-view"))},n.prototype.getData=function(b){return!this.searchText&&a.isEmptyObject(this.filterColumns)&&a.isEmptyObject(this.filterColumnsPartial)?b?this.options.data.slice(this.pageFrom-1,this.pageTo):this.options.data:b?this.data.slice(this.pageFrom-1,this.pageTo):this.data},n.prototype.load=function(b){var c=!1;"server"===this.options.sidePagination?(this.options.totalRows=b.total,c=b.fixedScroll,b=b[this.options.dataField]):a.isArray(b)||(c=b.fixedScroll,b=b.data),this.initData(b),this.initSearch(),this.initPagination(),this.initBody(c)},n.prototype.append=function(a){this.initData(a,"append"),this.initSearch(),this.initPagination(),this.initBody(!0)},n.prototype.prepend=function(a){this.initData(a,"prepend"),this.initSearch(),this.initPagination(),this.initBody(!0)},n.prototype.remove=function(b){var c,d,e=this.options.data.length;if(b.hasOwnProperty("field")&&b.hasOwnProperty("values")){for(c=e-1;c>=0;c--)d=this.options.data[c],d.hasOwnProperty(b.field)&&-1!==a.inArray(d[b.field],b.values)&&this.options.data.splice(c,1);e!==this.options.data.length&&(this.initSearch(),this.initPagination(),this.initBody(!0))}},n.prototype.removeAll=function(){this.options.data.length>0&&(this.options.data.splice(0,this.options.data.length),this.initSearch(),this.initPagination(),this.initBody(!0))},n.prototype.getRowByUniqueId=function(a){var b,c,d=this.options.uniqueId,e=this.options.data.length,f=null;for(b=e-1;b>=0;b--)if(c=this.options.data[b],c.hasOwnProperty(d)&&("string"==typeof c[d]?a=a.toString():"number"==typeof c[d]&&(Number(c[d])===c[d]&&c[d]%1===0?a=parseInt(a):c[d]===Number(c[d])&&0!==c[d]&&(a=parseFloat(a))),c[d]===a)){f=c;break}return f},n.prototype.removeByUniqueId=function(a){var b=this.options.data.length,c=this.getRowByUniqueId(a);c&&this.options.data.splice(this.options.data.indexOf(c),1),b!==this.options.data.length&&(this.initSearch(),this.initPagination(),this.initBody(!0))},n.prototype.insertRow=function(a){a.hasOwnProperty("index")&&a.hasOwnProperty("row")&&(this.data.splice(a.index,0,a.row),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0))},n.prototype.updateRow=function(b){b.hasOwnProperty("index")&&b.hasOwnProperty("row")&&(a.extend(this.data[b.index],b.row),this.initSort(),this.initBody(!0))},n.prototype.showRow=function(a){a.hasOwnProperty("index")&&a.hasOwnProperty("uniqueId")&&this.toggleRow(a.index,a.uniqueId,!0)},n.prototype.hideRow=function(a){a.hasOwnProperty("index")&&a.hasOwnProperty("uniqueId")&&this.toggleRow(a.index,a.uniqueId,!1)},n.prototype.getRowsHidden=function(b){var c=a(this.$body[0]).children().filter(":hidden"),d=0;if(b)for(;d<c.length;d++)a(c[d]).show();return c},n.prototype.mergeCells=function(b){var c,d,e,f=b.index,g=a.inArray(b.field,this.getVisibleFields()),h=b.rowspan||1,i=b.colspan||1,j=this.$body.find(">tr");if(this.options.detailView&&!this.options.cardView&&(g+=1),e=j.eq(f).find(">td").eq(g),!(0>f||0>g||f>=this.data.length)){for(c=f;f+h>c;c++)for(d=g;g+i>d;d++)j.eq(c).find(">td").eq(d).hide();e.attr("rowspan",h).attr("colspan",i).show()}},n.prototype.updateCell=function(a){a.hasOwnProperty("index")&&a.hasOwnProperty("field")&&a.hasOwnProperty("value")&&(this.data[a.index][a.field]=a.value,this.initSort(),this.initBody(!0))},n.prototype.getOptions=function(){return this.options},n.prototype.getSelections=function(){var b=this;return a.grep(this.data,function(a){return a[b.header.stateField]})},n.prototype.getAllSelections=function(){var b=this;return a.grep(this.options.data,function(a){return a[b.header.stateField]})},n.prototype.checkAll=function(){this.checkAll_(!0)},n.prototype.uncheckAll=function(){this.checkAll_(!1)},n.prototype.checkAll_=function(a){var b;a||(b=this.getSelections()),this.$selectAll.add(this.$selectAll_).prop("checked",a),this.$selectItem.filter(":enabled").prop("checked",a),this.updateRows(),a&&(b=this.getSelections()),this.trigger(a?"check-all":"uncheck-all",b)},n.prototype.check=function(a){this.check_(!0,a)},n.prototype.uncheck=function(a){this.check_(!1,a)},n.prototype.check_=function(a,b){this.$selectItem.filter(c('[data-index="%s"]',b)).prop("checked",a),this.data[b][this.header.stateField]=a,this.updateSelected(),this.trigger(a?"check":"uncheck",this.data[b])},n.prototype.checkBy=function(a){this.checkBy_(!0,a)},n.prototype.uncheckBy=function(a){this.checkBy_(!1,a)},n.prototype.checkBy_=function(b,d){if(d.hasOwnProperty("field")&&d.hasOwnProperty("values")){var e=this,f=[];a.each(this.options.data,function(g,h){return h.hasOwnProperty(d.field)?void(-1!==a.inArray(h[d.field],d.values)&&(e.$selectItem.filter(":enabled").filter(c('[data-index="%s"]',g)).prop("checked",b),h[e.header.stateField]=b,f.push(h),e.trigger(b?"check":"uncheck",h))):!1}),this.updateSelected(),this.trigger(b?"check-some":"uncheck-some",f)}},n.prototype.destroy=function(){this.$el.insertBefore(this.$container),a(this.options.toolbar).insertBefore(this.$el),this.$container.next().remove(),this.$container.remove(),this.$el.html(this.$el_.html()).css("margin-top","0").attr("class",this.$el_.attr("class")||"")},n.prototype.showLoading=function(){this.$tableLoading.show()},n.prototype.hideLoading=function(){this.$tableLoading.hide()},n.prototype.togglePagination=function(){this.options.pagination=!this.options.pagination;var a=this.$toolbar.find('button[name="paginationSwitch"] i');this.options.pagination?a.attr("class",this.options.iconsPrefix+" "+this.options.icons.paginationSwitchDown):a.attr("class",this.options.iconsPrefix+" "+this.options.icons.paginationSwitchUp),this.updatePagination()},n.prototype.refresh=function(a){a&&a.url&&(this.options.url=a.url,this.options.pageNumber=1),this.initServer(a&&a.silent,a&&a.query)},n.prototype.resetWidth=function(){this.options.showHeader&&this.options.height&&this.fitHeader(),this.options.showFooter&&this.fitFooter()},n.prototype.showColumn=function(a){this.toggleColumn(e(this.columns,a),!0,!0)},n.prototype.hideColumn=function(a){this.toggleColumn(e(this.columns,a),!1,!0)},n.prototype.getHiddenColumns=function(){return a.grep(this.columns,function(a){return!a.visible})},n.prototype.filterBy=function(b){this.filterColumns=a.isEmptyObject(b)?{}:b,this.options.pageNumber=1,this.initSearch(),this.updatePagination()},n.prototype.scrollTo=function(a){return"string"==typeof a&&(a="bottom"===a?this.$tableBody[0].scrollHeight:0),"number"==typeof a&&this.$tableBody.scrollTop(a),"undefined"==typeof a?this.$tableBody.scrollTop():void 0},n.prototype.getScrollPosition=function(){return this.scrollTo()},n.prototype.selectPage=function(a){a>0&&a<=this.options.totalPages&&(this.options.pageNumber=a,this.updatePagination())},n.prototype.prevPage=function(){this.options.pageNumber>1&&(this.options.pageNumber--,this.updatePagination())},n.prototype.nextPage=function(){this.options.pageNumber<this.options.totalPages&&(this.options.pageNumber++,this.updatePagination())},n.prototype.toggleView=function(){this.options.cardView=!this.options.cardView,this.initHeader(),this.initBody(),this.trigger("toggle",this.options.cardView)},n.prototype.refreshOptions=function(b){i(this.options,b,!1)||(this.options=a.extend(this.options,b),this.trigger("refresh-options",this.options),this.destroy(),this.init())},n.prototype.resetSearch=function(a){var b=this.$toolbar.find(".search input");b.val(a||""),this.onSearch({currentTarget:b})},n.prototype.expandRow_=function(a,b){var d=this.$body.find(c('> tr[data-index="%s"]',b));d.next().is("tr.detail-view")===(a?!1:!0)&&d.find("> td > .detail-icon").click()},n.prototype.expandRow=function(a){this.expandRow_(!0,a)},n.prototype.collapseRow=function(a){this.expandRow_(!1,a)},n.prototype.expandAllRows=function(b){if(b){var d=this.$body.find(c('> tr[data-index="%s"]',0)),e=this,f=null,g=!1,h=-1;if(d.next().is("tr.detail-view")?d.next().next().is("tr.detail-view")||(d.next().find(".detail-icon").click(),g=!0):(d.find("> td > .detail-icon").click(),g=!0),g)try{h=setInterval(function(){f=e.$body.find("tr.detail-view").last().find(".detail-icon"),f.length>0?f.click():clearInterval(h)},1)}catch(i){clearInterval(h)}}else for(var j=this.$body.children(),k=0;k<j.length;k++)this.expandRow_(!0,a(j[k]).data("index"))},n.prototype.collapseAllRows=function(b){if(b)this.expandRow_(!1,0);else for(var c=this.$body.children(),d=0;d<c.length;d++)this.expandRow_(!1,a(c[d]).data("index"))};var o=["getOptions","getSelections","getAllSelections","getData","load","append","prepend","remove","removeAll","insertRow","updateRow","updateCell","removeByUniqueId","getRowByUniqueId","showRow","hideRow","getRowsHidden","mergeCells","checkAll","uncheckAll","check","uncheck","checkBy","uncheckBy","refresh","resetView","resetWidth","destroy","showLoading","hideLoading","showColumn","hideColumn","getHiddenColumns","filterBy","scrollTo","getScrollPosition","selectPage","prevPage","nextPage","togglePagination","toggleView","refreshOptions","resetSearch","expandRow","collapseRow","expandAllRows","collapseAllRows"];a.fn.bootstrapTable=function(b){var c,d=Array.prototype.slice.call(arguments,1);return this.each(function(){var e=a(this),f=e.data("bootstrap.table"),g=a.extend({},n.DEFAULTS,e.data(),"object"==typeof b&&b);if("string"==typeof b){if(a.inArray(b,o)<0)throw new Error("Unknown method: "+b);if(!f)return;c=f[b].apply(f,d),"destroy"===b&&e.removeData("bootstrap.table")}f||e.data("bootstrap.table",f=new n(this,g))}),"undefined"==typeof c?this:c},a.fn.bootstrapTable.Constructor=n,a.fn.bootstrapTable.defaults=n.DEFAULTS,a.fn.bootstrapTable.columnDefaults=n.COLUMN_DEFAULTS,a.fn.bootstrapTable.locales=n.LOCALES,a.fn.bootstrapTable.methods=o,a.fn.bootstrapTable.utils={sprintf:c,getFieldIndex:e,compareObjects:i,calculateObjectValue:h},a(function(){a('[data-toggle="table"]').bootstrapTable()})}(jQuery),!function(a){"use strict";var b={},c=[{base:"A",letters:"AⒶＡÀÁÂẦẤẪẨÃĀĂẰẮẴẲȦǠÄǞẢÅǺǍȀȂẠẬẶḀĄȺⱯ"},{base:"AA",letters:"Ꜳ"},{base:"AE",letters:"ÆǼǢ"},{base:"AO",letters:"Ꜵ"},{base:"AU",letters:"Ꜷ"},{base:"AV",letters:"ꜸꜺ"},{base:"AY",letters:"Ꜽ"},{base:"B",letters:"BⒷＢḂḄḆɃƂƁ"},{base:"C",letters:"CⒸＣĆĈĊČÇḈƇȻꜾ"},{base:"D",letters:"DⒹＤḊĎḌḐḒḎĐƋƊƉꝹ"},{base:"DZ",letters:"ǱǄ"},{base:"Dz",letters:"ǲǅ"},{base:"E",letters:"EⒺＥÈÉÊỀẾỄỂẼĒḔḖĔĖËẺĚȄȆẸỆȨḜĘḘḚƐƎ"},{base:"F",letters:"FⒻＦḞƑꝻ"},{base:"G",letters:"GⒼＧǴĜḠĞĠǦĢǤƓꞠꝽꝾ"},{base:"H",letters:"HⒽＨĤḢḦȞḤḨḪĦⱧⱵꞍ"},{base:"I",letters:"IⒾＩÌÍÎĨĪĬİÏḮỈǏȈȊỊĮḬƗ"},{base:"J",letters:"JⒿＪĴɈ"},{base:"K",letters:"KⓀＫḰǨḲĶḴƘⱩꝀꝂꝄꞢ"},{base:"L",letters:"LⓁＬĿĹĽḶḸĻḼḺŁȽⱢⱠꝈꝆꞀ"},{base:"LJ",letters:"Ǉ"},{base:"Lj",letters:"ǈ"},{base:"M",letters:"MⓂＭḾṀṂⱮƜ"},{base:"N",letters:"NⓃＮǸŃÑṄŇṆŅṊṈȠƝꞐꞤ"},{base:"NJ",letters:"Ǌ"},{base:"Nj",letters:"ǋ"},{base:"O",letters:"OⓄＯÒÓÔỒỐỖỔÕṌȬṎŌṐṒŎȮȰÖȪỎŐǑȌȎƠỜỚỠỞỢỌỘǪǬØǾƆƟꝊꝌ"},{base:"OI",letters:"Ƣ"},{base:"OO",letters:"Ꝏ"},{base:"OU",letters:"Ȣ"},{base:"OE",letters:"Œ"},{base:"oe",letters:"œ"},{base:"P",letters:"PⓅＰṔṖƤⱣꝐꝒꝔ"},{base:"Q",letters:"QⓆＱꝖꝘɊ"},{base:"R",letters:"RⓇＲŔṘŘȐȒṚṜŖṞɌⱤꝚꞦꞂ"},{base:"S",letters:"SⓈＳẞŚṤŜṠŠṦṢṨȘŞⱾꞨꞄ"},{base:"T",letters:"TⓉＴṪŤṬȚŢṰṮŦƬƮȾꞆ"},{base:"TZ",letters:"Ꜩ"},{base:"U",letters:"UⓊＵÙÚÛŨṸŪṺŬÜǛǗǕǙỦŮŰǓȔȖƯỪỨỮỬỰỤṲŲṶṴɄ"},{base:"V",letters:"VⓋＶṼṾƲꝞɅ"},{base:"VY",letters:"Ꝡ"},{base:"W",letters:"WⓌＷẀẂŴẆẄẈⱲ"},{base:"X",letters:"XⓍＸẊẌ"},{base:"Y",letters:"YⓎＹỲÝŶỸȲẎŸỶỴƳɎỾ"},{base:"Z",letters:"ZⓏＺŹẐŻŽẒẔƵȤⱿⱫꝢ"},{base:"a",letters:"aⓐａẚàáâầấẫẩãāăằắẵẳȧǡäǟảåǻǎȁȃạậặḁąⱥɐ"},{base:"aa",letters:"ꜳ"},{base:"ae",letters:"æǽǣ"},{base:"ao",letters:"ꜵ"},{base:"au",letters:"ꜷ"},{base:"av",letters:"ꜹꜻ"},{base:"ay",letters:"ꜽ"},{base:"b",letters:"bⓑｂḃḅḇƀƃɓ"},{base:"c",letters:"cⓒｃćĉċčçḉƈȼꜿↄ"},{base:"d",letters:"dⓓｄḋďḍḑḓḏđƌɖɗꝺ"},{base:"dz",letters:"ǳǆ"},{base:"e",letters:"eⓔｅèéêềếễểẽēḕḗĕėëẻěȅȇẹệȩḝęḙḛɇɛǝ"},{base:"f",letters:"fⓕｆḟƒꝼ"},{base:"g",letters:"gⓖｇǵĝḡğġǧģǥɠꞡᵹꝿ"},{base:"h",letters:"hⓗｈĥḣḧȟḥḩḫẖħⱨⱶɥ"},{base:"hv",letters:"ƕ"},{base:"i",letters:"iⓘｉìíîĩīĭïḯỉǐȉȋịįḭɨı"},{base:"j",letters:"jⓙｊĵǰɉ"},{base:"k",letters:"kⓚｋḱǩḳķḵƙⱪꝁꝃꝅꞣ"},{base:"l",letters:"lⓛｌŀĺľḷḹļḽḻſłƚɫⱡꝉꞁꝇ"},{base:"lj",letters:"ǉ"},{base:"m",letters:"mⓜｍḿṁṃɱɯ"},{base:"n",letters:"nⓝｎǹńñṅňṇņṋṉƞɲŉꞑꞥ"},{base:"nj",letters:"ǌ"},{base:"o",letters:"oⓞｏòóôồốỗổõṍȭṏōṑṓŏȯȱöȫỏőǒȍȏơờớỡởợọộǫǭøǿɔꝋꝍɵ"},{base:"oi",letters:"ƣ"},{base:"ou",letters:"ȣ"},{base:"oo",letters:"ꝏ"},{base:"p",letters:"pⓟｐṕṗƥᵽꝑꝓꝕ"},{base:"q",letters:"qⓠｑɋꝗꝙ"},{base:"r",letters:"rⓡｒŕṙřȑȓṛṝŗṟɍɽꝛꞧꞃ"},{base:"s",letters:"sⓢｓßśṥŝṡšṧṣṩșşȿꞩꞅẛ"},{base:"t",letters:"tⓣｔṫẗťṭțţṱṯŧƭʈⱦꞇ"},{base:"tz",letters:"ꜩ"},{base:"u",letters:"uⓤｕùúûũṹūṻŭüǜǘǖǚủůűǔȕȗưừứữửựụṳųṷṵʉ"},{base:"v",letters:"vⓥｖṽṿʋꝟʌ"},{base:"vy",letters:"ꝡ"},{base:"w",letters:"wⓦｗẁẃŵẇẅẘẉⱳ"},{base:"x",letters:"xⓧｘẋẍ"},{base:"y",letters:"yⓨｙỳýŷỹȳẏÿỷẙỵƴɏỿ"},{base:"z",letters:"zⓩｚźẑżžẓẕƶȥɀⱬꝣ"}],d=function(){for(var a=0;a<c.length;a++)for(var d=c[a].letters,e=0;e<d.length;e++)b[d[e]]=c[a].base},e=function(a){return a.replace(/[^\u0000-\u007E]/g,function(a){return b[a]||a})};a.extend(a.fn.bootstrapTable.defaults,{searchAccentNeutralise:!1});{var f=a.fn.bootstrapTable.Constructor,g=f.prototype.init;f.prototype.initSearch}f.prototype.init=function(){this.options.searchAccentNeutralise&&d(),g.apply(this,Array.prototype.slice.apply(arguments))},f.prototype.initSearch=function(){var b=this;if("server"!==this.options.sidePagination){var c=this.searchText&&this.searchText.toLowerCase(),d=a.isEmptyObject(this.filterColumns)?null:this.filterColumns;this.data=d?a.grep(this.options.data,function(a){for(var b in d)if(a[b]!==d[b])return!1;return!0}):this.options.data,this.data=c?a.grep(this.data,function(d,f){for(var g in d){g=a.isNumeric(g)?parseInt(g,10):g;var h=d[g],i=b.columns[a.fn.bootstrapTable.utils.getFieldIndex(b.columns,g)],j=a.inArray(g,b.header.fields);i&&i.searchFormatter&&(h=a.fn.bootstrapTable.utils.calculateObjectValue(i,b.header.formatters[j],[h,d,f],h));var k=a.inArray(g,b.header.fields);if(-1!==k&&b.header.searchables[k]&&("string"==typeof h||"number"==typeof h))if(b.options.searchAccentNeutralise&&(h=e(h),c=e(c)),b.options.strictSearch){if((h+"").toLowerCase()===c)return!0}else if(-1!==(h+"").toLowerCase().indexOf(c))return!0}return!1}):this.data}}}(jQuery),function(){"undefined"!=typeof angular&&angular.module("bsTable",[]).directive("bsTableControl",function(){function a(a){var b;return $.each(f,function(d,e){return e.$el.closest(c).has(a).length?(b=e,!0):void 0}),b}function b(){var a=this,b=a.$s.bsTableControl.state;a.$s.$applyAsync(function(){b.scroll=a.$el.bootstrapTable("getScrollPosition")})}var c=".bootstrap-table",d=".fixed-table-body",e=".search input",f={};return $(window).resize(function(){$.each(f,function(a,b){b.$el.bootstrapTable("resetView")})}),$(document).on("post-header.bs.table",c+" table",function(e){var f=a(e.target);f&&f.$el.closest(c).find(d).on("scroll",b.bind(f))}).on("sort.bs.table",c+" table",function(b,c,d){var e=a(b.target);if(e){var f=e.$s.bsTableControl.state;e.$s.$applyAsync(function(){f.sortName=c,f.sortOrder=d})}}).on("page-change.bs.table",c+" table",function(b,c,d){var e=a(b.target);if(e){var f=e.$s.bsTableControl.state;e.$s.$applyAsync(function(){f.pageNumber=c,f.pageSize=d})}}).on("search.bs.table",c+" table",function(b,c){var d=a(b.target);if(d){var e=d.$s.bsTableControl.state;d.$s.$applyAsync(function(){e.searchText=c})}}).on("focus blur",c+" "+e,function(b){var c=a(b.target);if(c){var d=c.$s.bsTableControl.state;c.$s.$applyAsync(function(){d.searchHasFocus=$(b.target).is(":focus")})}}),{restrict:"EA",scope:{bsTableControl:"="},link:function(a,b){f[a.$id]={$s:a,$el:b};a.instantiated=!1,a.$watch("bsTableControl.options",function(d){d||(d=a.bsTableControl.options={});var f=a.bsTableControl.state||{};a.instantiated&&b.bootstrapTable("destroy"),b.bootstrapTable(angular.extend(angular.copy(d),f)),a.instantiated=!0,"scroll"in f&&b.bootstrapTable("scrollTo",f.scroll),"searchHasFocus"in f&&b.closest(c).find(e).focus()},!0),a.$watch("bsTableControl.state",function(c){c||(c=a.bsTableControl.state={}),b.trigger("directive-updated.bs.table",[c])},!0),a.$on("$destroy",function(){delete f[a.$id]})}}})}(),function(a){"use strict";var b={sortOrder:"bs.table.sortOrder",sortName:"bs.table.sortName",pageNumber:"bs.table.pageNumber",pageList:"bs.table.pageList",columns:"bs.table.columns",searchText:"bs.table.searchText",filterControl:"bs.table.filterControl"},c=function(a){var b=a.$header;return a.options.height&&(b=a.$tableHeader),b},d=function(a){var b="select, input";return a.options.height&&(b="table select, table input"),b},e=function(){return!!navigator.cookieEnabled},f=function(a,b){for(var c=-1,d=0;d<b.length;d++)if(a.toLowerCase()===b[d].toLowerCase()){c=d;break}return c},g=function(a,b,c){return a.options.cookie&&e()&&""!==a.options.cookieIdTable&&-1!==f(b,a.options.cookiesEnabled)?(b=a.options.cookieIdTable+"."+b,!b||/^(?:expires|max\-age|path|domain|secure)$/i.test(b)?!1:(document.cookie=encodeURIComponent(b)+"="+encodeURIComponent(c)+k(a.options.cookieExpire)+(a.options.cookieDomain?"; domain="+a.options.cookieDomain:"")+(a.options.cookiePath?"; path="+a.options.cookiePath:"")+(a.cookieSecure?"; secure":""),!0)):void 0},h=function(a,b,c){return c?-1===f(c,a.options.cookiesEnabled)?null:(c=b+"."+c,decodeURIComponent(document.cookie.replace(new RegExp("(?:(?:^|.*;)\\s*"+encodeURIComponent(c).replace(/[\-\.\+\*]/g,"\\$&")+"\\s*\\=\\s*([^;]*).*$)|^.*$"),"$1"))||null):null},i=function(a){return a?new RegExp("(?:^|;\\s*)"+encodeURIComponent(a).replace(/[\-\.\+\*]/g,"\\$&")+"\\s*\\=").test(document.cookie):!1},j=function(a,b,c,d){return b=a+"."+b,i(b)?(document.cookie=encodeURIComponent(b)+"=; expires=Thu, 01 Jan 1970 00:00:00 GMT"+(d?"; domain="+d:"")+(c?"; path="+c:""),!0):!1},k=function(a){var b=a.replace(/[0-9]*/,"");switch(a=a.replace(/[A-Za-z]/,""),b.toLowerCase()){case"s":a=+a;break;case"mi":a=60*a;break;case"h":a=60*a*60;break;case"d":a=24*a*60*60;break;case"m":a=30*a*24*60*60;break;case"y":a=365*a*30*24*60*60;break;default:a=void 0}return void 0===a?"":"; max-age="+a};a.extend(a.fn.bootstrapTable.defaults,{cookie:!1,cookieExpire:"2h",cookiePath:null,cookieDomain:null,cookieSecure:null,cookieIdTable:"",cookiesEnabled:["bs.table.sortOrder","bs.table.sortName","bs.table.pageNumber","bs.table.pageList","bs.table.columns","bs.table.searchText","bs.table.filterControl"],filterControls:[],filterControlValuesLoaded:!1}),a.fn.bootstrapTable.methods.push("deleteCookie");var l=a.fn.bootstrapTable.Constructor,m=l.prototype.init,n=l.prototype.initTable,o=l.prototype.onSort,p=l.prototype.onPageNumber,q=l.prototype.onPageListChange,r=l.prototype.onPageFirst,s=l.prototype.onPagePre,t=l.prototype.onPageNext,u=l.prototype.onPageLast,v=l.prototype.toggleColumn,w=l.prototype.selectPage,x=l.prototype.onSearch;l.prototype.init=function(){if(this.options.filterControls=[],this.options.filterControlValuesLoaded=!1,this.options.cookiesEnabled="string"==typeof this.options.cookiesEnabled?this.options.cookiesEnabled.replace("[","").replace("]","").replace(/ /g,"").toLowerCase().split(","):this.options.cookiesEnabled,this.options.filterControl){var e=this;this.$el.on("column-search.bs.table",function(a,c,d){for(var f=!0,h=0;h<e.options.filterControls.length;h++)if(e.options.filterControls[h].field===c){e.options.filterControls[h].text=d,f=!1;break}f&&e.options.filterControls.push({field:c,text:d}),g(e,b.filterControl,JSON.stringify(e.options.filterControls))}).on("post-body.bs.table",function(){setTimeout(function(){if(!e.options.filterControlValuesLoaded){e.options.filterControlValuesLoaded=!0;var f=JSON.parse(h(e,e.options.cookieIdTable,b.filterControl));if(f){var g=null,i=[],j=c(e),k=d(e);j.find(k).each(function(){g=a(this).parent().parent().parent().data("field"),i=a.grep(f,function(a){return a.field===g}),i.length>0&&(a(this).val(i[0].text),e.onColumnSearch({currentTarget:a(this)}))})}}},250)})}m.apply(this,Array.prototype.slice.apply(arguments))},l.prototype.initTable=function(){n.apply(this,Array.prototype.slice.apply(arguments)),this.initCookie()},l.prototype.initCookie=function(){if(this.options.cookie){if(""===this.options.cookieIdTable||""===this.options.cookieExpire||!e())throw new Error("Configuration error. Please review the cookieIdTable, cookieExpire properties, if those properties are ok, then this browser does not support the cookies");var c=h(this,this.options.cookieIdTable,b.sortOrder),d=h(this,this.options.cookieIdTable,b.sortName),f=h(this,this.options.cookieIdTable,b.pageNumber),g=h(this,this.options.cookieIdTable,b.pageList),i=JSON.parse(h(this,this.options.cookieIdTable,b.columns)),j=h(this,this.options.cookieIdTable,b.searchText);this.options.sortOrder=c?c:"asc",this.options.sortName=d?d:void 0,this.options.pageNumber=f?+f:this.options.pageNumber,this.options.pageSize=g?g===this.options.formatAllRows()?g:+g:this.options.pageSize,this.options.searchText=j?j:"",i&&a.each(this.columns,function(b,c){c.visible=-1!==a.inArray(c.field,i)})}},l.prototype.onSort=function(){o.apply(this,Array.prototype.slice.apply(arguments)),g(this,b.sortOrder,this.options.sortOrder),g(this,b.sortName,this.options.sortName)},l.prototype.onPageNumber=function(){p.apply(this,Array.prototype.slice.apply(arguments)),g(this,b.pageNumber,this.options.pageNumber)},l.prototype.onPageListChange=function(){q.apply(this,Array.prototype.slice.apply(arguments)),g(this,b.pageList,this.options.pageSize)},l.prototype.onPageFirst=function(){r.apply(this,Array.prototype.slice.apply(arguments)),g(this,b.pageNumber,this.options.pageNumber)},l.prototype.onPagePre=function(){s.apply(this,Array.prototype.slice.apply(arguments)),g(this,b.pageNumber,this.options.pageNumber)},l.prototype.onPageNext=function(){t.apply(this,Array.prototype.slice.apply(arguments)),g(this,b.pageNumber,this.options.pageNumber)},l.prototype.onPageLast=function(){u.apply(this,Array.prototype.slice.apply(arguments)),g(this,b.pageNumber,this.options.pageNumber)},l.prototype.toggleColumn=function(){v.apply(this,Array.prototype.slice.apply(arguments));var c=[];a.each(this.columns,function(a,b){b.visible&&c.push(b.field)}),g(this,b.columns,JSON.stringify(c))},l.prototype.selectPage=function(a){w.apply(this,Array.prototype.slice.apply(arguments)),g(this,idsStateSaveList.pageNumber,a)},l.prototype.onSearch=function(){x.apply(this,Array.prototype.slice.apply(arguments)),g(this,b.searchText,this.searchText)},l.prototype.deleteCookie=function(a){""!==a&&e()&&j(this.options.cookieIdTable,b[a],this.options.cookiePath,this.options.cookieDomain)}}(jQuery),!function(a){"use strict";a.extend(a.fn.bootstrapTable.defaults,{editable:!0,onEditableInit:function(){return!1},onEditableSave:function(){return!1},onEditableShown:function(){return!1},onEditableHidden:function(){return!1}}),a.extend(a.fn.bootstrapTable.Constructor.EVENTS,{"editable-init.bs.table":"onEditableInit","editable-save.bs.table":"onEditableSave","editable-shown.bs.table":"onEditableShown","editable-hidden.bs.table":"onEditableHidden"});var b=a.fn.bootstrapTable.Constructor,c=b.prototype.initTable,d=b.prototype.initBody;b.prototype.initTable=function(){var b=this;c.apply(this,Array.prototype.slice.apply(arguments)),this.options.editable&&a.each(this.columns,function(a,c){if(c.editable){var d=c.formatter;c.formatter=function(a,e,f){var g=d?d(a,e,f):a;return['<a href="javascript:void(0)"',' data-name="'+c.field+'"',' data-pk="'+e[b.options.idField]+'"',' data-value="'+g+'"',"></a>"].join("")}}})},b.prototype.initBody=function(){var b=this;d.apply(this,Array.prototype.slice.apply(arguments)),this.options.editable&&(a.each(this.columns,function(c,d){d.editable&&(b.$body.find('a[data-name="'+d.field+'"]').editable(d.editable).off("save").on("save",function(c,e){var f=b.getData(),g=a(this).parents("tr[data-index]").data("index"),h=f[g],i=h[d.field];h[d.field]=e.submitValue,b.trigger("editable-save",d.field,h,i,a(this))}),b.$body.find('a[data-name="'+d.field+'"]').editable(d.editable).off("shown").on("shown",function(c,e){var f=b.getData(),g=a(this).parents("tr[data-index]").data("index"),h=f[g];b.trigger("editable-shown",d.field,h,a(this),e)}),b.$body.find('a[data-name="'+d.field+'"]').editable(d.editable).off("hidden").on("hidden",function(c,e){var f=b.getData(),g=a(this).parents("tr[data-index]").data("index"),h=f[g];b.trigger("editable-hidden",d.field,h,a(this),e)}))}),this.trigger("editable-init"))}}(jQuery),function(a){"use strict";var b={json:"JSON",xml:"XML",png:"PNG",csv:"CSV",txt:"TXT",sql:"SQL",doc:"MS-Word",excel:"Ms-Excel",powerpoint:"Ms-Powerpoint",pdf:"PDF"};a.extend(a.fn.bootstrapTable.defaults,{showExport:!1,exportDataType:"basic",exportTypes:["json","xml","csv","txt","sql","excel"],exportOptions:{}});var c=a.fn.bootstrapTable.Constructor,d=c.prototype.initToolbar;c.prototype.initToolbar=function(){if(this.showToolbar=this.options.showExport,d.apply(this,Array.prototype.slice.apply(arguments)),this.options.showExport){var c=this,e=this.$toolbar.find(">.btn-group"),f=e.find("div.export");if(!f.length){f=a(['<div class="export btn-group">','<button class="btn btn-default dropdown-toggle" data-toggle="dropdown" type="button">','<i class="glyphicon glyphicon-export icon-share"></i> ','<span class="caret"></span>',"</button>",'<ul class="dropdown-menu" role="menu">',"</ul>","</div>"].join("")).appendTo(e);var g=f.find(".dropdown-menu"),h=this.options.exportTypes;if("string"==typeof this.options.exportTypes){var i=this.options.exportTypes.slice(1,-1).replace(/ /g,"").split(",");h=[],a.each(i,function(a,b){h.push(b.slice(1,-1))})}a.each(h,function(a,c){b.hasOwnProperty(c)&&g.append(['<li data-type="'+c+'">','<a href="javascript:void(0)">',b[c],"</a>","</li>"].join(""))}),g.find("li").click(function(){var b=a(this).data("type"),d=function(){c.$el.tableExport(a.extend({},c.options.exportOptions,{type:b,escape:!1}))};if("all"===c.options.exportDataType&&c.options.pagination)c.togglePagination(),c.$el.on("load-success.bs.table",function(){d(),c.$el.off("load-success.bs.table"),c.togglePagination()});else if("selected"===c.options.exportDataType){
var e=c.getData(),f=c.getAllSelections();c.load(f),d(),c.load(e)}else d()})}}}}(jQuery),!function(a){"use strict";var b=a.fn.bootstrapTable.utils.sprintf,c=function(b,c,e){if(b=a(b.get(b.length-1)),d(b,c)){b.append(a("<option></option>").attr("value",c).text(a("<div />").html(e).text()));var f=b.find("option:gt(0)");f.sort(function(b,c){return b=a(b).text().toLowerCase(),c=a(c).text().toLowerCase(),a.isNumeric(b)&&a.isNumeric(c)&&(b=parseFloat(b),c=parseFloat(c)),b>c?1:c>b?-1:0}),b.find("option:gt(0)").remove(),b.append(f)}},d=function(a,b){for(var c=a.get(a.length-1).options,d=0;d<c.length;d++)if(c[d].value===b.toString())return!1;return!0},e=function(a){a.$tableHeader.css("height","77px")},f=function(a){var b=a.$header;return a.options.height&&(b=a.$tableHeader),b},g=function(a){var b="select, input";return a.options.height&&(b="table select, table input"),b},h=function(b){var c=f(b),d=g(b);b.options.values=[],c.find(d).each(function(){b.options.values.push({field:a(this).parent().parent().parent().data("field"),value:a(this).val()})})},i=function(b){var c=null,d=[],e=f(b),h=g(b);b.options.values.length>0&&e.find(h).each(function(){c=a(this).parent().parent().parent().data("field"),d=a.grep(b.options.values,function(a){return a.field===c}),d.length>0&&a(this).val(d[0].value)})},j=function(d,e){var f,g,h=!1,i=0;a.each(d.columns,function(d,i){if(f="hidden",g=[],i.visible){if(i.filterControl)switch(g.push('<div style="margin: 0px 2px 2px 2px;" class="filterControl">'),i.filterControl&&i.searchable&&(h=!0,f="visible"),i.filterControl.toLowerCase()){case"input":g.push(b('<input type="text" class="form-control" style="width: 100%; visibility: %s">',f));break;case"select":g.push(b('<select class="%s form-control" style="width: 100%; visibility: %s"></select>',i.field,f));break;case"datepicker":g.push(b('<input type="text" class="date-filter-control %s form-control" style="width: 100%; visibility: %s">',i.field,f))}else g.push('<div style="height: 34px;"></div>');if(a.each(e.children().children(),function(b,c){return c=a(c),c.data("field")===i.field?(c.find(".fht-cell").append(g.join("")),!1):void 0}),void 0!==i.filterData&&"column"!==i.filterData.toLowerCase()){var j=i.filterData.substring(0,3),k=i.filterData.substring(4,i.filterData.length),l=a("."+i.field);switch(c(l,"",""),j){case"url":a.ajax({url:k,dataType:"json",success:function(b){a.each(b,function(a,b){c(l,a,b)})}});break;case"var":var m=window[k];for(var n in m)c(l,n,m[n])}}}}),h?(e.off("keyup","input").on("keyup","input",function(a){clearTimeout(i),i=setTimeout(function(){d.onColumnSearch(a)},d.options.searchTimeOut)}),e.off("change","select").on("change","select",function(a){clearTimeout(i),i=setTimeout(function(){d.onColumnSearch(a)},d.options.searchTimeOut)}),e.off("mouseup","input").on("mouseup","input",function(b){var c=a(this),e=c.val();""!==e&&setTimeout(function(){var a=c.val();""===a&&(clearTimeout(i),i=setTimeout(function(){d.onColumnSearch(b)},d.options.searchTimeOut))},1)}),e.find(".date-filter-control").length>0&&a.each(d.columns,function(b,c){void 0!==c.filterControl&&"datepicker"===c.filterControl.toLowerCase()&&e.find(".date-filter-control."+c.field).datepicker(c.filterDatepickerOptions).on("changeDate",function(b){a(b.currentTarget).keyup()})})):e.find(".filterControl").hide()};a.extend(a.fn.bootstrapTable.defaults,{filterControl:!1,onColumnSearch:function(){return!1},filterShowClear:!1,values:[]}),a.extend(a.fn.bootstrapTable.COLUMN_DEFAULTS,{filterControl:void 0,filterData:void 0,filterDatepickerOptions:void 0,filterStrictSearch:!1}),a.extend(a.fn.bootstrapTable.Constructor.EVENTS,{"column-search.bs.table":"onColumnSearch"});var k=a.fn.bootstrapTable.Constructor,l=k.prototype.init,m=k.prototype.initToolbar,n=k.prototype.initHeader,o=k.prototype.initBody,p=k.prototype.initSearch;k.prototype.init=function(){if(this.options.filterControl){var a=this;this.options.values=[],this.$el.on("reset-view.bs.table",function(){a.options.height&&(a.$tableHeader.find("select").length>0||a.$tableHeader.find("input").length>0||j(a,a.$tableHeader))}).on("post-header.bs.table",function(){i(a)}).on("post-body.bs.table",function(){a.options.height&&e(a)}).on("column-switch.bs.table",function(){i(a)})}l.apply(this,Array.prototype.slice.apply(arguments))},k.prototype.initToolbar=function(){if(!this.showToolbar&&this.options.filterControl&&(this.showToolbar=this.options.filterControl),m.apply(this,Array.prototype.slice.apply(arguments)),this.options.filterControl&&this.options.filterShowClear){var b=this.$toolbar.find(">.btn-group"),c=b.find("div.export");c.length||(c=a(['<button class="btn btn-default " type="button">','<i class="glyphicon glyphicon-trash icon-share"></i> ',"</button>","</ul>"].join("")).appendTo(b),c.off("click").on("click",a.proxy(this.clearFilterControl,this)))}},k.prototype.initHeader=function(){n.apply(this,Array.prototype.slice.apply(arguments)),this.options.filterControl&&j(this,this.$header)},k.prototype.initBody=function(){o.apply(this,Array.prototype.slice.apply(arguments));for(var b=this,d=this.options.data,e=this.pageTo<this.options.data.length?this.options.data.length:this.pageTo,f=this.pageFrom-1;e>f;f++){var g=d[f];a.each(this.header.fields,function(d,e){var h=g[e],i=b.columns[a.fn.bootstrapTable.utils.getFieldIndex(b.columns,e)];if(h=a.fn.bootstrapTable.utils.calculateObjectValue(b.header,b.header.formatters[d],[h,g,f],h),!(i.checkbox&&i.radio||void 0===i.filterControl||"select"!==i.filterControl.toLowerCase()||!i.searchable||void 0!==i.filterData&&"column"!==i.filterData.toLowerCase())){var j=a("."+i.field);void 0!==j&&j.length>0&&(0===j.get(j.length-1).options.length&&c(j,"",""),c(j,h,h))}})}},k.prototype.initSearch=function(){p.apply(this,Array.prototype.slice.apply(arguments));var b=this,c=a.isEmptyObject(this.filterColumnsPartial)?null:this.filterColumnsPartial;this.data=c?a.grep(this.data,function(d,e){for(var f in c){var g=b.columns[a.fn.bootstrapTable.utils.getFieldIndex(b.columns,f)],h=c[f].toLowerCase(),i=d[f];if(i=a.fn.bootstrapTable.utils.calculateObjectValue(b.header,b.header.formatters[a.inArray(f,b.header.fields)],[i,d,e],i),g.filterStrictSearch){if(-1===a.inArray(f,b.header.fields)||"string"!=typeof i&&"number"!=typeof i||i.toString().toLowerCase()!==h.toString().toLowerCase())return!1}else if(-1===a.inArray(f,b.header.fields)||"string"!=typeof i&&"number"!=typeof i||-1===(i+"").toLowerCase().indexOf(h))return!1}return!0}):this.data},k.prototype.onColumnSearch=function(b){h(this);var c=a.trim(a(b.currentTarget).val()),d=a(b.currentTarget).parent().parent().parent().data("field");a.isEmptyObject(this.filterColumnsPartial)&&(this.filterColumnsPartial={}),c?this.filterColumnsPartial[d]=c:delete this.filterColumnsPartial[d],this.options.pageNumber=1,this.onSearch(b),this.updatePagination(),this.trigger("column-search",d,c)},k.prototype.clearFilterControl=function(){if(this.options.filterControl&&this.options.filterShowClear){a.each(this.options.values,function(a,b){b.value=""}),i(this);var b=f(this).find(g(this)),c=0;b.length>0&&(this.filterColumnsPartial={},clearTimeout(c),c=setTimeout(function(){a(b[0]).trigger("INPUT"===b[0].tagName?"keyup":"change")},this.options.searchTimeOut))}}}(jQuery),!function(a){"use strict";a.extend(a.fn.bootstrapTable.defaults,{showFilter:!1});var b=a.fn.bootstrapTable.Constructor,c=b.prototype.init,d=b.prototype.initSearch;b.prototype.init=function(){c.apply(this,Array.prototype.slice.apply(arguments));var b=this;this.$el.on("load-success.bs.table",function(){b.options.showFilter&&a(b.options.toolbar).bootstrapTableFilter({connectTo:b.$el})})},b.prototype.initSearch=function(){d.apply(this,Array.prototype.slice.apply(arguments)),"server"!==this.options.sidePagination&&"function"==typeof this.searchCallback&&(this.data=a.grep(this.options.data,this.searchCallback))},b.prototype.getData=function(){return this.searchText||this.searchCallback?this.data:this.options.data},b.prototype.getColumns=function(){return this.columns},b.prototype.registerSearchCallback=function(a){this.searchCallback=a},b.prototype.updateSearch=function(){this.options.pageNumber=1,this.initSearch(),this.updatePagination()},b.prototype.getServerUrl=function(){return"server"===this.options.sidePagination?this.options.url:!1},a.fn.bootstrapTable.methods.push("getColumns","registerSearchCallback","updateSearch","getServerUrl")}(jQuery),function(a){"use strict";var b=function(b,c){function d(b,f){if(Object(b)!==b)e[f]=b;else if(a.isArray(b))for(var g=0,h=b.length;h>g;g++)d(b[g],f?f+c.options.flatSeparator+g:""+g),0==h&&(e[f]=[]);else{var i=!0;for(var j in b)i=!1,d(b[j],f?f+c.options.flatSeparator+j:j);i&&(e[f]={})}}var e={};return d(b,""),e},c=function(c,d){var e=[];return a.each(a.isArray(c)?c:[c],function(a,c){e.push(b(c,d))}),e};a.extend(a.fn.bootstrapTable.defaults,{flat:!1,flatSeparator:"."});var d=a.fn.bootstrapTable.Constructor,e=d.prototype.initData;d.prototype.initData=function(a,b){this.options.flat&&(a=c(a?a:this.options.data,this)),e.apply(this,[a,b])}}(jQuery),!function(a){"use strict";var b,c="data-tt-id",d="data-tt-parent-id",e={},f=void 0,g=function(b,c){for(var d=b.$body.find("tr").not("[data-tt-parent-id]"),e=0;e<d.length;e++)if(e===c)return a(d[e]).attr("data-tt-id");return void 0},h=function(b,c){var d={};return a.each(c,function(c,e){if(!e.IsParent)for(var f in e)isNaN(parseFloat(e[f]))||b.columns[a.fn.bootstrapTable.utils.getFieldIndex(b.columns,f)].groupBySumGroup&&(void 0===d[f]&&(d[f]=0),d[f]+=+e[f])}),d},i=function(a,g){return b.apply([a,g]),e[c.toString()]=g,a.IsParent?(f=g,delete e[d.toString()]):e[d.toString()]=void 0===f?g:f,e},j=function(){Object.keys=function(a){if(a!==Object(a))throw new TypeError("Object.keys called on a non-object");var b,c=[];for(b in a)Object.prototype.hasOwnProperty.call(a,b)&&c.push(b);return c}},k=function(a,b){for(var c=[],d=0;d<a.options.groupByField.length;d++)c.push(b[a.options.groupByField[d]]);return c},l=function(a,b,c){for(var d={},e=0;e<a.options.groupByField.length;e++)d[a.options.groupByField[e].toString()]=b[c][0][a.options.groupByField[e]];return d.IsParent=!0,d},m=function(b,c){var d={};return a.each(b,function(a,b){var e=JSON.stringify(c(b));d[e]=d[e]||[],d[e].push(b)}),Object.keys(d).map(function(a){return d[a]})},n=function(b,c){for(var d=[],e={},f=m(c,function(a){return k(b,a)}),g=0;g<f.length;g++)f[g].unshift(l(b,f,g)),b.options.groupBySumGroup&&(e=h(b,f[g]),a.isEmptyObject(e)||f[g].push(e));return d=d.concat.apply(d,f),!b.options.loaded&&d.length>0&&(b.options.loaded=!0,b.options.originalData=b.options.data,b.options.data=d),d};a.extend(a.fn.bootstrapTable.defaults,{groupBy:!1,groupByField:[],groupBySumGroup:!1,groupByInitExpanded:void 0,loaded:!1,originalData:void 0}),a.fn.bootstrapTable.methods.push("collapseAll","expandAll","refreshGroupByField"),a.extend(a.fn.bootstrapTable.COLUMN_DEFAULTS,{groupBySumGroup:!1});var o=a.fn.bootstrapTable.Constructor,p=o.prototype.init,q=o.prototype.initData;o.prototype.init=function(){if(!this.options.sortName&&this.options.groupBy&&this.options.groupByField.length>0){var a=this;Object.keys||j(),this.options.loaded=!1,this.options.originalData=void 0,b=this.options.rowAttributes,this.options.rowAttributes=i,this.$el.on("post-body.bs.table",function(){a.$el.treetable({expandable:!0,onNodeExpand:function(){a.options.height&&a.resetHeader()},onNodeCollapse:function(){a.options.height&&a.resetHeader()}},!0),void 0!==a.options.groupByInitExpanded&&("number"==typeof a.options.groupByInitExpanded?a.expandNode(a.options.groupByInitExpanded):"all"===a.options.groupByInitExpanded.toLowerCase()&&a.expandAll())})}p.apply(this,Array.prototype.slice.apply(arguments))},o.prototype.initData=function(a,b){this.options.sortName||this.options.groupBy&&this.options.groupByField.length>0&&(this.options.groupByField="string"==typeof this.options.groupByField?this.options.groupByField.replace("[","").replace("]","").replace(/ /g,"").toLowerCase().split(","):this.options.groupByField,a=n(this,a?a:this.options.data)),q.apply(this,[a,b])},o.prototype.expandAll=function(){this.$el.treetable("expandAll")},o.prototype.collapseAll=function(){this.$el.treetable("collapseAll")},o.prototype.expandNode=function(a){a=g(this,a),void 0!==a&&this.$el.treetable("expandNode",a)},o.prototype.refreshGroupByField=function(b){a.fn.bootstrapTable.utils.compareObjects(this.options.groupByField,b)||(this.options.groupByField=b,this.load(this.options.originalData))}}(jQuery),!function(a){"use strict";a.extend(a.fn.bootstrapTable.defaults,{keyEvents:!1});var b=a.fn.bootstrapTable.Constructor,c=b.prototype.init;b.prototype.init=function(){c.apply(this,Array.prototype.slice.apply(arguments)),this.initKeyEvents()},b.prototype.initKeyEvents=function(){if(this.options.keyEvents){var b=this;a(document).off("keydown").on("keydown",function(a){var c=b.$toolbar.find(".search input"),d=b.$toolbar.find('button[name="refresh"]'),e=b.$toolbar.find('button[name="toggle"]'),f=b.$toolbar.find('button[name="paginationSwitch"]');if(document.activeElement===c.get(0))return!0;switch(a.keyCode){case 83:if(!b.options.search)return;return c.focus(),!1;case 82:if(!b.options.showRefresh)return;return d.click(),!1;case 84:if(!b.options.showToggle)return;return e.click(),!1;case 80:if(!b.options.showPaginationSwitch)return;return f.click(),!1;case 37:if(!b.options.pagination)return;return b.prevPage(),!1;case 39:if(!b.options.pagination)return;return void b.nextPage()}})}}}(jQuery),!function(a){"use strict";var b=function(b,c){b.options.columnsHidden.length>0&&a.each(b.columns,function(d,e){-1!==b.options.columnsHidden.indexOf(e.field)&&e.visible!==c&&b.toggleColumn(a.fn.bootstrapTable.utils.getFieldIndex(b.columns,e.field),c,!0)})},c=function(a){(a.options.height||a.options.showFooter)&&setTimeout(function(){a.resetView.call(a)},1)},d=function(a,b,d){a.options.minHeight?b<=a.options.minWidth&&d<=a.options.minHeight?e(a):b>a.options.minWidth&&d>a.options.minHeight&&f(a):b<=a.options.minWidth?e(a):b>a.options.minWidth&&f(a),c(a)},e=function(a){g(a,!1),b(a,!1)},f=function(a){g(a,!0),b(a,!0)},g=function(a,b){a.options.cardView=b,a.toggleView()},h=function(a,b){var c;return function(){var d=this,e=arguments,f=function(){c=null,a.apply(d,e)};clearTimeout(c),c=setTimeout(f,b)}};a.extend(a.fn.bootstrapTable.defaults,{mobileResponsive:!1,minWidth:562,minHeight:void 0,heightThreshold:100,checkOnInit:!0,columnsHidden:[]});var i=a.fn.bootstrapTable.Constructor,j=i.prototype.init;i.prototype.init=function(){if(j.apply(this,Array.prototype.slice.apply(arguments)),this.options.mobileResponsive&&this.options.minWidth){var b=this,c={width:a(window).width(),height:a(window).height()};if(a(window).on("resize orientationchange",h(function(){var e=a(this).height(),f=a(this).width();(Math.abs(c.height-e)>b.options.heightThreshold||c.width!=f)&&(d(b,f,e),c={width:f,height:e})},200)),this.options.checkOnInit){var e=a(window).height(),f=a(window).width();d(this,f,e),c={width:f,height:e}}}}}(jQuery),!function(a){"use strict";a.extend(a.fn.bootstrapTable.defaults,{multipleSearch:!1});var b=a.fn.bootstrapTable.Constructor,c=b.prototype.initSearch;b.prototype.initSearch=function(){if(this.options.multipleSearch){var b=this.searchText.split(" "),d=this,e=(a.isEmptyObject(this.filterColumns)?null:this.filterColumns,[]);if(1===b.length)c.apply(this,Array.prototype.slice.apply(arguments));else{for(var f=0;f<b.length;f++){var g=b[f].trim();e=g?a.grep(0===e.length?this.options.data:e,function(b,c){for(var e in b){e=a.isNumeric(e)?parseInt(e,10):e;var f=b[e],h=d.columns[a.fn.bootstrapTable.utils.getFieldIndex(d.columns,e)],i=a.inArray(e,d.header.fields);h&&h.searchFormatter&&(f=a.fn.bootstrapTable.utils.calculateObjectValue(h,d.header.formatters[i],[f,b,c],f));var j=a.inArray(e,d.header.fields);if(-1!==j&&d.header.searchables[j]&&("string"==typeof f||"number"==typeof f))if(d.options.strictSearch){if((f+"").toLowerCase()===g)return!0}else if(-1!==(f+"").toLowerCase().indexOf(g))return!0}return!1}):this.data}this.data=e}}else c.apply(this,Array.prototype.slice.apply(arguments))}}(jQuery),function(a){"use strict";var b=!1,c={asc:"Ascending",desc:"Descending"},d=function(b){var c=b.$sortModal.selector,d=c.substr(1);if(!a(d).hasClass("modal")){var e='  <div class="modal fade" id="'+d+'" tabindex="-1" role="dialog" aria-labelledby="'+d+'Label" aria-hidden="true">';e+='         <div class="modal-dialog">',e+='             <div class="modal-content">',e+='                 <div class="modal-header">',e+='                     <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>',e+='                     <h4 class="modal-title" id="'+d+'Label">'+b.options.formatMultipleSort()+"</h4>",e+="                 </div>",e+='                 <div class="modal-body">',e+='                     <div class="bootstrap-table">',e+='                         <div class="fixed-table-toolbar">',e+='                             <div class="bars">',e+='                                 <div id="toolbar">',e+='                                     <button id="add" type="button" class="btn btn-default"><i class="'+b.options.iconsPrefix+" "+b.options.icons.plus+'"></i> '+b.options.formatAddLevel()+"</button>",e+='                                     <button id="delete" type="button" class="btn btn-default" disabled><i class="'+b.options.iconsPrefix+" "+b.options.icons.minus+'"></i> '+b.options.formatDeleteLevel()+"</button>",e+="                                 </div>",e+="                             </div>",e+="                         </div>",e+='                         <div class="fixed-table-container">',e+='                             <table id="multi-sort" class="table">',e+="                                 <thead>",e+="                                     <tr>",e+="                                         <th></th>",e+='                                         <th><div class="th-inner">'+b.options.formatColumn()+"</div></th>",e+='                                         <th><div class="th-inner">'+b.options.formatOrder()+"</div></th>",e+="                                     </tr>",e+="                                 </thead>",e+="                                 <tbody></tbody>",e+="                             </table>",e+="                         </div>",e+="                     </div>",e+="                 </div>",e+='                 <div class="modal-footer">',e+='                     <button type="button" class="btn btn-default" data-dismiss="modal">'+b.options.formatCancel()+"</button>",e+='                     <button type="button" class="btn btn-primary">'+b.options.formatSort()+"</button>",e+="                 </div>",e+="             </div>",e+="         </div>",e+="     </div>",a("body").append(a(e)),b.$sortModal=a(c);var f=b.$sortModal.find("tbody > tr");if(b.$sortModal.off("click","#add").on("click","#add",function(){var a=b.$sortModal.find(".multi-sort-name:first option").length,c=b.$sortModal.find("tbody tr").length;a>c&&(c++,b.addLevel(),b.setButtonStates())}),b.$sortModal.off("click","#delete").on("click","#delete",function(){var a=b.$sortModal.find(".multi-sort-name:first option").length,c=b.$sortModal.find("tbody tr").length;c>1&&a>=c&&(c--,b.$sortModal.find("tbody tr:last").remove(),b.setButtonStates())}),b.$sortModal.off("click",".btn-primary").on("click",".btn-primary",function(){var c=b.$sortModal.find("tbody > tr"),d=b.$sortModal.find("div.alert"),e=[],f=[];b.options.sortPriority=a.map(c,function(b){var c=a(b),d=c.find(".multi-sort-name").val(),f=c.find(".multi-sort-order").val();return e.push(d),{sortName:d,sortOrder:f}});for(var g=e.sort(),h=0;h<e.length-1;h++)g[h+1]==g[h]&&f.push(g[h]);f.length>0?0===d.length&&(d='<div class="alert alert-danger" role="alert"><strong>'+b.options.formatDuplicateAlertTitle()+"</strong> "+b.options.formatDuplicateAlertDescription()+"</div>",a(d).insertBefore(b.$sortModal.find(".bars"))):(1===d.length&&a(d).remove(),b.options.sortName="",b.onMultipleSort(),b.$sortModal.modal("hide"))}),(null===b.options.sortPriority||0===b.options.sortPriority.length)&&b.options.sortName&&(b.options.sortPriority=[{sortName:b.options.sortName,sortOrder:b.options.sortOrder}]),null!==b.options.sortPriority&&b.options.sortPriority.length>0){if(f.length<b.options.sortPriority.length&&"object"==typeof b.options.sortPriority)for(var g=0;g<b.options.sortPriority.length;g++)b.addLevel(g,b.options.sortPriority[g])}else b.addLevel(0);b.setButtonStates()}};a.extend(a.fn.bootstrapTable.defaults,{showMultiSort:!1,sortPriority:null,onMultipleSort:function(){return!1}}),a.extend(a.fn.bootstrapTable.defaults.icons,{sort:"glyphicon-sort",plus:"glyphicon-plus",minus:"glyphicon-minus"}),a.extend(a.fn.bootstrapTable.Constructor.EVENTS,{"multiple-sort.bs.table":"onMultipleSort"}),a.extend(a.fn.bootstrapTable.locales,{formatMultipleSort:function(){return"Multiple Sort"},formatAddLevel:function(){return"Add Level"},formatDeleteLevel:function(){return"Delete Level"},formatColumn:function(){return"Column"},formatOrder:function(){return"Order"},formatSortBy:function(){return"Sort by"},formatThenBy:function(){return"Then by"},formatSort:function(){return"Sort"},formatCancel:function(){return"Cancel"},formatDuplicateAlertTitle:function(){return"Duplicate(s) detected!"},formatDuplicateAlertDescription:function(){return"Please remove or change any duplicate column."}}),a.extend(a.fn.bootstrapTable.defaults,a.fn.bootstrapTable.locales);var e=a.fn.bootstrapTable.Constructor,f=e.prototype.initToolbar;e.prototype.initToolbar=function(){this.showToolbar=!0;var c=this,e="#sortModal_"+this.$el.attr("id");if(this.$sortModal=a(e),f.apply(this,Array.prototype.slice.apply(arguments)),this.options.showMultiSort){var g=this.$toolbar.find(">.btn-group").first(),h=this.$toolbar.find("div.multi-sort");h.length||(h='  <button class="multi-sort btn btn-default'+(void 0===this.options.iconSize?"":" btn-"+this.options.iconSize)+'" type="button" data-toggle="modal" data-target="'+e+'" title="'+this.options.formatMultipleSort()+'">',h+='     <i class="'+this.options.iconsPrefix+" "+this.options.icons.sort+'"></i>',h+="</button>",g.append(h),d(c)),this.$el.on("sort.bs.table",function(){b=!0}),this.$el.on("multiple-sort.bs.table",function(){b=!1}),this.$el.on("load-success.bs.table",function(){b||null===c.options.sortPriority||"object"!=typeof c.options.sortPriority||c.onMultipleSort()}),this.$el.on("column-switch.bs.table",function(a,b){for(var e=0;e<c.options.sortPriority.length;e++)c.options.sortPriority[e].sortName===b&&c.options.sortPriority.splice(e,1);c.assignSortableArrows(),c.$sortModal.remove(),d(c)}),this.$el.on("reset-view.bs.table",function(){b||null===c.options.sortPriority||"object"!=typeof c.options.sortPriority||c.assignSortableArrows()})}},e.prototype.onMultipleSort=function(){var b=this,c=function(a,b){return a>b?1:b>a?-1:0},d=function(d,e){for(var f=[],g=[],h=0;h<b.options.sortPriority.length;h++){var i="desc"===b.options.sortPriority[h].sortOrder?-1:1,j=d[b.options.sortPriority[h].sortName],k=e[b.options.sortPriority[h].sortName];(void 0===j||null===j)&&(j=""),(void 0===k||null===k)&&(k=""),a.isNumeric(j)&&a.isNumeric(k)&&(j=parseFloat(j),k=parseFloat(k)),"string"!=typeof j&&(j=j.toString()),f.push(i*c(j,k)),g.push(i*c(k,j))}return c(f,g)};this.data.sort(function(a,b){return d(a,b)}),this.initBody(),this.assignSortableArrows(),this.trigger("multiple-sort")},e.prototype.addLevel=function(b,d){var e=0===b?this.options.formatSortBy():this.options.formatThenBy();this.$sortModal.find("tbody").append(a("<tr>").append(a("<td>").text(e)).append(a("<td>").append(a('<select class="form-control multi-sort-name">'))).append(a("<td>").append(a('<select class="form-control multi-sort-order">'))));var f=this.$sortModal.find(".multi-sort-name").last(),g=this.$sortModal.find(".multi-sort-order").last();a.each(this.columns,function(a,b){return b.sortable===!1||b.visible===!1?!0:void f.append('<option value="'+b.field+'">'+b.title+"</option>")}),a.each(c,function(a,b){g.append('<option value="'+a+'">'+b+"</option>")}),void 0!==d&&(f.find('option[value="'+d.sortName+'"]').attr("selected",!0),g.find('option[value="'+d.sortOrder+'"]').attr("selected",!0))},e.prototype.assignSortableArrows=function(){for(var b=this,c=b.$header.find("th"),d=0;d<c.length;d++)for(var e=0;e<b.options.sortPriority.length;e++)a(c[d]).data("field")===b.options.sortPriority[e].sortName&&a(c[d]).find(".sortable").removeClass("desc asc").addClass(b.options.sortPriority[e].sortOrder)},e.prototype.setButtonStates=function(){var a=this.$sortModal.find(".multi-sort-name:first option").length,b=this.$sortModal.find("tbody tr").length;b==a&&this.$sortModal.find("#add").attr("disabled","disabled"),b>1&&this.$sortModal.find("#delete").removeAttr("disabled"),a>b&&this.$sortModal.find("#add").removeAttr("disabled"),1==b&&this.$sortModal.find("#delete").attr("disabled","disabled")}}(jQuery),!function(a){"use strict";a.extend(a.fn.bootstrapTable.defaults,{reorderableColumns:!1,maxMovingRows:10,onReorderColumn:function(){return!1},dragaccept:null}),a.extend(a.fn.bootstrapTable.Constructor.EVENTS,{"reorder-column.bs.table":"onReorderColumn"});var b=a.fn.bootstrapTable.Constructor,c=b.prototype.initHeader,d=b.prototype.toggleColumn,e=b.prototype.toggleView,f=b.prototype.resetView;b.prototype.initHeader=function(){c.apply(this,Array.prototype.slice.apply(arguments)),this.options.reorderableColumns&&this.makeRowsReorderable()},b.prototype.toggleColumn=function(){d.apply(this,Array.prototype.slice.apply(arguments)),this.options.reorderableColumns&&this.makeRowsReorderable()},b.prototype.toggleView=function(){e.apply(this,Array.prototype.slice.apply(arguments)),this.options.reorderableColumns&&(this.options.cardView||this.makeRowsReorderable())},b.prototype.resetView=function(){f.apply(this,Array.prototype.slice.apply(arguments)),this.options.reorderableColumns&&this.makeRowsReorderable()},b.prototype.makeRowsReorderable=function(){var b=this;try{a(this.$el).dragtable("destroy")}catch(c){}a(this.$el).dragtable({maxMovingRows:b.options.maxMovingRows,dragaccept:b.options.dragaccept,clickDelay:200,beforeStop:function(){var c=[],d=[],e=[],f=[],g=-1;if(b.$header.find("th").each(function(){c.push(a(this).data("field")),d.push(a(this).data("formatter"))}),c.length<b.columns.length){f=a.grep(b.columns,function(a){return!a.visible});for(var h=0;h<f.length;h++)c.push(f[h].field),d.push(f[h].formatter)}for(var h=0;h<c.length;h++)g=a.fn.bootstrapTable.utils.getFieldIndex(b.columns,c[h]),-1!==g&&(e.push(b.columns[g]),b.columns.splice(g,1));b.columns=b.columns.concat(e),b.header.fields=c,b.header.formatters=d,b.resetView(),b.trigger("reorder-column",c)}})}}(jQuery),!function(a){"use strict";var b=function(a,b){return{id:"customId_"+b}};a.extend(a.fn.bootstrapTable.defaults,{reorderableRows:!1,onDragStyle:null,onDropStyle:null,onDragClass:"reorder_rows_onDragClass",dragHandle:null,useRowAttrFunc:!1,onReorderRowsDrag:function(){return!1},onReorderRowsDrop:function(){return!1},onReorderRow:function(){return!1}}),a.extend(a.fn.bootstrapTable.Constructor.EVENTS,{"reorder-row.bs.table":"onReorderRow"});var c=a.fn.bootstrapTable.Constructor,d=c.prototype.init,e=c.prototype.initSearch;c.prototype.init=function(){if(this.options.reorderableRows){var a=this;this.options.useRowAttrFunc&&(this.options.rowAttributes=b);var c=this.options.onPostBody;this.options.onPostBody=function(){setTimeout(function(){a.makeRowsReorderable(),c.apply()},1)},d.apply(this,Array.prototype.slice.apply(arguments))}},c.prototype.initSearch=function(){e.apply(this,Array.prototype.slice.apply(arguments)),!this.options.reorderableRows},c.prototype.makeRowsReorderable=function(){if(!this.options.cardView){var a=this;this.$el.tableDnD({onDragStyle:a.options.onDragStyle,onDropStyle:a.options.onDropStyle,onDragClass:a.options.onDragClass,onDrop:a.onDrop,onDragStart:a.options.onReorderRowsDrag,dragHandle:a.options.dragHandle})}},c.prototype.onDrop=function(b,c){for(var d=a(b),e=d.data("bootstrap.table"),f=d.data("bootstrap.table").options,g=null,h=[],i=0;i<b.tBodies[0].rows.length;i++)g=a(b.tBodies[0].rows[i]),h.push(f.data[g.data("index")]),g.data("index",i).attr("data-index",i);f.data=h,f.onReorderRowsDrop.apply(b,[b,c]),e.trigger("reorder-row",h)}}(jQuery),function(a){"use strict";var b=function(a){a.$el.colResizable({disable:!0}),a.$el.colResizable({liveDrag:a.options.liveDrag,fixed:a.options.fixed,headerOnly:a.options.headerOnly,minWidth:a.options.minWidth,hoverCursor:a.options.hoverCursor,dragCursor:a.options.dragCursor,onResize:a.onResize,onDrag:a.options.onResizableDrag})};a.extend(a.fn.bootstrapTable.defaults,{resizable:!1,liveDrag:!1,fixed:!0,headerOnly:!1,minWidth:15,hoverCursor:"e-resize",dragCursor:"e-resize",onResizableResize:function(){return!1},onResizableDrag:function(){return!1}});var c=a.fn.bootstrapTable.Constructor,d=c.prototype.toggleView,e=c.prototype.resetView;c.prototype.toggleView=function(){d.apply(this,Array.prototype.slice.apply(arguments)),this.options.resizable&&this.options.cardView&&a(this.$el).colResizable({disable:!0})},c.prototype.resetView=function(){var a=this;e.apply(this,Array.prototype.slice.apply(arguments)),this.options.resizable&&setTimeout(function(){b(a)},100)},c.prototype.onResize=function(b){var c=a(b.currentTarget);c.bootstrapTable("resetView"),c.data("bootstrap.table").options.onResizableResize.apply(b)}}(jQuery),!function(a){"use strict";var b=!1,c=a.fn.bootstrapTable.utils.sprintf,d=function(b,d,f,g){if(a("#avdSearchModal_"+g.options.idTable).hasClass("modal"))a("#avdSearchModal_"+g.options.idTable).modal();else{var h=c('<div id="avdSearchModal%s"  class="modal fade" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">',"_"+g.options.idTable);h+='<div class="modal-dialog modal-xs">',h+=' <div class="modal-content">',h+='  <div class="modal-header">',h+='   <button type="button" class="close" data-dismiss="modal" aria-hidden="true" >&times;</button>',h+=c('   <h4 class="modal-title">%s</h4>',d),h+="  </div>",h+='  <div class="modal-body modal-body-custom">',h+=c('   <div class="container-fluid" id="avdSearchModalContent%s" style="padding-right: 0px;padding-left: 0px;" >',"_"+g.options.idTable),h+="   </div>",h+="  </div>",h+="  </div>",h+=" </div>",h+="</div>",a("body").append(a(h));var i=e(b,f,g),j=0;a("#avdSearchModalContent_"+g.options.idTable).append(i.join("")),a("#"+g.options.idForm).off("keyup blur","input").on("keyup blur","input",function(a){clearTimeout(j),j=setTimeout(function(){g.onColumnAdvancedSearch(a)},g.options.searchTimeOut)}),a("#btnCloseAvd_"+g.options.idTable).click(function(){a("#avdSearchModal_"+g.options.idTable).modal("hide")}),a("#avdSearchModal_"+g.options.idTable).modal()}},e=function(a,b,d){var e=[];e.push(c('<form class="form-horizontal" id="%s" action="%s" >',d.options.idForm,d.options.actionForm));for(var f in a){var g=a[f];!g.checkbox&&g.visible&&g.searchable&&(e.push('<div class="form-group">'),e.push(c('<label class="col-sm-4 control-label">%s</label>',g.title)),e.push('<div class="col-sm-6">'),e.push(c('<input type="text" class="form-control input-md" name="%s" placeholder="%s" id="%s">',g.field,g.title,g.field)),e.push("</div>"),e.push("</div>"))}return e.push('<div class="form-group">'),e.push('<div class="col-sm-offset-9 col-sm-3">'),e.push(c('<button type="button" id="btnCloseAvd%s" class="btn btn-default" >%s</button>',"_"+d.options.idTable,b)),e.push("</div>"),e.push("</div>"),e.push("</form>"),e};a.extend(a.fn.bootstrapTable.defaults,{advancedSearch:!1,idForm:"advancedSearch",actionForm:"",idTable:void 0,onColumnAdvancedSearch:function(){return!1}}),a.extend(a.fn.bootstrapTable.defaults.icons,{advancedSearchIcon:"glyphicon-chevron-down"}),a.extend(a.fn.bootstrapTable.Constructor.EVENTS,{"column-advanced-search.bs.table":"onColumnAdvancedSearch"}),a.extend(a.fn.bootstrapTable.locales,{formatAdvancedSearch:function(){return"Advanced search";

},formatAdvancedCloseButton:function(){return"Close"}}),a.extend(a.fn.bootstrapTable.defaults,a.fn.bootstrapTable.locales);var f=a.fn.bootstrapTable.Constructor,g=f.prototype.initToolbar,h=f.prototype.load,i=f.prototype.initSearch;f.prototype.initToolbar=function(){if(g.apply(this,Array.prototype.slice.apply(arguments)),this.options.search&&this.options.advancedSearch&&this.options.idTable){var a=this,b=[];b.push(c('<div class="columns columns-%s btn-group pull-%s" role="group">',this.options.buttonsAlign,this.options.buttonsAlign)),b.push(c('<button class="btn btn-default%s" type="button" name="advancedSearch" title="%s">',void 0===a.options.iconSize?"":" btn-"+a.options.iconSize,a.options.formatAdvancedSearch())),b.push(c('<i class="%s %s"></i>',a.options.iconsPrefix,a.options.icons.advancedSearchIcon)),b.push("</button></div>"),a.$toolbar.prepend(b.join("")),a.$toolbar.find('button[name="advancedSearch"]').off("click").on("click",function(){d(a.columns,a.options.formatAdvancedSearch(),a.options.formatAdvancedCloseButton(),a)})}},f.prototype.load=function(){if(h.apply(this,Array.prototype.slice.apply(arguments)),this.options.advancedSearch&&"undefined"!=typeof this.options.idTable&&!b){var c=parseInt(a(".bootstrap-table").height());c+=10,a("#"+this.options.idTable).bootstrapTable("resetView",{height:c}),b=!0}},f.prototype.initSearch=function(){if(i.apply(this,Array.prototype.slice.apply(arguments)),this.options.advancedSearch){var b=this,c=a.isEmptyObject(this.filterColumnsPartial)?null:this.filterColumnsPartial;this.data=c?a.grep(this.data,function(d,e){for(var f in c){var g=c[f].toLowerCase(),h=d[f];if(h=a.fn.bootstrapTable.utils.calculateObjectValue(b.header,b.header.formatters[a.inArray(f,b.header.fields)],[h,d,e],h),-1===a.inArray(f,b.header.fields)||"string"!=typeof h&&"number"!=typeof h||-1===(h+"").toLowerCase().indexOf(g))return!1}return!0}):this.data}},f.prototype.onColumnAdvancedSearch=function(b){var c=a.trim(a(b.currentTarget).val()),d=a(b.currentTarget)[0].id;a.isEmptyObject(this.filterColumnsPartial)&&(this.filterColumnsPartial={}),c?this.filterColumnsPartial[d]=c:delete this.filterColumnsPartial[d],this.options.pageNumber=1,this.onSearch(b),this.updatePagination(),this.trigger("column-advanced-search",d,c)}}(jQuery);