/*! ui-grid - v3.0.0-RC.18 - 2014-12-09
* Copyright (c) 2014 ; License: MIT */
!function(){"use strict";angular.module("ui.grid.i18n",[]),angular.module("ui.grid",["ui.grid.i18n"])}(),function(){"use strict";angular.module("ui.grid").constant("uiGridConstants",{LOG_DEBUG_MESSAGES:!0,LOG_WARN_MESSAGES:!0,LOG_ERROR_MESSAGES:!0,CUSTOM_FILTERS:/CUSTOM_FILTERS/g,COL_FIELD:/COL_FIELD/g,MODEL_COL_FIELD:/MODEL_COL_FIELD/g,DISPLAY_CELL_TEMPLATE:/DISPLAY_CELL_TEMPLATE/g,TEMPLATE_REGEXP:/<.+>/,FUNC_REGEXP:/(\([^)]*\))?$/,DOT_REGEXP:/\./g,APOS_REGEXP:/'/g,BRACKET_REGEXP:/^(.*)((?:\s*\[\s*\d+\s*\]\s*)|(?:\s*\[\s*"(?:[^"\\]|\\.)*"\s*\]\s*)|(?:\s*\[\s*'(?:[^'\\]|\\.)*'\s*\]\s*))(.*)$/,COL_CLASS_PREFIX:"ui-grid-col",events:{GRID_SCROLL:"uiGridScroll",COLUMN_MENU_SHOWN:"uiGridColMenuShown",ITEM_DRAGGING:"uiGridItemDragStart"},keymap:{TAB:9,STRG:17,CTRL:17,CTRLRIGHT:18,CTRLR:18,SHIFT:16,RETURN:13,ENTER:13,BACKSPACE:8,BCKSP:8,ALT:18,ALTR:17,ALTRIGHT:17,SPACE:32,WIN:91,MAC:91,FN:null,UP:38,DOWN:40,LEFT:37,RIGHT:39,ESC:27,DEL:46,F1:112,F2:113,F3:114,F4:115,F5:116,F6:117,F7:118,F8:119,F9:120,F10:121,F11:122,F12:123},ASC:"asc",DESC:"desc",filter:{STARTS_WITH:2,ENDS_WITH:4,EXACT:8,CONTAINS:16,GREATER_THAN:32,GREATER_THAN_OR_EQUAL:64,LESS_THAN:128,LESS_THAN_OR_EQUAL:256,NOT_EQUAL:512},aggregationTypes:{sum:2,count:4,avg:8,min:16,max:32},CURRENCY_SYMBOLS:["ƒ","$","£","$","¤","¥","៛","₩","₱","฿","₫"],dataChange:{ALL:"all",EDIT:"edit",ROW:"row",COLUMN:"column"},scrollbars:{NEVER:0,ALWAYS:1,WHEN_NEEDED:2}})}(),angular.module("ui.grid").directive("uiGridCell",["$compile","$parse","gridUtil","uiGridConstants",function(a,b,c,d){var e={priority:0,scope:!1,require:"?^uiGrid",compile:function(){return{pre:function(b,e,f,g){function h(){var a=b.col.compiledElementFn;a(b,function(a){e.append(a)})}if(g&&b.col.compiledElementFn)h();else if(g&&!b.col.compiledElementFn)b.col.getCompiledElementFn().then(function(a){a(b,function(a){e.append(a)})});else{var i=b.col.cellTemplate.replace(d.MODEL_COL_FIELD,"row.entity."+c.preEval(b.col.field)).replace(d.COL_FIELD,"grid.getCellValue(row, col)"),j=a(i)(b);e.append(j)}},post:function(a,b){b.addClass(a.col.getColClass(!1));var c,e=function(){var d=b;c&&(d.removeClass(c),c=null),c=angular.isFunction(a.col.cellClass)?a.col.cellClass(a.grid,a.row,a.col,a.rowRenderIndex,a.colRenderIndex):a.col.cellClass,d.addClass(c)};a.col.cellClass&&e();var f=a.grid.registerDataChangeCallback(e,[d.dataChange.COLUMN,d.dataChange.EDIT]),g=function(){a.grid.deregisterDataChangeCallback(f)};a.$on("$destroy",g)}}}};return e}]),function(){angular.module("ui.grid").service("uiGridColumnMenuService",["i18nService","uiGridConstants","gridUtil",function(a,b,c){var d={initialize:function(a,b){a.grid=b.grid,b.columnMenuScope=a,a.menuShown=!1},setColMenuItemWatch:function(a){var b=a.$watch("col.menuItems",function(b){"undefined"!=typeof b&&b&&angular.isArray(b)?(b.forEach(function(b){"undefined"!=typeof b.context&&b.context||(b.context={}),b.context.col=a.col}),a.menuItems=a.defaultMenuItems.concat(b)):a.menuItems=a.defaultMenuItems});a.$on("$destroy",b)},sortable:function(a){return a.grid.options.enableSorting&&"undefined"!=typeof a.col&&a.col&&a.col.enableSorting?!0:!1},isActiveSort:function(a,b){return"undefined"!=typeof a.col&&"undefined"!=typeof a.col.sort&&"undefined"!=typeof a.col.sort.direction&&a.col.sort.direction===b},suppressRemoveSort:function(a){return a.col&&a.col.colDef&&a.col.colDef.suppressRemoveSort?!0:!1},hideable:function(a){return"undefined"!=typeof a.col&&a.col&&a.col.colDef&&a.col.colDef.enableHiding===!1?!1:!0},getDefaultMenuItems:function(c){return[{title:a.getSafeText("sort.ascending"),icon:"ui-grid-icon-sort-alt-up",action:function(a){a.stopPropagation(),c.sortColumn(a,b.ASC)},shown:function(){return d.sortable(c)},active:function(){return d.isActiveSort(c,b.ASC)}},{title:a.getSafeText("sort.descending"),icon:"ui-grid-icon-sort-alt-down",action:function(a){a.stopPropagation(),c.sortColumn(a,b.DESC)},shown:function(){return d.sortable(c)},active:function(){return d.isActiveSort(c,b.DESC)}},{title:a.getSafeText("sort.remove"),icon:"ui-grid-icon-cancel",action:function(a){a.stopPropagation(),c.unsortColumn()},shown:function(){return d.sortable(c)&&"undefined"!=typeof c.col&&"undefined"!=typeof c.col.sort&&"undefined"!=typeof c.col.sort.direction&&null!==c.col.sort.direction&&!d.suppressRemoveSort(c)}},{title:a.getSafeText("column.hide"),icon:"ui-grid-icon-cancel",shown:function(){return d.hideable(c)},action:function(a){a.stopPropagation(),c.hideColumn()}}]},getColumnElementPosition:function(a,b,d){var e={};return e.left=d[0].offsetLeft,e.top=d[0].offsetTop,e.offset=0,b.grid.options.offsetLeft&&(e.offset=b.grid.options.offsetLeft),e.height=c.elementHeight(d,!0),e.width=c.elementWidth(d,!0),e},repositionMenu:function(a,b,d,e,f){var g=e[0].querySelectorAll(".ui-grid-menu"),h=b.renderContainer?b.renderContainer:"body",i=(b.grid.renderContainers[h],c.closestElm(f,".ui-grid-render-container")),j=i.getBoundingClientRect().left-a.grid.element[0].getBoundingClientRect().left,k=i.querySelectorAll(".ui-grid-viewport")[0].scrollLeft,l=b.lastMenuWidth?b.lastMenuWidth:a.lastMenuWidth?a.lastMenuWidth:170,m=b.lastMenuPaddingRight?b.lastMenuPaddingRight:a.lastMenuPaddingRight?a.lastMenuPaddingRight:10;if(0!==g.length){var n=g[0].querySelectorAll(".ui-grid-menu-mid");0===n.length||angular.element(n).hasClass("ng-hide")||(l=c.elementWidth(g,!0),a.lastMenuWidth=l,b.lastMenuWidth=l,m=parseInt(c.getStyles(angular.element(g)[0]).paddingRight,10),a.lastMenuPaddingRight=m,b.lastMenuPaddingRight=m)}var o=d.left+j-k+d.width-l+m;o<d.offset&&(o=d.offset),e.css("left",o+"px"),e.css("top",d.top+d.height+"px")}};return d}]).directive("uiGridColumnMenu",["$timeout","gridUtil","uiGridConstants","uiGridColumnMenuService",function(a,b,c,d){var e={priority:0,scope:!0,require:"?^uiGrid",templateUrl:"ui-grid/uiGridColumnMenu",replace:!0,link:function(b,c,e,f){var g=this;d.initialize(b,f),b.defaultMenuItems=d.getDefaultMenuItems(b),b.menuItems=b.defaultMenuItems,d.setColMenuItemWatch(b),b.showMenu=function(a,e,f){b.col=a;var h=d.getColumnElementPosition(b,a,e);b.menuShown?(b.colElement=e,b.colElementPosition=h,b.hideThenShow=!0,b.$broadcast("hide-menu",{originalEvent:f})):(g.shown=b.menuShown=!0,d.repositionMenu(b,a,h,c,e),b.colElement=e,b.colElementPosition=h,b.$broadcast("show-menu",{originalEvent:f}))},b.hideMenu=function(a){b.menuShown=!1,a||b.$broadcast("hide-menu")},b.$on("menu-hidden",function(){b.hideThenShow?(delete b.hideThenShow,d.repositionMenu(b,b.col,b.colElementPosition,c,b.colElement),b.$broadcast("show-menu"),b.menuShown=!0):b.hideMenu(!0)}),b.$on("menu-shown",function(){a(function(){d.repositionMenu(b,b.col,b.colElementPosition,c,b.colElement),delete b.colElementPosition,delete b.columnElement},200)}),b.sortColumn=function(a,c){a.stopPropagation(),b.grid.sortColumn(b.col,c,!0).then(function(){b.grid.refresh(),b.hideMenu()})},b.unsortColumn=function(){b.col.unsort(),b.grid.refresh(),b.hideMenu()},b.hideColumn=function(){b.col.colDef.visible=!1,b.grid.refresh(),b.hideMenu()}},controller:["$scope",function(a){var b=this;a.$watch("menuItems",function(a){b.menuItems=a})}]};return e}])}(),function(){"use strict";angular.module("ui.grid").directive("uiGridFooterCell",["$timeout","gridUtil","uiGridConstants","$compile",function(a,b,c,d){var e={priority:0,scope:{col:"=",row:"=",renderIndex:"="},replace:!0,require:"^uiGrid",compile:function(){return{pre:function(a,b){var c=d(a.col.footerCellTemplate)(a);b.append(c)},post:function(a,b,d,e){a.grid=e.grid,a.getExternalScopes=e.getExternalScopes,b.addClass(a.col.getColClass(!1));var f,g=function(){var c=b;f&&(c.removeClass(f),f=null),f=angular.isFunction(a.col.footerCellClass)?a.col.footerCellClass(a.grid,a.row,a.col,a.rowRenderIndex,a.colRenderIndex):a.col.footerCellClass,c.addClass(f)};a.col.footerCellClass&&g();var h=a.grid.registerDataChangeCallback(g,[c.dataChange.COLUMN]);a.$on("$destroy",function(){a.grid.deregisterDataChangeCallback(h)})}}}};return e}])}(),function(){"use strict";angular.module("ui.grid").directive("uiGridFooter",["$templateCache","$compile","uiGridConstants","gridUtil","$timeout",function(a,b,c,d){var e="ui-grid/ui-grid-footer";return{restrict:"EA",replace:!0,require:["^uiGrid","^uiGridRenderContainer"],scope:!0,compile:function(){return{pre:function(a,c,f,g){var h=g[0],i=g[1];a.grid=h.grid,a.colContainer=i.colContainer,a.getExternalScopes=h.getExternalScopes,i.footer=c;var j=a.grid.options.footerTemplate?a.grid.options.footerTemplate:e;d.getTemplate(j).then(function(d){var e=angular.element(d),f=b(e)(a);if(c.append(f),i){var g=c[0].getElementsByClassName("ui-grid-footer-viewport")[0];g&&(i.footerViewport=g)}})},post:function(a,b,c,e){{var f=e[0],g=e[1];f.grid}d.disableAnimations(b),g.footer=b;var h=b[0].getElementsByClassName("ui-grid-footer-viewport")[0];h&&(g.footerViewport=h)}}}}}])}(),function(){"use strict";angular.module("ui.grid").directive("uiGridGroupPanel",["$compile","uiGridConstants","gridUtil",function(a,b,c){var d="ui-grid/ui-grid-group-panel";return{restrict:"EA",replace:!0,require:"?^uiGrid",scope:!1,compile:function(){return{pre:function(b,e){var f=b.grid.options.groupPanelTemplate||d;c.getTemplate(f).then(function(c){var d=angular.element(c),f=a(d)(b);e.append(f)})},post:function(a,b){b.bind("$destroy",function(){})}}}}}])}(),function(){"use strict";angular.module("ui.grid").directive("uiGridHeaderCell",["$compile","$timeout","$window","$document","gridUtil","uiGridConstants",function(a,b,c,d,e,f){var g=500,h={priority:0,scope:{col:"=",row:"=",renderIndex:"="},require:["?^uiGrid","^uiGridRenderContainer"],replace:!0,compile:function(){return{pre:function(b,c){var d=a(b.col.headerCellTemplate)(b);c.append(d)},post:function(a,c,d,e){function h(b){var c=!1;b.shiftKey&&(c=!0),i.grid.sortColumn(a.col,c).then(function(){i.columnMenuScope&&i.columnMenuScope.hideMenu(),i.grid.refresh()})}var i=e[0],j=e[1];a.grid=i.grid,a.getExternalScopes=i.getExternalScopes,a.renderContainer=i.grid.renderContainers[j.containerId],c.addClass(a.col.getColClass(!1)),a.menuShown=!1,a.asc=f.ASC,a.desc=f.DESC;var k,l=(angular.element(c[0].querySelectorAll(".ui-grid-header-cell-menu")),angular.element(c[0].querySelectorAll(".ui-grid-cell-contents"))),m=function(){var b=c;k&&(b.removeClass(k),k=null),k=angular.isFunction(a.col.headerCellClass)?a.col.headerCellClass(a.grid,a.row,a.col,a.rowRenderIndex,a.colRenderIndex):a.col.headerCellClass,b.addClass(k)};a.col.headerCellClass&&m();var n=a.grid.registerDataChangeCallback(m,[f.dataChange.COLUMN]),o=function(){a.grid.deregisterDataChangeCallback(n)};if(a.$on("$destroy",o),a.sortable=i.grid.options.enableSorting&&a.col.enableSorting?!0:!1,a.filterable=i.grid.options.enableFiltering&&a.col.enableFiltering?!0:!1,a.colMenu=a.col.grid.options&&a.col.grid.options.enableColumnMenus!==!1&&a.col.colDef&&a.col.colDef.enableColumnMenu!==!1?!0:!1,a.sortable||a.colMenu){var p,q=0;l.on("mousedown touchstart",function(d){"undefined"!=typeof d.originalEvent&&void 0!==d.originalEvent&&(d=d.originalEvent),d.button&&0!==d.button||(q=(new Date).getTime(),p=b(function(){},g),p.then(function(){a.colMenu&&i.columnMenuScope.showMenu(a.col,c,d)}))}),l.on("mouseup touchend",function(){b.cancel(p)}),a.$on("$destroy",function(){l.off("mousedown touchstart")})}if(a.toggleMenu=function(b){b.stopPropagation(),i.columnMenuScope.menuShown&&i.columnMenuScope.col===a.col?i.columnMenuScope.hideMenu():i.columnMenuScope.showMenu(a.col,c)},a.sortable&&(l.on("click touchend",function(a){a.stopPropagation(),b.cancel(p);var c=(new Date).getTime(),d=c-q;d>g||h(a)}),a.$on("$destroy",function(){b.cancel(p)})),a.filterable){var r=[];angular.forEach(a.col.filters,function(b,c){r.push(a.$watch("col.filters["+c+"].term",function(a,b){a!==b&&(i.grid.api.core.raise.filterChanged(),i.grid.refresh().then(function(){i.prevScrollArgs&&i.prevScrollArgs.y&&i.prevScrollArgs.y.percentage&&i.fireScrollingEvent({y:{percentage:i.prevScrollArgs.y.percentage}})}))}))}),a.$on("$destroy",function(){angular.forEach(r,function(a){a()})})}}}}};return h}])}(),function(){"use strict";angular.module("ui.grid").directive("uiGridHeader",["$templateCache","$compile","uiGridConstants","gridUtil","$timeout",function(a,b,c,d){var e="ui-grid/ui-grid-header",f="ui-grid/ui-grid-no-header";return{restrict:"EA",replace:!0,require:["^uiGrid","^uiGridRenderContainer"],scope:!0,compile:function(){return{pre:function(a,c,g,h){var i=h[0],j=h[1];a.grid=i.grid,a.colContainer=j.colContainer,a.getExternalScopes=i.getExternalScopes,j.header=c,j.colContainer.header=c;var k;k=a.grid.options.showHeader?a.grid.options.headerTemplate?a.grid.options.headerTemplate:e:f,d.getTemplate(k).then(function(d){var e=angular.element(d),f=b(e)(a);if(c.replaceWith(f),j.header=f,j.colContainer.header=f,c=f,j){var g=c[0].getElementsByClassName("ui-grid-header-viewport")[0];g&&(j.headerViewport=g)}})},post:function(a,b,c,e){function f(){var a=h.colContainer.getViewportWidth();"undefined"!=typeof g.grid.verticalScrollbarWidth&&void 0!==g.grid.verticalScrollbarWidth&&g.grid.verticalScrollbarWidth>0&&(a+=g.grid.verticalScrollbarWidth);var b=h.colContainer.visibleColumnCache,c=0,e=0,f=0,j=a,k=!1,l=function(b){return"manual"===b.widthType?+b.width:"percent"===b.widthType?parseInt(b.width.replace(/%/g,""),10)*a/100:"auto"===b.widthType?(0===f&&(f=parseInt(j/e,10)),b.width.length*f):void 0};b.forEach(function(a){a.widthType=null,isFinite(+a.width)?a.widthType="manual":d.endsWith(a.width,"%")?(a.widthType="percent",k=!0):angular.isString(a.width)&&-1!==a.width.indexOf("*")&&(a.widthType="auto",e+=a.width.length,k=!0)});var m=["manual","percent","auto"];if(b.filter(function(a){return a.visible&&a.widthType}).sort(function(a,b){return m.indexOf(a.widthType)-m.indexOf(b.widthType)}).forEach(function(a){var b=l(a);a.minWidth&&(b=Math.max(b,a.minWidth)),a.maxWidth&&(b=Math.min(b,a.maxWidth)),a.drawnWidth=Math.floor(b),c+=a.drawnWidth,j-=a.drawnWidth}),k&&j>0&&c>0&&a>c){var n=function(a){j>0&&("auto"===a.widthType||"percent"===a.widthType)&&(a.drawnWidth=a.drawnWidth+1,c+=1,j--)},o=0;do o=j,b.forEach(n);while(j>0&&j!==o)}c=Math.max(c,a);var p="";return b.forEach(function(a){p+=a.getColClassDefinition()}),i.verticalScrollbarWidth&&(c+=i.verticalScrollbarWidth),b.length>0&&(b[b.length-1].headerWidth=b[b.length-1].drawnWidth-30),h.colContainer.canvasWidth=parseInt(c,10),p}var g=e[0],h=e[1],i=g.grid;d.disableAnimations(b),h.header=b;var j=b[0].getElementsByClassName("ui-grid-header-viewport")[0];j&&(h.headerViewport=j),g&&g.grid.registerStyleComputation({priority:5,func:f})}}}}}])}(),function(){angular.module("ui.grid").service("uiGridGridMenuService",["gridUtil","i18nService",function(a,b){var c={initialize:function(a,b){b.gridMenuScope=a,a.grid=b,a.registeredMenuItems=[],a.$on("$destroy",function(){a.grid&&a.grid.gridMenuScope&&(a.grid.gridMenuScope=null),a.grid&&(a.grid=null),a.registeredMenuItems&&(a.registeredMenuItems=null)}),a.registeredMenuItems=[],b.api.registerMethod("core","addToGridMenu",c.addToGridMenu),b.api.registerMethod("core","removeFromGridMenu",c.removeFromGridMenu)},addToGridMenu:function(b,c){angular.isArray(c)?b.gridMenuScope?(b.gridMenuScope.registeredMenuItems=b.gridMenuScope.registeredMenuItems?b.gridMenuScope.registeredMenuItems:[],b.gridMenuScope.registeredMenuItems=b.gridMenuScope.registeredMenuItems.concat(c)):a.logError("Asked to addToGridMenu, but gridMenuScope not present.  Timing issue?  Please log issue with ui-grid"):a.logError("addToGridMenu: menuItems must be an array, and is not, not adding any items")},removeFromGridMenu:function(b,c){var d=-1;b&&b.gridMenuScope&&b.gridMenuScope.registeredMenuItems.forEach(function(b,e){b.id===c&&(d>-1?a.logError("removeFromGridMenu: found multiple items with the same id, removing only the last"):d=e)}),d>-1&&b.gridMenuScope.registeredMenuItems.splice(d,1)},getMenuItems:function(b){var d=[];return b.grid.options.gridMenuCustomItems&&(angular.isArray(b.grid.options.gridMenuCustomItems)?d=d.concat(b.grid.options.gridMenuCustomItems):a.logError("gridOptions.gridMenuCustomItems must be an array, and is not")),d=d.concat(b.registeredMenuItems),b.grid.options.gridMenuShowHideColumns!==!1&&(d=d.concat(c.showHideColumns(b))),d},showHideColumns:function(a){var d=[];return a.grid.options.columnDefs&&0!==a.grid.options.columnDefs.length&&0!==a.grid.columns.length?(d.push({title:b.getSafeText("gridMenu.columns")}),a.grid.options.gridMenuTitleFilter=a.grid.options.gridMenuTitleFilter?a.grid.options.gridMenuTitleFilter:function(a){return a},a.grid.options.columnDefs.forEach(function(b){if(b.enableHiding!==!1){var e={icon:"ui-grid-icon-ok",action:function(a){a.stopPropagation(),c.toggleColumnVisibility(this.context.gridCol)},shown:function(){return this.context.gridCol.colDef.visible===!0||void 0===this.context.gridCol.colDef.visible},context:{gridCol:a.grid.getColumn(b.name||b.field)}};c.setMenuItemTitle(e,b,a.grid),d.push(e),e={icon:"ui-grid-icon-cancel",action:function(a){a.stopPropagation(),c.toggleColumnVisibility(this.context.gridCol)},shown:function(){return!(this.context.gridCol.colDef.visible===!0||void 0===this.context.gridCol.colDef.visible)},context:{gridCol:a.grid.getColumn(b.name||b.field)}},c.setMenuItemTitle(e,b,a.grid),d.push(e)}}),d):d},setMenuItemTitle:function(b,c,d){var e=d.options.gridMenuTitleFilter(c.displayName||c.name||c.field);"string"==typeof e?b.title=e:e.then?(b.title="",e.then(function(a){b.title=a},function(a){b.title=a})):(a.logError("Expected gridMenuTitleFilter to return a string or a promise, it has returned neither, bad config"),b.title="badconfig")},toggleColumnVisibility:function(a){a.colDef.visible=!(a.colDef.visible===!0||void 0===a.colDef.visible),a.grid.refresh()}};return c}]).directive("uiGridMenuButton",["gridUtil","uiGridConstants","uiGridGridMenuService",function(a,b,c){return{priority:0,scope:!0,require:["?^uiGrid"],templateUrl:"ui-grid/ui-grid-menu-button",replace:!0,link:function(a,b,d,e){var f=e[0];c.initialize(a,f.grid),a.shown=!1,a.toggleMenu=function(){a.shown?(a.$broadcast("hide-menu"),a.shown=!1):(a.menuItems=c.getMenuItems(a),a.$broadcast("show-menu"),a.shown=!0)},a.$on("menu-hidden",function(){a.shown=!1})}}}])}(),function(){angular.module("ui.grid").directive("uiGridMenu",["$compile","$timeout","$window","$document","gridUtil","uiGridConstants",function(a,b,c,d,e,f){var g={priority:0,scope:{menuItems:"=",autoHide:"=?"},require:"?^uiGrid",templateUrl:"ui-grid/uiGridMenu",replace:!1,link:function(a){var d=this;d.showMenu=a.showMenu=function(c,d){a.shown?a.shownMid||(a.shownMid=!0,a.$emit("menu-shown")):(a.shown=!0,b(function(){a.shownMid=!0,a.$emit("menu-shown")}));var f="click";d&&d.originalEvent&&d.originalEvent.type&&"touchstart"===d.originalEvent.type&&(f=d.originalEvent.type),angular.element(document).off("click touchstart",e),b(function(){angular.element(document).on(f,e)})},d.hideMenu=a.hideMenu=function(){a.shown&&(a.shownMid=!1,b(function(){a.shownMid||(a.shown=!1,a.$emit("menu-hidden"))},200)),angular.element(document).off("click touchstart",e)},a.$on("hide-menu",function(b,c){a.hideMenu(b,c)}),a.$on("show-menu",function(b,c){a.showMenu(b,c)});var e=function(){a.shown&&a.$apply(function(){a.hideMenu()})};("undefined"==typeof a.autoHide||void 0===a.autoHide)&&(a.autoHide=!0),a.autoHide&&angular.element(c).on("resize",e),a.$on("$destroy",function(){angular.element(document).off("click touchstart",e)}),a.$on("$destroy",function(){angular.element(c).off("resize",e)}),a.$on("$destroy",a.$on(f.events.GRID_SCROLL,e)),a.$on("$destroy",a.$on(f.events.ITEM_DRAGGING,e))},controller:["$scope","$element","$attrs",function(){}]};return g}]).directive("uiGridMenuItem",["gridUtil","$compile","i18nService",function(a,b,c){var d={priority:0,scope:{name:"=",active:"=",action:"=",icon:"=",shown:"=",context:"=",templateUrl:"="},require:["?^uiGrid","^uiGridMenu"],templateUrl:"ui-grid/uiGridMenuItem",replace:!0,compile:function(){return{pre:function(c,d,e,f){f[0],f[1];c.templateUrl&&a.getTemplate(c.templateUrl).then(function(a){var e=angular.element(a),f=b(e)(c);d.replaceWith(f)})},post:function(a,b,d,e){{var f=e[0];e[1]}("undefined"==typeof a.shown||null===a.shown)&&(a.shown=function(){return!0}),a.itemShown=function(){var b={};return a.context&&(b.context=a.context),"undefined"!=typeof f&&f&&(b.grid=f.grid),a.shown.call(b)},a.itemAction=function(b,c){if(b.stopPropagation(),"function"==typeof a.action){var d={};a.context&&(d.context=a.context),"undefined"!=typeof f&&f&&(d.grid=f.grid),a.action.call(d,b,c),a.$emit("hide-menu")}},a.i18n=c.get()}}}};return d}])}(),function(){angular.module("ui.grid").directive("uiGridNativeScrollbar",["$timeout","$document","uiGridConstants","gridUtil",function(a,b,c,d){var e=d.getScrollbarWidth();angular.isNumber(e)||(e=0);var f=d.detectBrowser();return"ie"===f&&(e+=1),{scope:{type:"@"},require:["^uiGrid","^uiGridRenderContainer"],link:function(a,b,f,g){function h(){var a=n.getViewportHeight(),b=n.getCanvasHeight(),d=o.headerHeight?o.headerHeight:p.headerHeight,e=p.options.enableVerticalScrollbar===c.scrollbars.WHEN_NEEDED?"overflow-y:auto;":"",f=".grid"+p.id+" .ui-grid-render-container-"+m.containerId+" .ui-grid-native-scrollbar.vertical .contents { height: "+b+"px; }";return f+="\n .grid"+p.id+" .ui-grid-render-container-"+m.containerId+" .ui-grid-native-scrollbar.vertical { height: "+a+"px; top: "+d+"px;"+e+"}",s=b,f}function i(){var a=o.getCanvasWidth(),b=u;p.options.showFooter&&(b-=1);var d=o.getViewportAdjustment();b-=d.height;var e=p.options.enableHorizontalScrollbar===c.scrollbars.WHEN_NEEDED?"overflow-x:auto":"",f=".grid"+p.id+" .ui-grid-render-container-"+m.containerId+" .ui-grid-native-scrollbar.horizontal { bottom: "+b+"px;"+e+" }";return f+=".grid"+p.id+" .ui-grid-render-container-"+m.containerId+" .ui-grid-native-scrollbar.horizontal .contents { width: "+a+"px; }",s=a,f}function j(){if("vertical"===a.type){p.flagScrollingVertically();var c=b[0].scrollTop,e=n.getCanvasHeight()-n.getViewportHeight();p.horizontalScrollbarHeight&&p.horizontalScrollbarHeight>0&&(e-=l.grid.horizontalScrollbarHeight);var f=c/e;f>1&&(f=1),0>f&&(f=0);var g={target:b,y:{percentage:f}};a.scrollSource?a.scrollSource=null:l.fireScrollingEvent(g),r=c}else if("horizontal"===a.type){p.flagScrollingHorizontally();var h=d.normalizeScrollLeft(b),i=o.getCanvasWidth()-o.getViewportWidth(),j=h/i,k={target:b,x:{percentage:j}};a.scrollSource?a.scrollSource=null:l.fireScrollingEvent(k),r=h}}function k(c,e){if(!e.target||e.target!==b&&!angular.element(e.target).hasClass("ui-grid-native-scrollbar"))if(a.scrollSource=e.target,"vertical"===a.type){if(e.y&&"undefined"!=typeof e.y.percentage&&void 0!==e.y.percentage){p.flagScrollingVertically();var f=n.getCanvasHeight()-n.getViewportHeight(),g=Math.max(0,e.y.percentage*f);b[0].scrollTop=g}}else if("horizontal"===a.type&&e.x&&"undefined"!=typeof e.x.percentage&&void 0!==e.x.percentage){p.flagScrollingHorizontally();var h=o.getCanvasWidth()-o.getViewportWidth(),i=Math.max(0,e.x.percentage*h);b[0].scrollLeft=d.denormalizeScrollLeft(b,i)}}var l=g[0],m=g[1],n=m.rowContainer,o=m.colContainer,p=l.grid,q=angular.element('<div class="contents">&nbsp;</div>');b.addClass("ui-grid-native-scrollbar");var r,s=0;"vertical"===a.type?(b.css("width",e+"px"),b.addClass("vertical"),p.verticalScrollbarWidth=p.options.enableVerticalScrollbar===c.scrollbars.WHEN_NEEDED?0:e,o.verticalScrollbarWidth=p.verticalScrollbarWidth,r=b[0].scrollTop):"horizontal"===a.type&&(b.css("height",e+"px"),b.addClass("horizontal"),p.horizontalScrollbarHeight=p.options.enableHorizontalScrollbar===c.scrollbars.WHEN_NEEDED?0:e,n.horizontalScrollbarHeight=p.horizontalScrollbarHeight,r=d.normalizeScrollLeft(b)),b.append(q),"vertical"===a.type?s=d.elementHeight(b):"horizontal"===a.type&&(s=d.elementWidth(b));var t=d.closestElm(b,".ui-grid"),u=d.getBorderSize(t,"bottom");"vertical"===a.type?p.registerStyleComputation({priority:6,func:h}):"horizontal"===a.type&&p.registerStyleComputation({priority:6,func:i}),a.scrollSource=null,b.on("scroll",j),b.on("$destroy",function(){b.off("scroll")});var v=a.$on(c.events.GRID_SCROLL,k);a.$on("$destroy",v)}}}])}(),function(){"use strict";var a=angular.module("ui.grid");a.directive("uiGridRenderContainer",["$timeout","$document","uiGridConstants","gridUtil",function(a,b,c,d){return{replace:!0,transclude:!0,templateUrl:"ui-grid/uiGridRenderContainer",require:["^uiGrid","uiGridRenderContainer"],scope:{containerId:"=",rowContainerName:"=",colContainerName:"=",bindScrollHorizontal:"=",bindScrollVertical:"=",enableVerticalScrollbar:"=",enableHorizontalScrollbar:"="},controller:"uiGridRenderContainer as RenderContainer",compile:function(){return{pre:function(a,b,c,d){var e=d[0],f=d[1],g=a.grid=e.grid;if(!a.rowContainerName)throw"No row render container name specified";if(!a.colContainerName)throw"No column render container name specified";if(!g.renderContainers[a.rowContainerName])throw"Row render container '"+a.rowContainerName+"' is not registered.";if(!g.renderContainers[a.colContainerName])throw"Column render container '"+a.colContainerName+"' is not registered.";var h=a.rowContainer=g.renderContainers[a.rowContainerName],i=a.colContainer=g.renderContainers[a.colContainerName];f.containerId=a.containerId,f.rowContainer=h,f.colContainer=i},post:function(e,f,g,h){function i(a,b){if(b.y&&e.bindScrollVertical){n.prevScrollArgs=b;var c=p.getCanvasHeight()-p.getViewportHeight();o.horizontalScrollbarHeight&&o.horizontalScrollbarHeight>0&&(c+=o.horizontalScrollbarHeight);var f,g=n.viewport[0].scrollTop;if("undefined"!=typeof b.y.percentage&&void 0!==b.y.percentage)f=b.y.percentage;else{if("undefined"==typeof b.y.pixels||void 0===b.y.pixels)throw new Error("No percentage or pixel value provided for scroll event Y axis");f=b.y.percentage=(g+b.y.pixels)/c}var h=Math.max(0,f*c);n.viewport[0].scrollTop=h,n.prevScrollArgs.y.pixels=h-g}if(b.x&&e.bindScrollHorizontal){n.prevScrollArgs=b;var i,j=q.getCanvasWidth()-q.getViewportWidth(),k=d.normalizeScrollLeft(n.viewport);if("undefined"!=typeof b.x.percentage&&void 0!==b.x.percentage)i=b.x.percentage;else{if("undefined"==typeof b.x.pixels||void 0===b.x.pixels)throw new Error("No percentage or pixel value provided for scroll event X axis");i=b.x.percentage=(k+b.x.pixels)/j}var l=Math.max(0,i*j);n.viewport[0].scrollLeft=d.denormalizeScrollLeft(n.viewport,l),n.prevScrollLeft=l,n.headerViewport&&(n.headerViewport.scrollLeft=d.denormalizeScrollLeft(n.headerViewport,l)),n.footerViewport&&(n.footerViewport.scrollLeft=d.denormalizeScrollLeft(n.footerViewport,l)),n.prevScrollArgs.x.pixels=l-k}}function j(a){a.originalEvent&&(a=a.originalEvent),a.preventDefault();var b,c,d,e;d=a.targetTouches[0].screenX,e=a.targetTouches[0].screenY,b=-(d-v),c=-(e-u),y=1>c?-1:1,z=1>b?-1:1,c*=2,b*=2;var f={target:a.target};if(0!==c){var g=(w+c)/(p.getCanvasHeight()-p.getViewportHeight());g>1?g=1:0>g&&(g=0),f.y={percentage:g,pixels:c}}if(0!==b){var h=(x+b)/(q.getCanvasWidth()-q.getViewportWidth());h>1?h=1:0>h&&(h=0),f.x={percentage:h,pixels:b}}m.fireScrollingEvent(f)}function k(c){function d(){a(function(){var a={target:c.target};if(0!==u){var b=(n.viewport[0].scrollTop+u)/(p.getCanvasHeight()-p.getViewportHeight());a.y={percentage:b,pixels:u}}if(0!==v){var e=(n.viewport[0].scrollLeft+v)/(q.getCanvasWidth()-q.getViewportWidth());a.x={percentage:e,pixels:v}}m.fireScrollingEvent(a),s-=1,u/=2,v/=2,s>0?d():m.scrollbars.forEach(function(a){a.removeClass("ui-grid-scrollbar-visible"),a.removeClass("ui-grid-scrolling")})},r)}c.originalEvent&&(c=c.originalEvent),c.preventDefault(),b.unbind("touchmove",j),b.unbind("touchend",k),b.unbind("touchcancel",k);var e=n.viewport[0].scrollTop,f=n.viewport[0].scrollTop,g=Math.abs(e-w),h=Math.abs(f-x),i=new Date-t,l=g/i,o=h/i,r=63,s=8,u=120*y*l,v=120*z*o;d()}function l(){var a="",b=q.getCanvasWidth(),c=q.getViewportWidth(),d=p.getCanvasHeight(),f=p.getViewportHeight(),g=q.getHeaderViewportWidth(),h=q.getHeaderViewportWidth();return a+="\n .grid"+m.grid.id+" .ui-grid-render-container-"+e.containerId+" .ui-grid-canvas { width: "+b+"px; height: "+d+"px; }",a+="\n .grid"+m.grid.id+" .ui-grid-render-container-"+e.containerId+" .ui-grid-header-canvas { width: "+b+"px; }",a+="\n .grid"+m.grid.id+" .ui-grid-render-container-"+e.containerId+" .ui-grid-viewport { width: "+c+"px; height: "+f+"px; }",a+="\n .grid"+m.grid.id+" .ui-grid-render-container-"+e.containerId+" .ui-grid-header-viewport { width: "+g+"px; }",a+="\n .grid"+m.grid.id+" .ui-grid-render-container-"+e.containerId+" .ui-grid-footer-canvas { width: "+b+"px; }",a+="\n .grid"+m.grid.id+" .ui-grid-render-container-"+e.containerId+" .ui-grid-footer-viewport { width: "+h+"px; }",void 0!==r.explicitHeaderHeight&&null!==r.explicitHeaderHeight&&r.explicitHeaderHeight>0?a+="\n .grid"+m.grid.id+" .ui-grid-render-container-"+e.containerId+" .ui-grid-header-cell { height: "+r.explicitHeaderHeight+"px; }":void 0!==r.innerHeaderHeight&&null!==r.innerHeaderHeight&&r.innerHeaderHeight>0&&(a+="\n .grid"+m.grid.id+" .ui-grid-render-container-"+e.containerId+" .ui-grid-header-cell { height: "+r.innerHeaderHeight+"px; }"),a}var m=h[0],n=h[1],o=m.grid,p=n.rowContainer,q=n.colContainer,r=o.renderContainers[e.containerId];f.addClass("ui-grid-render-container-"+e.containerId);var s;(e.bindScrollHorizontal||e.bindScrollVertical)&&(s=e.$on(c.events.GRID_SCROLL,i)),f.bind("wheel mousewheel DomMouseScroll MozMousePixelScroll",function(a){a.preventDefault();var b=d.normalizeWheelEvent(a),c={target:f};if(0!==b.deltaY){var e=-120*b.deltaY,g=(n.viewport[0].scrollTop+e)/(p.getCanvasHeight()-p.getViewportHeight());0>g?g=0:g>1&&(g=1),c.y={percentage:g,pixels:e}}if(0!==b.deltaX){var h=-120*b.deltaX,i=d.normalizeScrollLeft(n.viewport),j=(i+h)/(q.getCanvasWidth()-q.getViewportWidth());0>j?j=0:j>1&&(j=1),c.x={percentage:j,pixels:h}}m.fireScrollingEvent(c)});var t,u=0,v=0,w=0,x=0,y=1,z=1;d.isTouchEnabled()&&f.bind("touchstart",function(a){a.originalEvent&&(a=a.originalEvent),a.preventDefault(),m.scrollbars.forEach(function(a){a.addClass("ui-grid-scrollbar-visible"),a.addClass("ui-grid-scrolling")}),t=new Date,u=a.targetTouches[0].screenY,v=a.targetTouches[0].screenX,w=n.viewport[0].scrollTop,x=n.viewport[0].scrollLeft,b.on("touchmove",j),b.on("touchend touchcancel",k)}),f.bind("$destroy",function(){s(),f.unbind("keydown"),["touchstart","touchmove","touchend","keydown","wheel","mousewheel","DomMouseScroll","MozMousePixelScroll"].forEach(function(a){f.unbind(a)})}),m.grid.registerStyleComputation({priority:6,func:l})}}}}}]),a.controller("uiGridRenderContainer",["$scope","gridUtil",function(a){var b=this;b.rowStyle=function(c){var d=a.grid.renderContainers[a.containerId],e={};if(!d.disableRowOffset&&0===c&&0!==b.currentTopRow){var f=a.rowContainer.currentTopRow*a.rowContainer.visibleRowCache[a.rowContainer.currentTopRow].height;e["margin-top"]=f+"px"}return d.disableColumnOffset||0===a.colContainer.currentFirstColumn||(a.grid.isRTL()?e["margin-right"]=a.colContainer.columnOffset+"px":e["margin-left"]=a.colContainer.columnOffset+"px"),e},b.columnStyle=function(b){var c=a.grid.renderContainers[a.containerId];if(!c.disableColumnOffset&&0===b&&0!==a.colContainer.currentFirstColumn){var d=a.colContainer.columnOffset;return a.grid.isRTL()?{"margin-right":d+"px"}:{"margin-left":d+"px"}}return null}}])}(),function(){"use strict";angular.module("ui.grid").directive("uiGridRow",["gridUtil",function(){return{replace:!0,require:["^uiGrid","^uiGridRenderContainer"],scope:{row:"=uiGridRow",rowRenderIndex:"="},compile:function(){return{pre:function(a,b,c,d){var e=d[0],f=d[1],g=e.grid;a.grid=e.grid,a.colContainer=f.colContainer,g.getRowTemplateFn.then(function(c){c(a,function(a){b.replaceWith(a)})})},post:function(a,b,c,d){{var e=d[0];d[1]}a.getExternalScopes=e.getExternalScopes}}}}}])}(),function(){angular.module("ui.grid").directive("uiGridStyle",["gridUtil","$interpolate",function(a,b){return{link:function(a,c){var d=b(c.text(),!0);d&&a.$watch(d,function(a){c.text(a)})}}}])}(),function(){"use strict";angular.module("ui.grid").directive("uiGridViewport",["gridUtil",function(a){return{replace:!0,scope:{},templateUrl:"ui-grid/uiGridViewport",require:["^uiGrid","^uiGridRenderContainer"],link:function(b,c,d,e){var f=e[0],g=e[1];
b.containerCtrl=g;{var h=g.rowContainer,i=g.colContainer;f.grid}b.grid=f.grid,b.rowContainer=g.rowContainer,b.colContainer=g.colContainer,g.viewport=c,c.on("scroll",function(){var d=c[0].scrollTop,e=a.normalizeScrollLeft(c),g=-1,j=-1;if(e!==i.prevScrollLeft){var k=(e-i.prevScrollLeft,i.getCanvasWidth()-i.getViewportWidth());g=e/k,i.adjustScrollHorizontal(e,g)}if(d!==h.prevScrollTop){var l=(d-h.prevScrollTop,h.getCanvasHeight()-h.getViewportHeight());j=d/l,j>1&&(j=1),0>j&&(j=0),h.adjustScrollVertical(d,j)}if(!b.grid.isScrollingVertically&&!b.grid.isScrollingHorizontally){var m={};g>-1&&(m.x={percentage:g}),j>-1&&(m.y={percentage:j}),f.fireScrollingEvent(m)}})}}}])}(),function(){angular.module("ui.grid").directive("uiGridVisible",function(){return function(a,b,c){a.$watch(c.uiGridVisible,function(a){b[a?"removeClass":"addClass"]("ui-grid-invisible")})}})}(),function(){"use strict";angular.module("ui.grid").controller("uiGridController",["$scope","$element","$attrs","gridUtil","$q","uiGridConstants","$templateCache","gridClassFactory","$timeout","$parse","$compile",function(a,b,c,d,e,f,g,h,i,j,k){function l(a,b){a&&a!==b&&(n.grid.options.columnDefs=a,n.grid.buildColumns().then(function(){n.grid.preCompileCellTemplates(),n.grid.callDataChangeCallbacks(f.dataChange.COLUMN)}))}function m(b){var d=[];b&&(n.grid.columns.length===(n.grid.rowHeaderColumns?n.grid.rowHeaderColumns.length:0)&&!c.uiGridColumns&&0===n.grid.options.columnDefs.length&&b.length>0&&n.grid.buildColumnDefsFromData(b),(n.grid.options.columnDefs.length>0||b.length>0)&&d.push(n.grid.buildColumns().then(function(){n.grid.preCompileCellTemplates()})),e.all(d).then(function(){n.grid.modifyRows(b).then(function(){n.grid.redrawInPlace(),a.$evalAsync(function(){n.grid.refreshCanvas(!0),n.grid.callDataChangeCallbacks(f.dataChange.ROW)})})}))}var n=this;n.grid=h.createGrid(a.uiGrid),b.addClass("grid"+n.grid.id),n.grid.rtl="rtl"===d.getStyles(b[0]).direction,n.getExternalScopes=a.getExternalScopes,a.grid=n.grid,c.uiGridColumns&&c.$observe("uiGridColumns",function(a){n.grid.options.columnDefs=a,n.grid.buildColumns().then(function(){n.grid.preCompileCellTemplates(),n.grid.refreshCanvas(!0)})});var o;o=angular.isString(a.uiGrid.data)?a.$parent.$watchCollection(a.uiGrid.data,m):a.$parent.$watchCollection(function(){return a.uiGrid.data},m);var p=a.$parent.$watchCollection(function(){return a.uiGrid.columnDefs},l);a.$on("$destroy",function(){o(),p()}),a.$watch(function(){return n.grid.styleComputations},function(){n.grid.refreshCanvas(!0)}),n.fireScrollingEvent=d.throttle(function(b){a.$broadcast(f.events.GRID_SCROLL,b)},n.grid.options.scrollThrottle,{trailing:!0}),n.fireEvent=function(b,c){("undefined"==typeof c||void 0===c)&&(c={}),("undefined"==typeof c.grid||void 0===c.grid)&&(c.grid=n.grid),a.$broadcast(b,c)},n.innerCompile=function(b){k(b)(a)}}]),angular.module("ui.grid").directive("uiGrid",["$compile","$templateCache","gridUtil","$window","uiGridConstants",function(a,b,c,d,e){return{templateUrl:"ui-grid/ui-grid",scope:{uiGrid:"=",getExternalScopes:"&?externalScopes"},replace:!0,transclude:!0,controller:"uiGridController",compile:function(){return{post:function(a,b,f,g){function h(){i.gridWidth=a.gridWidth=c.elementWidth(b),i.gridHeight=a.gridHeight=c.elementHeight(b),i.queueRefresh()}var i=g.grid;if(g.scrollbars=[],i.renderingComplete(),i.element=b,i.gridWidth=a.gridWidth=c.elementWidth(b),i.canvasWidth=g.grid.gridWidth,i.gridHeight=a.gridHeight=c.elementHeight(b),i.gridHeight<i.options.rowHeight){var j=i.options.minRowsToShow*i.options.rowHeight,k=i.options.showHeader?i.options.headerRowHeight:0,l=i.options.showFooter?i.options.footerRowHeight:0,m=0;i.options.enableHorizontalScrollbar===e.scrollbars.ALWAYS&&(m=c.getScrollbarWidth());var n=0;angular.forEach(i.options.columnDefs,function(a){a.hasOwnProperty("filter")?1>n&&(n=1):a.hasOwnProperty("filters")&&n<a.filters.length&&(n=a.filters.length)});var o=n*k,p=k+j+l+m+o;b.css("height",p+"px"),i.gridHeight=a.gridHeight=c.elementHeight(b)}i.refreshCanvas();var q=angular.element('<div ng-if="grid.hasLeftContainer()" style="width: 0" ui-grid-pinned-container="\'left\'"></div>');b.prepend(q),g.innerCompile(q);var r=angular.element('<div  ng-if="grid.hasRightContainer()" style="width: 0" ui-grid-pinned-container="\'right\'"></div>');b.append(r),g.innerCompile(r),a.$watch(function(){return i.hasLeftContainer()},function(a,b){a!==b&&i.refreshCanvas(!0)}),a.$watch(function(){return i.hasRightContainer()},function(a,b){a!==b&&i.refreshCanvas(!0)}),angular.element(d).on("resize",h),b.on("$destroy",function(){angular.element(d).off("resize",h)})}}}}}])}(),function(){"use strict";angular.module("ui.grid").directive("uiGridPinnedContainer",["gridUtil",function(){return{restrict:"EA",replace:!0,template:'<div class="ui-grid-pinned-container"><div ui-grid-render-container container-id="side" row-container-name="\'body\'" col-container-name="side" bind-scroll-vertical="true" class="{{ side }} ui-grid-render-container-{{ side }}"></div></div>',scope:{side:"=uiGridPinnedContainer"},require:"^uiGrid",compile:function(){return{post:function(a,b,c,d){function e(){if("left"===a.side||"right"===a.side){for(var b=g.renderContainers[a.side].visibleColumnCache,c=0,d=0;d<b.length;d++){var e=b[d];c+=e.drawnWidth}h=c}}function f(){var c="";if("left"===a.side||"right"===a.side){e(),b.attr("style",null);var d=g.renderContainers.body.getViewportHeight();c+=".grid"+g.id+" .ui-grid-pinned-container-"+a.side+", .grid"+g.id+" .ui-grid-pinned-container-"+a.side+" .ui-grid-render-container-"+a.side+" .ui-grid-viewport { width: "+h+"px; height: "+d+"px; } "}return c}var g=d.grid,h=0;b.addClass("ui-grid-pinned-container-"+a.side),g.renderContainers.body.registerViewportAdjuster(function(a){return 0===h&&e(),a.width-=h,a}),g.registerStyleComputation({priority:15,func:f})}}}}}])}(),function(){angular.module("ui.grid").factory("Grid",["$q","$compile","$parse","gridUtil","uiGridConstants","GridOptions","GridColumn","GridRow","GridApi","rowSorter","rowSearcher","GridRenderContainer","$timeout",function(a,b,c,d,e,f,g,h,i,j,k,l,m){function n(){}var o=function(a){var b=this;if(void 0===a||"undefined"==typeof a.id||!a.id)throw new Error("No ID provided. An ID must be given when creating a grid.");if(!/^[_a-zA-Z0-9-]+$/.test(a.id))throw new Error("Grid id '"+a.id+'" is invalid. It must follow CSS selector syntax rules.');b.id=a.id,delete a.id,b.options=f.initialize(a),b.headerHeight=b.options.headerRowHeight,b.footerHeight=b.options.showFooter===!0?b.options.footerRowHeight:0,b.rtl=!1,b.gridHeight=0,b.gridWidth=0,b.columnBuilders=[],b.rowBuilders=[],b.rowsProcessors=[],b.columnsProcessors=[],b.styleComputations=[],b.viewportAdjusters=[],b.rowHeaderColumns=[],b.dataChangeCallbacks={},b.renderContainers={},b.renderContainers.body=new l("body",b),b.cellValueGetterCache={},b.getRowTemplateFn=null,b.rows=[],b.columns=[],b.isScrollingVertically=!1,b.isScrollingHorizontally=!1;var c=d.debounce(function(){b.isScrollingVertically=!1},300),g=d.debounce(function(){b.isScrollingHorizontally=!1},300);b.flagScrollingVertically=function(){b.isScrollingVertically=!0,c()},b.flagScrollingHorizontally=function(){b.isScrollingHorizontally=!0,g()},b.api=new i(b),b.api.registerMethod("core","refresh",this.refresh),b.api.registerMethod("core","refreshRows",this.refreshRows),b.api.registerMethod("core","handleWindowResize",this.handleWindowResize),b.api.registerMethod("core","addRowHeaderColumn",this.addRowHeaderColumn),b.api.registerMethod("core","sortHandleNulls",j.handleNulls),b.api.registerEvent("core","sortChanged"),b.api.registerMethod("core","notifyDataChange",this.notifyDataChange),b.registerDataChangeCallback(b.columnRefreshCallback,[e.dataChange.COLUMN]),b.registerDataChangeCallback(b.processRowsCallback,[e.dataChange.EDIT])};return o.prototype.isRTL=function(){return this.rtl},o.prototype.registerColumnBuilder=function(a){this.columnBuilders.push(a)},o.prototype.buildColumnDefsFromData=function(a){this.options.columnDefs=d.getColumnsFromData(a,this.options.excludeProperties)},o.prototype.registerRowBuilder=function(a){this.rowBuilders.push(a)},o.prototype.registerDataChangeCallback=function(a,b){var c=d.nextUid();return b||(b=[e.dataChange.ALL]),Array.isArray(b)||d.logError("Expected types to be an array or null in registerDataChangeCallback, value passed was: "+b),this.dataChangeCallbacks[c]={callback:a,types:b},c},o.prototype.deregisterDataChangeCallback=function(a){delete this.dataChangeCallbacks[a]},o.prototype.callDataChangeCallbacks=function(a){angular.forEach(this.dataChangeCallbacks,function(b){(-1!==b.types.indexOf(e.dataChange.ALL)||-1!==b.types.indexOf(a)||a===e.dataChange.ALL)&&b.callback(this)},this)},o.prototype.notifyDataChange=function(a,b){var c=e.dataChange;b===c.ALL||b===c.COLUMN||b===c.EDIT||b===c.ROW?a.callDataChangeCallbacks(b):d.logError("Notified of a data change, but the type was not recognised, so no action taken, type was: "+b)},o.prototype.columnRefreshCallback=function(a){a.buildColumns(),a.refresh()},o.prototype.processRowsCallback=function(a){a.refreshRows()},o.prototype.getColumn=function(a){var b=this.columns.filter(function(b){return b.colDef.name===a});return b.length>0?b[0]:null},o.prototype.getColDef=function(a){var b=this.options.columnDefs.filter(function(b){return b.name===a});return b.length>0?b[0]:null},o.prototype.assignTypes=function(){var a=this;a.options.columnDefs.forEach(function(b,c){if(!b.type){var e=new g(b,c,a),f=a.rows.length>0?a.rows[0]:null;f?b.type=d.guessType(a.getCellValue(f,e)):(d.logWarn("Unable to assign type from data, so defaulting to string"),b.type="string")}})},o.prototype.addRowHeaderColumn=function(a){var b=this,c=new g(a,b.rowHeaderColumns.length,b);c.isRowHeader=!0,b.isRTL()?(b.createRightContainer(),c.renderContainer="right"):(b.createLeftContainer(),c.renderContainer="left"),b.columnBuilders[0](a,c,b.options).then(function(){c.enableFiltering=!1,c.enableSorting=!1,c.enableHiding=!1,b.rowHeaderColumns.push(c),b.buildColumns().then(function(){b.preCompileCellTemplates(),b.handleWindowResize()})})},o.prototype.buildColumns=function(){var b,c=this,e=[],f=c.rowHeaderColumns.length;for(b=0;b<c.columns.length;b++)c.getColDef(c.columns[b].name)||(c.columns.splice(b,1),b--);return c.rowHeaderColumns.forEach(function(a){c.columns.unshift(a)}),c.options.columnDefs.forEach(function(a,b){c.preprocessColDef(a);var h=c.getColumn(a.name);h?h.updateColumnDef(a):(h=new g(a,d.nextUid(),c),c.columns.splice(b+f,0,h)),c.columnBuilders.forEach(function(b){e.push(b.call(c,a,h,c.options))})}),a.all(e)},o.prototype.preCompileCellTemplates=function(){var a=this;this.columns.forEach(function(c){var d=c.cellTemplate.replace(e.MODEL_COL_FIELD,a.getQualifiedColField(c));d=d.replace(e.COL_FIELD,"grid.getCellValue(row, col)");var f=b(d);c.compiledElementFn=f,c.compiledElementFnDefer&&c.compiledElementFnDefer.resolve(c.compiledElementFn)})},o.prototype.getQualifiedColField=function(a){return"row.entity."+d.preEval(a.field)},o.prototype.createLeftContainer=function(){this.hasLeftContainer()||(this.renderContainers.left=new l("left",this,{disableColumnOffset:!0}))},o.prototype.createRightContainer=function(){this.hasRightContainer()||(this.renderContainers.right=new l("right",this,{disableColumnOffset:!0}))},o.prototype.hasLeftContainer=function(){return void 0!==this.renderContainers.left},o.prototype.hasRightContainer=function(){return void 0!==this.renderContainers.right},o.prototype.preprocessColDef=function(a){if(!a.field&&!a.name)throw new Error("colDef.name or colDef.field property is required");void 0===a.name&&void 0!==a.field&&(a.name=a.field)},o.prototype.newInN=function(a,b,c,d){for(var e=this,f=[],g=0;g<b.length;g++){for(var h=d?b[g][d]:b[g],i=!1,j=0;j<a.length;j++){var k=c?a[j][c]:a[j];if(e.options.rowEquality(h,k)){i=!0;break}}i||f.push(h)}return f},o.prototype.getRow=function(a){var b=this,c=this.rows.filter(function(c){return b.options.rowEquality(c.entity,a)});return c.length>0?c[0]:null},o.prototype.modifyRows=function(b){var c,d,e,f,g=this;if((g.options.useExternalSorting||0===g.getColumnSorting().length)&&b.length>0){var i=g.rowHashMap;i||(i={get:function(){return null}}),g.createRowHashMap(),d=g.rowHashMap;var j=0===g.rows.length;for(g.rows.length=0,c=0;c<b.length;c++){var k=b[c];e=i.get(k),f=e?e.row:g.processRowBuilders(new h(k,c,g)),g.rows.push(f),d.put(k,{i:c,entity:k,row:f})}j&&g.assignTypes()}else if(0===g.rows.length&&b.length>0){if(g.options.enableRowHashing)for(g.rowHashMap||g.createRowHashMap(),c=0;c<b.length;c++)f=b[c],g.rowHashMap.put(f,{i:c,entity:f});g.addRows(b),g.assignTypes()}else if(b.length>0){var l,m,n;if(g.options.enableRowHashing){l=[],n=[];var o={};for(m=[],g.rowHashMap||g.createRowHashMap(),d=g.rowHashMap,c=0;c<b.length;c++){f=b[c];var p=!1;g.options.getRowIdentity(f)||(p=!0),e=d.get(f),e?e.row&&(o[g.options.rowIdentity(f)]=!0):(d.put(f,{i:c,entity:f}),p?n.push(f):l.push(f))}for(c=0;c<g.rows.length;c++){var q=g.rows[c],r=g.options.rowIdentity(q.entity);o[r]||m.push(q)}}var s=l||[],t=n||b;s=s.concat(g.newInN(g.rows,t,"entity")),g.addRows(s);var u=g.getDeletedRows(m||g.rows,b);for(c=0;c<u.length;c++)g.options.enableRowHashing&&g.rowHashMap.remove(u[c].entity),g.rows.splice(g.rows.indexOf(u[c]),1)}else g.createRowHashMap(),g.rows.length=0;var v=a.when(g.processRowsProcessors(g.rows)).then(function(a){return g.setVisibleRows(a)}),w=a.when(g.processColumnsProcessors(g.columns)).then(function(a){return g.setVisibleColumns(a)});return a.all([v,w])},o.prototype.getDeletedRows=function(a,b){var c=this,d=a.filter(function(a){return!b.some(function(b){return c.options.rowEquality(b,a.entity)})});return d},o.prototype.addRows=function(a){for(var b=this,c=b.rows.length,d=0;d<a.length;d++){var e=b.processRowBuilders(new h(a[d],d+c,b));if(b.options.enableRowHashing){var f=b.rowHashMap.get(e.entity);f&&(f.row=e)}b.rows.push(e)}},o.prototype.processRowBuilders=function(a){var b=this;return b.rowBuilders.forEach(function(c){c.call(b,a,b.options)}),a},o.prototype.registerStyleComputation=function(a){this.styleComputations.push(a)},o.prototype.registerRowsProcessor=function(a){if(!angular.isFunction(a))throw"Attempt to register non-function rows processor: "+a;this.rowsProcessors.push(a)},o.prototype.removeRowsProcessor=function(a){var b=this.rowsProcessors.indexOf(a);"undefined"!=typeof b&&void 0!==b&&this.rowsProcessors.splice(b,1)},o.prototype.processRowsProcessors=function(b){function c(b,e){var g=d.rowsProcessors[b];return a.when(g.call(d,e,d.columns)).then(function(a){if(!a)throw"Processor at index "+b+" did not return a set of renderable rows";if(!angular.isArray(a))throw"Processor at index "+b+" did not return an array";return b++,b<=d.rowsProcessors.length-1?c(b,a):void f.resolve(a)})}var d=this,e=b.slice(0);if(0===d.rowsProcessors.length)return a.when(e);var f=a.defer();return c(0,e),f.promise},o.prototype.setVisibleRows=function(a){var b=this;for(var c in b.renderContainers){var d=b.renderContainers[c];d.visibleRowCache.length=0}for(var e=0;e<a.length;e++){var f=a[e];f.visible&&("undefined"!=typeof f.renderContainer&&f.renderContainer?b.renderContainers[f.renderContainer].visibleRowCache.push(f):b.renderContainers.body.visibleRowCache.push(f))}},o.prototype.registerColumnsProcessor=function(a){if(!angular.isFunction(a))throw"Attempt to register non-function rows processor: "+a;this.columnsProcessors.push(a)},o.prototype.removeColumnsProcessor=function(a){var b=this.columnsProcessors.indexOf(a);"undefined"!=typeof b&&void 0!==b&&this.columnsProcessors.splice(b,1)},o.prototype.processColumnsProcessors=function(b){function c(b,g){var h=d.columnsProcessors[b];return a.when(h.call(d,g,d.rows)).then(function(a){if(!a)throw"Processor at index "+b+" did not return a set of renderable rows";if(!angular.isArray(a))throw"Processor at index "+b+" did not return an array";return b++,b<=d.columnsProcessors.length-1?c(b,e):void f.resolve(e)})}var d=this,e=b.slice(0);if(0===d.columnsProcessors.length)return a.when(e);var f=a.defer();return c(0,e),f.promise},o.prototype.setVisibleColumns=function(a){var b=this;for(var c in b.renderContainers){var d=b.renderContainers[c];d.visibleColumnCache.length=0}for(var e=0;e<a.length;e++){var f=a[e];f.visible&&("undefined"!=typeof f.renderContainer&&f.renderContainer?b.renderContainers[f.renderContainer].visibleColumnCache.push(f):b.renderContainers.body.visibleColumnCache.push(f))}},o.prototype.handleWindowResize=function(){var a=this;a.gridWidth=d.elementWidth(a.element),a.gridHeight=d.elementHeight(a.element),a.queueRefresh()},o.prototype.queueRefresh=function(){var a=this;return a.refreshCanceller&&m.cancel(a.refreshCanceller),a.refreshCanceller=m(function(){a.refreshCanvas(!0)}),a.refreshCanceller.then(function(){a.refreshCanceller=null}),a.refreshCanceller},o.prototype.buildStyles=function(){var a=this;a.customStyles="",a.styleComputations.sort(function(a,b){return null===a.priority?1:null===b.priority?-1:null===a.priority&&null===b.priority?0:a.priority-b.priority}).forEach(function(b){var c=b.func.call(a);angular.isString(c)&&(a.customStyles+="\n"+c)})},o.prototype.minColumnsToRender=function(){var a=this,b=this.getViewportWidth(),c=0,d=0;return a.columns.forEach(function(e,f){if(b>d)d+=e.drawnWidth,c++;else{for(var g=0,h=f;h>=f-c;h--)g+=a.columns[h].drawnWidth;b>g&&c++}}),c},o.prototype.getBodyHeight=function(){var a=this.getViewportHeight();return"undefined"!=typeof this.horizontalScrollbarHeight&&void 0!==this.horizontalScrollbarHeight&&this.horizontalScrollbarHeight>0&&(a+=this.horizontalScrollbarHeight),a},o.prototype.getViewportHeight=function(){var a=this,b=this.gridHeight-this.headerHeight-this.footerHeight;"undefined"!=typeof this.horizontalScrollbarHeight&&void 0!==this.horizontalScrollbarHeight&&this.horizontalScrollbarHeight>0&&(b-=this.horizontalScrollbarHeight);var c=a.getViewportAdjustment();return b+=c.height},o.prototype.getViewportWidth=function(){var a=this,b=this.gridWidth;"undefined"!=typeof this.verticalScrollbarWidth&&void 0!==this.verticalScrollbarWidth&&this.verticalScrollbarWidth>0&&(b-=this.verticalScrollbarWidth);var c=a.getViewportAdjustment();return b+=c.width},o.prototype.getHeaderViewportWidth=function(){var a=this.getViewportWidth();return"undefined"!=typeof this.verticalScrollbarWidth&&void 0!==this.verticalScrollbarWidth&&this.verticalScrollbarWidth>0&&(a+=this.verticalScrollbarWidth),a},o.prototype.registerViewportAdjuster=function(a){this.viewportAdjusters.push(a)},o.prototype.removeViewportAdjuster=function(a){var b=this.viewportAdjusters.indexOf(a);"undefined"!=typeof b&&void 0!==b&&this.viewportAdjusters.splice(b,1)},o.prototype.getViewportAdjustment=function(){var a=this,b={height:0,width:0};return a.viewportAdjusters.forEach(function(a){b=a.call(this,b)}),b},o.prototype.getVisibleRowCount=function(){return this.renderContainers.body.visibleRowCache.length},o.prototype.getVisibleRows=function(){return this.renderContainers.body.visibleRowCache},o.prototype.getVisibleColumnCount=function(){return this.renderContainers.body.visibleColumnCache.length},o.prototype.searchRows=function(a){return k.search(this,a,this.columns)},o.prototype.sortByColumn=function(a){return j.sort(this,a,this.columns)},o.prototype.getCellValue=function(a,b){var d=this;return d.cellValueGetterCache[b.colDef.name]||(d.cellValueGetterCache[b.colDef.name]=c(a.getEntityQualifiedColField(b))),d.cellValueGetterCache[b.colDef.name](a)},o.prototype.getNextColumnSortPriority=function(){var a=this,b=0;return a.columns.forEach(function(a){a.sort&&a.sort.priority&&a.sort.priority>b&&(b=a.sort.priority)}),b+1},o.prototype.resetColumnSorting=function(a){var b=this;b.columns.forEach(function(b){b!==a&&(b.sort={})})},o.prototype.getColumnSorting=function(){var a,b=this,c=[];return a=b.columns.slice(0),a.sort(j.prioritySort).forEach(function(a){a.sort&&"undefined"!=typeof a.sort.direction&&a.sort.direction&&(a.sort.direction===e.ASC||a.sort.direction===e.DESC)&&c.push(a)}),c},o.prototype.sortColumn=function(b,c,d){var f=this,g=null;if("undefined"==typeof b||!b)throw new Error("No column parameter provided");return"boolean"==typeof c?d=c:g=c,d?b.sort.priority=f.getNextColumnSortPriority():(f.resetColumnSorting(b),b.sort.priority=0),b.sort.direction=g?g:b.sort.direction&&b.sort.direction===e.ASC?e.DESC:b.sort.direction&&b.sort.direction===e.DESC?b.colDef&&b.colDef.suppressRemoveSort?e.ASC:null:e.ASC,f.api.core.raise.sortChanged(f,f.getColumnSorting()),a.when(b)},o.prototype.renderingComplete=function(){angular.isFunction(this.options.onRegisterApi)&&this.options.onRegisterApi(this.api),this.api.core.raise.renderingComplete(this.api)},o.prototype.createRowHashMap=function(){var a=this,b=new n;b.grid=a,a.rowHashMap=b},o.prototype.refresh=function(){d.logDebug("grid refresh");var b=this,c=b.processRowsProcessors(b.rows).then(function(a){b.setVisibleRows(a)}),e=b.processColumnsProcessors(b.columns).then(function(a){b.setVisibleColumns(a)});return a.all([c,e]).then(function(){b.redrawInPlace(),b.refreshCanvas(!0)})},o.prototype.refreshRows=function(){var a=this;return a.processRowsProcessors(a.rows).then(function(b){a.setVisibleRows(b),a.redrawInPlace(),a.refreshCanvas(!0)})},o.prototype.refreshCanvas=function(b){var c=this;b&&c.buildStyles();var e=a.defer(),f=[];for(var g in c.renderContainers)if(c.renderContainers.hasOwnProperty(g)){var h=c.renderContainers[g];if(null===h.canvasWidth||isNaN(h.canvasWidth))continue;h.header&&f.push(h)}return m(f.length>0?function(){var a,g,h=!1,i=0;for(a=0;a<f.length;a++)if(g=f[a],null!==g.canvasWidth&&!isNaN(g.canvasWidth)&&g.header){var j=g.headerHeight,k=d.outerElementHeight(g.header);g.headerHeight=parseInt(k,10),j!==k&&(h=!0);var l=d.getBorderSize(g.header,"top"),m=d.getBorderSize(g.header,"bottom"),n=parseInt(k-l-m,10);n=0>n?0:n,g.innerHeaderHeight=n,n>i&&(i=n)}for(a=0;a<f.length;a++)g=f[a],g.headerHeight<i&&(g.explicitHeaderHeight=i);b&&h&&c.buildStyles(),e.resolve()}:function(){e.resolve()}),e.promise},o.prototype.redrawInPlace=function(){var a=this;for(var b in a.renderContainers){var c=a.renderContainers[b];c.adjustRows(c.prevScrollTop,null),c.adjustColumns(c.prevScrollLeft,null)}},n.prototype={put:function(a,b){this[this.grid.options.rowIdentity(a)]=b},get:function(a){return this[this.grid.options.rowIdentity(a)]},remove:function(a){var b=this[a=this.grid.options.rowIdentity(a)];return delete this[a],b}},o}])}(),function(){angular.module("ui.grid").factory("GridApi",["$q","$rootScope","gridUtil","uiGridConstants","GridRow","uiGridGridMenuService",function(a,b,c,d,e){function f(a,b,c,d){return a.$on(b,function(){var a=Array.prototype.slice.call(arguments);a.splice(0,1),c.apply(d.api,a)})}var g=function(a){this.grid=a,this.listeners=[],this.registerEvent("core","renderingComplete"),this.registerEvent("core","filterChanged"),this.registerMethod("core","setRowInvisible",e.prototype.setRowInvisible),this.registerMethod("core","clearRowInvisible",e.prototype.clearRowInvisible),this.registerMethod("core","getVisibleRows",this.grid.getVisibleRows),this.registerEvent("core","rowsVisibleChanged")};return g.prototype.suppressEvents=function(a,b){var c=this,d=angular.isArray(a)?a:[a],e=[];d.forEach(function(a){e=c.listeners.filter(function(b){return a===b.handler})}),e.forEach(function(a){a.dereg()}),b(),e.forEach(function(a){a.dereg=f(a.scope,a.eventId,a.handler,c.grid)})},g.prototype.registerEvent=function(a,c){var d=this;d[a]||(d[a]={});var e=d[a];e.on||(e.on={},e.raise={});var g=d.grid.id+a+c;e.raise[c]=function(){b.$broadcast.apply(b,[g].concat(Array.prototype.slice.call(arguments)))},e.on[c]=function(a,b){var c=f(a,g,b,d.grid),e={handler:b,dereg:c,eventId:g,scope:a};d.listeners.push(e),a.$on("$destroy",function(){e.dereg=null,e.handler=null,e.eventId=null,e.scope=null})}},g.prototype.registerEventsFromObject=function(a){var b=this,c=[];angular.forEach(a,function(a,b){var d={name:b,events:[]};angular.forEach(a,function(a,b){d.events.push(b)}),c.push(d)}),c.forEach(function(a){a.events.forEach(function(c){b.registerEvent(a.name,c)})})},g.prototype.registerMethod=function(a,b,d,e){this[a]||(this[a]={});var f=this[a];f[b]=c.createBoundedWrapper(e||this.grid,d)},g.prototype.registerMethodsFromObject=function(a,b){var c=this,d=[];angular.forEach(a,function(a,b){var c={name:b,methods:[]};angular.forEach(a,function(a,b){c.methods.push({name:b,fn:a})}),d.push(c)}),d.forEach(function(a){a.methods.forEach(function(d){c.registerMethod(a.name,d.name,d.fn,b)})})},g}])}(),function(){angular.module("ui.grid").factory("GridColumn",["gridUtil","uiGridConstants","i18nService",function(a,b,c){function d(a,b,c){var d=this;d.grid=c,d.uid=b,d.updateColumnDef(a)}return d.prototype.setPropertyOrDefault=function(a,b,c){var d=this;d[b]="undefined"!=typeof a[b]&&a[b]?a[b]:"undefined"!=typeof d[b]?d[b]:c?c:{}},d.prototype.updateColumnDef=function(b){var c=this;if(c.colDef=b,void 0===b.name)throw new Error("colDef.name is required for column at index "+c.grid.options.columnDefs.indexOf(b));c.displayName=void 0===b.displayName?a.readableColumnName(b.name):b.displayName;var e="Cannot parse column width '"+b.width+"' for column named '"+b.name+"'";if(a.isNullOrUndefined(c.width)||!angular.isNumber(c.width))if(a.isNullOrUndefined(b.width))c.width="*";else if(angular.isNumber(b.width))c.width=b.width;else if(a.endsWith(b.width,"%")){var f=b.width.replace(/%/g,""),g=parseInt(f,10);if(isNaN(g))throw new Error(e);c.width=b.width}else if(b.width.match(/^(\d+)$/))c.width=parseInt(b.width.match(/^(\d+)$/)[1],10);else{if(!b.width.match(/^\*+$/))throw new Error(e);c.width=b.width}c.minWidth=b.minWidth?b.minWidth:30,c.maxWidth=b.maxWidth?b.maxWidth:9e3,c.field=void 0===b.field?b.name:b.field,"string"!=typeof c.field&&a.logError("Field is not a string, this is likely to break the code, Field is: "+c.field),c.name=b.name,c.displayName=void 0===b.displayName?a.readableColumnName(b.name):b.displayName,c.aggregationType=angular.isDefined(b.aggregationType)?b.aggregationType:null,c.footerCellTemplate=angular.isDefined(b.footerCellTemplate)?b.footerCellTemplate:null,c.footerCellClass=b.footerCellClass,c.cellClass=b.cellClass,c.headerCellClass=b.headerCellClass,c.cellFilter=b.cellFilter?b.cellFilter:"",c.headerCellFilter=b.headerCellFilter?b.headerCellFilter:"",c.footerCellFilter=b.footerCellFilter?b.footerCellFilter:"",c.visible=a.isNullOrUndefined(b.visible)||b.visible,c.headerClass=b.headerClass,c.visible=!0,c.enableSorting="undefined"!=typeof b.enableSorting?b.enableSorting:!0,c.sortingAlgorithm=b.sortingAlgorithm,c.enableFiltering="undefined"!=typeof b.enableFiltering?b.enableFiltering:!0,c.setPropertyOrDefault(b,"menuItems",[]),c.setPropertyOrDefault(b,"sort");var h=[];b.filter?h.push(b.filter):c.enableFiltering&&c.grid.options.enableFiltering&&h.push({}),c.setPropertyOrDefault(b,"filter"),c.setPropertyOrDefault(b,"filters",h),d.prototype.unsort=function(){this.sort={},c.grid.api.core.raise.sortChanged(c,c.grid.getColumnSorting())}},d.prototype.getColClass=function(a){var c=b.COL_CLASS_PREFIX+this.uid;return a?"."+c:c},d.prototype.getColClassDefinition=function(){return" .grid"+this.grid.id+" "+this.getColClass(!0)+" { width: "+this.drawnWidth+"px; }"},d.prototype.getRenderContainer=function(){var a=this,b=a.renderContainer;return(null===b||""===b||void 0===b)&&(b="body"),a.grid.renderContainers[b]},d.prototype.showColumn=function(){this.colDef.visible=!0},d.prototype.hideColumn=function(){this.colDef.visible=!1},d.prototype.getAggregationValue=function(){var a=this,c=0,d=a.grid.getVisibleRows(),e=function(){var b=[];return angular.forEach(d,function(c){var d=a.grid.getCellValue(c,a);angular.isNumber(d)&&b.push(d)}),b};return angular.isFunction(a.aggregationType)?a.aggregationType(d,a):a.aggregationType===b.aggregationTypes.count?a.grid.getVisibleRowCount():a.aggregationType===b.aggregationTypes.sum?(angular.forEach(e(),function(a){c+=a}),c):a.aggregationType===b.aggregationTypes.avg?(angular.forEach(e(),function(a){c+=a}),c/=e().length):a.aggregationType===b.aggregationTypes.min?Math.min.apply(null,e()):a.aggregationType===b.aggregationTypes.max?Math.max.apply(null,e()):" "},d.prototype.getAggregationText=function(){var a=this;if(a.colDef.aggregationHideLabel)return"";if(a.colDef.aggregationLabel)return a.colDef.aggregationLabel;switch(a.colDef.aggregationType){case b.aggregationTypes.count:return c.getSafeText("aggregation.count");case b.aggregationTypes.sum:return c.getSafeText("aggregation.sum");case b.aggregationTypes.avg:return c.getSafeText("aggregation.avg");case b.aggregationTypes.min:return c.getSafeText("aggregation.min");case b.aggregationTypes.max:return c.getSafeText("aggregation.max");default:return""}},d.prototype.getCellTemplate=function(){var a=this;return a.cellTemplatePromise},d.prototype.getCompiledElementFn=function(){var a=this;return a.compiledElementFnDefer.promise},d}])}(),function(){angular.module("ui.grid").factory("GridOptions",["gridUtil","uiGridConstants",function(a,b){return{initialize:function(c){return c.onRegisterApi=c.onRegisterApi||angular.noop(),c.data=c.data||[],c.columnDefs=c.columnDefs||[],c.excludeProperties=c.excludeProperties||["$$hashKey"],c.enableRowHashing=c.enableRowHashing!==!1,c.rowIdentity=c.rowIdentity||function(b){return a.hashKey(b)},c.getRowIdentity=c.getRowIdentity||function(a){return a.$$hashKey},c.showHeader="undefined"!=typeof c.showHeader?c.showHeader:!0,c.headerRowHeight=c.showHeader?"undefined"!=typeof c.headerRowHeight?c.headerRowHeight:30:0,c.rowHeight=c.rowHeight||30,c.maxVisibleRowCount=c.maxVisibleRowCount||200,c.minRowsToShow="undefined"!=typeof c.minRowsToShow?c.minRowsToShow:10,c.showFooter=c.showFooter===!0,c.footerRowHeight="undefined"!=typeof c.footerRowHeight?c.footerRowHeight:30,c.columnWidth="undefined"!=typeof c.columnWidth?c.columnWidth:50,c.maxVisibleColumnCount="undefined"!=typeof c.maxVisibleColumnCount?c.maxVisibleColumnCount:200,c.virtualizationThreshold="undefined"!=typeof c.virtualizationThreshold?c.virtualizationThreshold:20,c.columnVirtualizationThreshold="undefined"!=typeof c.columnVirtualizationThreshold?c.columnVirtualizationThreshold:10,c.excessRows="undefined"!=typeof c.excessRows?c.excessRows:4,c.scrollThreshold="undefined"!=typeof c.scrollThreshold?c.scrollThreshold:4,c.excessColumns="undefined"!=typeof c.excessColumns?c.excessColumns:4,c.horizontalScrollThreshold="undefined"!=typeof c.horizontalScrollThreshold?c.horizontalScrollThreshold:2,c.scrollThrottle="undefined"!=typeof c.scrollThrottle?c.scrollThrottle:70,c.enableSorting=c.enableSorting!==!1,c.enableFiltering=c.enableFiltering===!0,c.enableColumnMenus=c.enableColumnMenus!==!1,c.enableVerticalScrollbar="undefined"!=typeof c.enableVerticalScrollbar?c.enableVerticalScrollbar:b.scrollbars.ALWAYS,c.enableHorizontalScrollbar="undefined"!=typeof c.enableHorizontalScrollbar?c.enableHorizontalScrollbar:b.scrollbars.ALWAYS,c.minimumColumnSize="undefined"!=typeof c.minimumColumnSize?c.minimumColumnSize:10,c.rowEquality=c.rowEquality||function(a,b){return a===b},c.headerTemplate=c.headerTemplate||null,c.footerTemplate=c.footerTemplate||null,c.rowTemplate=c.rowTemplate||"ui-grid/ui-grid-row",c}}}])}(),function(){angular.module("ui.grid").factory("GridRenderContainer",["gridUtil",function(a){function b(a,b,c){var d=this;d.name=a,d.grid=b,d.visibleRowCache=[],d.visibleColumnCache=[],d.renderedRows=[],d.renderedColumns=[],d.prevScrollTop=0,d.prevScrolltopPercentage=0,d.prevRowScrollIndex=0,d.prevScrollLeft=0,d.prevScrollleftPercentage=0,d.prevColumnScrollIndex=0,d.columnStyles="",d.viewportAdjusters=[],c&&angular.isObject(c)&&angular.extend(d,c),b.registerStyleComputation({priority:5,func:function(){return d.columnStyles}})}return b.prototype.reset=function(){this.visibleColumnCache.length=0,this.visibleRowCache.length=0,this.renderedRows.length=0,this.renderedColumns.length=0},b.prototype.minRowsToRender=function(){for(var a=this,b=0,c=0,d=a.getViewportHeight(),e=a.visibleRowCache.length-1;d>c&&e>=0;e--)c+=a.visibleRowCache[e].height,b++;
return b},b.prototype.minColumnsToRender=function(){for(var a=this,b=this.getViewportWidth(),c=0,d=0,e=0;e<a.visibleColumnCache.length;e++){var f=a.visibleColumnCache[e];if(b>d)d+=f.drawnWidth?f.drawnWidth:0,c++;else{for(var g=0,h=e;h>=e-c;h--)g+=a.visibleColumnCache[h].drawnWidth?a.visibleColumnCache[h].drawnWidth:0;b>g&&c++}}return c},b.prototype.getVisibleRowCount=function(){return this.visibleRowCache.length},b.prototype.registerViewportAdjuster=function(a){this.viewportAdjusters.push(a)},b.prototype.removeViewportAdjuster=function(a){var b=this.viewportAdjusters.indexOf(a);"undefined"!=typeof b&&void 0!==b&&this.viewportAdjusters.splice(b,1)},b.prototype.getViewportAdjustment=function(){var a=this,b={height:0,width:0};return a.viewportAdjusters.forEach(function(a){b=a.call(this,b)}),b},b.prototype.getViewportHeight=function(){var a=this,b=a.headerHeight?a.headerHeight:a.grid.headerHeight,c=a.grid.gridHeight-b-a.grid.footerHeight;"undefined"!=typeof a.horizontalScrollbarHeight&&void 0!==a.horizontalScrollbarHeight&&a.horizontalScrollbarHeight>0&&(c-=a.horizontalScrollbarHeight);var d=a.getViewportAdjustment();return c+=d.height},b.prototype.getViewportWidth=function(){var a=this,b=a.grid.gridWidth;"undefined"!=typeof a.grid.verticalScrollbarWidth&&void 0!==a.grid.verticalScrollbarWidth&&a.grid.verticalScrollbarWidth>0&&(b-=a.grid.verticalScrollbarWidth);var c=a.getViewportAdjustment();return b+=c.width},b.prototype.getHeaderViewportWidth=function(){var a=this,b=this.getViewportWidth();return"undefined"!=typeof a.grid.verticalScrollbarWidth&&void 0!==a.grid.verticalScrollbarWidth&&a.grid.verticalScrollbarWidth>0&&(b+=a.grid.verticalScrollbarWidth),b},b.prototype.getCanvasHeight=function(){var a=this,b=0;return a.visibleRowCache.forEach(function(a){b+=a.height}),"undefined"!=typeof a.grid.horizontalScrollbarHeight&&void 0!==a.grid.horizontalScrollbarHeight&&a.grid.horizontalScrollbarHeight>0&&(b-=a.grid.horizontalScrollbarHeight),b},b.prototype.getCanvasWidth=function(){var a=this,b=a.canvasWidth;return"undefined"!=typeof a.verticalScrollbarWidth&&void 0!==a.verticalScrollbarWidth&&a.verticalScrollbarWidth>0&&(b-=a.verticalScrollbarWidth),b},b.prototype.setRenderedRows=function(a){this.renderedRows.length=a.length;for(var b=0;b<a.length;b++)this.renderedRows[b]=a[b]},b.prototype.setRenderedColumns=function(a){this.renderedColumns.length=a.length;for(var b=0;b<a.length;b++)this.renderedColumns[b]=a[b];this.updateColumnOffset()},b.prototype.updateColumnOffset=function(){for(var a=0,b=0;b<this.currentFirstColumn;b++)a+=this.visibleColumnCache[b].drawnWidth;this.columnOffset=a},b.prototype.adjustScrollVertical=function(a,b,c){(this.prevScrollTop!==a||c)&&(("undefined"==typeof a||void 0===a||null===a)&&(a=(this.getCanvasHeight()-this.getCanvasWidth())*b),this.adjustRows(a,b),this.prevScrollTop=a,this.prevScrolltopPercentage=b,this.grid.queueRefresh())},b.prototype.adjustScrollHorizontal=function(a,b,c){(this.prevScrollLeft!==a||c)&&(("undefined"==typeof a||void 0===a||null===a)&&(a=(this.getCanvasWidth()-this.getViewportWidth())*b),this.adjustColumns(a,b),this.prevScrollLeft=a,this.prevScrollleftPercentage=b,this.grid.queueRefresh())},b.prototype.adjustRows=function(a,b){var c=this,d=c.minRowsToRender(),e=c.visibleRowCache,f=e.length-d;c.maxRowIndex=f;c.prevRowScrollIndex;"undefined"!=typeof b&&null!==b||!a||(b=a/c.getCanvasHeight());var g=Math.ceil(Math.min(f,f*b));g>f&&(g=f);var h=[];if(e.length>c.grid.options.virtualizationThreshold){if(c.prevScrollTop<a&&g<c.prevRowScrollIndex+c.grid.options.scrollThreshold&&f>g)return;if(c.prevScrollTop>a&&g>c.prevRowScrollIndex-c.grid.options.scrollThreshold&&f>g)return;var i=Math.max(0,g-c.grid.options.excessRows),j=Math.min(e.length,g+d+c.grid.options.excessRows);h=[i,j]}else{var k=c.visibleRowCache.length;h=[0,Math.max(k,d+c.grid.options.excessRows)]}c.updateViewableRowRange(h),c.prevRowScrollIndex=g},b.prototype.adjustColumns=function(a,b){var c=this,d=c.minColumnsToRender(),e=c.visibleColumnCache,f=e.length-d;"undefined"!=typeof b&&null!==b||!a||(b=a/c.getCanvasWidth());var g=Math.ceil(Math.min(f,f*b));g>f&&(g=f);var h=[];if(e.length>c.grid.options.columnVirtualizationThreshold&&c.getCanvasWidth()>c.getViewportWidth()){if(c.prevScrollLeft<a&&g<c.prevColumnScrollIndex+c.grid.options.horizontalScrollThreshold&&f>g)return;if(c.prevScrollLeft>a&&g>c.prevColumnScrollIndex-c.grid.options.horizontalScrollThreshold&&f>g)return;var i=Math.max(0,g-c.grid.options.excessColumns),j=Math.min(e.length,g+d+c.grid.options.excessColumns);h=[i,j]}else{var k=c.visibleColumnCache.length;h=[0,Math.max(k,d+c.grid.options.excessColumns)]}c.updateViewableColumnRange(h),c.prevColumnScrollIndex=g},b.prototype.updateViewableRowRange=function(a){var b=this.visibleRowCache.slice(a[0],a[1]);this.currentTopRow=a[0],this.setRenderedRows(b)},b.prototype.updateViewableColumnRange=function(a){var b=this.visibleColumnCache.slice(a[0],a[1]);this.currentFirstColumn=a[0],this.setRenderedColumns(b)},b.prototype.rowStyle=function(a){var b=this,c={};if(0===a&&0!==b.currentTopRow){var d=b.currentTopRow*b.grid.options.rowHeight;c["margin-top"]=d+"px"}return 0!==b.currentFirstColumn&&(b.grid.isRTL()?c["margin-right"]=b.columnOffset+"px":c["margin-left"]=b.columnOffset+"px"),c},b.prototype.columnStyle=function(a){var b=this;if(0===a&&0!==b.currentFirstColumn){var c=b.columnOffset;return b.grid.isRTL()?{"margin-right":c+"px"}:{"margin-left":c+"px"}}return null},b.prototype.updateColumnWidths=function(){var b=this,c=[],d=[],e=0,f=b.getViewportWidth();"undefined"!=typeof b.grid.verticalScrollbarWidth&&void 0!==b.grid.verticalScrollbarWidth&&b.grid.verticalScrollbarWidth>0&&(f+=b.grid.verticalScrollbarWidth);var g,h=0,i=0,j="",k=b.visibleColumnCache;k.forEach(function(b){if(b.visible){var f=!1;angular.isNumber(b.width)||(f=isNaN(b.width)&&a.endsWith(b.width,"%")),angular.isString(b.width)&&-1!==b.width.indexOf("*")?(e=parseInt(e+b.width.length,10),c.push(b)):f?d.push(b):angular.isNumber(b.width)&&(h=parseInt(h+b.width,10),i=parseInt(i,10)+parseInt(b.width,10),b.drawnWidth=b.width)}});var l,m,n,o=f-h;if(d.length>0){for(l=0;l<d.length;l++){m=d[l];var p=parseInt(m.width.replace(/%/g,""),10)/100;n=parseInt(p*o,10),m.colDef.minWidth&&n<m.colDef.minWidth?(n=m.colDef.minWidth,o-=n,i+=n,m.drawnWidth=n,d.splice(l,1)):m.colDef.maxWidth&&n>m.colDef.maxWidth&&(n=m.colDef.maxWidth,o-=n,i+=n,m.drawnWidth=n,d.splice(l,1))}d.forEach(function(a){var b=parseInt(a.width.replace(/%/g,""),10)/100,c=parseInt(b*o,10);i+=c,a.drawnWidth=c})}if(c.length>0){var q=parseInt(o/e,10);for(l=0;l<c.length;l++)m=c[l],n=parseInt(q*m.width.length,10),m.colDef.minWidth&&n<m.colDef.minWidth?(n=m.colDef.minWidth,o-=n,e--,i+=n,m.drawnWidth=n,g=m,c.splice(l,1)):m.colDef.maxWidth&&n>m.colDef.maxWidth&&(n=m.colDef.maxWidth,o-=n,e--,i+=n,m.drawnWidth=n,c.splice(l,1));q=parseInt(o/e,10),c.forEach(function(a){var b=parseInt(q*a.width.length,10);i+=b,a.drawnWidth=b})}var r=f-parseInt(i,10);if(r>0&&i>0&&f>i){var s=!1;if(k.forEach(function(a){a.width&&!angular.isNumber(a.width)&&(s=!0)}),s)for(var t=function(a){r>0&&(a.drawnWidth=a.drawnWidth+1,i+=1,r--)};r>0;)k.forEach(t)}f>i&&(i=f),k.forEach(function(a){j+=a.getColClassDefinition()}),b.grid.verticalScrollbarWidth&&(i+=b.grid.verticalScrollbarWidth),b.canvasWidth=parseInt(i,10),this.columnStyles=j},b}])}(),function(){angular.module("ui.grid").factory("GridRow",["gridUtil",function(a){function b(b,c,d){this.grid=d,this.entity=b,this.uid=a.nextUid(),this.visible=!0,this.height=d.options.rowHeight}return b.prototype.getQualifiedColField=function(a){return"row."+this.getEntityQualifiedColField(a)},b.prototype.getEntityQualifiedColField=function(b){return a.preEval("entity."+b.field)},b.prototype.setRowInvisible=function(a){null!==a&&(a.forceInvisible=!0,a.visible&&(a.visible=!1,a.grid.refresh(),a.grid.api.core.raise.rowsVisibleChanged()))},b.prototype.clearRowInvisible=function(a){null!==a&&(a.forceInvisible=!1,a.visible||(a.visible=!0,a.grid.refresh(),a.grid.api.core.raise.rowsVisibleChanged()))},b}])}(),function(){"use strict";angular.module("ui.grid").service("gridClassFactory",["gridUtil","$q","$compile","$templateCache","uiGridConstants","Grid","GridColumn","GridRow",function(a,b,c,d,e,f){var g={createGrid:function(d){d="undefined"!=typeof d?d:{},d.id=a.newId();var e=new f(d);if(e.options.rowTemplate){var h=b.defer();e.getRowTemplateFn=h.promise,a.getTemplate(e.options.rowTemplate).then(function(a){var b=c(a);h.resolve(b)},function(){throw new Error("Couldn't fetch/use row template '"+e.options.rowTemplate+"'")})}return e.registerColumnBuilder(g.defaultColumnBuilder),e.registerRowsProcessor(function(a){return a.forEach(function(a){a.visible=!a.forceInvisible}),a}),e.registerColumnsProcessor(function(a){return a.forEach(function(a){a.visible=!0}),a}),e.registerColumnsProcessor(function(a){return a.forEach(function(a){a.colDef.visible===!1&&(a.visible=!1)}),a}),e.options.enableFiltering&&e.registerRowsProcessor(e.searchRows),e.registerRowsProcessor(e.options.externalSort&&angular.isFunction(e.options.externalSort)?e.options.externalSort:e.sortByColumn),e},defaultColumnBuilder:function(c,d){var f=[];return d.providedHeaderCellTemplate=c.headerCellTemplate?c.headerCellTemplate:"ui-grid/uiGridHeaderCell",d.providedCellTemplate=c.cellTemplate?c.cellTemplate:"ui-grid/uiGridCell",d.providedFooterCellTemplate=c.footerCellTemplate?c.footerCellTemplate:"ui-grid/uiGridFooterCell",d.cellTemplatePromise=a.getTemplate(d.providedCellTemplate),f.push(d.cellTemplatePromise.then(function(a){d.cellTemplate=a.replace(e.CUSTOM_FILTERS,d.cellFilter?"|"+d.cellFilter:"")},function(){throw new Error("Couldn't fetch/use colDef.cellTemplate '"+c.cellTemplate+"'")})),f.push(a.getTemplate(d.providedHeaderCellTemplate).then(function(a){d.headerCellTemplate=a.replace(e.CUSTOM_FILTERS,d.headerCellFilter?"|"+d.headerCellFilter:"")},function(){throw new Error("Couldn't fetch/use colDef.headerCellTemplate '"+c.headerCellTemplate+"'")})),f.push(a.getTemplate(d.providedFooterCellTemplate).then(function(a){d.footerCellTemplate=a.replace(e.CUSTOM_FILTERS,d.footerCellFilter?"|"+d.footerCellFilter:"")},function(){throw new Error("Couldn't fetch/use colDef.footerCellTemplate '"+c.footerCellTemplate+"'")})),d.compiledElementFnDefer=b.defer(),b.all(f)}};return g}])}(),function(){function a(a){return a.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&")}function b(){var a=function(b,c){return b&&a.cache[b]?a.cache[b]:b&&c?(a.cache[b]=c,a.cache[b]):void 0};return a.cache={},a.clear=function(){a.cache={}},a}var c=angular.module("ui.grid");c.service("rowSearcher",["gridUtil","uiGridConstants",function(c,d){var e=d.filter.STARTS_WITH,f={};return f.getTerm=function(a){if("undefined"==typeof a.term)return a.term;var b=a.term;return"string"==typeof b&&(b=b.trim()),b},f.stripTerm=function(b){var c=f.getTerm(b);return"string"==typeof c?a(c.replace(/(^\*|\*$)/g,"")):c},f.guessCondition=function(a){if("undefined"==typeof a.term||!a.term)return e;var b=f.getTerm(a);if(/\*/.test(b)){var c="";a.flags&&a.flags.caseSensitive||(c+="i");var d=b.replace(/(\\)?\*/g,function(a,b){return b?a:"[\\s\\S]*?"});return new RegExp("^"+d+"$",c)}return e},f.runColumnFilter=function(a,b,c,e,g,h){var i=typeof h.condition;"undefined"!==i&&h.condition||(h.condition=d.filter.CONTAINS);var j=f.stripTerm(h);if(!h.noTerm&&(null===j||void 0===j||""===j))return!0;var k=a.getCellValue(b,c),l="";h.flags&&h.flags.caseSensitive||(l+="i");var m=c.field+g;if(h.condition instanceof RegExp){if(!h.condition.test(k))return!1}else{if("function"===i)return h.condition(j,k,b,c);if(h.condition===d.filter.STARTS_WITH){var n=e(m)?e(m):e(m,new RegExp("^"+j,l));if(!n.test(k))return!1}else if(h.condition===d.filter.ENDS_WITH){var o=e(m)?e(m):e(m,new RegExp(j+"$",l));if(!o.test(k))return!1}else if(h.condition===d.filter.CONTAINS){var p=e(m)?e(m):e(m,new RegExp(j,l));if(!p.test(k))return!1}else if(h.condition===d.filter.EXACT){var q=e(m)?e(m):e(m,new RegExp("^"+j+"$",l));if(!q.test(k))return!1}else if(h.condition===d.filter.GREATER_THAN){if(j>=k)return!1}else if(h.condition===d.filter.GREATER_THAN_OR_EQUAL){if(j>k)return!1}else if(h.condition===d.filter.LESS_THAN){if(k>=j)return!1}else if(h.condition===d.filter.LESS_THAN_OR_EQUAL){if(k>j)return!1}else if(h.condition===d.filter.NOT_EQUAL&&!angular.equals(k,j))return!1}return!0},f.searchColumn=function(a,b,c,d){var e=[];if(a.options.useExternalFiltering)return!0;if(!("undefined"!=typeof c.filters&&c.filters&&c.filters.length>0))return!0;e=c.filters;for(var g in e){var h=e[g];if(!h.condition){var i="cond-"+c.field+"-"+h.term,j=d(i)?d(i):d(i,f.guessCondition(h));h={term:h.term,condition:j,flags:angular.extend({caseSensitive:!1},h.flags)}}var k=f.runColumnFilter(a,b,c,d,g,h);if(!k)return!1}return!0},f.search=function(a,c,d){if(c){var e=new b,g=[];return d.forEach(function(a){"undefined"!=typeof a.filters&&a.filters.length>0?g.push(a):"undefined"!=typeof a.filter&&a.filter&&("undefined"!=typeof a.filter.term&&a.filter.term||a.filter.noTerm)&&g.push(a)}),g.length>0&&(g.forEach(function(b){c.forEach(function(c){(c.forceInvisible||!f.searchColumn(a,c,b,e))&&(c.visible=!1)})}),a.api.core.raise.rowsVisibleChanged&&a.api.core.raise.rowsVisibleChanged()),e.clear(),c}},f}])}(),function(){var a=angular.module("ui.grid");a.service("rowSorter",["$parse","uiGridConstants",function(a,b){var c="("+b.CURRENCY_SYMBOLS.map(function(a){return"\\"+a}).join("|")+")?",d=(new RegExp("^[-+]?"+c+"[\\d,.]+"+c+"%?$"),{colSortFnCache:[]});return d.guessSortFn=function(a){switch(a){case"number":return d.sortNumber;case"boolean":return d.sortBool;case"string":return d.sortAlpha;case"date":return d.sortDate;case"object":return d.basicSort;default:throw new Error("No sorting function found for type:"+a)}},d.handleNulls=function(a,b){if(!a&&0!==a&&a!==!1||!b&&0!==b&&b!==!1){if(!a&&0!==a&&a!==!1&&!b&&0!==b&&b!==!1)return 0;if(!a&&0!==a&&a!==!1)return 1;if(!b&&0!==b&&b!==!1)return-1}return null},d.basicSort=function(a,b){var c=d.handleNulls(a,b);return null!==c?c:a===b?0:b>a?-1:1},d.sortNumber=function(a,b){var c=d.handleNulls(a,b);return null!==c?c:a-b},d.sortNumberStr=function(a,b){var c=d.handleNulls(a,b);if(null!==c)return c;var e,f,g=!1,h=!1;return e=parseFloat(a.replace(/[^0-9.-]/g,"")),isNaN(e)&&(g=!0),f=parseFloat(b.replace(/[^0-9.-]/g,"")),isNaN(f)&&(h=!0),g&&h?0:g?1:h?-1:e-f},d.sortAlpha=function(a,b){var c=d.handleNulls(a,b);if(null!==c)return c;var e=a.toLowerCase(),f=b.toLowerCase();return e===f?0:f>e?-1:1},d.sortDate=function(a,b){var c=d.handleNulls(a,b);if(null!==c)return c;var e=a.getTime(),f=b.getTime();return e===f?0:f>e?-1:1},d.sortBool=function(a,b){var c=d.handleNulls(a,b);return null!==c?c:a&&b?0:a||b?a?1:-1:0},d.getSortFn=function(a,b){var c;return d.colSortFnCache[b.colDef.name]?c=d.colSortFnCache[b.colDef.name]:void 0!==b.sortingAlgorithm?(c=b.sortingAlgorithm,d.colSortFnCache[b.colDef.name]=b.sortingAlgorithm):(c=d.guessSortFn(b.colDef.type),c?d.colSortFnCache[b.colDef.name]=c:c=d.sortAlpha),c},d.prioritySort=function(a,b){return void 0!==a.sort.priority&&void 0!==b.sort.priority?a.sort.priority<b.sort.priority?-1:a.sort.priority===b.sort.priority?0:1:a.sort.priority||0===a.sort.priority?-1:b.sort.priority||0===b.sort.priority?1:0},d.sort=function(a,c,e){if(c){if(a.options.useExternalSorting)return c;var f=[];if(e.forEach(function(a){a.sort&&a.sort.direction&&(a.sort.direction===b.ASC||a.sort.direction===b.DESC)&&f.push(a)}),f=f.sort(d.prioritySort),0===f.length)return c;var g,h,i=c.slice(0);return c.sort(function(c,e){for(var j,k=0,l=0;0===k&&l<f.length;){g=f[l],h=f[l].sort.direction,j=d.getSortFn(a,g,i);var m=a.getCellValue(c,g),n=a.getCellValue(e,g);k=j(m,n),l++}return h===b.ASC?k:0-k})}},d}])}(),function(){function a(a){var b=a;return"undefined"!=typeof b.length&&b.length&&(b=a[0]),b.ownerDocument.defaultView.getComputedStyle(b,null)}function b(a,b,c,d,e){for(var f=c===(d?"border":"content")?4:"width"===b?1:0,g=0,h=["Top","Right","Bottom","Left"];4>f;f+=2){var i=h[f];if("margin"===c){var j=parseFloat(e[c+i]);isNaN(j)||(g+=j)}if(d){if("content"===c){var k=parseFloat(e["padding"+i]);isNaN(k)||(g-=k)}if("margin"!==c){var l=parseFloat(e["border"+i+"Width"]);isNaN(l)||(g-=l)}}else{var m=parseFloat(e["padding"+i]);if(isNaN(m)||(g+=m),"padding"!==c){var n=parseFloat(e["border"+i+"Width"]);isNaN(n)||(g+=n)}}}return g}function c(c,d,f){var g,h=!0,i=a(c),j="border-box"===i.boxSizing;if(0>=g||null==g){if(g=i[d],(0>g||null==g)&&(g=c.style[d]),e.test(g))return g;h=j&&!0,g=parseFloat(g)||0}var k=g+b(c,d,f||(j?"border":"content"),h,i);return k}var d=angular.module("ui.grid"),e=new RegExp("^("+/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source+")(?!px)[a-z%]+$","i"),f=/^(block|none|table(?!-c[ea]).+)/,g={position:"absolute",visibility:"hidden",display:"block"},h=["0","0","0"],i="uiGrid-";d.service("gridUtil",["$log","$window","$document","$http","$templateCache","$timeout","$injector","$q","$interpolate","uiGridConstants",function(b,d,e,j,k,l,m,n,o,p){var q={getStyles:a,createBoundedWrapper:function(a,b){return function(){return b.apply(a,arguments)}},readableColumnName:function(a){return"undefined"==typeof a||void 0===a||null===a?a:("string"!=typeof a&&(a=String(a)),a.replace(/_+/g," ").replace(/^[A-Z]+$/,function(a){return angular.lowercase(angular.uppercase(a.charAt(0))+a.slice(1))}).replace(/(\w+)/g,function(a){return angular.uppercase(a.charAt(0))+a.slice(1)}).replace(/(\w+?(?=[A-Z]))/g,"$1 "))},getColumnsFromData:function(a,b){var c=[];if(!a||"undefined"==typeof a[0]||void 0===a[0])return[];angular.isUndefined(b)&&(b=[]);var d=a[0];return angular.forEach(d,function(a,d){-1===b.indexOf(d)&&c.push({name:d})}),c},newId:function(){var a=(new Date).getTime();return function(){return a+=1}}(),getTemplate:function(a){if(k.get(a))return q.postProcessTemplate(k.get(a));if(a.hasOwnProperty("then"))return a.then(q.postProcessTemplate);try{if(angular.element(a).length>0)return n.when(a).then(q.postProcessTemplate)}catch(b){}return q.logDebug("fetching url",a),j({method:"GET",url:a}).then(function(b){var c=b.data.trim();return k.put(a,c),c},function(b){throw new Error("Could not get template "+a+": "+b)}).then(q.postProcessTemplate)},postProcessTemplate:function(a){var b=o.startSymbol(),c=o.endSymbol();return("{{"!==b||"}}"!==c)&&(a=a.replace(/\{\{/g,b),a=a.replace(/\}\}/g,c)),n.when(a)},guessType:function(a){var b=typeof a;switch(b){case"number":case"boolean":case"string":return b;default:return angular.isDate(a)?"date":"object"}},elementWidth:function(){},elementHeight:function(){},getScrollbarWidth:function(){var a=document.createElement("div");a.style.visibility="hidden",a.style.width="100px",a.style.msOverflowStyle="scrollbar",document.body.appendChild(a);var b=a.offsetWidth;a.style.overflow="scroll";var c=document.createElement("div");c.style.width="100%",a.appendChild(c);var d=c.offsetWidth;return a.parentNode.removeChild(a),b-d},swap:function(a,b,c,d){var e,f,g={};for(f in b)g[f]=a.style[f],a.style[f]=b[f];e=c.apply(a,d||[]);for(f in b)a.style[f]=g[f];return e},fakeElement:function(a,b,c){var d,e,f=angular.element(a).clone()[0];for(e in b)f.style[e]=b[e];return angular.element(document.body).append(f),d=c.call(f,f),angular.element(f).remove(),d},normalizeWheelEvent:function(a){var b,c,d,e=a||window.event,f=([].slice.call(arguments,1),0),g=0,h=0,i=0,j=0;return e.originalEvent&&(e=e.originalEvent),e.wheelDelta&&(f=e.wheelDelta),e.detail&&(f=-1*e.detail),h=f,void 0!==e.axis&&e.axis===e.HORIZONTAL_AXIS&&(h=0,g=-1*f),e.deltaY&&(h=-1*e.deltaY,f=h),e.deltaX&&(g=e.deltaX,f=-1*g),void 0!==e.wheelDeltaY&&(h=e.wheelDeltaY),void 0!==e.wheelDeltaX&&(g=e.wheelDeltaX),i=Math.abs(f),(!b||b>i)&&(b=i),j=Math.max(Math.abs(h),Math.abs(g)),(!c||c>j)&&(c=j),d=f>0?"floor":"ceil",f=Math[d](f/b),g=Math[d](g/c),h=Math[d](h/c),{delta:f,deltaX:g,deltaY:h}},isTouchEnabled:function(){var a;return("ontouchstart"in d||d.DocumentTouch&&e instanceof DocumentTouch)&&(a=!0),a},isNullOrUndefined:function(a){return void 0===a||null===a?!0:!1},endsWith:function(a,b){return a&&b&&"string"==typeof a?-1!==a.indexOf(b,a.length-b.length):!1},arrayContainsObjectWithProperty:function(a,b,c){var d=!1;return angular.forEach(a,function(a){a[b]===c&&(d=!0)}),d},requestAnimationFrame:d.requestAnimationFrame&&d.requestAnimationFrame.bind(d)||d.webkitRequestAnimationFrame&&d.webkitRequestAnimationFrame.bind(d)||function(a){return l(a,10,!1)},numericAndNullSort:function(a,b){return null===a?1:null===b?-1:null===a&&null===b?0:a-b},disableAnimations:function(a){var b;try{b=m.get("$animate"),b.enabled(!1,a)}catch(c){}},enableAnimations:function(a){var b;try{return b=m.get("$animate"),b.enabled(!0,a),b}catch(c){}},nextUid:function(){for(var a,b=h.length;b;){if(b--,a=h[b].charCodeAt(0),57===a)return h[b]="A",i+h.join("");if(90!==a)return h[b]=String.fromCharCode(a+1),i+h.join("");h[b]="0"}return h.unshift("0"),i+h.join("")},hashKey:function(a){var b,c=typeof a;return"object"===c&&null!==a?"function"==typeof(b=a.$$hashKey)?b=a.$$hashKey():"undefined"!=typeof a.$$hashKey&&a.$$hashKey?b=a.$$hashKey:void 0===b&&(b=a.$$hashKey=q.nextUid()):b=a,c+":"+b},resetUids:function(){h=["0","0","0"]},logError:function(a){p.LOG_ERROR_MESSAGES&&b.error(a)},logWarn:function(a){p.LOG_WARN_MESSAGES&&b.warn(a)},logDebug:function(){p.LOG_DEBUG_MESSAGES&&b.debug.apply(b,arguments)}};return["width","height"].forEach(function(b){var d=angular.uppercase(b.charAt(0))+b.substr(1);q["element"+d]=function(d,e){var h=d;if(h&&"undefined"!=typeof h.length&&h.length&&(h=d[0]),h){var i=a(h);return 0===h.offsetWidth&&f.test(i.display)?q.fakeElement(h,g,function(a){return c(a,b,e)}):c(h,b,e)}return null},q["outerElement"+d]=function(a,b){return a?q["element"+d].call(this,a,b?"margin":"border"):null}}),q.closestElm=function(a,b){"undefined"!=typeof a.length&&a.length&&(a=a[0]);var c;["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"].some(function(a){return"function"==typeof document.body[a]?(c=a,!0):!1});for(var d;null!==a;){if(d=a.parentElement,null!==d&&d[c](b))return d;a=d}return null},q.type=function(a){var b=Function.prototype.toString.call(a.constructor);return b.match(/function (.*?)\(/)[1]},q.getBorderSize=function(b,c){"undefined"!=typeof b.length&&b.length&&(b=b[0]);var d=a(b);c=c?"border"+c.charAt(0).toUpperCase()+c.slice(1):"border",c+="Width";var e=parseInt(d[c],10);return isNaN(e)?0:e},q.detectBrowser=function(){var a=d.navigator.userAgent,b={chrome:/chrome/i,safari:/safari/i,firefox:/firefox/i,ie:/internet explorer|trident\//i};for(var c in b)if(b[c].test(a))return c;return"unknown"},q.normalizeScrollLeft=function(a){"undefined"!=typeof a.length&&a.length&&(a=a[0]);var b=q.detectBrowser(),c=a.scrollLeft,d=q.getStyles(a).direction;if("ie"===b)return c;if("chrome"===b){if("rtl"===d){var e=a.scrollWidth-a.clientWidth;return e-c}return c}return"firefox"===b?Math.abs(c):c},q.denormalizeScrollLeft=function(a,b){"undefined"!=typeof a.length&&a.length&&(a=a[0]);var c=q.detectBrowser(),d=q.getStyles(a).direction;if("ie"===c)return b;if("chrome"===c){if("rtl"===d){var e=a.scrollWidth-a.clientWidth;return e-b}return b}return"firefox"===c&&"rtl"===d?-1*b:b},q.preEval=function(a){var b=p.BRACKET_REGEXP.exec(a);if(b)return(b[1]?q.preEval(b[1]):b[1])+b[2]+(b[3]?q.preEval(b[3]):b[3]);a=a.replace(p.APOS_REGEXP,"\\'");var c=a.split(p.DOT_REGEXP),d=[c.shift()];return angular.forEach(c,function(a){d.push(a.replace(p.FUNC_REGEXP,"']$1"))}),d.join("['")},q.debounce=function(a,b,c){function d(){g=this,f=arguments;var d=function(){e=null,c||(h=a.apply(g,f))},i=c&&!e;return e&&l.cancel(e),e=l(d,b),i&&(h=a.apply(g,f)),h}var e,f,g,h;return d.cancel=function(){l.cancel(e),e=null},d},q.throttle=function(a,b,c){function d(){g=+new Date,a.apply(e,f),l(function(){h=null},0)}c=c||{};var e,f,g=0,h=null;return function(){if(e=this,f=arguments,null===h){var a=+new Date-g;a>b?d():c.trailing&&(h=l(d,b-a))}}},q}]),d.filter("px",function(){return function(a){return a.match(/^[\d\.]+$/)?a+"px":a}})}(),function(){angular.module("ui.grid").config(["$provide",function(a){a.decorator("i18nService",["$delegate",function(a){return a.add("da",{aggregate:{label:"artikler"},groupPanel:{description:"Grupér rækker udfra en kolonne ved at trække dens overskift hertil."},search:{placeholder:"Søg...",showingItems:"Viste rækker:",selectedItems:"Valgte rækker:",totalItems:"Rækker totalt:",size:"Side størrelse:",first:"Første side",next:"Næste side",previous:"Forrige side",last:"Sidste side"},menu:{text:"Vælg kolonner:"},column:{hide:"Skjul kolonne"},aggregation:{count:"samlede rækker: ",sum:"smalede: ",avg:"gns: ",min:"min: ",max:"max: "},gridMenu:{columns:"Columns:",importerTitle:"Import file",exporterAllAsCsv:"Export all data as csv",exporterVisibleAsCsv:"Export visible data as csv",exporterSelectedAsCsv:"Export selected data as csv",exporterAllAsPdf:"Export all data as pdf",exporterVisibleAsPdf:"Export visible data as pdf",exporterSelectedAsPdf:"Export selected data as pdf"},importer:{noHeaders:"Column names were unable to be derived, does the file have a header?",noObjects:"Objects were not able to be derived, was there data in the file other than headers?",invalidCsv:"File was unable to be processed, is it valid CSV?",invalidJson:"File was unable to be processed, is it valid Json?",jsonNotArray:"Imported json file must contain an array, aborting."}}),a}])}])}(),function(){angular.module("ui.grid").config(["$provide",function(a){a.decorator("i18nService",["$delegate",function(a){return a.add("de",{aggregate:{label:"eintrag"},groupPanel:{description:"Ziehen Sie eine Spaltenüberschrift hierhin, um nach dieser Spalte zu gruppieren."},search:{placeholder:"Suche...",showingItems:"Zeige Einträge:",selectedItems:"Ausgewählte Einträge:",totalItems:"Einträge gesamt:",size:"Einträge pro Seite:",first:"Erste Seite",next:"Nächste Seite",previous:"Vorherige Seite",last:"Letzte Seite"},menu:{text:"Spalten auswählen:"},sort:{ascending:"aufsteigend sortieren",descending:"absteigend sortieren",remove:"Sortierung zurücksetzen"},column:{hide:"Spalte ausblenden"},aggregation:{count:"Zeilen insgesamt: ",sum:"gesamt: ",avg:"Durchschnitt: ",min:"min: ",max:"max: "},gridMenu:{columns:"Spalten:",importerTitle:"Datei importieren",exporterAllAsCsv:"Alle Daten als CSV exportieren",exporterVisibleAsCsv:"sichtbare Daten als CSV exportieren",exporterSelectedAsCsv:"markierte Daten als CSV exportieren",exporterAllAsPdf:"Alle Daten als PDF exportieren",exporterVisibleAsPdf:"sichtbare Daten als PDF exportieren",exporterSelectedAsPdf:"markierte Daten als CSV exportieren"},importer:{noHeaders:"Es konnten keine Spaltennamen ermittelt werden. Sind in der Datei Spaltendefinitionen enthalten?",noObjects:"Es konnten keine Zeileninformationen gelesen werden, Sind in der Datei außer den Spaltendefinitionen auch Daten enthalten?",invalidCsv:"Die Datei konnte nicht eingelesen werden, ist es eine gültige CSV-Datei?",invalidJson:"Die Datei konnte nicht eingelesen werden. Enthält sie gültiges JSON?",jsonNotArray:"Die importierte JSON-Datei muß ein Array enthalten. Breche Import ab."}}),a}])}])}(),function(){angular.module("ui.grid").config(["$provide",function(a){a.decorator("i18nService",["$delegate",function(a){return a.add("en",{aggregate:{label:"items"},groupPanel:{description:"Drag a column header here and drop it to group by that column."},search:{placeholder:"Search...",showingItems:"Showing Items:",selectedItems:"Selected Items:",totalItems:"Total Items:",size:"Page Size:",first:"First Page",next:"Next Page",previous:"Previous Page",last:"Last Page"},menu:{text:"Choose Columns:"},sort:{ascending:"Sort Ascending",descending:"Sort Descending",remove:"Remove Sort"},column:{hide:"Hide Column"},aggregation:{count:"total rows: ",sum:"total: ",avg:"avg: ",min:"min: ",max:"max: "},pinning:{pinLeft:"Pin Left",pinRight:"Pin Right",unpin:"Unpin"},gridMenu:{columns:"Columns:",importerTitle:"Import file",exporterAllAsCsv:"Export all data as csv",exporterVisibleAsCsv:"Export visible data as csv",exporterSelectedAsCsv:"Export selected data as csv",exporterAllAsPdf:"Export all data as pdf",exporterVisibleAsPdf:"Export visible data as pdf",exporterSelectedAsPdf:"Export selected data as pdf"},importer:{noHeaders:"Column names were unable to be derived, does the file have a header?",noObjects:"Objects were not able to be derived, was there data in the file other than headers?",invalidCsv:"File was unable to be processed, is it valid CSV?",invalidJson:"File was unable to be processed, is it valid Json?",jsonNotArray:"Imported json file must contain an array, aborting."},paging:{sizes:"items per page",totalItems:"items"}}),a}])}])}(),function(){angular.module("ui.grid").config(["$provide",function(a){a.decorator("i18nService",["$delegate",function(a){return a.add("es",{aggregate:{label:"Artículos"},groupPanel:{description:"Arrastre un encabezado de columna aquí y suéltelo para agrupar por esa columna."},search:{placeholder:"Buscar...",showingItems:"Artículos Mostrados:",selectedItems:"Artículos Seleccionados:",totalItems:"Artículos Totales:",size:"Tamaño de Página:",first:"Primera Página",next:"Página Siguiente",previous:"Página Anterior",last:"Última Página"},menu:{text:"Elegir columnas:"},sort:{ascending:"Orden Ascendente",descending:"Orden Descendente",remove:"Sin Ordenar"},column:{hide:"Ocultar la columna"},aggregation:{count:"filas totales: ",sum:"total: ",avg:"media: ",min:"min: ",max:"max: "},pinning:{pinLeft:"Fijar a la Izquierda",pinRight:"Fijar a la Derecha",unpin:"Quitar Fijación"},gridMenu:{columns:"Columnas:",importerTitle:"Importar archivo",exporterAllAsCsv:"Exportar todo como csv",exporterVisibleAsCsv:"Exportar vista como csv",exporterSelectedAsCsv:"Exportar selección como csv",exporterAllAsPdf:"Exportar todo como pdf",exporterVisibleAsPdf:"Exportar vista como pdf",exporterSelectedAsPdf:"Exportar selección como pdf"},importer:{noHeaders:"No fue posible derivar los nombres de las columnas, ¿tiene encabezados el archivo?",noObjects:"No fue posible obtener registros, ¿contiene datos el archivo, aparte de los encabezados?",invalidCsv:"No fue posible procesar el archivo, ¿es un CSV válido?",invalidJson:"No fue posible procesar el archivo, ¿es un Json válido?",jsonNotArray:"El archivo json importado debe contener un array, abortando."}}),a}])}])}(),function(){angular.module("ui.grid").config(["$provide",function(a){a.decorator("i18nService",["$delegate",function(a){return a.add("fa",{aggregate:{label:"موردها"},groupPanel:{description:"یک عنوان ستون اینجا را بردار و به گروهی از آن ستون بیانداز."},search:{placeholder:"جستجو...",showingItems:"نمایش موردها:",selectedItems:"موردهای انتخاب‌شده:",totalItems:"همهٔ موردها:",size:"اندازهٔ صفحه:",first:"صفحهٔ اول",next:"صفحهٔ بعد",previous:"صفحهٔ قبل",last:"آخرین صفحه"},menu:{text:"انتخاب ستون‌ها:"},column:{hide:"ستون پنهان کن"},aggregation:{count:"total rows: ",sum:"total: ",avg:"avg: ",min:"min: ",max:"max: "},gridMenu:{columns:"Columns:",importerTitle:"Import file",exporterAllAsCsv:"Export all data as csv",exporterVisibleAsCsv:"Export visible data as csv",exporterSelectedAsCsv:"Export selected data as csv",exporterAllAsPdf:"Export all data as pdf",exporterVisibleAsPdf:"Export visible data as pdf",exporterSelectedAsPdf:"Export selected data as pdf"},importer:{noHeaders:"Column names were unable to be derived, does the file have a header?",noObjects:"Objects were not able to be derived, was there data in the file other than headers?",invalidCsv:"File was unable to be processed, is it valid CSV?",invalidJson:"File was unable to be processed, is it valid Json?",jsonNotArray:"Imported json file must contain an array, aborting."}}),a}])}])}(),function(){angular.module("ui.grid").config(["$provide",function(a){a.decorator("i18nService",["$delegate",function(a){return a.add("fi",{aggregate:{label:"rivit"},groupPanel:{description:"Raahaa ja pudota otsikko tähän ryhmittääksesi sarakkeen mukaan."},search:{placeholder:"Hae...",showingItems:"Näytetään rivejä:",selectedItems:"Valitut rivit:",totalItems:"Rivejä yht.:",size:"Näytä:",first:"Ensimmäinen sivu",next:"Seuraava sivu",previous:"Edellinen sivu",last:"Viimeinen sivu"},menu:{text:"Valitse sarakkeet:"},sort:{ascending:"Järjestä nouseva",descending:"Järjestä laskeva",remove:"Poista järjestys"},column:{hide:"Piilota sarake"},aggregation:{count:"Rivejä yht.: ",sum:"Summa: ",avg:"K.a.: ",min:"Min: ",max:"Max: "},pinning:{pinLeft:"Lukitse vasemmalle",pinRight:"Lukitse oikealle",unpin:"Poista lukitus"},gridMenu:{columns:"Sarakkeet:",importerTitle:"Tuo tiedosto",exporterAllAsCsv:"Vie tiedot csv-muodossa",exporterVisibleAsCsv:"Vie näkyvä tieto csv-muodossa",exporterSelectedAsCsv:"Vie valittu tieto csv-muodossa",exporterAllAsPdf:"Vie tiedot pdf-muodossa",exporterVisibleAsPdf:"Vie näkyvä tieto pdf-muodossa",exporterSelectedAsPdf:"Vie valittu tieto pdf-muodossa"},importer:{noHeaders:"Sarakkeen nimiä ei voitu päätellä, onko tiedostossa otsikkoriviä?",noObjects:"Tietoja ei voitu lukea, onko tiedostossa muuta kuin otsikkot?",invalidCsv:"Tiedostoa ei voitu käsitellä, oliko se CSV-muodossa?",invalidJson:"Tiedostoa ei voitu käsitellä, oliko se JSON-muodossa?",jsonNotArray:"Tiedosto ei sisältänyt taulukkoa, lopetetaan."}}),a
}])}])}(),function(){angular.module("ui.grid").config(["$provide",function(a){a.decorator("i18nService",["$delegate",function(a){return a.add("fr",{aggregate:{label:"articles"},groupPanel:{description:"Faites glisser un en-tête de colonne ici et déposez-le vers un groupe par cette colonne."},search:{placeholder:"Recherche...",showingItems:"Articles Affichage des:",selectedItems:"Éléments Articles:",totalItems:"Nombre total d'articles:",size:"Taille de page:",first:"Première page",next:"Page Suivante",previous:"Page précédente",last:"Dernière page"},menu:{text:"Choisir des colonnes:"},sort:{ascending:"Trier par ordre croissant",descending:"Trier par ordre décroissant",remove:"Enlever le tri"},column:{hide:"Cacher la colonne"},aggregation:{count:"total lignes: ",sum:"total: ",avg:"moy: ",min:"min: ",max:"max: "},pinning:{pinLeft:"Épingler à gauche",pinRight:"Épingler à droite",unpin:"Détacher"},gridMenu:{columns:"Colonnes:",importerTitle:"Importer un fichier",exporterAllAsCsv:"Exporter toutes les données en CSV",exporterVisibleAsCsv:"Exporter les données visibles en CSV",exporterSelectedAsCsv:"Exporter les données sélectionnées en CSV",exporterAllAsPdf:"Exporter toutes les données en PDF",exporterVisibleAsPdf:"Exporter les données visibles en PDF",exporterSelectedAsPdf:"Exporter les données sélectionnées en PDF"},importer:{noHeaders:"Impossible de déterminer le nom des colonnes, le fichier possède-t-il un en-tête ?",noObjects:"Aucun objet trouvé, le fichier possède-t-il des données autres que l'en-tête ?",invalidCsv:"Le fichier n'a pas pu être traité, le CSV est-il valide ?",invalidJson:"Le fichier n'a pas pu être traité, le JSON est-il valide ?",jsonNotArray:"Le fichier JSON importé doit contenir un tableau. Abandon."}}),a}])}])}(),function(){angular.module("ui.grid").config(["$provide",function(a){a.decorator("i18nService",["$delegate",function(a){return a.add("he",{aggregate:{label:"items"},groupPanel:{description:"גרור עמודה לכאן ושחרר בכדי לקבץ עמודה זו."},search:{placeholder:"חפש...",showingItems:"מציג:",selectedItems:'סה"כ נבחרו:',totalItems:'סה"כ רשומות:',size:"תוצאות בדף:",first:"דף ראשון",next:"דף הבא",previous:"דף קודם",last:"דף אחרון"},menu:{text:"בחר עמודות:"},sort:{ascending:"סדר עולה",descending:"סדר יורד",remove:"בטל"},column:{hide:"טור הסתר"},aggregation:{count:"total rows: ",sum:"total: ",avg:"avg: ",min:"min: ",max:"max: "},gridMenu:{columns:"Columns:",importerTitle:"Import file",exporterAllAsCsv:"Export all data as csv",exporterVisibleAsCsv:"Export visible data as csv",exporterSelectedAsCsv:"Export selected data as csv",exporterAllAsPdf:"Export all data as pdf",exporterVisibleAsPdf:"Export visible data as pdf",exporterSelectedAsPdf:"Export selected data as pdf"},importer:{noHeaders:"Column names were unable to be derived, does the file have a header?",noObjects:"Objects were not able to be derived, was there data in the file other than headers?",invalidCsv:"File was unable to be processed, is it valid CSV?",invalidJson:"File was unable to be processed, is it valid Json?",jsonNotArray:"Imported json file must contain an array, aborting."}}),a}])}])}(),function(){angular.module("ui.grid").config(["$provide",function(a){a.decorator("i18nService",["$delegate",function(a){return a.add("it",{aggregate:{label:"elementi"},groupPanel:{description:"Trascina un'intestazione all'interno del gruppo della colonna."},search:{placeholder:"Ricerca...",showingItems:"Mostra:",selectedItems:"Selezionati:",totalItems:"Totali:",size:"Tot Pagine:",first:"Prima",next:"Prossima",previous:"Precedente",last:"Ultima"},menu:{text:"Scegli le colonne:"},sort:{ascending:"Asc.",descending:"Desc.",remove:"Annulla ordinamento"},column:{hide:"Nascondi"},aggregation:{count:"righe totali: ",sum:"tot: ",avg:"media: ",min:"minimo: ",max:"massimo: "},pinning:{pinLeft:"Blocca a sx",pinRight:"Blocca a dx",unpin:"Blocca in alto"},gridMenu:{columns:"Colonne:",importerTitle:"Importa",exporterAllAsCsv:"Esporta tutti i dati in CSV",exporterVisibleAsCsv:"Esporta i dati visibili in CSV",exporterSelectedAsCsv:"Esporta i dati selezionati in CSV",exporterAllAsPdf:"Esporta tutti i dati in PDF",exporterVisibleAsPdf:"Esporta i dati visibili in PDF",exporterSelectedAsPdf:"Esporta i dati selezionati in PDF"},importer:{noHeaders:"Impossibile reperire i nomi delle colonne, sicuro che siano indicati all'interno del file?",noObjects:"Impossibile reperire gli oggetti, sicuro che siano indicati all'interno del file?",invalidCsv:"Impossibile elaborare il file, sicuro che sia un CSV?",invalidJson:"Impossibile elaborare il file, sicuro che sia un JSON valido?",jsonNotArray:"Errore! Il file JSON da importare deve contenere un array."}}),a}])}])}(),function(){angular.module("ui.grid").config(["$provide",function(a){a.decorator("i18nService",["$delegate",function(a){return a.add("nl",{aggregate:{label:"items"},groupPanel:{description:"Sleep hier een kolomnaam heen om op te groeperen."},search:{placeholder:"Zoeken...",showingItems:"Getoonde items:",selectedItems:"Geselecteerde items:",totalItems:"Totaal aantal items:",size:"Items per pagina:",first:"Eerste pagina",next:"Volgende pagina",previous:"Vorige pagina",last:"Laatste pagina"},menu:{text:"Kies kolommen:"},sort:{ascending:"Sorteer oplopend",descending:"Sorteer aflopend",remove:"Verwijder sortering"},column:{hide:"Verberg kolom"},aggregation:{count:"Aantal rijen: ",sum:"Som: ",avg:"Gemiddelde: ",min:"Min: ",max:"Max: "},pinning:{pinLeft:"Zet links vast",pinRight:"Zet rechts vast",unpin:"Maak los"},gridMenu:{columns:"Kolommen:",importerTitle:"Importeer bestand",exporterAllAsCsv:"Exporteer alle data als csv",exporterVisibleAsCsv:"Exporteer zichtbare data als csv",exporterSelectedAsCsv:"Exporteer geselecteerde data als csv",exporterAllAsPdf:"Exporteer alle data als pdf",exporterVisibleAsPdf:"Exporteer zichtbare data als pdf",exporterSelectedAsPdf:"Exporteer geselecteerde data als pdf"},importer:{noHeaders:"Kolomnamen kunnen niet worden afgeleid. Heeft het bestand een header?",noObjects:"Objecten kunnen niet worden afgeleid. Bevat het bestand data naast de headers?",invalidCsv:"Het bestand kan niet verwerkt worden. Is het een valide csv bestand?",invalidJson:"Het bestand kan niet verwerkt worden. Is het valide json?",jsonNotArray:"Het json bestand moet een array bevatten. De actie wordt geannuleerd."}}),a}])}])}(),function(){angular.module("ui.grid").config(["$provide",function(a){a.decorator("i18nService",["$delegate",function(a){return a.add("pt-br",{aggregate:{label:"itens"},groupPanel:{description:"Arraste e solte uma coluna aqui para agrupar por essa coluna"},search:{placeholder:"Procurar...",showingItems:"Mostrando os Itens:",selectedItems:"Items Selecionados:",totalItems:"Total de Itens:",size:"Tamanho da Página:",first:"Primeira Página",next:"Próxima Página",previous:"Página Anterior",last:"Última Página"},menu:{text:"Selecione as colunas:"},sort:{ascending:"Ordenar Ascendente",descending:"Ordenar Descendente",remove:"Remover Ordenação"},column:{hide:"Esconder coluna"},aggregation:{count:"total de linhas: ",sum:"total: ",avg:"med: ",min:"min: ",max:"max: "},pinning:{pinLeft:"Fixar Esquerda",pinRight:"Fixar Direita",unpin:"Desprender"},gridMenu:{columns:"Colunas:",exporterAllAsCsv:"Exportar todos os dados como csv",exporterVisibleAsCsv:"Exportar dados visíveis como csv",exporterSelectedAsCsv:"Exportar dados selecionados como csv",exporterAllAsPdf:"Exportar todos os dados como pdf",exporterVisibleAsPdf:"Exportar dados visíveis como pdf",exporterSelectedAsPdf:"Exportar dados selecionados como pdf"},importer:{noHeaders:"Nomes de colunas não puderam ser derivados. O arquivo tem um cabeçalho?",noObjects:"Objetos não puderam ser derivados. Havia dados no arquivo, além dos cabeçalhos?",invalidCsv:"Arquivo não pode ser processado. É um CSV válido?",invalidJson:"Arquivo não pode ser processado. É um Json válido?",jsonNotArray:"Arquivo json importado tem que conter um array. Abortando."}}),a}])}])}(),function(){angular.module("ui.grid").config(["$provide",function(a){a.decorator("i18nService",["$delegate",function(a){return a.add("ru",{aggregate:{label:"элементы"},groupPanel:{description:"Для группировки по столбцу перетащите сюда его название."},search:{placeholder:"Поиск...",showingItems:"Показать элементы:",selectedItems:"Выбранные элементы:",totalItems:"Всего элементов:",size:"Размер страницы:",first:"Первая страница",next:"Следующая страница",previous:"Предыдущая страница",last:"Последняя страница"},menu:{text:"Выбрать столбцы:"},sort:{ascending:"По возрастанию",descending:"По убыванию",remove:"Убрать сортировку"},column:{hide:"спрятать столбец"},aggregation:{count:"total rows: ",sum:"total: ",avg:"avg: ",min:"min: ",max:"max: "},gridMenu:{columns:"Columns:",importerTitle:"Import file",exporterAllAsCsv:"Export all data as csv",exporterVisibleAsCsv:"Export visible data as csv",exporterSelectedAsCsv:"Export selected data as csv",exporterAllAsPdf:"Export all data as pdf",exporterVisibleAsPdf:"Export visible data as pdf",exporterSelectedAsPdf:"Export selected data as pdf"},importer:{noHeaders:"Column names were unable to be derived, does the file have a header?",noObjects:"Objects were not able to be derived, was there data in the file other than headers?",invalidCsv:"File was unable to be processed, is it valid CSV?",invalidJson:"File was unable to be processed, is it valid Json?",jsonNotArray:"Imported json file must contain an array, aborting."}}),a}])}])}(),function(){angular.module("ui.grid").config(["$provide",function(a){a.decorator("i18nService",["$delegate",function(a){return a.add("sk",{aggregate:{label:"items"},groupPanel:{description:"Pretiahni sem názov stĺpca pre zoskupenie podľa toho stĺpca."},search:{placeholder:"Hľadaj...",showingItems:"Zobrazujem položky:",selectedItems:"Vybraté položky:",totalItems:"Počet položiek:",size:"Počet:",first:"Prvá strana",next:"Ďalšia strana",previous:"Predchádzajúca strana",last:"Posledná strana"},menu:{text:"Vyberte stĺpce:"},sort:{ascending:"Zotriediť vzostupne",descending:"Zotriediť zostupne",remove:"Vymazať triedenie"},aggregation:{count:"total rows: ",sum:"total: ",avg:"avg: ",min:"min: ",max:"max: "},gridMenu:{columns:"Columns:",importerTitle:"Import file",exporterAllAsCsv:"Export all data as csv",exporterVisibleAsCsv:"Export visible data as csv",exporterSelectedAsCsv:"Export selected data as csv",exporterAllAsPdf:"Export all data as pdf",exporterVisibleAsPdf:"Export visible data as pdf",exporterSelectedAsPdf:"Export selected data as pdf"},importer:{noHeaders:"Column names were unable to be derived, does the file have a header?",noObjects:"Objects were not able to be derived, was there data in the file other than headers?",invalidCsv:"File was unable to be processed, is it valid CSV?",invalidJson:"File was unable to be processed, is it valid Json?",jsonNotArray:"Imported json file must contain an array, aborting."}}),a}])}])}(),function(){angular.module("ui.grid").config(["$provide",function(a){a.decorator("i18nService",["$delegate",function(a){return a.add("sv",{aggregate:{label:"Artiklar"},groupPanel:{description:"Dra en kolumnrubrik hit och släpp den för att gruppera efter den kolumnen."},search:{placeholder:"Sök...",showingItems:"Visar artiklar:",selectedItems:"Valda artiklar:",totalItems:"Antal artiklar:",size:"Sidstorlek:",first:"Första sidan",next:"Nästa sida",previous:"Föregående sida",last:"Sista sidan"},menu:{text:"Välj kolumner:"},sort:{ascending:"Sortera stigande",descending:"Sortera fallande",remove:"Inaktivera sortering"},column:{hide:"Göm kolumn"},aggregation:{count:"Antal rader: ",sum:"Summa: ",avg:"Genomsnitt: ",min:"Min: ",max:"Max: "},pinning:{pinLeft:"Fäst vänster",pinRight:"Fäst höger",unpin:"Lösgör"},gridMenu:{columns:"Kolumner:",importerTitle:"Importera fil",exporterAllAsCsv:"Exportera all data som CSV",exporterVisibleAsCsv:"Exportera synlig data som CSV",exporterSelectedAsCsv:"Exportera markerad data som CSV",exporterAllAsPdf:"Exportera all data som PDF",exporterVisibleAsPdf:"Exportera synlig data som PDF",exporterSelectedAsPdf:"Exportera markerad data som PDF"},importer:{noHeaders:"Kolumnnamn kunde inte härledas. Har filen ett sidhuvud?",noObjects:"Objekt kunde inte härledas. Har filen data undantaget sidhuvud?",invalidCsv:"Filen kunde inte behandlas, är den en giltig CSV?",invalidJson:"Filen kunde inte behandlas, är den en giltig JSON?",jsonNotArray:"Importerad JSON-fil måste innehålla ett fält. Import avbruten."},paging:{sizes:"Artiklar per sida",totalItems:"Artiklar"}}),a}])}])}(),function(){var a=["uiT","uiTranslate"],b=["t","uiTranslate"],c=angular.module("ui.grid.i18n");c.constant("i18nConstants",{MISSING:"[MISSING]",UPDATE_EVENT:"$uiI18n",LOCALE_DIRECTIVE_ALIAS:"uiI18n",DEFAULT_LANG:"en"}),c.service("i18nService",["$log","i18nConstants","$rootScope",function(a,b,c){var d={_langs:{},current:null,get:function(a){return this._langs[a.toLowerCase()]},add:function(a,b){var c=a.toLowerCase();this._langs[c]||(this._langs[c]={}),angular.extend(this._langs[c],b)},getAllLangs:function(){var a=[];if(!this._langs)return a;for(var b in this._langs)a.push(b);return a},setCurrent:function(a){this.current=a.toLowerCase()},getCurrentLang:function(){return this.current}},e={add:function(a,b){"object"==typeof a?angular.forEach(a,function(a){a&&d.add(a,b)}):d.add(a,b)},getAllLangs:function(){return d.getAllLangs()},get:function(a){var b=a?a:e.getCurrentLang();return d.get(b)},getSafeText:function(a,c){var f=c?c:e.getCurrentLang(),g=d.get(f);if(!g)return b.MISSING;for(var h=a.split("."),i=g,j=0;j<h.length;++j){if(void 0===i[h[j]]||null===i[h[j]])return b.MISSING;i=i[h[j]]}return i},setCurrentLang:function(a){a&&(d.setCurrent(a),c.$broadcast(b.UPDATE_EVENT))},getCurrentLang:function(){var a=d.getCurrentLang();return a||(a=b.DEFAULT_LANG,d.setCurrent(a)),a}};return e}]);var d=function(a,b){return{compile:function(){return{pre:function(c,d,e){var f=b.LOCALE_DIRECTIVE_ALIAS,g=c.$eval(e[f]);g?c.$watch(e[f],function(){a.setCurrentLang(g)}):e.$$observers&&e.$observe(f,function(){a.setCurrentLang(e[f]||b.DEFAULT_LANG)})}}}}};c.directive("uiI18n",["i18nService","i18nConstants",d]);var e=function(b,c,d){return{restrict:"EA",compile:function(){return{pre:function(e,f,g){var h,i=a[0],j=a[1],k=g[i]||g[j]||f.html(),l=d.MISSING+k;if(g.$$observers){var m=g[i]?i:j;h=g.$observe(m,function(a){a&&f.html(b(a)(c.getCurrentLang())||l)})}var n=b(k),o=e.$on(d.UPDATE_EVENT,function(){h?h(g[i]||g[j]):f.html(n(c.get())||l)});e.$on("$destroy",o),f.html(n(c.get())||l)}}}}};angular.forEach(a,function(a){c.directive(a,["$parse","i18nService","i18nConstants",e])});var f=function(a,b,c){return function(d){var e=a(d);return e(b.get())||c.MISSING+d}};angular.forEach(b,function(a){c.filter(a,["$parse","i18nService","i18nConstants",f])})}(),function(){angular.module("ui.grid").config(["$provide",function(a){a.decorator("i18nService",["$delegate",function(a){return a.add("zh-cn",{aggregate:{label:"行"},groupPanel:{description:"拖曳表头到此处进行分组"},search:{placeholder:"查找",showingItems:"已显示行数：",selectedItems:"已选择行数：",totalItems:"总行数：",size:"每页显示行数：",first:"首页",next:"下一页",previous:"上一页",last:"末页"},menu:{text:"选择列："},sort:{ascending:"升序",descending:"降序",remove:"取消排序"},column:{hide:"隐藏列"},aggregation:{count:"计数：",sum:"求和：",avg:"均值：",min:"最小值：",max:"最大值："},pinning:{pinLeft:"左侧固定",pinRight:"右侧固定",unpin:"取消固定"},gridMenu:{columns:"列：",importerTitle:"导入文件",exporterAllAsCsv:"导出全部数据到CSV",exporterVisibleAsCsv:"导出可见数据到CSV",exporterSelectedAsCsv:"导出已选数据到CSV",exporterAllAsPdf:"导出全部数据到PDF",exporterVisibleAsPdf:"导出可见数据到PDF",exporterSelectedAsPdf:"导出已选数据到PDF"},importer:{noHeaders:"无法获取列名，确定文件包含表头？",noObjects:"无法获取数据，确定文件包含数据？",invalidCsv:"无法处理文件，确定是合法的CSV文件？",invalidJson:"无法处理文件，确定是合法的JSON文件？",jsonNotArray:"导入的文件不是JSON数组！"}}),a}])}])}(),function(){angular.module("ui.grid").config(["$provide",function(a){a.decorator("i18nService",["$delegate",function(a){return a.add("zh-tw",{aggregate:{label:"行"},groupPanel:{description:"拖曳表頭到此處進行分組"},search:{placeholder:"查找",showingItems:"已顯示行數：",selectedItems:"已選擇行數：",totalItems:"總行數：",size:"每頁顯示行數：",first:"首頁",next:"下壹頁",previous:"上壹頁",last:"末頁"},menu:{text:"選擇列："},sort:{ascending:"升序",descending:"降序",remove:"取消排序"},column:{hide:"隱藏列"},aggregation:{count:"計數：",sum:"求和：",avg:"均值：",min:"最小值：",max:"最大值："},pinning:{pinLeft:"左側固定",pinRight:"右側固定",unpin:"取消固定"},gridMenu:{columns:"列：",importerTitle:"導入文件",exporterAllAsCsv:"導出全部數據到CSV",exporterVisibleAsCsv:"導出可見數據到CSV",exporterSelectedAsCsv:"導出已選數據到CSV",exporterAllAsPdf:"導出全部數據到PDF",exporterVisibleAsPdf:"導出可見數據到PDF",exporterSelectedAsPdf:"導出已選數據到PDF"},importer:{noHeaders:"無法獲取列名，確定文件包含表頭？",noObjects:"無法獲取數據，確定文件包含數據？",invalidCsv:"無法處理文件，確定是合法的CSV文件？",invalidJson:"無法處理文件，確定是合法的JSON文件？",jsonNotArray:"導入的文件不是JSON數組！"}}),a}])}])}(),function(){"use strict";var a=angular.module("ui.grid.autoResize",["ui.grid"]);a.directive("uiGridAutoResize",["$timeout","gridUtil",function(a,b){return{require:"uiGrid",scope:!1,link:function(a,c,d,e){function f(){i=b.elementHeight(c),h=b.elementWidth(c)}function g(){clearTimeout(j),j=setTimeout(function(){var d=b.elementHeight(c),j=b.elementWidth(c);d!==i||j!==h?(e.grid.gridHeight=d,e.grid.gridWidth=j,a.$apply(function(){e.grid.refresh().then(function(){f(),g()})})):g()},250)}var h,i;f();var j;g(),a.$on("$destroy",function(){clearTimeout(j)})}}}])}(),function(){"use strict";function a(a,b){this.row=a,this.col=b}var b=angular.module("ui.grid.cellNav",["ui.grid"]);b.constant("uiGridCellNavConstants",{FEATURE_NAME:"gridCellNav",CELL_NAV_EVENT:"cellNav",direction:{LEFT:0,RIGHT:1,UP:2,DOWN:3},EVENT_TYPE:{KEYDOWN:0,CLICK:1}}),b.factory("uiGridCellNavFactory",["gridUtil","uiGridConstants","uiGridCellNavConstants","$q",function(b,c,d){var e=function(a,b,c,d){this.rows=a.visibleRowCache,this.columns=b.visibleColumnCache,this.leftColumns=c?c.visibleColumnCache:[],this.rightColumns=d?d.visibleColumnCache:[]};return e.prototype.getFocusableCols=function(){var a=this.leftColumns.concat(this.columns,this.rightColumns);return a.filter(function(a){return a.colDef.allowCellFocus})},e.prototype.getNextRowCol=function(a,b,c){switch(a){case d.direction.LEFT:return this.getRowColLeft(b,c);case d.direction.RIGHT:return this.getRowColRight(b,c);case d.direction.UP:return this.getRowColUp(b,c);case d.direction.DOWN:return this.getRowColDown(b,c)}},e.prototype.getRowColLeft=function(b,c){var d=this.getFocusableCols(),e=d.indexOf(c),f=this.rows.indexOf(b);-1===e&&(e=1);var g=0===e?d.length-1:e-1;return g>e?0===f?new a(b,d[g]):new a(this.rows[f-1],d[g]):new a(b,d[g])},e.prototype.getRowColRight=function(b,c){var d=this.getFocusableCols(),e=d.indexOf(c),f=this.rows.indexOf(b);-1===e&&(e=0);var g=e===d.length-1?0:e+1;return e>g?f===this.rows.length-1?new a(b,d[g]):new a(this.rows[f+1],d[g]):new a(b,d[g])},e.prototype.getRowColDown=function(b,c){var d=this.getFocusableCols(),e=d.indexOf(c),f=this.rows.indexOf(b);return-1===e&&(e=0),f===this.rows.length-1?new a(b,d[e]):new a(this.rows[f+1],d[e])},e.prototype.getRowColUp=function(b,c){var d=this.getFocusableCols(),e=d.indexOf(c),f=this.rows.indexOf(b);return-1===e&&(e=0),0===f?new a(b,d[e]):new a(this.rows[f-1],d[e])},e}]),b.service("uiGridCellNavService",["gridUtil","uiGridConstants","uiGridCellNavConstants","$q","uiGridCellNavFactory",function(a,b,c,d,e){var f={initializeGrid:function(a){a.registerColumnBuilder(f.cellNavColumnBuilder),a.cellNav={},a.cellNav.lastRowCol=null;var b={events:{cellNav:{navigate:function(){}}},methods:{cellNav:{scrollTo:function(a,b,c,d){f.scrollTo(a,b,c,d)},getFocusedCell:function(){return a.cellNav.lastRowCol}}}};a.api.registerEventsFromObject(b.events),a.api.registerMethodsFromObject(b.methods)},decorateRenderContainers:function(a){var b=a.hasRightContainer()?a.renderContainers.right:null,c=a.hasLeftContainer()?a.renderContainers.left:null;null!==c&&(a.renderContainers.left.cellNav=new e(a.renderContainers.body,c,b,a.renderContainers.body)),null!==b&&(a.renderContainers.right.cellNav=new e(a.renderContainers.body,b,a.renderContainers.body,c)),a.renderContainers.body.cellNav=new e(a.renderContainers.body,a.renderContainers.body,c,b)},getDirection:function(a){return a.keyCode===b.keymap.LEFT||a.keyCode===b.keymap.TAB&&a.shiftKey?c.direction.LEFT:a.keyCode===b.keymap.RIGHT||a.keyCode===b.keymap.TAB?c.direction.RIGHT:a.keyCode===b.keymap.UP||a.keyCode===b.keymap.ENTER&&a.shiftKey?c.direction.UP:a.keyCode===b.keymap.DOWN||a.keyCode===b.keymap.ENTER?c.direction.DOWN:null},cellNavColumnBuilder:function(a){var b=[];return a.allowCellFocus=void 0===a.allowCellFocus?!0:a.allowCellFocus,d.all(b)},scrollTo:function(a,b,c,d){var e=null,f=null;null!==c&&(e=a.getRow(c)),null!==d&&(f=a.getColumn(d.name?d.name:d.field)),this.scrollToInternal(a,b,e,f)},scrollToInternal:function(a,c,d,e){var f={};if(null!==d){var g=a.renderContainers.body.visibleRowCache.indexOf(d),h=a.renderContainers.body.visibleRowCache.length,i=(g+g/(h-1))/h;f.y={percentage:i}}null!==e&&(f.x={percentage:this.getLeftWidth(a,e)/this.getLeftWidth(a,a.renderContainers.body.visibleColumnCache[a.renderContainers.body.visibleColumnCache.length-1])}),(f.y||f.x)&&c.$broadcast(b.events.GRID_SCROLL,f)},scrollToIfNecessary:function(a,c,d,e){var f={},g=a.renderContainers.body.visibleRowCache,h=a.renderContainers.body.visibleColumnCache,i=a.renderContainers.body.prevScrollTop+a.headerHeight;i=0>i?0:i;var j=a.renderContainers.body.prevScrollLeft,k=a.renderContainers.body.prevScrollTop+a.gridHeight-a.headerHeight;a.horizontalScrollbarHeight&&(k-=a.horizontalScrollbarHeight);var l=a.renderContainers.body.prevScrollLeft+a.gridWidth;if(a.verticalScrollbarWidth&&(l-=a.verticalScrollbarWidth),null!==d){var m=g.indexOf(d),n=a.renderContainers.body.getCanvasHeight()-a.renderContainers.body.getViewportHeight();a.horizontalScrollbarHeight&&a.horizontalScrollbarHeight>0&&(n+=a.horizontalScrollbarHeight);var o=(m+1)*a.options.rowHeight;o=0>o?0:o;var p,q;i>o?(p=a.renderContainers.body.prevScrollTop-(i-o),q=p/n,f.y={percentage:q}):o>k&&(p=o-k+a.renderContainers.body.prevScrollTop,q=p/n,f.y={percentage:q})}if(null!==e){for(var r=h.indexOf(e),s=a.renderContainers.body.getCanvasWidth()-a.renderContainers.body.getViewportWidth(),t=0,u=0;r>u;u++){var v=h[u];t+=v.drawnWidth}t=0>t?0:t;var w=t+e.drawnWidth;w=0>w?0:w;var x,y;j>t?(x=a.renderContainers.body.prevScrollLeft-(j-t),y=x/s,y=y>1?1:y,f.x={percentage:y}):w>l&&(x=w-l+a.renderContainers.body.prevScrollLeft,y=x/s,y=y>1?1:y,f.x={percentage:y})}(f.y||f.x)&&c.$broadcast(b.events.GRID_SCROLL,f)},getLeftWidth:function(a,b){var c=0;if(!b)return c;var d=a.renderContainers.body.visibleColumnCache.indexOf(b);a.renderContainers.body.visibleColumnCache.forEach(function(a,b){d>b&&(c+=a.drawnWidth)});var e=0===d?0:(d+1)/a.renderContainers.body.visibleColumnCache.length;return c+=b.drawnWidth*e}};return f}]),b.directive("uiGridCellnav",["gridUtil","uiGridCellNavService","uiGridCellNavConstants",function(b,c,d){return{replace:!0,priority:-150,require:"^uiGrid",scope:!1,controller:function(){},compile:function(){return{pre:function(b,e,f,g){var h=g.grid;c.initializeGrid(h),g.cellNav={},g.cellNav.focusCell=function(a,b){g.cellNav.broadcastCellNav({row:a,col:b})},g.cellNav.broadcastCellNav=function(a){b.$broadcast(d.CELL_NAV_EVENT,a),g.cellNav.broadcastFocus(a)},g.cellNav.broadcastFocus=function(b){var c=b.row,d=b.col;if(null===h.cellNav.lastRowCol||h.cellNav.lastRowCol.row!==c||h.cellNav.lastRowCol.col!==d){var e=new a(c,d);h.api.cellNav.raise.navigate(e,h.cellNav.lastRowCol),h.cellNav.lastRowCol=e}},g.cellNav.handleKeyDown=function(a){var e=c.getDirection(a);if(null===e)return!0;var f="body";a.uiGridTargetRenderContainerId&&(f=a.uiGridTargetRenderContainerId);var i=g.grid.api.cellNav.getFocusedCell();if(i){var j=g.grid.renderContainers[f].cellNav.getNextRowCol(e,i.row,i.col);return j.eventType=d.EVENT_TYPE.KEYDOWN,g.cellNav.broadcastCellNav(j),c.scrollToIfNecessary(h,b,j.row,j.col),a.stopPropagation(),a.preventDefault(),!1}}},post:function(){}}}}}]),b.directive("uiGridRenderContainer",["$timeout","$document","gridUtil","uiGridConstants","uiGridCellNavService","uiGridCellNavConstants",function(a,b,c,d,e){return{replace:!0,priority:-99999,require:["^uiGrid","uiGridRenderContainer","?^uiGridCellnav"],scope:!1,compile:function(){return{post:function(c,f,g,h){var i=h[0],j=h[1],k=h[2];if(k){var l=j.containerId,m=i.grid;e.decorateRenderContainers(m),f.attr("tabindex",-1),f.on("keydown",function(a){return a.uiGridTargetRenderContainerId=l,i.cellNav.handleKeyDown(a)}),c.$on(d.events.GRID_SCROLL,function(){null!=i.grid.api.cellNav.getFocusedCell()&&a(function(){a(function(){var a=i.grid.api.cellNav.getFocusedCell();b.activeElement===b.body&&f[0].focus(),i.cellNav.broadcastCellNav(a)})})})}}}}}}]),b.directive("uiGridCell",["$timeout","$document","uiGridCellNavService","gridUtil","uiGridCellNavConstants","uiGridConstants",function(b,c,d,e,f){return{priority:-150,restrict:"A",require:["^uiGrid","?^uiGridCellnav"],scope:!1,link:function(b,c,d,e){function g(){c.find("div").attr("tabindex",-1)}function h(){var a=c.find("div");a.addClass("ui-grid-cell-focus")}function i(){var a=c.find("div");a.removeClass("ui-grid-cell-focus")}var j=e[0],k=e[1];k&&b.col.colDef.allowCellFocus&&(g(),c.find("div").on("click",function(c){j.cellNav.broadcastCellNav(new a(b.row,b.col)),c.stopPropagation()}),b.$on(f.CELL_NAV_EVENT,function(a,d){d.row===b.row&&d.col===b.col?(h(),d.hasOwnProperty("eventType")&&d.eventType===f.EVENT_TYPE.KEYDOWN&&c.find("div")[0].focus()):i()}),b.$on("$destroy",function(){c.find("div").off("click")}))}}}])}(),function(){"use strict";var a=angular.module("ui.grid.edit",["ui.grid"]);a.constant("uiGridEditConstants",{EDITABLE_CELL_TEMPLATE:/EDITABLE_CELL_TEMPLATE/g,EDITABLE_CELL_DIRECTIVE:/editable_cell_directive/g,events:{BEGIN_CELL_EDIT:"uiGridEventBeginCellEdit",END_CELL_EDIT:"uiGridEventEndCellEdit",CANCEL_CELL_EDIT:"uiGridEventCancelCellEdit"}}),a.service("uiGridEditService",["$q","$templateCache","uiGridConstants","gridUtil",function(a,b,c,d){var e={initializeGrid:function(a){e.defaultGridOptions(a.options),a.registerColumnBuilder(e.editColumnBuilder);var b={events:{edit:{afterCellEdit:function(){},beginCellEdit:function(){},cancelCellEdit:function(){}}},methods:{edit:{}}};a.api.registerEventsFromObject(b.events)},defaultGridOptions:function(a){a.cellEditableCondition=void 0===a.cellEditableCondition?!0:a.cellEditableCondition,a.enableCellEditOnFocus=void 0===a.enableCellEditOnFocus?!1:a.enableCellEditOnFocus},editColumnBuilder:function(b,c,e){var f=[];return b.enableCellEdit=void 0===b.enableCellEdit?void 0===e.enableCellEdit?"object"!==b.type:e.enableCellEdit:b.enableCellEdit,b.cellEditableCondition=void 0===b.cellEditableCondition?e.cellEditableCondition:b.cellEditableCondition,b.enableCellEdit&&(b.editableCellTemplate=b.editableCellTemplate||e.editableCellTemplate||"ui-grid/cellEditor",f.push(d.getTemplate(b.editableCellTemplate).then(function(a){c.editableCellTemplate=a},function(){throw new Error("Couldn't fetch/use colDef.editableCellTemplate '"+b.editableCellTemplate+"'")}))),b.enableCellEditOnFocus=void 0===b.enableCellEditOnFocus?e.enableCellEditOnFocus:b.enableCellEditOnFocus,a.all(f)},isStartEditKey:function(a){return a.keyCode===c.keymap.LEFT||a.keyCode===c.keymap.TAB&&a.shiftKey||a.keyCode===c.keymap.RIGHT||a.keyCode===c.keymap.TAB||a.keyCode===c.keymap.UP||a.keyCode===c.keymap.ENTER&&a.shiftKey||a.keyCode===c.keymap.DOWN||a.keyCode===c.keymap.ENTER?!1:!0}};return e}]),a.directive("uiGridEdit",["gridUtil","uiGridEditService",function(a,b){return{replace:!0,priority:0,require:"^uiGrid",scope:!1,compile:function(){return{pre:function(a,c,d,e){b.initializeGrid(e.grid)},post:function(){}}}}}]),a.directive("uiGridCell",["$compile","$injector","uiGridConstants","uiGridEditConstants","gridUtil","$parse","uiGridEditService",function(a,b,c,d,e,f,g){return{priority:-100,restrict:"A",scope:!1,require:"?^uiGrid",link:function(e,h,i,j){function k(){h.on("dblclick",p),h.on("keydown",n),e.col.colDef.enableCellEditOnFocus&&h.find("div").on("focus",m)}function l(){h.off("dblclick",p),h.off("keydown",n),e.col.colDef.enableCellEditOnFocus&&h.find("div").off("focus",m)}function m(a){j&&j.cellNav&&j.cellNav.focusCell(e.row,e.col),a.stopPropagation(),p()}function n(a){g.isStartEditKey(a)&&p()}function o(a,b){return!b.isSaving&&(angular.isFunction(a.colDef.cellEditableCondition)?a.colDef.cellEditableCondition(e):a.colDef.cellEditableCondition)}function p(){if(!v&&o(e.col,e.row)){u=f(e.row.getQualifiedColField(e.col)),t=u(e),s=e.col.editableCellTemplate,s=s.replace(c.MODEL_COL_FIELD,e.row.getQualifiedColField(e.col));var b=e.col.colDef.editDropdownFilter?"|"+e.col.colDef.editDropdownFilter:"";switch(s=s.replace(c.CUSTOM_FILTERS,b),e.inputType="text",e.col.colDef.type){case"boolean":e.inputType="checkbox";break;case"number":e.inputType="number";break;case"date":e.inputType="date"}e.editDropdownOptionsArray=e.col.colDef.editDropdownOptionsArray,e.editDropdownIdLabel=e.col.colDef.editDropdownIdLabel?e.col.colDef.editDropdownIdLabel:"id",e.editDropdownValueLabel=e.col.colDef.editDropdownValueLabel?e.col.colDef.editDropdownValueLabel:"value";e.$apply(function(){v=!0,l();var b=angular.element(s);h.append(b),a(b)(e.$new());var c=angular.element(h.children()[0]);w=c.hasClass("ui-grid-cell-focus"),c.addClass("ui-grid-cell-contents-hidden")});var g=e.$on(c.events.GRID_SCROLL,function(){q(!0),e.grid.api.edit.raise.afterCellEdit(e.row.entity,e.col.colDef,u(e),t),g()}),i=e.$on(d.events.END_CELL_EDIT,function(a,b){q(b),e.grid.api.edit.raise.afterCellEdit(e.row.entity,e.col.colDef,u(e),t),i()}),j=e.$on(d.events.CANCEL_CELL_EDIT,function(){r(),j()});e.$broadcast(d.events.BEGIN_CELL_EDIT),e.grid.api.edit.raise.beginCellEdit(e.row.entity,e.col.colDef)}}function q(a){if(v){var b=angular.element(h.children()[0]);angular.element(h.children()[1]).remove(),b.removeClass("ui-grid-cell-contents-hidden"),a&&w&&b[0].focus(),w=!1,v=!1,k(),e.grid.api.core.notifyDataChange(e.grid,c.dataChange.EDIT)}}function r(){v&&(u.assign(e,t),e.$apply(),e.grid.api.edit.raise.cancelCellEdit(e.row.entity,e.col.colDef),q(!0))}if(e.col.colDef.enableCellEdit){var s,t,u,v=!1,w=!1;k();try{var x=b.get("uiGridCellNavConstants");e.col.colDef.enableCellEditOnFocus&&e.$on(x.CELL_NAV_EVENT,function(a,b){b.row===e.row&&b.col===e.col?p():q()})}catch(y){}}}}}]),a.directive("uiGridEditor",["uiGridConstants","uiGridEditConstants",function(a,b){return{scope:!0,require:["?^uiGrid","?^uiGridRenderContainer"],compile:function(){return{pre:function(){},post:function(c,d,e,f){var g,h;f[0]&&(g=f[0]),f[1]&&(h=f[1]),c.$on(b.events.BEGIN_CELL_EDIT,function(){d[0].focus(),d[0].select(),d.on("blur",function(a){c.stopEdit(a)})}),c.deepEdit=!1,c.stopEdit=function(a){c.inputForm&&!c.inputForm.$valid?(a.stopPropagation(),c.$emit(b.events.CANCEL_CELL_EDIT)):c.$emit(b.events.END_CELL_EDIT),c.deepEdit=!1},d.on("click",function(){c.deepEdit=!0}),d.on("keydown",function(d){switch(d.keyCode){case a.keymap.ESC:d.stopPropagation(),c.$emit(b.events.CANCEL_CELL_EDIT);break;case a.keymap.ENTER:c.stopEdit(d);break;case a.keymap.TAB:c.stopEdit(d)}if(c.deepEdit)switch(d.keyCode){case a.keymap.LEFT:d.stopPropagation();break;case a.keymap.RIGHT:d.stopPropagation();break;case a.keymap.UP:d.stopPropagation();break;case a.keymap.DOWN:d.stopPropagation()}else g&&g.hasOwnProperty("cellNav")&&h&&(d.uiGridTargetRenderContainerId=h.containerId,g.cellNav.handleKeyDown(d));return!0})}}}}}]),a.directive("input",["$filter",function(a){function b(a){if("undefined"==typeof a||""===a)return null;var b=a.split("-");if(3!==b.length)return null;var c=parseInt(b[0],10),d=parseInt(b[1],10),e=parseInt(b[2],10);return 1>d||1>c||1>e?null:new Date(c,d-1,e)}return{restrict:"E",require:"?ngModel",link:function(c,d,e,f){2===angular.version.minor&&e.type&&"date"===e.type&&f&&(f.$formatters.push(function(b){return f.$setValidity(null,!b||!isNaN(b.getTime())),a("date")(b,"yyyy-MM-dd")}),f.$parsers.push(function(a){if(a&&a.length>0){var c=b(a);return f.$setValidity(null,c&&!isNaN(c.getTime())),c}return f.$setValidity(null,!0),null
}))}}}]),a.directive("uiGridEditDropdown",["uiGridConstants","uiGridEditConstants",function(a,b){return{scope:!0,compile:function(){return{pre:function(){},post:function(c,d){c.$on(b.events.BEGIN_CELL_EDIT,function(){d[0].focus(),d[0].style.width=d[0].parentElement.offsetWidth-1+"px",d.on("blur",function(a){c.stopEdit(a)})}),c.stopEdit=function(){c.$emit(b.events.END_CELL_EDIT)},d.on("keydown",function(d){switch(d.keyCode){case a.keymap.ESC:d.stopPropagation(),c.$emit(b.events.CANCEL_CELL_EDIT);break;case a.keymap.ENTER:c.stopEdit(d);break;case a.keymap.LEFT:c.stopEdit(d);break;case a.keymap.RIGHT:c.stopEdit(d);break;case a.keymap.UP:d.stopPropagation();break;case a.keymap.DOWN:d.stopPropagation();break;case a.keymap.TAB:c.stopEdit(d)}return!0})}}}}}])}(),function(){"use strict";var a=angular.module("ui.grid.expandable",["ui.grid"]);a.service("uiGridExpandableService",["gridUtil","$compile",function(a){var b={initializeGrid:function(c){c.options.enableExpandable=c.options.enableExpandable!==!1,c.options.expandableRowHeight=c.options.expandableRowHeight||150,c.options.enableExpandable&&!c.options.expandableRowTemplate&&(a.logError("You have not set the expandableRowTemplate, disabling expandable module"),c.options.enableExpandable=!1);var d={events:{expandable:{rowExpandedStateChanged:function(){}}},methods:{expandable:{toggleRowExpansion:function(a){var d=c.getRow(a);null!==d&&b.toggleRowExpansion(c,d)},expandAllRows:function(){b.expandAllRows(c)},collapseAllRows:function(){b.collapseAllRows(c)}}}};c.api.registerEventsFromObject(d.events),c.api.registerMethodsFromObject(d.methods)},toggleRowExpansion:function(a,b){b.isExpanded=!b.isExpanded,b.height=b.isExpanded?b.grid.options.rowHeight+a.options.expandableRowHeight:b.grid.options.rowHeight,a.api.expandable.raise.rowExpandedStateChanged(b)},expandAllRows:function(a){angular.forEach(a.renderContainers.body.visibleRowCache,function(c){c.isExpanded||b.toggleRowExpansion(a,c)}),a.refresh()},collapseAllRows:function(a){angular.forEach(a.renderContainers.body.visibleRowCache,function(c){c.isExpanded&&b.toggleRowExpansion(a,c)}),a.refresh()}};return b}]),a.directive("uiGridExpandable",["uiGridExpandableService","$templateCache",function(a,b){return{replace:!0,priority:0,require:"^uiGrid",scope:!1,compile:function(){return{pre:function(c,d,e,f){if(f.grid.options.enableExpandableRowHeader!==!1){var g={name:"expandableButtons",width:40};g.cellTemplate=b.get("ui-grid/expandableRowHeader"),f.grid.addRowHeaderColumn(g)}a.initializeGrid(f.grid)},post:function(){}}}}}]),a.directive("uiGridExpandableRow",["uiGridExpandableService","$timeout","$compile","uiGridConstants","gridUtil","$interval","$log",function(a,b,c,d,e){return{replace:!1,priority:0,scope:!1,compile:function(){return{pre:function(a,b){e.getTemplate(a.grid.options.expandableRowTemplate).then(function(d){if(a.grid.options.expandableRowScope){var e=a.grid.options.expandableRowScope;for(var f in e)e.hasOwnProperty(f)&&(a[f]=e[f])}var g=c(d)(a);b.append(g),a.row.expandedRendered=!0})},post:function(a){a.$on("$destroy",function(){a.row.expandedRendered=!1})}}}}}]),a.directive("uiGridRow",["$compile","gridUtil","$templateCache",function(){return{priority:-200,scope:!1,compile:function(){return{pre:function(a){function b(){var b=a.grid,c=0;return angular.forEach(b.columns,function(a){"left"===a.renderContainer&&(c+=a.width)}),c=Math.floor(c),".grid"+b.id+" .ui-grid-pinned-container-"+a.colContainer.name+", .grid"+b.id+" .ui-grid-pinned-container-"+a.colContainer.name+" .ui-grid-render-container-"+a.colContainer.name+" .ui-grid-viewport .ui-grid-canvas .ui-grid-row { width: "+c+"px; }"}a.expandableRow={},a.expandableRow.shouldRenderExpand=function(){var b="body"===a.colContainer.name&&a.grid.options.enableExpandable!==!1&&a.row.isExpanded&&(!a.grid.isScrollingVertically||a.row.expandedRendered);return b},a.expandableRow.shouldRenderFiller=function(){var b=a.row.isExpanded&&("body"!==a.colContainer.name||a.grid.isScrollingVertically&&!a.row.expandedRendered);return b},"left"===a.colContainer.name&&a.grid.registerStyleComputation({priority:15,func:b})},post:function(){}}}}}]),a.directive("uiGridViewport",["$compile","gridUtil","$templateCache",function(a,b,c){return{priority:-200,scope:!1,compile:function(a){var b=angular.element(a.children().children()[0]),d=c.get("ui-grid/expandableScrollFiller"),e=c.get("ui-grid/expandableRow");return b.append(e),b.append(d),{pre:function(){},post:function(){}}}}}])}(),function(){"use strict";var a=angular.module("ui.grid.exporter",["ui.grid"]);a.constant("uiGridExporterConstants",{featureName:"exporter",ALL:"all",VISIBLE:"visible",SELECTED:"selected",CSV_CONTENT:"CSV_CONTENT",LINK_LABEL:"LINK_LABEL",BUTTON_LABEL:"BUTTON_LABEL"}),a.service("uiGridExporterService",["$q","uiGridExporterConstants","uiGridSelectionConstants","gridUtil","$compile","$interval","i18nService",function(a,b,c,d,e,f,g){var h={initializeGrid:function(a){a.exporter={},this.defaultGridOptions(a.options);var b={events:{exporter:{}},methods:{exporter:{csvExport:function(b,c,d){h.csvExport(a,b,c,d)},pdfExport:function(b,c){h.pdfExport(a,b,c)}}}};a.api.registerEventsFromObject(b.events),a.api.registerMethodsFromObject(b.methods),a.api.core.addToGridMenu?h.addToMenu(a):f(function(){a.api.core.addToGridMenu&&h.addToMenu(a)},100,1)},defaultGridOptions:function(a){a.exporterSuppressMenu=a.exporterSuppressMenu===!0,a.exporterLinkTemplate=a.exporterLinkTemplate?a.exporterLinkTemplate:"ui-grid/csvLink",a.exporterHeaderTemplate=a.exporterHeaderTemplate?a.exporterHeaderTemplate:"ui-grid/exporterHeader",a.exporterLinkLabel=a.exporterLinkLabel?a.exporterLinkLabel:"Download CSV",a.exporterMenuLabel=a.exporterMenuLabel?a.exporterMenuLabel:"Export",a.exporterSuppressColumns=a.exporterSuppressColumns?a.exporterSuppressColumns:[],a.exporterCsvColumnSeparator=a.exporterCsvColumnSeparator?a.exporterCsvColumnSeparator:",",a.exporterPdfDefaultStyle=a.exporterPdfDefaultStyle?a.exporterPdfDefaultStyle:{fontSize:11},a.exporterPdfTableStyle=a.exporterPdfTableStyle?a.exporterPdfTableStyle:{margin:[0,5,0,15]},a.exporterPdfTableHeaderStyle=a.exporterPdfTableHeaderStyle?a.exporterPdfTableHeaderStyle:{bold:!0,fontSize:12,color:"black"},a.exporterPdfHeader=a.exporterPdfHeader?a.exporterPdfHeader:null,a.exporterPdfFooter=a.exporterPdfFooter?a.exporterPdfFooter:null,a.exporterPdfOrientation=a.exporterPdfOrientation?a.exporterPdfOrientation:"landscape",a.exporterPdfPageSize=a.exporterPdfPageSize?a.exporterPdfPageSize:"A4",a.exporterPdfMaxGridWidth=a.exporterPdfMaxGridWidth?a.exporterPdfMaxGridWidth:720,a.exporterMenuCsv=void 0!==a.exporterMenuCsv?a.exporterMenuCsv:!0,a.exporterMenuPdf=void 0!==a.exporterMenuPdf?a.exporterMenuPdf:!0,a.exporterPdfCustomFormatter=a.exporterPdfCustomFormatter&&"function"==typeof a.exporterPdfCustomFormatter?a.exporterPdfCustomFormatter:function(a){return a},a.exporterFieldCallback=a.exporterFieldCallback?a.exporterFieldCallback:function(a,b,c,d){return d}},addToMenu:function(a){a.api.core.addToGridMenu(a,[{title:g.getSafeText("gridMenu.exporterAllAsCsv"),action:function(){this.grid.api.exporter.csvExport(b.ALL,b.ALL)},shown:function(){return this.grid.options.exporterMenuCsv}},{title:g.getSafeText("gridMenu.exporterVisibleAsCsv"),action:function(){this.grid.api.exporter.csvExport(b.VISIBLE,b.VISIBLE)},shown:function(){return this.grid.options.exporterMenuCsv}},{title:g.getSafeText("gridMenu.exporterSelectedAsCsv"),action:function(){this.grid.api.exporter.csvExport(b.SELECTED,b.VISIBLE)},shown:function(){return this.grid.options.exporterMenuCsv&&this.grid.api.selection&&this.grid.api.selection.getSelectedRows().length>0}},{title:g.getSafeText("gridMenu.exporterAllAsPdf"),action:function(){this.grid.api.exporter.pdfExport(b.ALL,b.ALL)},shown:function(){return this.grid.options.exporterMenuPdf}},{title:g.getSafeText("gridMenu.exporterVisibleAsPdf"),action:function(){this.grid.api.exporter.pdfExport(b.VISIBLE,b.VISIBLE)},shown:function(){return this.grid.options.exporterMenuPdf}},{title:g.getSafeText("gridMenu.exporterSelectedAsPdf"),action:function(){this.grid.api.exporter.pdfExport(b.SELECTED,b.VISIBLE)},shown:function(){return this.grid.options.exporterMenuPdf&&this.grid.api.selection&&this.grid.api.selection.getSelectedRows().length>0}}])},csvExport:function(a,b,c,e){var f=this.getColumnHeaders(a,c),g=this.getData(a,b,c),h=this.formatAsCsv(f,g,a.options.exporterCsvColumnSeparator);!e&&a.options.exporterCsvLinkElement&&(e=a.options.exporterCsvLinkElement),e?this.renderCsvLink(a,h,e):d.logError("Exporter asked to export as csv, but no element provided.  Perhaps you should set gridOptions.exporterCsvLinkElement?")},getColumnHeaders:function(a,d){var e=[];return angular.forEach(a.columns,function(f){!f.visible&&d!==b.ALL||f.name===c.selectionRowHeaderColName||-1!==a.options.exporterSuppressColumns.indexOf(f.name)||e.push({name:f.field,displayName:a.options.exporterHeaderFilter?a.options.exporterHeaderFilter(f.displayName):f.displayName,width:f.drawnWidth?f.drawnWidth:f.width,align:"number"===f.colDef.type?"right":"left"})}),e},getData:function(a,e,f){var g,h=[];switch(e){case b.ALL:g=a.rows;break;case b.VISIBLE:g=a.getVisibleRows();break;case b.SELECTED:a.api.selection?g=a.api.selection.getSelectedGridRows():d.logError("selection feature must be enabled to allow selected rows to be exported")}return angular.forEach(g,function(d){var e=[];angular.forEach(a.columns,function(g){if((g.visible||f===b.ALL)&&g.name!==c.selectionRowHeaderColName&&-1===a.options.exporterSuppressColumns.indexOf(g.name)){var h={value:a.options.exporterFieldCallback(a,d,g,a.getCellValue(d,g))};g.colDef.exporterPdfAlign&&(h.alignment=g.colDef.exporterPdfAlign),e.push(h)}}),h.push(e)}),h},formatAsCsv:function(a,b,c){var d=this,e=a.map(function(a){return{value:a.displayName}}),f=d.formatRowAsCsv(this,c)(e)+"\n";return f+=b.map(this.formatRowAsCsv(this,c)).join("\n")},formatRowAsCsv:function(a,b){return function(c){return c.map(a.formatFieldAsCsv).join(b)}},formatFieldAsCsv:function(a){return null==a.value?"":"number"==typeof a.value?a.value:"boolean"==typeof a.value?a.value?"TRUE":"FALSE":"string"==typeof a.value?'"'+a.value.replace(/"/g,'""')+'"':JSON.stringify(a.value)},renderCsvLink:function(a,c,f){var g=f?f:angular.element(a.exporter.gridElm[0].querySelectorAll(".ui-grid-exporter-csv-link"));angular.element(g[0].querySelectorAll(".ui-grid-exporter-csv-link-span"))&&angular.element(g[0].querySelectorAll(".ui-grid-exporter-csv-link-span")).remove();d.getTemplate(a.options.exporterLinkTemplate).then(function(d){var f=angular.element(d);f.children("a").html(f.children("a").html().replace(b.LINK_LABEL,a.options.exporterLinkLabel)),f.children("a").attr("href",f.children("a").attr("href").replace(b.CSV_CONTENT,encodeURIComponent(c)));var h=e(f)(a.exporter.$scope);g.append(h)})},pdfExport:function(a,b,c){var d=this.getColumnHeaders(a,c),e=this.getData(a,b,c),f=this.prepareAsPdf(a,d,e);pdfMake.createPdf(f).open()},prepareAsPdf:function(a,b,c){var d=this.calculatePdfHeaderWidths(a,b),e=b.map(function(a){return{text:a.displayName,style:"tableHeader"}}),f=c.map(this.formatRowAsPdf(this)),g=[e].concat(f),h={pageOrientation:a.options.exporterPdfOrientation,pageSize:a.options.exporterPdfPageSize,content:[{style:"tableStyle",table:{headerRows:1,widths:d,body:g}}],styles:{tableStyle:a.options.exporterPdfTableStyle,tableHeader:a.options.exporterPdfTableHeaderStyle},defaultStyle:a.options.exporterPdfDefaultStyle};return a.options.exporterPdfLayout&&(h.layout=a.options.exporterPdfLayout),a.options.exporterPdfHeader&&h.content.unshift(a.options.exporterPdfHeader),a.options.exporterPdfFooter&&h.content.push(a.options.exporterPdfFooter),a.options.exporterPdfCustomFormatter&&(h=a.options.exporterPdfCustomFormatter(h)),h},calculatePdfHeaderWidths:function(a,b){var c=0;angular.forEach(b,function(a){"number"==typeof a.width&&(c+=a.width)});var d=0;angular.forEach(b,function(a){if("*"===a.width&&(d+=100),"string"==typeof a.width&&a.width.match(/(\d)*%/)){var b=parseInt(a.width.match(/(\d)*%/)[0]);a.width=c*b/100,d+=a.width}});var e=c+d;return b.map(function(b){return"*"===b.width?b.width:b.width*a.options.exporterPdfMaxGridWidth/e})},formatRowAsPdf:function(a){return function(b){return b.map(a.formatFieldAsPdfString)}},formatFieldAsPdfString:function(a){var b;return b=null==a.value?"":"number"==typeof a.value?a.value.toString():"boolean"==typeof a.value?a.value?"TRUE":"FALSE":"string"==typeof a.value?a.value.replace(/"/g,'""'):JSON.stringify(a.value).replace(/^"/,"").replace(/"$/,""),a.alignment&&"string"==typeof a.alignment&&(b={text:b,alignment:a.alignment}),b}};return h}]),a.directive("uiGridExporter",["uiGridExporterConstants","uiGridExporterService","gridUtil","$compile",function(a,b){return{replace:!0,priority:0,require:"^uiGrid",scope:!1,link:function(a,c,d,e){b.initializeGrid(e.grid),e.grid.exporter.$scope=a}}}])}(),function(){"use strict";var a=angular.module("ui.grid.importer",["ui.grid"]);a.constant("uiGridImporterConstants",{featureName:"importer"}),a.service("uiGridImporterService",["$q","uiGridConstants","uiGridImporterConstants","gridUtil","$compile","$interval","i18nService","$window",function(a,b,c,d,e,f,g,h){var i={initializeGrid:function(a,b){b.importer={$scope:a},this.defaultGridOptions(b.options);var c={events:{importer:{}},methods:{importer:{importFile:function(a,b){i.importFile(a,b)}}}};b.api.registerEventsFromObject(c.events),b.api.registerMethodsFromObject(c.methods),b.options.enableImporter&&b.options.importerShowMenu&&(b.api.core.addToGridMenu?i.addToMenu(b):f(function(){b.api.core.addToGridMenu&&i.addToMenu(b)},100,1))},defaultGridOptions:function(a){a.enableImporter||void 0===a.enableImporter?h.hasOwnProperty("File")&&h.hasOwnProperty("FileReader")&&h.hasOwnProperty("FileList")&&h.hasOwnProperty("Blob")?a.enableImporter=!0:(d.logError("The File APIs are not fully supported in this browser, grid importer cannot be used."),a.enableImporter=!1):a.enableImporter=!1,a.importerProcessHeaders=a.importerProcessHeaders||i.processHeaders,a.importerHeaderFilter=a.importerHeaderFilter||function(a){return a},a.importerErrorCallback&&"function"==typeof a.importerErrorCallback||delete a.importerErrorCallback,a.enableImporter!==!0||a.importerDataAddCallback||(d.logError("You have not set an importerDataAddCallback, importer is disabled"),a.enableImporter=!1),a.importerShowMenu=a.importerShowMenu!==!1,a.importerObjectCallback=a.importerObjectCallback||function(a,b){return b}},addToMenu:function(a){a.api.core.addToGridMenu(a,[{title:g.getSafeText("gridMenu.importerTitle")},{templateUrl:"ui-grid/importerMenuItemContainer",action:function(){this.grid.api.importer.importAFile(a)}}])},importThisFile:function(a,b){if(!b)return void d.logError("No file object provided to importThisFile, should be impossible, aborting");var c=new FileReader;switch(b.type){case"application/json":c.onload=i.importJsonClosure(a);break;default:c.onload=i.importCsvClosure(a)}c.readAsText(b)},importJsonClosure:function(a){return function(b){var c,d=[];angular.forEach(i.parseJson(a,b),function(b){c=i.newObject(a),angular.extend(c,b),c=a.options.importerObjectCallback(a,c),d.push(c)}),i.addObjects(a,d)}},parseJson:function(a,b){var c;try{c=JSON.parse(b.target.result)}catch(d){return void i.alertError(a,"importer.invalidJson","File could not be processed, is it valid json? Content was: ",b.target.result)}return Array.isArray(c)?c:(i.alertError(a,"importer.jsonNotarray","Import failed, file is not an array, file was: ",b.target.result),[])},importCsvClosure:function(a){return function(b){var c=i.parseCsv(b);if(!c||c.length<1)return void i.alertError(a,"importer.invalidCsv","File could not be processed, is it valid csv? Content was: ",b.target.result);var d=i.createCsvObjects(a,c);return d&&0!==d.length?void i.addObjects(a,d):void i.alertError(a,"importer.noObjects","Objects were not able to be derived, content was: ",b.target.result)}},parseCsv:function(a){var b=a.target.result;return CSV.parse(b)},createCsvObjects:function(a,b){var c=a.options.importerProcessHeaders(a,b.shift());if(!c||0===c.length)return i.alertError(a,"importer.noHeaders","Column names could not be derived, content was: ",b),[];var d,e=[];return angular.forEach(b,function(b){d=i.newObject(a),angular.forEach(b,function(a,b){null!==c[b]&&(d[c[b]]=a)}),d=a.options.importerObjectCallback(a,d),e.push(d)}),e},processHeaders:function(a,b){var c=[];if(a.options.columnDefs&&0!==a.options.columnDefs.length){var d=i.flattenColumnDefs(a,a.options.columnDefs);return angular.forEach(b,function(a){c.push(d[a]?d[a]:d[a.toLowerCase()]?d[a.toLowerCase()]:null)}),c}return angular.forEach(b,function(a){c.push(a.replace(/[^0-9a-zA-Z\-_]/g,"_"))}),c},flattenColumnDefs:function(a,b){var c={};return angular.forEach(b,function(b){b.name&&(c[b.name]=b.field||b.name,c[b.name.toLowerCase()]=b.field||b.name),b.field&&(c[b.field]=b.field||b.name,c[b.field.toLowerCase()]=b.field||b.name),b.displayName&&(c[b.displayName]=b.field||b.name,c[b.displayName.toLowerCase()]=b.field||b.name),b.displayName&&a.options.importerHeaderFilter&&(c[a.options.importerHeaderFilter(b.displayName)]=b.field||b.name,c[a.options.importerHeaderFilter(b.displayName).toLowerCase()]=b.field||b.name)}),c},addObjects:function(a,c){if(a.api.rowEdit){var d=a.registerDataChangeCallback(function(){a.api.rowEdit.setRowsDirty(a,c),a.deregisterDataChangeCallback(d)},[b.dataChange.ROW]),e=function(){a.deregisterDataChangeCallback(d)};a.importer.$scope.$on("$destroy",e)}a.importer.$scope.$apply(a.options.importerDataAddCallback(a,c))},newObject:function(a){return"undefined"!=typeof a.options&&"undefined"!=typeof a.options.importerNewObject?new a.options.importerNewObject:{}},alertError:function(a,b,c,e){a.options.importerErrorCallback?a.options.importerErrorCallback(a,b,c,e):(h.alert(g.getSafeText(b)),d.logError(c+e))}};return i}]),a.directive("uiGridImporter",["uiGridImporterConstants","uiGridImporterService","gridUtil","$compile",function(a,b){return{replace:!0,priority:0,require:"^uiGrid",scope:!1,link:function(a,c,d,e){b.initializeGrid(a,e.grid)}}}]),a.directive("uiGridImporterMenuItem",["uiGridImporterConstants","uiGridImporterService","gridUtil","$compile",function(a,b,c){return{replace:!0,priority:0,require:"^uiGrid",scope:!1,templateUrl:"ui-grid/importerMenuItem",link:function(a,d,e,f){var g=function(a){if(1===a.srcElement.files.length){var c=a.srcElement.files[0];b.importThisFile(i,c),a.srcElement.form.reset()}},h=d[0].querySelectorAll(".ui-grid-importer-file-chooser"),i=f.grid;1!==h.length?c.logError("Found > 1 or < 1 file choosers within the menu item, error, cannot continue"):h[0].addEventListener("change",g,!1)}}}])}(),function(){"use strict";var a=angular.module("ui.grid.infiniteScroll",["ui.grid"]);a.service("uiGridInfiniteScrollService",["gridUtil","$compile","$timeout",function(){var a={initializeGrid:function(b){a.defaultGridOptions(b.options);var c={events:{infiniteScroll:{needLoadMoreData:function(){}}},methods:{infiniteScroll:{dataLoaded:function(){b.options.loadTimout=!1}}}};b.options.loadTimout=!1,b.api.registerEventsFromObject(c.events),b.api.registerMethodsFromObject(c.methods)},defaultGridOptions:function(a){a.enableInfiniteScroll=a.enableInfiniteScroll!==!1},loadData:function(a){a.options.loadTimout=!0,a.api.infiniteScroll.raise.needLoadMoreData()},checkScroll:function(a,b){var c=a.options.infiniteScrollPercentage?a.options.infiniteScrollPercentage:20;return!a.options.loadTimout&&c>=b?(this.loadData(a),!0):!1}};return a}]),a.directive("uiGridInfiniteScroll",["uiGridInfiniteScrollService",function(a){return{priority:-200,scope:!1,require:"^uiGrid",compile:function(){return{pre:function(b,c,d,e){a.initializeGrid(e.grid)},post:function(){}}}}}]),a.directive("uiGridViewport",["$compile","gridUtil","uiGridInfiniteScrollService","uiGridConstants",function(a,b,c,d){return{priority:-200,scope:!1,link:function(a){a.grid.options.enableInfiniteScroll&&a.$on(d.events.GRID_SCROLL,function(b,d){if(d.y){var e=100-100*d.y.percentage;c.checkScroll(a.grid,e)}})}}}])}(),function(){"use strict";var a=angular.module("ui.grid.moveColumns",["ui.grid"]);a.service("uiGridMoveColumnService",["$q","$timeout","$log",function(a,b){var c={initializeGrid:function(a){var b=this;this.registerPublicApi(a),this.defaultGridOptions(a.options),a.registerColumnBuilder(b.movableColumnBuilder)},registerPublicApi:function(a){var b=this,c={events:{colMovable:{columnPositionChanged:function(){}}},methods:{colMovable:{moveColumn:function(c,d){b.redrawColumnAtPosition(a,c,d)}}}};a.api.registerEventsFromObject(c.events),a.api.registerMethodsFromObject(c.methods)},defaultGridOptions:function(a){a.enableColumnMoving=a.enableColumnMoving!==!1},movableColumnBuilder:function(b,c,d){var e=[];return b.enableColumnMoving=void 0===b.enableColumnMoving?d.enableColumnMoving:b.enableColumnMoving,a.all(e)},redrawColumnAtPosition:function(a,c,d){var e=a.columns,f=function(a){for(var b=a,c=0;b>=c;c++)angular.isDefined(e[c])&&angular.isDefined(e[c].colDef.visible)&&e[c].colDef.visible===!1&&b++;return b};c=f(c),d=f(d);var g=e[c];if(g.colDef.enableColumnMoving){if(c>d)for(var h=c;h>d;h--)e[h]=e[h-1];else if(d>c)for(var i=c;d>i;i++)e[i]=e[i+1];e[d]=g,b(function(){a.refresh(),a.api.colMovable.raise.columnPositionChanged(g.colDef,c,d)})}}};return c}]),a.directive("uiGridMoveColumns",["uiGridMoveColumnService",function(a){return{replace:!0,priority:0,require:"^uiGrid",scope:!1,compile:function(){return{pre:function(b,c,d,e){a.initializeGrid(e.grid)},post:function(){}}}}}]),a.directive("uiGridHeaderCell",["$q","gridUtil","uiGridMoveColumnService","$document","$log",function(a,b,c,d){return{priority:-10,require:"^uiGrid",compile:function(){return{post:function(e,f,g,h){if(e.col.colDef.enableColumnMoving){var i=function(i){if("ui-grid-icon-angle-down"!==i.target.className&&"I"!==i.target.tagName&&i.target.className.indexOf("ui-grid-filter-input")<0){var j,k,l=e.grid.element[0].getBoundingClientRect().left,m=i.pageX,n=0,o=l+e.grid.getViewportWidth()-e.grid.verticalScrollbarWidth,p=!1,q=function(){p=!0,j=f.clone(),f.append(j),j.addClass("movingColumn");var a={},b=f[0].getBoundingClientRect().left;a.left=b-l+"px";var c=e.grid.element[0].getBoundingClientRect().right,g=f[0].getBoundingClientRect().right;g>c&&(k=e.col.drawnWidth+(c-g),a.width=k+"px"),j.css(a),d.on("mouseup",t)},r=function(a){h.fireEvent("hide-menu");var c,d=j[0].getBoundingClientRect().left-1,f=j[0].getBoundingClientRect().right;c="ie"===b.detectBrowser()?d+a:d-l+a,c=o>c?c:o,(d>=l||a>0)&&(o>=f||0>a)?j.css({visibility:"visible",left:c+"px"}):(a*=5,h.fireScrollingEvent({x:{pixels:2.5*a}})),n+=a,k<e.col.drawnWidth&&(k+=Math.abs(a),j.css({width:k+"px"}))},s=function(a){var b=a.pageX-m;!p&&Math.abs(b)>50?q():p&&(r(b),m=a.pageX)};e.$on("$destroy",function(){d.off("mousemove",s),d.off("mouseup",t)}),d.on("mousemove",s);var t=function(b){var f,i=a.defer();g.$observe("renderIndex",function(a){f=e.$eval(a),i.resolve()}),i.promise.then(function(){j&&j.remove();for(var a=e.grid.renderContainers.body.renderedColumns,g=0,i=e.grid.columns,k=0;k<i.length&&(i[k].colDef.visible&&i[k].colDef.name!==a[0].colDef.name);k++)g++;if(0>n){for(var l=0,m=f-1;m>=0;m--)if(l+=a[m].drawnWidth,l>Math.abs(n)){c.redrawColumnAtPosition(e.grid,g+f,g+m+1);break}l<Math.abs(n)&&c.redrawColumnAtPosition(e.grid,g+f,g+0)}else if(n>0){for(var o=0,p=f+1;p<a.length;p++)if(o+=a[p].drawnWidth,o>n){c.redrawColumnAtPosition(e.grid,g+f,g+p-1);break}n>o&&c.redrawColumnAtPosition(e.grid,g+f,g+a.length-1)}else if(0===n&&h.grid.options.enableSorting&&e.col.enableSorting){var q=!1;b.shiftKey&&(q=!0),h.grid.sortColumn(e.col,q).then(function(){h.columnMenuScope&&h.columnMenuScope.hideMenu(),h.grid.refresh()})}d.off("mousemove",s),d.off("mouseup",t)})}}};f.on("mousedown",i)}}}}}}])}(),function(){"use strict";var a=angular.module("ui.grid.pagination",["ui.grid"]);a.service("uiGridPaginationService",function(){var a={initializeGrid:function(b){a.defaultGridOptions(b.options),b.pagination={page:1,totalPages:1};var c={methods:{pagination:{getPage:function(){return b.pagination.page},getTotalPages:function(){return b.pagination.totalPages},nextPage:function(){b.pagination.page++,b.refresh()},previousPage:function(){b.pagination.page=Math.max(1,b.pagination.page-1),b.refresh()},seek:function(a){if(!angular.isNumber(a)||1>a)throw"Invalid page number: "+a;b.pagination.page=a,b.refresh()}}}};b.api.registerMethodsFromObject(c.methods),b.registerRowsProcessor(function(a){if(!b.options.enablePagination)return a;b.pagination.totalPages=Math.max(1,Math.ceil(a.length/b.options.rowsPerPage));var c=(b.pagination.page-1)*b.options.rowsPerPage;return c>=a.length&&(b.pagination.page=b.pagination.totalPages,c=(b.pagination.page-1)*b.options.rowsPerPage),a.slice(c,c+b.options.rowsPerPage)})},defaultGridOptions:function(a){a.enablePagination=a.enablePagination!==!1,a.rowsPerPage=angular.isNumber(a.rowsPerPage)?a.rowsPerPage:10}};return a}),a.directive("uiGridPagination",["uiGridPaginationService",function(a){return{priority:-400,scope:!1,require:"^uiGrid",link:{pre:function(b,c,d,e){a.initializeGrid(e.grid)}}}}])}(),function(){"use strict";var a=angular.module("ui.grid.paging",["ui.grid"]);a.service("uiGridPagingService",["gridUtil",function(a){var b={initializeGrid:function(a){b.defaultGridOptions(a.options);var c={events:{paging:{pagingChanged:function(){}}},methods:{paging:{}}};a.api.registerEventsFromObject(c.events),a.api.registerMethodsFromObject(c.methods),a.registerRowsProcessor(function(b){if(a.options.useExternalPaging||!a.options.enablePaging)return b;var c=parseInt(a.options.pagingPageSize,10),d=parseInt(a.options.pagingCurrentPage,10),e=(d-1)*c;return b.slice(e,e+c)})},defaultGridOptions:function(b){b.enablePaging=b.enablePaging!==!1,b.useExternalPaging=b.useExternalPaging===!0,a.isNullOrUndefined(b.totalItems)&&(b.totalItems=0),a.isNullOrUndefined(b.pagingPageSizes)&&(b.pagingPageSizes=[250,500,1e3]),a.isNullOrUndefined(b.pagingPageSize)&&(b.pagingPageSize=b.pagingPageSizes.length>0?b.pagingPageSizes[0]:0),a.isNullOrUndefined(b.pagingCurrentPage)&&(b.pagingCurrentPage=1)},onPagingChanged:function(a,b,c){a.api.paging.raise.pagingChanged(b,c),a.options.useExternalPaging||a.refresh()}};return b}]),a.directive("uiGridPaging",["gridUtil","uiGridPagingService",function(a,b){var c="ui-grid/ui-grid-paging";return{priority:-200,scope:!1,require:"uiGrid",compile:function(){return{pre:function(d,e,f,g){b.initializeGrid(g.grid);var h=g.grid.options.pagingTemplate||c;a.getTemplate(h).then(function(a){var b=angular.element(a);e.append(b),g.innerCompile(b)})},post:function(){}}}}}]),a.directive("uiGridPager",["uiGridPagingService","uiGridConstants","gridUtil","i18nService",function(a,b,c,d){return{priority:-200,scope:!0,require:"^uiGrid",link:function(e,f,g,h){e.sizesLabel=d.getSafeText("paging.sizes"),e.totalItemsLabel=d.getSafeText("paging.totalItems");var i=e.grid.options;h.grid.renderContainers.body.registerViewportAdjuster(function(a){return a.height=a.height-c.elementHeight(f),a}),h.grid.registerDataChangeCallback(function(a){a.options.useExternalPaging||(a.options.totalItems=a.rows.length)},[b.dataChange.ROW]);var j=function(){e.showingLow=(i.pagingCurrentPage-1)*i.pagingPageSize+1,e.showingHigh=Math.min(i.pagingCurrentPage*i.pagingPageSize,i.totalItems)},k=function(){return 0===i.totalItems?1:Math.ceil(i.totalItems/i.pagingPageSize)},l=e.$watch("grid.options.totalItems + grid.options.pagingPageSize",function(){e.currentMaxPages=k(),j()}),m=e.$watch("grid.options.pagingCurrentPage + grid.options.pagingPageSize",function(b,c){if(b!==c){if(!angular.isNumber(i.pagingCurrentPage)||i.pagingCurrentPage<1)return void(i.pagingCurrentPage=1);if(i.totalItems>0&&i.pagingCurrentPage>k())return void(i.pagingCurrentPage=k());j(),a.onPagingChanged(e.grid,i.pagingCurrentPage,i.pagingPageSize)}});e.$on("$destroy",function(){l(),m()}),e.pageForward=function(){i.totalItems>0?i.pagingCurrentPage=Math.min(i.pagingCurrentPage+1,e.currentMaxPages):i.pagingCurrentPage++},e.pageBackward=function(){i.pagingCurrentPage=Math.max(i.pagingCurrentPage-1,1)},e.pageToFirst=function(){i.pagingCurrentPage=1},e.pageToLast=function(){i.pagingCurrentPage=e.currentMaxPages},e.cantPageForward=function(){return i.totalItems>0?i.pagingCurrentPage>=e.currentMaxPages:i.data.length<1},e.cantPageToLast=function(){return i.totalItems>0?e.cantPageForward():!0},e.cantPageBackward=function(){return i.pagingCurrentPage<=1}}}}])}(),function(){"use strict";var a=angular.module("ui.grid.pinning",["ui.grid"]);a.service("uiGridPinningService",["gridUtil","GridRenderContainer","i18nService",function(a,b,c){var d={initializeGrid:function(a){d.defaultGridOptions(a.options),a.registerColumnBuilder(d.pinningColumnBuilder)},defaultGridOptions:function(a){a.enablePinning=a.enablePinning!==!1},pinningColumnBuilder:function(b,d,e){if(b.enablePinning=void 0===b.enablePinning?e.enablePinning:b.enablePinning,b.pinnedLeft?"*"===d.width?d.grid.refresh().then(function(){d.renderContainer="left",d.width=d.grid.canvasWidth/d.grid.columns.length,d.grid.createLeftContainer()}):(d.renderContainer="left",d.grid.createLeftContainer()):b.pinnedRight&&("*"===d.width?d.grid.refresh().then(function(){d.renderContainer="right",d.width=d.grid.canvasWidth/d.grid.columns.length,d.grid.createRightContainer()}):(d.renderContainer="right",d.grid.createRightContainer())),b.enablePinning){var f={name:"ui.grid.pinning.pinLeft",title:c.get().pinning.pinLeft,icon:"ui-grid-icon-left-open",shown:function(){return"undefined"==typeof this.context.col.renderContainer||!this.context.col.renderContainer||"left"!==this.context.col.renderContainer},action:function(){this.context.col.renderContainer="left",this.context.col.width=this.context.col.drawnWidth,this.context.col.grid.createLeftContainer(),d.grid.refresh().then(function(){d.grid.refresh()})}},g={name:"ui.grid.pinning.pinRight",title:c.get().pinning.pinRight,icon:"ui-grid-icon-right-open",shown:function(){return"undefined"==typeof this.context.col.renderContainer||!this.context.col.renderContainer||"right"!==this.context.col.renderContainer},action:function(){this.context.col.renderContainer="right",this.context.col.width=this.context.col.drawnWidth,this.context.col.grid.createRightContainer(),d.grid.refresh().then(function(){d.grid.refresh()})}},h={name:"ui.grid.pinning.unpin",title:c.get().pinning.unpin,icon:"ui-grid-icon-cancel",shown:function(){return"undefined"!=typeof this.context.col.renderContainer&&null!==this.context.col.renderContainer&&"body"!==this.context.col.renderContainer},action:function(){this.context.col.renderContainer=null,d.grid.refresh().then(function(){d.grid.refresh()})}};a.arrayContainsObjectWithProperty(d.menuItems,"name","ui.grid.pinning.pinLeft")||d.menuItems.push(f),a.arrayContainsObjectWithProperty(d.menuItems,"name","ui.grid.pinning.pinRight")||d.menuItems.push(g),a.arrayContainsObjectWithProperty(d.menuItems,"name","ui.grid.pinning.unpin")||d.menuItems.push(h)}}};return d}]),a.directive("uiGridPinning",["gridUtil","uiGridPinningService",function(a,b){return{require:"uiGrid",scope:!1,compile:function(){return{pre:function(a,c,d,e){b.initializeGrid(e.grid)},post:function(){}}}}}])}(),function(){"use strict";var a=angular.module("ui.grid.resizeColumns",["ui.grid"]);a.constant("columnBounds",{minWidth:35}),a.service("uiGridResizeColumnsService",["gridUtil","$q","$timeout",function(a,b,c){var d={defaultGridOptions:function(a){a.enableColumnResizing=a.enableColumnResizing!==!1,a.enableColumnResize===!1&&(a.enableColumnResizing=!1)},colResizerColumnBuilder:function(a,c,d){var e=[];return a.enableColumnResizing=void 0===a.enableColumnResizing?d.enableColumnResizing:a.enableColumnResizing,a.enableColumnResize===!1&&(a.enableColumnResizing=!1),b.all(e)},registerPublicApi:function(a){var b={events:{colResizable:{columnSizeChanged:function(){}}}};a.api.registerEventsFromObject(b.events)},fireColumnSizeChanged:function(a,b,d){c(function(){a.api.colResizable.raise.columnSizeChanged(b,d)
})}};return d}]),a.directive("uiGridResizeColumns",["gridUtil","uiGridResizeColumnsService",function(a,b){return{replace:!0,priority:0,require:"^uiGrid",scope:!1,compile:function(){return{pre:function(a,c,d,e){b.defaultGridOptions(e.grid.options),e.grid.registerColumnBuilder(b.colResizerColumnBuilder),b.registerPublicApi(e.grid)},post:function(){}}}}}]),a.directive("uiGridHeaderCell",["gridUtil","$templateCache","$compile","$q",function(a,b,c,d){return{priority:-10,require:"^uiGrid",compile:function(){return{post:function(a,e,f,g){if(g.grid.options.enableColumnResizing){var h=d.defer();f.$observe("renderIndex",function(b){a.renderIndex=a.$eval(b),h.resolve()}),h.promise.then(function(){var d=b.get("ui-grid/columnResizer"),f=angular.element(d).clone(),g=angular.element(d).clone();f.attr("position","left"),g.attr("position","right");var h=a.col,i=h.getRenderContainer(),j=i.renderedColumns[a.renderIndex-1];j&&0!==i.visibleColumnCache.indexOf(a.col)&&j.colDef.enableColumnResizing!==!1&&(e.prepend(f),c(f)(a)),a.col.colDef.enableColumnResizing!==!1&&(e.append(g),c(g)(a))})}}}}}}]),a.directive("uiGridColumnResizer",["$document","gridUtil","uiGridConstants","columnBounds","uiGridResizeColumnsService",function(a,b,c,d,e){var f=angular.element('<div class="ui-grid-resize-overlay"></div>'),g={priority:0,scope:{col:"=",position:"@",renderIndex:"="},require:"?^uiGrid",link:function(g,h,i,j){function k(a){var b=a.getRenderContainer();b.visibleColumnCache.forEach(function(b){if(b!==a){var c=b.colDef;(!c.width||angular.isString(c.width)&&(-1!==c.width.indexOf("*")||-1!==c.width.indexOf("%")))&&(b.width=b.drawnWidth)}})}function l(){j.grid.buildColumns().then(function(){j.grid.refreshCanvas(!0)})}function m(a){a.originalEvent&&(a=a.originalEvent),a.preventDefault(),p=a.clientX-q,0>p?p=0:p>j.grid.gridWidth&&(p=j.grid.gridWidth);var b,e=g.col,h=e.getRenderContainer();if("left"===g.position?(e=h.renderedColumns[g.renderIndex-1],b=g.col):"right"===g.position&&(b=h.renderedColumns[g.renderIndex+1]),e.colDef.enableColumnResizing!==!1){j.grid.element.hasClass("column-resizing")||j.grid.element.addClass("column-resizing");var i=p-o,k=parseInt(e.drawnWidth+i*r,10);e.colDef.minWidth&&k<e.colDef.minWidth?p+=(e.colDef.minWidth-k)*r:!e.colDef.minWidth&&d.minWidth&&k<d.minWidth?p+=e.colDef.minWidth-k:e.colDef.maxWidth&&k>e.colDef.maxWidth&&(p+=(e.colDef.maxWidth-k)*r),f.css({left:p+"px"}),j.fireEvent(c.events.ITEM_DRAGGING)}}function n(b){b.originalEvent&&(b=b.originalEvent),b.preventDefault(),j.grid.element.removeClass("column-resizing"),f.remove(),p=b.clientX-q;var c=p-o;if(0===c)return a.off("mouseup",n),void a.off("mousemove",m);var h,i=g.col,s=i.getRenderContainer();if("left"===g.position?(i=s.renderedColumns[g.renderIndex-1],h=g.col):"right"===g.position&&(h=s.renderedColumns[g.renderIndex+1]),i.colDef.enableColumnResizing!==!1){var t=parseInt(i.drawnWidth+c*r,10);i.colDef.minWidth&&t<i.colDef.minWidth?t=i.colDef.minWidth:!i.colDef.minWidth&&d.minWidth&&t<d.minWidth&&(t=d.minWidth),i.colDef.maxWidth&&t>i.colDef.maxWidth&&(t=i.colDef.maxWidth),i.width=t,k(i),l(c),e.fireColumnSizeChanged(j.grid,i.colDef,c),a.off("mouseup",n),a.off("mousemove",m)}}var o=0,p=0,q=0,r=1;j.grid.isRTL()&&(g.position="left",r=-1),"left"===g.position?h.addClass("left"):"right"===g.position&&h.addClass("right"),h.on("mousedown",function(b){b.originalEvent&&(b=b.originalEvent),b.stopPropagation(),q=j.grid.element[0].getBoundingClientRect().left,o=b.clientX-q,j.grid.element.append(f),f.css({left:o}),a.on("mouseup",n),a.on("mousemove",m)}),h.on("dblclick",function(a){a.stopPropagation();var f,i,m=g.col,n=m.getRenderContainer();"left"===g.position?(m=n.renderedColumns[g.renderIndex-1],f=g.col,i=1):"right"===g.position&&(f=n.renderedColumns[g.renderIndex+1],f=n.renderedColumns[g.renderIndex+1],i=-1);var o=0,p=0,q=b.closestElm(h,".ui-grid-render-container"),r=q.querySelectorAll("."+c.COL_CLASS_PREFIX+m.uid+" .ui-grid-cell-contents");Array.prototype.forEach.call(r,function(a){var c;angular.element(a).parent().hasClass("ui-grid-header-cell")&&(c=angular.element(a).parent()[0].querySelectorAll(".ui-grid-column-menu-button")),b.fakeElement(a,{},function(a){var d=angular.element(a);d.attr("style","float: left");var e=b.elementWidth(d);if(c){var f=b.elementWidth(c);e+=f}e>o&&(o=e,p=o-e)})}),m.colDef.minWidth&&o<m.colDef.minWidth?o=m.colDef.minWidth:!m.colDef.minWidth&&d.minWidth&&o<d.minWidth&&(o=d.minWidth),m.colDef.maxWidth&&o>m.colDef.maxWidth&&(o=m.colDef.maxWidth),m.width=parseInt(o,10),k(m),l(p),e.fireColumnSizeChanged(j.grid,m.colDef,p)}),h.on("$destroy",function(){h.off("mousedown"),h.off("dblclick"),a.off("mousemove",m),a.off("mouseup",n)})}};return g}])}(),function(){"use strict";var a=angular.module("ui.grid.rowEdit",["ui.grid","ui.grid.edit","ui.grid.cellNav"]);a.constant("uiGridRowEditConstants",{}),a.service("uiGridRowEditService",["$interval","$q","uiGridConstants","uiGridRowEditConstants","gridUtil",function(a,b,c,d,e){var f={initializeGrid:function(a,b){b.rowEdit={};var c={events:{rowEdit:{saveRow:function(){}}},methods:{rowEdit:{setSavePromise:function(a,b,c){f.setSavePromise(a,b,c)},getDirtyRows:function(a){return a.rowEdit.dirtyRows?a.rowEdit.dirtyRows:[]},getErrorRows:function(a){return a.rowEdit.errorRows?a.rowEdit.errorRows:[]},flushDirtyRows:function(a){return f.flushDirtyRows(a)},setRowsDirty:function(a,b){f.setRowsDirty(a,b)}}}};b.api.registerEventsFromObject(c.events),b.api.registerMethodsFromObject(c.methods),b.api.core.on.renderingComplete(a,function(){b.api.edit.on.afterCellEdit(a,f.endEditCell),b.api.edit.on.beginCellEdit(a,f.beginEditCell),b.api.edit.on.cancelCellEdit(a,f.cancelEditCell),b.api.cellNav&&b.api.cellNav.on.navigate(a,f.navigate)})},defaultGridOptions:function(){},saveRow:function(a,b){var c=this;return function(){b.isSaving=!0;var d=a.api.rowEdit.raise.saveRow(b.entity);return b.rowEditSavePromise?b.rowEditSavePromise.then(c.processSuccessPromise(a,b),c.processErrorPromise(a,b)):e.logError("A promise was not returned when saveRow event was raised, either nobody is listening to event, or event handler did not return a promise"),d}},setSavePromise:function(a,b,c){var d=a.getRow(b);d.rowEditSavePromise=c},processSuccessPromise:function(a,b){var c=this;return function(){delete b.isSaving,delete b.isDirty,delete b.isError,delete b.rowEditSaveTimer,c.removeRow(a.rowEdit.errorRows,b),c.removeRow(a.rowEdit.dirtyRows,b)}},processErrorPromise:function(a,b){return function(){delete b.isSaving,delete b.rowEditSaveTimer,b.isError=!0,a.rowEdit.errorRows||(a.rowEdit.errorRows=[]),f.isRowPresent(a.rowEdit.errorRows,b)||a.rowEdit.errorRows.push(b)}},removeRow:function(a,b){angular.forEach(a,function(c,d){c.uid===b.uid&&a.splice(d,1)})},isRowPresent:function(a,b){var c=!1;return angular.forEach(a,function(a){a.uid===b.uid&&(c=!0)}),c},flushDirtyRows:function(a){var c=[];return angular.forEach(a.rowEdit.dirtyRows,function(b){f.saveRow(a,b)(),c.push(b.rowEditSavePromise)}),b.all(c)},endEditCell:function(a,b,c,d){var g=this.grid,h=g.getRow(a);return h?void((c!==d||h.isDirty)&&(g.rowEdit.dirtyRows||(g.rowEdit.dirtyRows=[]),h.isDirty||(h.isDirty=!0,g.rowEdit.dirtyRows.push(h)),delete h.isError,f.considerSetTimer(g,h))):void e.logError("Unable to find rowEntity in grid data, dirty flag cannot be set")},beginEditCell:function(a){var b=this.grid,c=b.getRow(a);return c?void f.cancelTimer(b,c):void e.logError("Unable to find rowEntity in grid data, timer cannot be cancelled")},cancelEditCell:function(a){var b=this.grid,c=b.getRow(a);return c?void f.considerSetTimer(b,c):void e.logError("Unable to find rowEntity in grid data, timer cannot be set")},navigate:function(a,b){var c=this.grid;a.row.rowEditSaveTimer&&f.cancelTimer(c,a.row),b&&b.row&&b.row!==a.row&&f.considerSetTimer(c,b.row)},considerSetTimer:function(b,c){if(f.cancelTimer(b,c),c.isDirty&&!c.isSaving&&-1!==b.options.rowEditWaitInterval){var d=b.options.rowEditWaitInterval?b.options.rowEditWaitInterval:2e3;c.rowEditSaveTimer=a(f.saveRow(b,c),d,1)}},cancelTimer:function(b,c){c.rowEditSaveTimer&&!c.isSaving&&(a.cancel(c.rowEditSaveTimer),delete c.rowEditSaveTimer)},setRowsDirty:function(a,b){var c;b.forEach(function(b){c=a.getRow(b),c?(a.rowEdit.dirtyRows||(a.rowEdit.dirtyRows=[]),c.isDirty||(c.isDirty=!0,a.rowEdit.dirtyRows.push(c)),delete c.isError,f.considerSetTimer(a,c)):e.logError("requested row not found in rowEdit.setRowsDirty, row was: "+b)})}};return f}]),a.directive("uiGridRowEdit",["gridUtil","uiGridRowEditService","uiGridEditConstants",function(a,b){return{replace:!0,priority:0,require:"^uiGrid",scope:!1,compile:function(){return{pre:function(a,c,d,e){b.initializeGrid(a,e.grid)},post:function(){}}}}}]),a.directive("uiGridViewport",["$compile","uiGridConstants","gridUtil","$parse",function(){return{priority:-200,scope:!1,compile:function(a){var b=angular.element(a.children().children()[0]),c=b.attr("ng-class"),d="";return d=c?c.slice(0,-1)+", 'ui-grid-row-dirty': row.isDirty, 'ui-grid-row-saving': row.isSaving, 'ui-grid-row-error': row.isError}":"{'ui-grid-row-dirty': row.isDirty, 'ui-grid-row-saving': row.isSaving, 'ui-grid-row-error': row.isError}",b.attr("ng-class",d),{pre:function(){},post:function(){}}}}}])}(),function(){"use strict";var a=angular.module("ui.grid.selection",["ui.grid"]);a.constant("uiGridSelectionConstants",{featureName:"selection",selectionRowHeaderColName:"selectionRowHeaderCol"}),a.service("uiGridSelectionService",["$q","$templateCache","uiGridSelectionConstants","gridUtil",function(){var a={initializeGrid:function(b){b.selection={},b.selection.lastSelectedRow=null,b.selection.selectAll=!1,a.defaultGridOptions(b.options);var c={events:{selection:{rowSelectionChanged:function(){},rowSelectionChangedBatch:function(){}}},methods:{selection:{toggleRowSelection:function(c){var d=b.getRow(c);null!==d&&a.toggleRowSelection(b,d,b.options.multiSelect,b.options.noUnselect)},selectRow:function(c){var d=b.getRow(c);null===d||d.isSelected||a.toggleRowSelection(b,d,b.options.multiSelect,b.options.noUnselect)},selectRowByVisibleIndex:function(c){var d=b.renderContainers.body.visibleRowCache[c];null===d||d.isSelected||a.toggleRowSelection(b,d,b.options.multiSelect,b.options.noUnselect)},unSelectRow:function(c){var d=b.getRow(c);null!==d&&d.isSelected&&a.toggleRowSelection(b,d,b.options.multiSelect,b.options.noUnselect)},selectAllRows:function(){if(b.options.multiSelect!==!1){var c=[];b.rows.forEach(function(d){d.isSelected||(d.isSelected=!0,a.decideRaiseSelectionEvent(b,d,c))}),a.decideRaiseSelectionBatchEvent(b,c)}},selectAllVisibleRows:function(){if(b.options.multiSelect!==!1){var c=[];b.rows.forEach(function(d){d.visible?d.isSelected||(d.isSelected=!0,a.decideRaiseSelectionEvent(b,d,c)):d.isSelected&&(d.isSelected=!1,a.decideRaiseSelectionEvent(b,d,c))}),a.decideRaiseSelectionBatchEvent(b,c)}},clearSelectedRows:function(){a.clearSelectedRows(b)},getSelectedRows:function(){return a.getSelectedRows(b).map(function(a){return a.entity})},getSelectedGridRows:function(){return a.getSelectedRows(b)},setMultiSelect:function(a){b.options.multiSelect=a},setModifierKeysToMultiSelect:function(a){b.options.modifierKeysToMultiSelect=a},getSelectAllState:function(){return b.selection.selectAll}}}};b.api.registerEventsFromObject(c.events),b.api.registerMethodsFromObject(c.methods)},defaultGridOptions:function(a){a.enableRowSelection=a.enableRowSelection!==!1,a.multiSelect=a.multiSelect!==!1,a.noUnselect=a.noUnselect===!0,a.modifierKeysToMultiSelect=a.modifierKeysToMultiSelect===!0,a.enableRowHeaderSelection=a.enableRowHeaderSelection!==!1,a.enableSelectAll=a.enableSelectAll!==!1,a.enableSelectionBatchEvent=a.enableSelectionBatchEvent!==!1,a.selectionRowHeaderWidth=angular.isDefined(a.selectionRowHeaderWidth)?a.selectionRowHeaderWidth:30},toggleRowSelection:function(b,c,d,e){var f=c.isSelected;if(d||f){if(!d&&f){var g=a.getSelectedRows(b);g.length>1&&(f=!1,a.clearSelectedRows(b))}}else a.clearSelectedRows(b);f&&e||(c.isSelected=!f,c.isSelected===!0&&(b.selection.lastSelectedRow=c),b.api.selection.raise.rowSelectionChanged(c))},shiftSelect:function(b,c,d){if(d){var e=a.getSelectedRows(b),f=e.length>0?b.renderContainers.body.visibleRowCache.indexOf(b.selection.lastSelectedRow):0,g=b.renderContainers.body.visibleRowCache.indexOf(c);if(f>g){var h=f;f=g,g=h}for(var i=[],j=f;g>=j;j++){var k=b.renderContainers.body.visibleRowCache[j];k&&(k.isSelected||(k.isSelected=!0,b.selection.lastSelectedRow=k,a.decideRaiseSelectionEvent(b,k,i)))}a.decideRaiseSelectionBatchEvent(b,i)}},getSelectedRows:function(a){return a.rows.filter(function(a){return a.isSelected})},clearSelectedRows:function(b){var c=[];a.getSelectedRows(b).forEach(function(d){d.isSelected&&(d.isSelected=!1,a.decideRaiseSelectionEvent(b,d,c))}),a.decideRaiseSelectionBatchEvent(b,c)},decideRaiseSelectionEvent:function(a,b,c){a.options.enableSelectionBatchEvent?c.push(b):a.api.selection.raise.rowSelectionChanged(b)},decideRaiseSelectionBatchEvent:function(a,b){b.length>0&&a.api.selection.raise.rowSelectionChangedBatch(b)}};return a}]),a.directive("uiGridSelection",["uiGridSelectionConstants","uiGridSelectionService","$templateCache",function(a,b){return{replace:!0,priority:0,require:"^uiGrid",scope:!1,compile:function(){return{pre:function(c,d,e,f){if(b.initializeGrid(f.grid),f.grid.options.enableRowHeaderSelection){var g={name:a.selectionRowHeaderColName,displayName:"",width:f.grid.options.selectionRowHeaderWidth,cellTemplate:"ui-grid/selectionRowHeader",headerCellTemplate:"ui-grid/selectionHeaderCell",enableColumnResizing:!1,enableColumnMenu:!1};f.grid.addRowHeaderColumn(g)}},post:function(){}}}}}]),a.directive("uiGridSelectionRowHeaderButtons",["$templateCache","uiGridSelectionService",function(a,b){return{replace:!0,restrict:"E",template:a.get("ui-grid/selectionRowHeaderButtons"),scope:!0,require:"^uiGrid",link:function(a,c,d,e){var f=e.grid;a.selectButtonClick=function(a,c){c.shiftKey?b.shiftSelect(f,a,f.options.multiSelect):c.ctrlKey||c.metaKey?b.toggleRowSelection(f,a,f.options.multiSelect,f.options.noUnselect):b.toggleRowSelection(f,a,f.options.multiSelect&&!f.options.modifierKeysToMultiSelect,f.options.noUnselect)}}}}]),a.directive("uiGridSelectionSelectAllButtons",["$templateCache","uiGridSelectionService",function(a,b){return{replace:!0,restrict:"E",template:a.get("ui-grid/selectionSelectAllButtons"),scope:!1,link:function(a){var c=a.col.grid;a.headerButtonClick=function(){c.selection.selectAll?(b.clearSelectedRows(c),c.options.noUnselect&&c.api.selection.selectRowByVisibleIndex(0),c.selection.selectAll=!1):c.options.multiSelect&&(c.api.selection.selectAllVisibleRows(),c.selection.selectAll=!0)}}}}]),a.directive("uiGridViewport",["$compile","uiGridConstants","uiGridSelectionConstants","gridUtil","$parse","uiGridSelectionService",function(){return{priority:-200,scope:!1,compile:function(a){var b=angular.element(a.children().children()[0]),c=b.attr("ng-class"),d="";return d=c?c.slice(0,-1)+",'ui-grid-row-selected': row.isSelected}":"{'ui-grid-row-selected': row.isSelected}",b.attr("ng-class",d),{pre:function(){},post:function(){}}}}}]),a.directive("uiGridCell",["$compile","uiGridConstants","uiGridSelectionConstants","gridUtil","$parse","uiGridSelectionService",function(a,b,c,d,e,f){return{priority:-200,restrict:"A",scope:!1,link:function(a,b){function c(){var c=0,d=300,e=function(b){b.shiftKey?f.shiftSelect(a.grid,a.row,a.grid.options.multiSelect):b.ctrlKey||b.metaKey?f.toggleRowSelection(a.grid,a.row,a.grid.options.multiSelect,a.grid.options.noUnselect):f.toggleRowSelection(a.grid,a.row,a.grid.options.multiSelect&&!a.grid.options.modifierKeysToMultiSelect,a.grid.options.noUnselect),a.$apply()};b.on("touchstart",function(){c=(new Date).getTime()}),b.on("touchend",function(a){var b=(new Date).getTime(),f=b-c;d>f&&e(a)}),b.on("click",function(a){e(a)})}a.grid.options.enableRowSelection&&!a.grid.options.enableRowHeaderSelection&&(b.addClass("ui-grid-disable-selection"),c())}}}])}(),angular.module("ui.grid").run(["$templateCache",function(a){"use strict";a.put("ui-grid/ui-grid-footer",'<div class="ui-grid-footer-panel"><div ui-grid-group-panel ng-show="grid.options.showGroupPanel"></div><div class="ui-grid-footer ui-grid-footer-viewport"><div class="ui-grid-footer-canvas"><div ng-repeat="col in colContainer.renderedColumns track by col.colDef.name" ui-grid-footer-cell col="col" render-index="$index" class="ui-grid-footer-cell clearfix" ng-style="$index === 0 && colContainer.columnStyle($index)"></div></div></div></div>'),a.put("ui-grid/ui-grid-group-panel",'<div class="ui-grid-group-panel"><div ui-t="groupPanel.description" class="description" ng-show="groupings.length == 0"></div><ul ng-show="groupings.length > 0" class="ngGroupList"><li class="ngGroupItem" ng-repeat="group in configGroups"><span class="ngGroupElement"><span class="ngGroupName">{{group.displayName}} <span ng-click="removeGroup($index)" class="ngRemoveGroup">x</span></span> <span ng-hide="$last" class="ngGroupArrow"></span></span></li></ul></div>'),a.put("ui-grid/ui-grid-header",'<div class="ui-grid-header"><div class="ui-grid-top-panel"><div ui-grid-group-panel ng-show="grid.options.showGroupPanel"></div><div class="ui-grid-header-viewport"><div class="ui-grid-header-canvas"><div class="ui-grid-header-cell clearfix" ng-repeat="col in colContainer.renderedColumns track by col.colDef.name" ui-grid-header-cell col="col" render-index="$index" ng-style="$index === 0 && colContainer.columnStyle($index)"></div></div></div><div ui-grid-menu></div></div></div>'),a.put("ui-grid/ui-grid-menu-button",'<div class="ui-grid-menu-button" ng-click="toggleMenu()"><div class="ui-grid-icon-container"><i class="ui-grid-icon-menu">&nbsp;</i></div><div ui-grid-menu menu-items="menuItems"></div></div>'),a.put("ui-grid/ui-grid-no-header",'<div class="ui-grid-top-panel"></div>'),a.put("ui-grid/ui-grid-row",'<div ng-repeat="(colRenderIndex, col) in colContainer.renderedColumns track by col.colDef.name" class="ui-grid-cell" ng-class="{ \'ui-grid-row-header-cell\': col.isRowHeader }" ui-grid-cell></div>'),a.put("ui-grid/ui-grid",'<div ui-i18n="en" class="ui-grid"><!-- TODO (c0bra): add "scoped" attr here, eventually? --><style ui-grid-style>.grid{{ grid.id }} {\n      /* Styles for the grid */\n    }\n\n    .grid{{ grid.id }} .ui-grid-row, .grid{{ grid.id }} .ui-grid-cell, .grid{{ grid.id }} .ui-grid-cell .ui-grid-vertical-bar {\n      height: {{ grid.options.rowHeight }}px;\n    }\n\n    .grid{{ grid.id }} .ui-grid-row:last-child .ui-grid-cell {\n      border-bottom-width: {{ ((grid.getTotalRowHeight() < grid.getViewportHeight()) && \'1\') || \'0\' }}px;\n    }\n\n    {{ grid.verticalScrollbarStyles }}\n    {{ grid.horizontalScrollbarStyles }}\n\n    .ui-grid[dir=rtl] .ui-grid-viewport {\n      padding-left: {{ grid.verticalScrollbarWidth }}px;\n    }\n\n    {{ grid.customStyles }}</style><div ui-grid-menu-button ng-if="grid.options.enableGridMenu"></div><div ui-grid-render-container container-id="\'body\'" col-container-name="\'body\'" row-container-name="\'body\'" bind-scroll-horizontal="true" bind-scroll-vertical="true" enable-horizontal-scrollbar="grid.options.enableHorizontalScrollbar" enable-vertical-scrollbar="grid.options.enableVerticalScrollbar"></div><div ui-grid-column-menu ng-if="grid.options.enableColumnMenus"></div><div ng-transclude></div></div>'),a.put("ui-grid/uiGridCell",'<div class="ui-grid-cell-contents">{{COL_FIELD CUSTOM_FILTERS}}</div>'),a.put("ui-grid/uiGridColumnFilter",'<li class="ui-grid-menu-item ui-grid-clearfix ui-grid-column-filter" ng-show="itemShown()" ng-click="$event.stopPropagation();"><div class="input-container"><input class="column-filter-input" type="text" ng-model="item.model" placeholder="{{ i18n.search.placeholder }}"><div class="column-filter-cancel-icon-container"><i class="ui-grid-filter-cancel ui-grid-icon-cancel column-filter-cancel-icon">&nbsp;</i></div></div><div style="button-container" ng-click="item.action($event)"><div class="ui-grid-button"><i class="ui-grid-icon-search">&nbsp;</i></div></div></li>'),a.put("ui-grid/uiGridColumnMenu",'<div class="ui-grid-column-menu"><div ui-grid-menu menu-items="menuItems"><!-- <div class="ui-grid-column-menu">\n    <div class="inner" ng-show="menuShown">\n      <ul>\n        <div ng-show="grid.options.enableSorting">\n          <li ng-click="sortColumn($event, asc)" ng-class="{ \'selected\' : col.sort.direction == asc }"><i class="ui-grid-icon-sort-alt-up"></i> Sort Ascending</li>\n          <li ng-click="sortColumn($event, desc)" ng-class="{ \'selected\' : col.sort.direction == desc }"><i class="ui-grid-icon-sort-alt-down"></i> Sort Descending</li>\n          <li ng-show="col.sort.direction" ng-click="unsortColumn()"><i class="ui-grid-icon-cancel"></i> Remove Sort</li>\n        </div>\n      </ul>\n    </div>\n  </div> --></div></div>'),a.put("ui-grid/uiGridFooterCell",'<div class="ui-grid-cell-contents" col-index="renderIndex"><div>{{ col.getAggregationText() + ( col.getAggregationValue() CUSTOM_FILTERS ) }}</div></div>'),a.put("ui-grid/uiGridHeaderCell",'<div ng-class="{ \'sortable\': sortable }"><div class="ui-grid-vertical-bar">&nbsp;</div><div class="ui-grid-cell-contents" col-index="renderIndex"><span>{{ col.displayName CUSTOM_FILTERS }}</span> <span ui-grid-visible="col.sort.direction" ng-class="{ \'ui-grid-icon-up-dir\': col.sort.direction == asc, \'ui-grid-icon-down-dir\': col.sort.direction == desc, \'ui-grid-icon-blank\': !col.sort.direction }">&nbsp;</span></div><div class="ui-grid-column-menu-button" ng-if="grid.options.enableColumnMenus && !col.isRowHeader  && col.colDef.enableColumnMenu !== false" class="ui-grid-column-menu-button" ng-click="toggleMenu($event)"><i class="ui-grid-icon-angle-down">&nbsp;</i></div><div ng-if="filterable" class="ui-grid-filter-container" ng-repeat="colFilter in col.filters"><input type="text" class="ui-grid-filter-input" ng-model="colFilter.term" ng-click="$event.stopPropagation()" ng-attr-placeholder="{{colFilter.placeholder || \'\'}}"><div class="ui-grid-filter-button" ng-click="colFilter.term = null"><i class="ui-grid-icon-cancel" ng-show="!!colFilter.term">&nbsp;</i><!-- use !! because angular interprets \'f\' as false --></div></div></div>'),a.put("ui-grid/uiGridMenu",'<div class="ui-grid-menu" ng-if="shown"><div class="ui-grid-menu-mid" ng-show="shownMid"><div class="ui-grid-menu-inner"><ul class="ui-grid-menu-items"><li ng-repeat="item in menuItems" ui-grid-menu-item action="item.action" name="item.title" active="item.active" icon="item.icon" shown="item.shown" context="item.context" template-url="item.templateUrl"></li></ul></div></div></div>'),a.put("ui-grid/uiGridMenuItem",'<li class="ui-grid-menu-item" ng-click="itemAction($event, title)" ng-show="itemShown()" ng-class="{ \'ui-grid-menu-item-active\' : active() }"><i ng-class="icon"></i> {{ name }}</li>'),a.put("ui-grid/uiGridRenderContainer",'<div class="ui-grid-render-container"><div ui-grid-header></div><div ui-grid-viewport></div><div ui-grid-footer ng-if="grid.options.showFooter"></div><!-- native scrolling --><div ui-grid-native-scrollbar ng-if="enableVerticalScrollbar" type="vertical"></div><div ui-grid-native-scrollbar ng-if="enableHorizontalScrollbar" type="horizontal"></div></div>'),a.put("ui-grid/uiGridViewport",'<div class="ui-grid-viewport"><div class="ui-grid-canvas"><div ng-repeat="(rowRenderIndex, row) in rowContainer.renderedRows track by $index" class="ui-grid-row" ng-style="containerCtrl.rowStyle(rowRenderIndex)"><div ui-grid-row="row" row-render-index="rowRenderIndex"></div></div></div></div>'),a.put("ui-grid/cellEditor",'<div><form name="inputForm"><input type="{{inputType}}" ng-class="\'colt\' + col.uid" ui-grid-editor ng-model="MODEL_COL_FIELD"></form></div>'),a.put("ui-grid/dropdownEditor",'<div><form name="inputForm"><select ng-class="\'colt\' + col.uid" ui-grid-edit-dropdown ng-model="MODEL_COL_FIELD" ng-options="field[editDropdownIdLabel] as field[editDropdownValueLabel] CUSTOM_FILTERS for field in editDropdownOptionsArray"></select></form></div>'),a.put("ui-grid/expandableRow",'<div ui-grid-expandable-row ng-if="expandableRow.shouldRenderExpand()" class="expandableRow" style="float:left; margin-top: 1px; margin-bottom: 1px" ng-style="{width: (grid.renderContainers.body.getCanvasWidth() - grid.verticalScrollbarWidth) + \'px\'\n     , height: grid.options.expandableRowHeight + \'px\'}"></div>'),a.put("ui-grid/expandableRowHeader",'<div class="ui-grid-row-header-cell ui-grid-expandable-buttons-cell"><div class="ui-grid-cell-contents"><i ng-class="{ \'ui-grid-icon-plus-squared\' : !row.isExpanded, \'ui-grid-icon-minus-squared\' : row.isExpanded }" ng-click="grid.api.expandable.toggleRowExpansion(row.entity)"></i></div></div>'),a.put("ui-grid/expandableScrollFiller","<div ng-if=\"expandableRow.shouldRenderFiller()\" style=\"float:left; margin-top: 2px; margin-bottom: 2px\" ng-style=\"{ width: (grid.getViewportWidth()) + 'px',\n              height: grid.options.expandableRowHeight + 'px', 'margin-left': grid.options.rowHeader.rowHeaderWidth + 'px' }\"><i class=\"ui-grid-icon-spin5 ui-grid-animate-spin\" ng-style=\"{ 'margin-top': ( grid.options.expandableRowHeight/2 - 5) + 'px',\n            'margin-left' : ((grid.getViewportWidth() - grid.options.rowHeader.rowHeaderWidth)/2 - 5) + 'px' }\"></i></div>"),a.put("ui-grid/csvLink",'<span class="ui-grid-exporter-csv-link-span"><a href="data:text/csv;charset=UTF-8,CSV_CONTENT">LINK_LABEL</a></span>'),a.put("ui-grid/importerMenuItem",'<li class="ui-grid-menu-item"><form><input class="ui-grid-importer-file-chooser" type="file" id="files" name="files[]"></form></li>'),a.put("ui-grid/importerMenuItemContainer","<div ui-grid-importer-menu-item></div>"),a.put("ui-grid/ui-grid-paging",'<div class="ui-grid-pager-panel" ui-grid-pager><div class="ui-grid-pager-container"><div class="ui-grid-pager-control"><button type="button" ng-click="pageToFirst()" ng-disabled="cantPageBackward()"><div class="first-triangle"><div class="first-bar"></div></div></button> <button type="button" ng-click="pageBackward()" ng-disabled="cantPageBackward()"><div class="first-triangle prev-triangle"></div></button> <input type="number" ng-model="grid.options.pagingCurrentPage" min="1" max="{{currentMaxPages}}" required> <span class="ui-grid-pager-max-pages-number" ng-show="currentMaxPages > 0">/ {{currentMaxPages}}</span> <button type="button" ng-click="pageForward()" ng-disabled="cantPageForward()"><div class="last-triangle next-triangle"></div></button> <button type="button" ng-click="pageToLast()" ng-disabled="cantPageToLast()"><div class="last-triangle"><div class="last-bar"></div></div></button></div><div class="ui-grid-pager-row-count-picker"><select ng-model="grid.options.pagingPageSize" ng-options="o as o for o in grid.options.pagingPageSizes"></select><span class="ui-grid-pager-row-count-label">&nbsp;{{sizesLabel}}</span></div></div><div class="ui-grid-pager-count-container"><div class="ui-grid-pager-count"><span ng-show="grid.options.totalItems > 0">{{showingLow}} - {{showingHigh}} of {{grid.options.totalItems}} {{totalItemsLabel}}</span></div></div></div>'),a.put("ui-grid/columnResizer",'<div ui-grid-column-resizer ng-if="grid.options.enableColumnResizing" class="ui-grid-column-resizer" col="col" position="right" render-index="renderIndex"></div>'),a.put("ui-grid/selectionHeaderCell",'<div><div class="ui-grid-vertical-bar">&nbsp;</div><div class="ui-grid-cell-contents" col-index="renderIndex"><ui-grid-selection-select-all-buttons ng-if="grid.options.enableSelectAll"></ui-grid-selection-select-all-buttons>&nbsp;</div></div>'),a.put("ui-grid/selectionRowHeader",'<div class="ui-grid-row-header-cell ui-grid-disable-selection"><div class="ui-grid-cell-contents"><ui-grid-selection-row-header-buttons></ui-grid-selection-row-header-buttons></div></div>'),a.put("ui-grid/selectionRowHeaderButtons",'<div class="ui-grid-selection-row-header-buttons ui-grid-icon-ok" ng-class="{\'ui-grid-row-selected\': row.isSelected}" ng-click="selectButtonClick(row, $event)">&nbsp;</div>'),a.put("ui-grid/selectionSelectAllButtons",'<div class="ui-grid-selection-row-header-buttons ui-grid-icon-ok" ng-class="{\'ui-grid-all-selected\': grid.selection.selectAll}" ng-click="headerButtonClick($event)"></div>')}]);