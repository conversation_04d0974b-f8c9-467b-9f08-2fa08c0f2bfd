/*! jQuery asScrollable - v0.3.1 - 2015-06-15
* https://github.com/amazingSurge/jquery-asScrollable
* Copyright (c) 2015 amazingSurge; Licensed GPL */
!function(a,b,c,d,e){"use strict";function f(){return"undefined"!=typeof a.performance&&a.performance.now?a.performance.now():Date.now()}function g(a){return"string"==typeof a&&-1!=a.indexOf("%")}function h(a){return 0>a?a=0:a>1&&(a=1),100*parseFloat(a).toFixed(4)+"%"}function i(a){return parseFloat(a.slice(0,-1)/100,10)}var j="asScrollable",k=0,l=function(){var b,c,d;return c=a.navigator.userAgent,(b=/(?=.+Mac OS X)(?=.+Firefox)/.test(c))?(d=/Firefox\/\d{2}\./.exec(c),d&&(d=d[0].replace(/\D+/g,"")),b&&+d>23):!1}(),m=c[j]=function(a,b){this.$element=c(b),a=this.options=c.extend({},m.defaults,a||{},this.$element.data("options")||{}),this.classes={wrap:a.namespace,content:a.namespace+"-content",container:a.namespace+"-container",bar:a.namespace+"-bar",barHide:a.namespace+"-bar-hide",skin:a.skin},this.attributes={vertical:{axis:"Y",overflow:"overflow-y",scroll:"scrollTop",scrollLength:"scrollHeight",pageOffset:"pageYOffset",ffPadding:"padding-right",length:"height",clientLength:"clientHeight",offset:"offsetHeight",crossLength:"width",crossClientLength:"clientWidth",crossOffset:"offsetWidth"},horizontal:{axis:"X",overflow:"overflow-x",scroll:"scrollLeft",scrollLength:"scrollWidth",pageOffset:"pageXOffset",ffPadding:"padding-bottom",length:"width",clientLength:"clientWidth",offset:"offsetWidth",crossLength:"height",crossClientLength:"clientHeight",crossOffset:"offsetHeight"}},this._states={},this.horizontal=null,this.vertical=null,this.$bar=null,this._frameId=null,this._timeoutId=null,this.instanceId=++k,this.easing=d.easing[this.options.easing]||d.easing.ease;var e=this.$element.css("position");this.options.containerSelector?(this.$container=this.$element.find(this.options.containerSelector),this.$wrap=this.$element,"static"==e&&this.$wrap.css("position","relative")):(this.$container=this.$element.wrap("<div>"),this.$wrap=this.$container.parent(),this.$wrap.height(this.$element.height()),"static"!==e?this.$wrap.css("position",e):this.$wrap.css("position","relative")),this.options.contentSelector?this.$content=this.$container.find(this.options.contentSelector):(this.$content=this.$container.wrap("<div>"),this.$container=this.$content.parent()),this.init()};m.defaults={namespace:j,skin:null,contentSelector:null,containerSelector:null,enabledClass:"is-enabled",disabledClass:"is-disabled",draggingClass:"is-dragging",hoveringClass:"is-hovering",scrollingClass:"is-scrolling",direction:"vertical",showOnHover:!0,showOnBarHover:!1,duration:500,easing:"ease-in",responsive:!0,throttle:20,scrollbar:{}},m.prototype={constructor:m,init:function(){switch(this.options.direction){case"vertical":this.vertical=!0;break;case"horizontal":this.horizontal=!0;break;case"both":this.horizontal=!0,this.vertical=!0;break;case"auto":var a=this.$element.css("overflow-x"),b=this.$element.css("overflow-y");("scroll"===a||"auto"===a)&&(this.horizontal=!0),("scroll"===b||"auto"===b)&&(this.vertical=!0)}(this.vertical||this.horizontal)&&(this.$wrap.addClass(this.classes.wrap),this.$container.addClass(this.classes.container),this.$content.addClass(this.classes.content),this.options.skin&&this.$wrap.addClass(this.classes.skin),this.$wrap.addClass(this.options.enabledClass),this.vertical&&(this.$wrap.addClass(this.classes.wrap+"-vertical"),this.initLayout("vertical"),this.createBar("vertical")),this.horizontal&&(this.$wrap.addClass(this.classes.wrap+"-horizontal"),this.initLayout("horizontal"),this.createBar("horizontal")),this.bindEvents())},bindEvents:function(){var b=this,d=this.options;d.responsive&&(c(a).on(this.eventNameWithId("orientationchange"),function(){b.update.call(b)}),c(a).on(this.eventNameWithId("resize"),this.throttle(function(){b.update.call(b)},d.throttle))),(this.horizontal||this.vertical)&&(this.$wrap.on(this.eventName("mouseenter"),function(){b.$wrap.addClass(b.options.hoveringClass),b.enter("hovering"),b.trigger("hover")}),this.$wrap.on(this.eventName("mouseleave"),function(){b.$wrap.removeClass(b.options.hoveringClass),b.is("hovering")&&(b.leave("hovering"),b.trigger("hovered"))}),d.showOnHover&&(d.showOnBarHover?this.$bar.on("asScrollbar::hover",function(){b.showBar(this.direction)}).on("asScrollbar::hovered",function(){b.hideBar(this.direction)}):(this.$element.on(j+"::hover",c.proxy(this.showBar,this)),this.$element.on(j+"::hovered",c.proxy(this.hideBar,this)))),this.$container.on(this.eventName("scroll"),function(){if(b.horizontal){var a=b.offsetLeft;b.offsetLeft=b.getOffset("horizontal"),a!==b.offsetLeft&&(b.trigger("scroll",b.getPercentOffset("horizontal"),"horizontal"),0===b.offsetLeft&&b.trigger("scrolltop","horizontal"),b.offsetLeft===b.getScrollLength("horizontal")&&b.trigger("scrollend","horizontal"))}if(b.vertical){var c=b.offsetTop;b.offsetTop=b.getOffset("vertical"),c!==b.offsetTop&&(b.trigger("scroll",b.getPercentOffset("vertical"),"vertical"),0===b.offsetTop&&b.trigger("scrolltop","vertical"),b.offsetTop===b.getScrollLength("vertical")&&b.trigger("scrollend","vertical"))}}),this.$element.on(j+"::scroll",function(a,c,d,e){b.is("scrolling")||(b.enter("scrolling"),b.$wrap.addClass(b.options.scrollingClass));var f=c.getBarApi(e);f.moveTo(h(d),!1,!0),clearTimeout(b._timeoutId),b._timeoutId=setTimeout(function(){b.$wrap.removeClass(b.options.scrollingClass),b.leave("scrolling")},200)}),this.$bar.on("asScrollbar::change",function(a,c,d){b.scrollTo(this.direction,h(d),!1,!0)}),this.$bar.on("asScrollbar::drag",function(){b.$wrap.addClass(b.options.draggingClass)}).on("asScrollbar::dragged",function(){b.$wrap.removeClass(b.options.draggingClass)}))},unbindEvents:function(){this.$wrap.off(this.eventName()),this.$element.off(j+"::scroll").off(j+"::hover").off(j+"::hovered"),this.$container.off(this.eventName()),c(a).off(this.eventNameWithId())},initLayout:function(a){"vertical"===a&&this.$container.css("height",this.$wrap.height());var b=this.attributes[a],c=this.$container[0],d=this.getBrowserScrollbarWidth(a),e=c.parentNode[b.crossClientLength];this.$content.css(b.crossLength,e+"px"),this.$container.css(b.crossLength,d+e+"px"),0===d&&l&&this.$container.css(b.ffPadding,16)},createBar:function(a){var b=c.extend(this.options.scrollbar,{namespace:this.classes.bar,direction:a,useCssTransitions:!1,keyboard:!1}),d=c("<div>");d.asScrollbar(b),this.options.showOnHover&&d.addClass(this.classes.barHide),d.appendTo(this.$wrap),this["$"+a]=d,this.$bar=null===this.$bar?d:this.$bar.add(d),this.updateBarHandle(a)},trigger:function(a){var b=Array.prototype.slice.call(arguments,1),c=[this].concat(b);this.$element.trigger(j+"::"+a,c),a=a.replace(/\b\w+\b/g,function(a){return a.substring(0,1).toUpperCase()+a.substring(1)});var d="on"+a;"function"==typeof this.options[d]&&this.options[d].apply(this,b)},is:function(a){return this._states[a]&&this._states[a]>0},enter:function(a){this._states[a]===e&&(this._states[a]=0),this._states[a]++},leave:function(a){this._states[a]--},eventName:function(a){if("string"!=typeof a||""===a)return"."+this.options.namespace;a=a.split(" ");for(var b=a.length,c=0;b>c;c++)a[c]=a[c]+"."+this.options.namespace;return a.join(" ")},eventNameWithId:function(a){if("string"!=typeof a||""===a)return this.options.namespace+"-"+this.instanceId;a=a.split(" ");for(var b=a.length,c=0;b>c;c++)a[c]=a[c]+"."+this.options.namespace+"-"+this.instanceId;return a.join(" ")},throttle:function(a,b){var c,d,e,f=Date.now||function(){return(new Date).getTime()},g=null,h=0,i=function(){h=f(),g=null,e=a.apply(c,d),c=d=null};return function(){var j=f(),k=b-(j-h);return c=this,d=arguments,0>=k?(clearTimeout(g),g=null,h=j,e=a.apply(c,d),c=d=null):g||(g=setTimeout(i,k)),e}},getBrowserScrollbarWidth:function(a){var c,d,e=this.attributes[a];return e.scrollbarWidth?e.scrollbarWidth:(c=b.createElement("div"),d=c.style,d.position="absolute",d.width="100px",d.height="100px",d.overflow="scroll",d.top="-9999px",b.body.appendChild(c),e.scrollbarWidth=c[e.offset]-c[e.clientLength],b.body.removeChild(c),e.scrollbarWidth)},getOffset:function(a){var b=this.attributes[a],c=this.$container[0];return c[b.pageOffset]||c[b.scroll]},getPercentOffset:function(a){return this.getOffset(a)/this.getScrollLength(a)},getContainerLength:function(a){return this.$container[0][this.attributes[a].clientLength]},getScrollLength:function(a){var b=this.$content[0][this.attributes[a].scrollLength];return b-this.getContainerLength(a)},scrollTo:function(a,b,c,d){var e=typeof b;"string"===e&&(g(b)&&(b=i(b)*this.getScrollLength(a)),b=parseFloat(b),e="number"),"number"===e&&this.move(a,b,c,d)},scrollBy:function(a,b,c,d){var e=typeof b;"string"===e&&(g(b)&&(b=i(b)*this.getScrollLength(a)),b=parseFloat(b),e="number"),"number"===e&&this.move(a,this.getOffset(a)+b,c,d)},move:function(b,c,d,e){if(this[b]===!0&&"number"==typeof c){var g=this;this.enter("moving"),0>c?c=0:c>this.getScrollLength(b)&&(c=this.getScrollLength(b));var h=this.attributes[b],i=function(){g.leave("moving")};if(e)this.$container[0][h.scroll]=c,d!==!1&&this.trigger("change",c/this.getScrollLength(b)),i();else{g.enter("animating");var j=f(),k=g.getOffset(b),l=c,m=function(e){var f=(e-j)/g.options.duration;f>1&&(f=1),f=g.easing.fn(f);var n=parseFloat(k+f*(l-k),10);g.$container[0][h.scroll]=n,d!==!1&&g.trigger("change",c/g.getScrollLength(b)),1===f?(a.cancelAnimationFrame(g._frameId),g._frameId=null,g.leave("animating"),i()):g._frameId=a.requestAnimationFrame(m)};g._frameId=a.requestAnimationFrame(m)}}},scrollXto:function(a,b,c){return this.scrollTo("horizontal",a,b,c)},scrollYto:function(a,b,c){return this.scrollTo("vertical",a,b,c)},scrollXby:function(a,b,c){return this.scrollBy("horizontal",a,b,c)},scrollYby:function(a,b,c){return this.scrollBy("vertical",a,b,c)},getBar:function(a){return a&&this["$"+a]?this["$"+a]:this.$bar},getBarApi:function(a){return this.getBar(a).data("asScrollbar")},getBarX:function(){return this.getBar("horizontal")},getBarY:function(){return this.getBar("vertical")},showBar:function(a){this.getBar(a).removeClass(this.classes.barHide)},hideBar:function(a){this.getBar(a).addClass(this.classes.barHide)},updateBarHandle:function(a){var b=this.getBarApi(a),c=this.getScrollLength(a),d=this.getContainerLength(a);c>0?(b.is("disabled")&&b.enable(),b.setHandleLength(b.getBarLength()*d/(c+d),!0)):b.disable()},disable:function(){this.is("disabled")||(this.enter("disabled"),this.$wrap.addClass(this.options.disabledClass).removeClass(this.options.enabledClass),this.unbindEvents(),this.unStyle())},enable:function(){this.is("disabled")&&(this.leave("disabled"),this.$wrap.addClass(this.options.enabledClass).removeClass(this.options.disabledClass),this.bindEvents(),this.update())},update:function(){this.is("disabled")||(this.vertical&&(this.initLayout("vertical"),this.updateBarHandle("vertical")),this.horizontal&&(this.initLayout("horizontal"),this.updateBarHandle("horizontal")))},unStyle:function(){this.horizontal&&(this.$container.css({height:"","padding-bottom":""}),this.$content.css({height:""})),this.vertical&&(this.$container.css({width:"",height:"","padding-right":""}),this.$content.css({width:""})),this.options.containerSelector||this.$wrap.css({height:""})},destory:function(){this.$wrap.removeClass(this.classes.wrap+"-vertical").removeClass(this.classes.wrap+"-horizontal").removeClass(this.classes.wrap).removeClass(this.options.enabledClass).removeClass(this.classes.disabledClass),this.unStyle(),this.$bar&&this.$bar.remove(),this.unbindEvents(),this.options.containerSelector?this.$container.removeClass(this.classes.container):this.$container.unwrap(),this.options.contentSelector||this.$content.unwrap(),this.$content.removeClass(this.classes.content),this.$element.data(j,null)}},c.fn[j]=function(a){if("string"!=typeof a)return this.each(function(){c(this).data(j)?c(this).data(j).update():c(this).data(j,new m(a,this))});var b=a,d=Array.prototype.slice.call(arguments,1);if(/^\_/.test(b))return!1;if(!/^(get)/.test(b))return this.each(function(){var a=c.data(this,j);a&&"function"==typeof a[b]&&a[b].apply(a,d)});var e=this.first().data(j);return e&&"function"==typeof e[b]?e[b].apply(e,d):this}}(window,document,jQuery,function(a){"use strict";return void 0===a.asScrollbar?!1:a.asScrollbar}(jQuery));