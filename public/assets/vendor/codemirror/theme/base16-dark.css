/*

    Name:       Base16 Default Dark
    Author:     <PERSON> (http://chriskempson.com)

    CodeMirror template by <PERSON> (https://github.com/idleberg/base16-chrome-devtools)
    Original Base16 color scheme by <PERSON> (https://github.com/chriske<PERSON>on/base16)

*/

.cm-s-base16-dark.CodeMirror {
  color: #e0e0e0;
  background: #151515;
}
.cm-s-base16-dark div.CodeMirror-selected {
  background: #303030;
}
.cm-s-base16-dark .CodeMirror-line::-moz-selection, .cm-s-base16-dark .CodeMirror-line > span::-moz-selection, .cm-s-base16-dark .CodeMirror-line > span > span::-moz-selection {
  background: rgba(48, 48, 48, .99);
}
.cm-s-base16-dark .CodeMirror-line::selection, .cm-s-base16-dark .CodeMirror-line > span::selection, .cm-s-base16-dark .CodeMirror-line > span > span::selection {
  background: rgba(48, 48, 48, .99);
}
.cm-s-base16-dark .CodeMirror-line::-moz-selection, .cm-s-base16-dark .CodeMirror-line > span::-moz-selection, .cm-s-base16-dark .CodeMirror-line > span > span::-moz-selection {
  background: rgba(48, 48, 48, .99);
}
.cm-s-base16-dark .CodeMirror-gutters {
  background: #151515;
  border-right: 0;
}
.cm-s-base16-dark .CodeMirror-guttermarker {
  color: #ac4142;
}
.cm-s-base16-dark .CodeMirror-guttermarker-subtle {
  color: #505050;
}
.cm-s-base16-dark .CodeMirror-linenumber {
  color: #505050;
}
.cm-s-base16-dark .CodeMirror-cursor {
  border-left: 1px solid #b0b0b0;
}

.cm-s-base16-dark span.cm-comment {
  color: #8f5536;
}
.cm-s-base16-dark span.cm-atom {
  color: #aa759f;
}
.cm-s-base16-dark span.cm-number {
  color: #aa759f;
}

.cm-s-base16-dark span.cm-property, .cm-s-base16-dark span.cm-attribute {
  color: #90a959;
}
.cm-s-base16-dark span.cm-keyword {
  color: #ac4142;
}
.cm-s-base16-dark span.cm-string {
  color: #f4bf75;
}

.cm-s-base16-dark span.cm-variable {
  color: #90a959;
}
.cm-s-base16-dark span.cm-variable-2 {
  color: #6a9fb5;
}
.cm-s-base16-dark span.cm-def {
  color: #d28445;
}
.cm-s-base16-dark span.cm-bracket {
  color: #e0e0e0;
}
.cm-s-base16-dark span.cm-tag {
  color: #ac4142;
}
.cm-s-base16-dark span.cm-link {
  color: #aa759f;
}
.cm-s-base16-dark span.cm-error {
  color: #b0b0b0;
  background: #ac4142;
}

.cm-s-base16-dark .CodeMirror-activeline-background {
  background: #202020;
}
.cm-s-base16-dark .CodeMirror-matchingbracket {
  color: white !important;
  text-decoration: underline;
}
