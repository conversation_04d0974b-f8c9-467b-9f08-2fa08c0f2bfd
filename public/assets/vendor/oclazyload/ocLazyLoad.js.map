{"version": 3, "sources": ["ocLazyLoad.core.js", "ocLazyLoad.directive.js", "ocLazyLoad.loaders.common.js", "ocLazyLoad.loaders.core.js", "ocLazyLoad.loaders.cssLoader.js", "ocLazyLoad.loaders.jsLoader.js", "ocLazyLoad.loaders.templatesLoader.js", "ocLazyLoad.polyfill.ie8.js"], "names": [], "mappings": "AAAA,CAAC,UAAC,SAAS,QAAW;IAClB;;IAEA,IAAI,aAAa,CAAC,MAAM;QACpB,aAAa;QACb,aAAa;QACb,gBAAgB;QAChB,qBAAqB;QACrB,YAAY,QAAQ;QACpB,YAAY;QACZ,aAAa;;IAEjB,IAAI,aAAa,QAAQ,OAAO,eAAe,CAAC;;IAEhD,WAAW,SAAS,2HAAe,UAAS,qBAAqB,UAAU,kBAAkB,iBAAiB,WAAW,kBAAkB;QACvI,IAAI,UAAU;YACV,YAAY;YACR,qBAAqB;YACrB,kBAAkB;YAClB,iBAAiB;YACjB,UAAU;YACV,WAAW;YACX,kBAAkB;;YAEtB,QAAQ;YACR,SAAS;YACT,cAAc;;QAElB,YAAY,OAAO,UAAS,OAAO;YAC/B,IAAG,KAAK,QAAQ,WAAW,CAAC,GAAG;gBAC3B,MAAM,UAAU,KAAK,MAAM,MAAM;;;;QAIzC,KAAK,SAAS,UAAS,QAAQ;;YAE3B,IAAG,QAAQ,UAAU,OAAO,UAAU;gBAClC,IAAG,QAAQ,QAAQ,OAAO,UAAU;oBAChC,QAAQ,QAAQ,OAAO,SAAS,UAAA,cAAgB;wBAC5C,QAAQ,aAAa,QAAQ;;uBAE9B;oBACH,QAAQ,OAAO,QAAQ,QAAQ,OAAO;;;;YAI9C,IAAG,QAAQ,UAAU,OAAO,QAAQ;gBAChC,QAAQ,OAAO;;;YAGnB,IAAG,QAAQ,UAAU,OAAO,SAAS;gBACjC,SAAS,OAAO;;;;;;;;QAQxB,KAAK,QAAQ,SAAS,MAAM,SAAS;;YAEjC,IAAG,cAAc,WAAW,GAAG;gBAC3B,IAAI,WAAW,CAAC;oBACZ,QAAQ,CAAC,UAAU,UAAU,YAAY;oBACzC,sBAAsB;oBACtB,SAAS,SAAS,OAAO,KAAK;oBAC1B,OAAQ,OAAO,SAAS,KAAK;;;gBAGrC,QAAQ,QAAQ,OAAO,UAAA,MAAQ;oBAC3B,MAAM,QAAQ;oBACd,OAAO,SAAS,eAAe;oBAC/B,OAAO,KAAK,QAAQ,KAAK;oBACzB,IAAG,OAAO,QAAQ,OAAQ,eAAe,QAAQ,GAAG,kBAAkB;wBAClE,QAAQ,QAAQ,QAAQ,GAAG,iBAAgB,MAAM,OAAU;wBAC3D,QAAQ,QAAQ,QAAQ,GAAG,iBAAgB,MAAM,OAAI,QAAS;wBAC9D,QAAQ,QAAQ,QAAQ,GAAG,iBAAgB,MAAM,OAAI,MAAO;;;;gBAIpE,QAAQ,QAAQ,UAAU,UAAA,KAAO;oBAC7B,IAAG,cAAc,WAAW,GAAG;wBAC3B,IAAI,YAAS,MAAQ,QAAQ,YAAS;wBACtC,IAAI,QAAQ,oBAAoB,KAAK;wBACrC,IAAG,OAAO;4BACN,cAAc,KAAK,CAAC,MAAM,MAAM,IAAI,QAAQ,QAAQ;+BACjD;4BACH,QAAQ,QAAQ,IAAI,YAAY,UAAA,MAAQ;gCACpC,IAAG,cAAc,WAAW,KAAK,MAAM,KAAK,OAAO;oCAC/C,cAAc,KAAK,KAAK;;;;;;;;YAQhD,IAAG,cAAc,WAAW,KAAK,EAAE,CAAC,OAAO,WAAW,OAAO,UAAU,QAAQ,UAAU,QAAQ,QAAQ;gBACrG,QAAQ,MAAM;;;YAGlB,IAAI,SAAS,SAAS,OAAO,YAAY;gBACrC,IAAG,WAAW,QAAQ,gBAAgB,CAAC,GAAG;;oBAEtC,WAAW,KAAK;oBAChB,IAAI,aAAa,QAAQ,OAAO;;;oBAGhC,aAAa,MAAM,WAAW,cAAc;oBAC5C,aAAa,MAAM,WAAW,eAAe;;oBAE7C,QAAQ,QAAQ,WAAW,UAAU;;;;YAI7C,QAAQ,QAAQ,eAAe,UAAA,YAAc;gBACzC,OAAO;;;YAGX,gBAAgB;YAChB,mBAAmB;;;;;;;QAOvB,IAAI,YAAY,SAAS,UAAU,KAAK;YACpC,IAAI,QAAQ;YACZ,OAAO,KAAK,UAAU,KAAK,UAAC,KAAK,OAAU;gBACvC,IAAG,QAAQ,SAAS,UAAU,UAAU,MAAM;oBAC1C,IAAG,MAAM,QAAQ,WAAW,CAAC,GAAG;;wBAE5B;;;oBAGJ,MAAM,KAAK;;gBAEf,OAAO;;;;QAIf,IAAI,WAAW,SAAS,SAAS,KAAK;YAClC,IAAI,OAAO;gBAAG;gBAAG;gBAAK;YACtB,IAAG,IAAI,UAAU,GAAG;gBAChB,OAAO;;YAEX,KAAI,IAAI,GAAG,MAAM,IAAI,QAAQ,IAAI,KAAK,KAAK;gBACvC,MAAM,IAAI,WAAW;gBACrB,OAAO,CAAC,QAAQ,KAAK,OAAO;gBAC5B,QAAQ;;YAEZ,OAAO;;;QAGX,SAAS,UAAU,WAAW,iBAAiB,QAAQ;YACnD,IAAG,iBAAiB;gBAChB,IAAI;oBAAG;oBAAY;oBAAU,gBAAgB;gBAC7C,KAAI,IAAI,gBAAgB,SAAS,GAAG,KAAK,GAAG,KAAK;oBAC7C,aAAa,gBAAgB;oBAC7B,IAAG,CAAC,QAAQ,SAAS,aAAa;wBAC9B,aAAa,cAAc;;oBAE/B,IAAG,CAAC,cAAc,WAAW,QAAQ,gBAAgB,CAAC,GAAG;wBACrD;;oBAEJ,IAAI,YAAY,WAAW,QAAQ,gBAAgB,CAAC;oBACpD,WAAW,YAAY;oBACvB,IAAG,WAAW;;wBACV,WAAW,KAAK;wBAChB,UAAU,WAAW,SAAS,UAAU;;oBAE5C,IAAG,SAAS,WAAW,SAAS,GAAG;;wBAE/B,UAAU,cAAc;wBACxB,OAAM,SAAS,WAAW,SAAS,GAAG;4BAClC,UAAU,YAAY,KAAK,SAAS,WAAW;;;oBAGvD,IAAG,QAAQ,UAAU,UAAU,iBAAiB,aAAa,OAAO,QAAQ;wBACxE,gBAAgB,cAAc,OAAO,UAAU;;oBAEnD,aAAa,WAAW,SAAS,cAAc,YAAY,OAAO;oBAClE,aAAa,WAAW,SAAS,eAAe,YAAY,OAAO;oBACnE,UAAU,YAAY,4BAA4B,6BAA6B;oBAC/E,gBAAgB;oBAChB,WAAW,KAAK;;;gBAGpB,IAAI,mBAAmB,UAAU;gBACjC,QAAQ,QAAQ,eAAe,UAAA,IAAM;oBACjC,iBAAiB,OAAO;;;;;QAKpC,SAAS,oBAAoB,MAAM,YAAY;YAC3C,IAAI,aAAa,KAAK,GAAG;gBACrB,OAAO,KAAK;gBACZ,YAAY;YAChB,IAAG,QAAQ,YAAY,WAAW,cAAc;gBAC5C,WAAW,cAAc;;YAE7B,IAAG,QAAQ,YAAY,WAAW,YAAY,QAAQ;gBAClD,WAAW,YAAY,QAAQ;;YAEnC,IAAI,WAAW,SAAX,SAAoB,YAAY,WAAW;gBAC3C,IAAG,CAAC,WAAW,YAAY,MAAM,eAAe,aAAa;oBACzD,WAAW,YAAY,MAAM,cAAc;;gBAE/C,IAAG,WAAW,YAAY,MAAM,YAAY,QAAQ,eAAe,CAAC,GAAG;oBACnE,YAAY;oBACZ,WAAW,YAAY,MAAM,YAAY,KAAK;oBAC9C,UAAU,8BAA8B,CAAC,YAAY,MAAM;;;;YAInE,SAAS,UAAU,MAAM;gBACrB,IAAG,QAAQ,QAAQ,OAAO;;oBACtB,OAAO,SAAS,KAAK;uBAClB,IAAG,QAAQ,SAAS,OAAO;;oBAC9B,OAAO,SAAS,UAAU;uBACvB;oBACH,IAAG,QAAQ,UAAU,SAAS,SAAS,MAAM;wBACzC,OAAO,SAAS,KAAK;2BAClB;;wBACH,OAAO;;;;;YAKnB,IAAG,QAAQ,SAAS,aAAa;gBAC7B,SAAS,YAAY,UAAU,KAAK,GAAG;mBACpC,IAAG,QAAQ,SAAS,aAAa;gBACpC,QAAQ,QAAQ,YAAY,UAAS,QAAQ,KAAK;oBAC9C,IAAG,QAAQ,SAAS,SAAS;;wBACzB,SAAS,QAAQ,UAAU,WAAW;2BACnC;;wBACH,SAAS,KAAK,UAAU;;;mBAG7B;gBACH,OAAO;;YAEX,OAAO;;;QAGX,SAAS,aAAa,WAAW,OAAO,YAAY,UAAU;YAC1D,IAAG,CAAC,OAAO;gBACP;;;YAGJ,IAAI,GAAG,KAAK,MAAM;YAClB,KAAI,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;gBACzC,OAAO,MAAM;gBACb,IAAG,QAAQ,QAAQ,OAAO;oBACtB,IAAG,cAAc,MAAM;wBACnB,IAAG,UAAU,eAAe,KAAK,KAAK;4BAClC,WAAW,UAAU,KAAK;+BACvB;4BACH,MAAM,IAAI,MAAK,0BAA0B,KAAK;;;oBAGtD,IAAI,QAAQ,oBAAoB,MAAM;oBACtC,IAAG,KAAK,OAAO,UAAU;wBACrB,IAAG,SAAS,QAAQ,UAAU,WAAW;4BACrC,SAAS,KAAK,IAAI,MAAM,UAAU,KAAK;;2BAExC;;wBACH,IAAI,aAAa,SAAb,WAAsB,KAAK;4BAC3B,IAAI,UAAU,WAAW,QAAO,KAAK,aAAU,MAAM;4BACrD,IAAG,YAAY,CAAC,KAAK,UAAU;gCAC3B,IAAG,YAAY,CAAC,GAAG;oCACf,WAAW,KAAI,KAAK,aAAU,MAAM;;gCAExC,IAAG,QAAQ,UAAU,WAAW;oCAC5B,SAAS,KAAK,IAAI,MAAM,UAAU,KAAK;;;;wBAInD,IAAG,QAAQ,WAAW,KAAK,GAAG,KAAK;4BAC/B,WAAW,KAAK,GAAG;+BAChB,IAAG,QAAQ,QAAQ,KAAK,GAAG,KAAK;4BACnC,KAAI,IAAI,IAAI,GAAG,OAAO,KAAK,GAAG,GAAG,QAAQ,IAAI,MAAM,KAAK;gCACpD,IAAG,QAAQ,WAAW,KAAK,GAAG,GAAG,KAAK;oCAClC,WAAW,KAAK,GAAG,GAAG;;;;;;;;;QASlD,SAAS,cAAc,QAAQ;YAC3B,IAAI,aAAa;YACjB,IAAG,QAAQ,SAAS,SAAS;gBACzB,aAAa;mBACV,IAAG,QAAQ,SAAS,WAAW,OAAO,eAAe,WAAW,QAAQ,SAAS,OAAO,OAAO;gBAClG,aAAa,OAAO;;YAExB,OAAO;;;QAGX,SAAS,aAAa,YAAY;YAC9B,IAAG,CAAC,QAAQ,SAAS,aAAa;gBAC9B,OAAO;;YAEX,IAAI;gBACA,OAAO,YAAY;cACrB,OAAM,GAAG;gBACP,IAAG,YAAY,KAAK,MAAM,EAAE,QAAQ,QAAQ,qBAAqB,CAAC,GAAG;oBACjE,OAAO;;;;;QAKnB,KAAK,qEAAO,UAAS,MAAM,cAAc,YAAY,eAAe,IAAI;YACpE,IAAI;gBACA,aAAa,cAAc;;YAE/B,IAAG,CAAC,OAAO;gBACP,OAAO;gBACP,KAAK,WAAW,QAAQ;gBACxB,KAAK,UAAU,QAAQ;gBACvB,KAAK,UAAU,QAAQ;;;;YAI3B,UAAU,sBAAsB,YAAW;gBACvC,OAAO,mBAAmB,mBAAoB,mBAAoB,aAAa,KAAK,gBAAgB,QAAQ;;;YAGhH,YAAY,SAAS,UAAU,WAAW,QAAQ;gBAC9C,IAAG,QAAQ;oBACP,WAAW,WAAW,WAAW;;gBAErC,IAAG,OAAO;oBACN,KAAK,KAAK,WAAW;;;;YAI7B,SAAS,OAAO,GAAG;gBACf,IAAI,WAAW,GAAG;gBAClB,KAAK,MAAM,EAAE;gBACb,SAAS,OAAO;gBAChB,OAAO,SAAS;;;YAGpB,OAAO;gBACH,YAAY;;gBAEZ,OAAO;;;;;;gBAMP,gBAAgB,SAAS,gBAAgB;oBACrC,OAAO;;;;;;;gBAOX,aAAa,SAAA,YAAS,OAAO;oBACzB,IAAG,OAAO;wBACN,mBAAmB,KAAK;2BACrB;wBACH,mBAAmB;;;;;;;;;gBAS3B,iBAAiB,SAAA,gBAAS,YAAY;oBAClC,IAAG,CAAC,QAAQ,SAAS,aAAa;wBAC9B,MAAM,IAAI,MAAM;;oBAEpB,IAAG,CAAC,QAAQ,aAAa;wBACrB,OAAO;;oBAEX,OAAO,QAAQ,KAAK,QAAQ;;;;;;;;gBAQhC,iBAAiB,SAAA,gBAAS,cAAc;oBACpC,IAAG,CAAC,QAAQ,SAAS,eAAe;wBAChC,MAAM,IAAI,MAAM;;oBAEpB,QAAQ,aAAa,QAAQ;oBAC7B,OAAO;;;;;;;gBAOX,YAAY,SAAA,aAAA;oBAcR,OAdc;;;;;;;;gBAOlB,UAAU,SAAA,SAAS,cAAc;oBAC7B,IAAI,eAAe,SAAf,aAAwB,QAAQ;wBAChC,IAAI,WAAW,WAAW,QAAQ,UAAU,CAAC;wBAC7C,IAAG,CAAC,UAAU;4BACV,WAAW,CAAC,CAAC,aAAa;;wBAE9B,OAAO;;oBAEX,IAAG,QAAQ,SAAS,eAAe;wBAC/B,eAAe,CAAC;;oBAEpB,IAAG,QAAQ,QAAQ,eAAe;wBAC9B,IAAI,GAAG;wBACP,KAAI,IAAI,GAAG,MAAM,aAAa,QAAQ,IAAI,KAAK,KAAK;4BAChD,IAAG,CAAC,aAAa,aAAa,KAAK;gCAC/B,OAAO;;;wBAGf,OAAO;2BACJ;wBACH,MAAM,IAAI,MAAM;;;;;;;;;gBASxB,gBAAgB;;;;;;;gBAOhB,YAAY,SAAS,UAAU,YAAY;oBACvC,IAAI;wBACA,OAAO,YAAY;sBACrB,OAAM,GAAG;;wBAEP,IAAG,YAAY,KAAK,MAAM,EAAE,QAAQ,QAAQ,qBAAqB,CAAC,GAAG;4BACjE,EAAE,UAAO,iBAAmB,UAAU,cAAW,mDAAmD,EAAE;;wBAE1G,MAAM;;;;;;;;;gBASd,cAAc;;;;;;;;;gBASd,mBAAmB,SAAS,kBAAkB,YAAY,aAAa;oBACnE,IAAI;wBACA;wBACA;wBACA,eAAe;wBACf,OAAO;;oBAEX,aAAa,KAAK,eAAe;;oBAEjC,IAAG,eAAe,MAAM;wBACpB,OAAO,GAAG;2BACP;wBACH,IAAI;4BACA,eAAe,KAAK,WAAW;0BACjC,OAAM,GAAG;4BACP,OAAO,OAAO;;;wBAGlB,WAAW,KAAK,YAAY;;;oBAGhC,QAAQ,QAAQ,UAAU,UAAA,cAAgB;;;wBAGtC,IAAG,QAAQ,SAAS,eAAe;4BAC/B,IAAI,SAAS,KAAK,gBAAgB;4BAClC,IAAG,WAAW,MAAM;gCAChB,YAAY,KAAK;gCACjB;;4BAEJ,eAAe;;;;wBAInB,IAAG,KAAK,aAAa,aAAa,OAAO;;4BAErC,OAAO,aAAa,MAAM,OAAO,UAAA,GAAC;gCAgB9B,OAhBkC,KAAK,gBAAgB,aAAa,MAAM,MAAM,QAAQ,KAAK;;;;4BAGjG,IAAG,KAAK,WAAW,GAAG;gCAClB,KAAK,MAAM,KAAK,YAAY,YAAY,2DAA2D,aAAa,MAAM,gCAAgC;;;;4BAI1J,IAAG,QAAQ,UAAU,KAAK,cAAc;;gCACpC,aAAa,KAAK,KAAK,YAAY,cAAc,aAAa,KAAK,YAAA;oCAmB/D,OAnBqE,KAAK,kBAAkB;;mCAC7F;gCACH,OAAO,OAAO,IAAI,MAAK,oEAAmE,aAAa,QAAK;;4BAEhH;+BACG,IAAG,QAAQ,QAAQ,eAAe;4BACrC,eAAe;gCACX,OAAO;;+BAER,IAAG,QAAQ,SAAS,eAAe;4BACtC,IAAG,aAAa,eAAe,WAAW,aAAa,SAAS;;gCAE5D,KAAK,gBAAgB;gCACrB,YAAY,KAAK,aAAa;;;;;wBAKtC,IAAG,QAAQ,UAAU,aAAa,UAAU,aAAa,MAAM,WAAW,GAAG;4BACzE,IAAG,QAAQ,UAAU,KAAK,cAAc;;gCACpC,aAAa,KAAK,KAAK,YAAY,cAAc,aAAa,KAAK,YAAA;oCAsB/D,OAtBqE,KAAK,kBAAkB;;mCAC7F;gCACH,OAAO,OAAO,IAAI,MAAK,wBAAuB,aAAa,OAAI,qCAAmC,aAAa,QAAK;;;;;;oBAMhI,OAAO,GAAG,IAAI;;;;;;;;gBAQlB,QAAQ,SAAA,OAAS,YAA8B;oBAwB3C,IAxByB,cAAW,UAAA,OAAA,YAAG,KAAE,UAAA;;oBACzC,IAAI,OAAO;wBACP,WAAW,GAAG;oBAClB,IAAG,QAAQ,UAAU,eAAe,eAAe,MAAM;wBACrD,IAAG,QAAQ,QAAQ,aAAa;4BAC5B,IAAI,eAAe;4BACnB,QAAQ,QAAQ,YAAY,UAAA,QAAU;gCAClC,aAAa,KAAK,KAAK,OAAO;;4BAElC,OAAO,GAAG,IAAI;+BACX;4BACH,KAAK,eAAe,KAAK,eAAe,aAAa;;;oBAG7D,IAAG,cAAc,SAAS,GAAG;wBACzB,IAAI,MAAM,cAAc;wBACxB,IAAI,WAAW,SAAS,SAAS,YAAY;4BACzC,YAAY,KAAK;4BACjB,KAAK,kBAAkB,YAAY,aAAa,KAAK,SAAS,UAAU;gCACpE,IAAI;oCACA,aAAa;oCACb,UAAU,WAAW,aAAa;kCACpC,OAAM,GAAG;oCACP,KAAK,MAAM,MAAM,EAAE;oCACnB,SAAS,OAAO;oCAChB;;;gCAGJ,IAAG,cAAc,SAAS,GAAG;oCACzB,SAAS,cAAc;uCACpB;oCACH,SAAS,QAAQ;;+BAEtB,SAAS,MAAM,KAAK;gCACnB,SAAS,OAAO;;;;;wBAKxB,SAAS,cAAc;2BACpB;wBACH,SAAS;;oBAEb,OAAO,SAAS;;;;;;;;gBAQpB,aAAa,SAAS,YAAY,QAAQ;oBACtC,IAAI,WAAW;oBACf,QAAQ,QAAQ,OAAO,UAAU,UAAA,eAAiB;wBAC9C,IAAG,WAAW,QAAQ,mBAAmB,CAAC,GAAG;4BACzC,SAAS,KAAK;;;oBAGtB,OAAO;;;;;;;;;;;gBAWX,cAAc;;;;;;;;gBAQd,qBAAqB;;;;;;;;;gBASrB,WAAW;;;;;;;;gBAQX,gBAAgB;;;;;QAKxB,KAAK,MAAM,QAAQ,QAAQ,OAAO;;;IAGtC,IAAI,eAAe,QAAQ;IAC3B,QAAQ,YAAY,UAAS,SAAS,SAAS,QAAQ;;QAEnD,QAAQ,QAAQ,QAAQ,SAAS,UAAA,QAAU;YACvC,eAAe,QAAQ;;QAE3B,OAAO,aAAa,SAAS,SAAS;;;IAG1C,IAAI,iBAAiB,SAAS,eAAe,MAAM,OAAO;QACtD,IAAG,CAAC,mBAAmB,SAAS,KAAK,UAAU,QAAQ,SAAS,SAAS,cAAc,QAAQ,UAAU,CAAC,GAAG;YACzG,cAAc,KAAK;;;;IAI3B,IAAI,cAAc,QAAQ;IAC1B,QAAQ,SAAS,UAAS,MAAM,UAAU,UAAU;QAChD,eAAe;QACf,OAAO,YAAY,MAAM,UAAU;;;;IAIvC,IAAG,OAAO,WAAW,eAAe,OAAO,YAAY,eAAe,OAAO,YAAY,SAAS;QAC9F,OAAO,UAAU;;GAGtB,SAAS,QAAQ;AC5qBpB,CAAC,UAAA,SAAW;IACR;;IAEA,QAAQ,OAAO,eAAe,UAAU,gEAAc,UAAS,aAAa,UAAU,UAAU,QAAQ;QACpG,OAAO;YACH,UAAU;YACV,UAAU;YACV,UAAU;YACV,SAAS,SAAA,QAAS,SAAS,OAAO;;gBAE9B,IAAI,UAAU,QAAQ,GAAG;gBACzB,QAAQ,KAAK;;gBAEb,OAAO,UAAS,QAAQ,UAAU,OAAO;oBACrC,IAAI,QAAQ,OAAO,MAAM;oBACzB,OAAO,OAAO,YAAM;wBAChB,OAAO,MAAM,WAAW,MAAM;uBAC/B,UAAA,YAAc;wBACb,IAAG,QAAQ,UAAU,aAAa;4BAC9B,YAAY,KAAK,YAAY,KAAK,YAAM;gCACpC,SAAS,MAAM,SAAS;gCACxB,IAAI,WAAW,QAAQ;gCACvB,QAAQ,QAAQ,UAAU,UAAA,SAAW;oCACjC,IAAG,QAAQ,aAAa,GAAG;;wCACvB,SAAS,SAAS;;;;;uBAKnC;;;;;GAMpB,SAAS;ACnCZ,CAAC,UAAA,SAAW;IACR;;IAEA,QAAQ,OAAO,eAAe,oBAAO,UAAA,UAAY;QAC7C,SAAS,UAAU,2DAAe,UAAS,WAAW,IAAI,SAAS,WAAW;YAC1E,IAAI,eAAe;gBACf,kBAAkB;gBAClB,SAAS,QAAQ,SAAS,qBAAqB,QAAQ,MAAM,QAAQ,SAAS,qBAAqB,QAAQ;;;;;;;;;YAS/G,UAAU,eAAe,SAAS,aAAa,MAAM,MAAM,QAAQ;gBAC/D,IAAI,WAAW,GAAG;oBACd;oBACA;oBACA,aAAa,UAAU;oBACvB,cAAc,SAAS,YAAY,KAAK;oBACpC,IAAI,KAAK,IAAI,OAAO;oBACpB,IAAG,IAAI,QAAQ,QAAQ,GAAG;wBACtB,IAAG,IAAI,UAAU,GAAG,IAAI,SAAS,OAAO,KAAK;4BACzC,OAAA,KAAW,MAAG,SAAS;;wBAE3B,OAAA,KAAW,MAAG,UAAU;2BACrB;wBACH,OAAA,KAAW,MAAG,UAAU;;;;;;;gBAOpC,IAAG,QAAQ,YAAY,WAAW,IAAI,QAAQ;oBAC1C,WAAW,IAAI,MAAM,SAAS;;;;gBAIlC,QAAO;oBACH,KAAK;wBACD,KAAK,QAAQ,SAAS,cAAc;wBACpC,GAAG,OAAO;wBACV,GAAG,MAAM;wBACT,GAAG,OAAO,OAAO,UAAU,QAAQ,YAAY,QAAQ;wBACvD;oBACJ,KAAK;wBACD,KAAK,QAAQ,SAAS,cAAc;wBACpC,GAAG,MAAM,OAAO,UAAU,QAAQ,YAAY,QAAQ;wBACtD;oBACJ;wBACI,WAAW,OAAO;wBAClB,SAAS,OAAO,IAAI,MAAK,qBAAqB,OAAI,uCAAuC,OAAI;wBAC7F;;gBAER,GAAG,SAAS,GAAG,wBAAwB,UAAS,GAAG;oBAC/C,IAAG,GAAI,iBAAiB,CAAC,WAAW,KAAK,GAAG,kBAAmB,QAAQ;oBACvE,GAAG,SAAS,GAAG,wBAAwB;oBACvC,SAAS;oBACT,UAAU,WAAW,yBAAyB;oBAC9C,SAAS;;gBAEb,GAAG,UAAU,YAAW;oBACpB,WAAW,OAAO;oBAClB,SAAS,OAAO,IAAI,MAAK,oBAAoB;;gBAEjD,GAAG,QAAQ,OAAO,QAAQ,IAAI;;gBAE9B,IAAI,mBAAmB,OAAO;gBAC9B,IAAG,OAAO,cAAc;oBACpB,IAAI,UAAU,QAAQ,QAAQ,QAAQ,UAAU,OAAO,UAAU,OAAO,eAAe,SAAS,cAAc,OAAO;oBACrH,IAAG,WAAW,QAAQ,SAAS,GAAG;wBAC9B,mBAAmB,QAAQ;;;gBAGnC,iBAAiB,WAAW,aAAa,IAAI;;;;;;;;gBAQ7C,IAAG,QAAQ,OAAO;oBACd,IAAG,CAAC,cAAc;wBACd,IAAI,KAAK,QAAQ,UAAU,UAAU;;;wBAGrC,IAAG,iBAAiB,KAAK,QAAQ,UAAU,WAAW;4BAClD,IAAI,IAAI,QAAS,UAAU,WAAY,MAAM;4BAC7C,IAAI,aAAa,WAAW,CAAC,SAAS,EAAE,IAAI,KAAK,SAAS,EAAE,IAAI,KAAK,SAAS,EAAE,MAAM,GAAG,KAAK,KAAK;4BACnG,kBAAkB,aAAa;+BAC5B,IAAG,GAAG,QAAQ,aAAa,CAAC,GAAG;;4BAClC,IAAI,iBAAiB,WAAW,GAAG,MAAM,GAAG,QAAQ,aAAa;4BACjE,kBAAkB,iBAAiB;+BAChC,IAAG,GAAG,QAAQ,YAAY,CAAC,GAAG;4BACjC,IAAI,eAAe,GAAG,MAAM;4BAC5B,kBAAmB,gBAAgB,aAAa,MAAM,WAAW,aAAa,MAAM;;;;oBAI5F,IAAG,iBAAiB;wBAChB,IAAI,QAAQ;wBACZ,IAAI,WAAW,UAAU,YAAM;4BAC3B,IAAI;gCACA,GAAG,MAAM;gCACT,UAAU,OAAO;gCACjB,GAAG;8BACL,OAAM,GAAG;gCACP,IAAG,EAAE,SAAS,GAAG;oCACb,GAAG;;;2BAGZ;;;;gBAIX,OAAO,SAAS;;;YAGpB,OAAO;;;GAIhB,SAAS;AC9HZ,CAAC,UAAA,SAAW;IACR;;IAEA,QAAQ,OAAO,eAAe,oBAAO,UAAS,UAAU;QACpD,SAAS,UAAU,mCAAe,UAAS,WAAW,IAAI;;;;;;;YAOtD,UAAU,cAAc,SAAS,YAAY,QAAqB;gBAC9D,IADiD,SAAM,UAAA,OAAA,YAAG,KAAE,UAAA;;gBAC5D,IAAI,WAAW;oBACX,iBAAiB;oBACjB,UAAU;oBACV,WAAW;oBACX,eAAe;oBACf,aAAa,UAAU;;gBAE3B,UAAU,YAAY;;gBAEtB,QAAQ,OAAO,QAAQ;;gBAEvB,IAAI,WAAW,SAAX,SAAoB,MAAM;oBAC1B,IAAI,YAAY;wBAAM;oBACtB,IAAG,QAAQ,SAAS,OAAO;wBACvB,YAAY,KAAK;wBACjB,OAAO,KAAK;;oBAEhB,eAAe,WAAW,IAAI;oBAC9B,IAAG,QAAQ,YAAY,iBAAiB,OAAO,UAAU,OAAO;;;wBAG5D,IAAG,CAAC,IAAI,gCAAgC,KAAK,WAAW,MAAM;;4BAC1D,YAAY,EAAE;4BACd,OAAO,KAAK,OAAO,EAAE,GAAG,SAAS,GAAG,KAAK;;;wBAG7C,IAAG,CAAC,WAAW;4BACX,IAAG,CAAC,IAAI,yCAAyC,KAAK,WAAW,MAAM;;gCACnE,YAAY,EAAE;mCACX,IAAG,CAAC,UAAU,SAAS,eAAe,uBAAuB,UAAU,SAAS,eAAe,SAAS;;gCAC3G,YAAY;mCACT;gCACH,UAAU,MAAM,MAAK,wCAAwC;gCAC7D;;;;wBAIR,IAAG,CAAC,cAAc,SAAS,cAAc,WAAW,SAAS,QAAQ,UAAU,CAAC,GAAG;4BAC/E,SAAS,KAAK;+BACX,IAAG,CAAC,cAAc,UAAU,cAAc,UAAU,eAAe,QAAQ,UAAU,CAAC,GAAG;4BAC5F,eAAe,KAAK;+BACjB,IAAG,cAAc,QAAQ,QAAQ,QAAQ,UAAU,CAAC,GAAG;4BAC1D,QAAQ,KAAK;+BACV;4BACH,UAAU,MAAM,MAAK,6BAA6B;;2BAGnD,IAAG,cAAc;wBACpB,SAAS,KAAK;;;;gBAItB,IAAG,OAAO,OAAO;oBACb,SAAS,OAAO,MAAM;uBACnB;oBACH,QAAQ,QAAQ,OAAO,OAAO,UAAA,MAAQ;wBAClC,SAAS;;;;gBAIjB,IAAG,SAAS,SAAS,GAAG;oBACpB,IAAI,cAAc,GAAG;oBACrB,UAAU,UAAU,UAAU,UAAA,KAAO;wBACjC,IAAG,QAAQ,UAAU,QAAQ,UAAU,UAAU,eAAe,qBAAqB;4BACjF,UAAU,MAAM,MAAM;4BACtB,YAAY,OAAO;+BAChB;4BACH,YAAY;;uBAEjB;oBACH,SAAS,KAAK,YAAY;;;gBAG9B,IAAG,eAAe,SAAS,GAAG;oBAC1B,IAAI,oBAAoB,GAAG;oBAC3B,UAAU,gBAAgB,gBAAgB,UAAA,KAAO;wBAC7C,IAAG,QAAQ,UAAU,QAAQ,UAAU,gBAAgB,eAAe,qBAAqB;4BACvF,UAAU,MAAM,MAAM;4BACtB,kBAAkB,OAAO;+BACtB;4BACH,kBAAkB;;uBAEvB;oBACH,SAAS,KAAK,kBAAkB;;;gBAGpC,IAAG,QAAQ,SAAS,GAAG;oBACnB,IAAI,aAAa,GAAG;oBACpB,UAAU,SAAS,SAAS,UAAA,KAAO;wBAC/B,IAAG,QAAQ,UAAU,QAAQ,UAAU,SAAS,eAAe,qBAAqB;4BAChF,UAAU,MAAM,MAAM;4BACtB,WAAW,OAAO;+BACf;4BACH,WAAW;;uBAEhB;oBACH,SAAS,KAAK,WAAW;;;gBAG7B,IAAG,SAAS,WAAW,GAAG;oBACtB,IAAI,WAAW,GAAG;wBACd,MAAM;oBACV,UAAU,MAAM,MAAM;oBACtB,SAAS,OAAO;oBAChB,OAAO,SAAS;uBACb,IAAG,OAAO,SAAS,OAAO,MAAM,SAAS,GAAG;oBAC/C,OAAO,GAAG,IAAI,UAAU,KAAK,YAAA;wBAMzB,OAN+B,UAAU,YAAY,QAAQ;;uBAC9D;oBACH,OAAO,GAAG,IAAI,UAAS,WAAS,UAAA,KAAO;wBACnC,UAAU,YAAY;wBACtB,OAAO;;;;;;;;;;;YAWnB,UAAU,OAAO,UAAS,gBAAqC;gBAQ3D,IARsC,iBAAc,UAAA,OAAA,YAAG,KAAE,UAAA;;gBACzD,IAAI,OAAO;oBACP,SAAS;oBACT,eAAe;oBACf,WAAW,GAAG;oBACd;;;gBAGJ,IAAI,SAAS,QAAQ,KAAK;gBAC1B,IAAI,SAAS,QAAQ,KAAK;;;gBAG1B,IAAG,QAAQ,QAAQ,SAAS;;oBAExB,QAAQ,QAAQ,QAAQ,UAAA,GAAK;wBACzB,aAAa,KAAK,KAAK,KAAK,GAAG;;;;oBAInC,GAAG,IAAI,cAAc,KAAK,UAAA,KAAO;wBAC7B,SAAS,QAAQ;uBAClB,UAAA,KAAO;wBACN,SAAS,OAAO;;;oBAGpB,OAAO,SAAS;;;;gBAIpB,IAAG,QAAQ,SAAS,SAAS;oBACzB,SAAS,KAAK,gBAAgB;oBAC9B,IAAG,CAAC,QAAQ;wBACR,SAAS;4BACL,OAAO,CAAC;;;uBAGb,IAAG,QAAQ,SAAS,SAAS;;oBAEhC,IAAG,QAAQ,UAAU,OAAO,SAAS,QAAQ,UAAU,OAAO,OAAO;wBACjE,SAAS;4BACL,OAAO,CAAC;;2BAET;wBACH,SAAS,KAAK,gBAAgB;;;;gBAItC,IAAG,WAAW,MAAM;oBAChB,IAAI,aAAa,KAAK,eAAe;oBACrC,UAAO,cAAe,cAAc,aAAS;oBAC7C,UAAU,MAAM,MAAM;oBACtB,SAAS,OAAO,IAAI,MAAM;oBAC1B,OAAO,SAAS;uBACb;;oBAEH,IAAG,QAAQ,UAAU,OAAO,WAAW;wBACnC,IAAG,QAAQ,YAAY,OAAO,QAAQ;4BAClC,OAAO,QAAQ;;wBAEnB,IAAG,QAAQ,SAAS,OAAO,WAAW;4BAClC,OAAO,MAAM,KAAK,OAAO;+BACtB,IAAG,QAAQ,QAAQ,OAAO,WAAW;4BACxC,OAAO,MAAM,OAAO,OAAO;;;;;gBAKvC,IAAI,cAAc,QAAQ,OAAO,IAAI,QAAQ;;;gBAG7C,IAAG,QAAQ,YAAY,OAAO,UAAU,QAAQ,UAAU,OAAO,SAAS,UAAU,aAAa,OAAO,OAAO;oBAC3G,OAAO,UAAU,OAAO,OAAO,MAAM;;;gBAGzC,UAAU,YAAY,QAAQ,aAAa,KAAK,YAAM;oBAClD,UAAU,OAAO,MAAM,aAAa,KAAK,UAAA,KAAO;wBAC5C,SAAS,QAAQ;uBAClB,UAAA,KAAO;wBACN,SAAS,OAAO;;mBAErB,UAAA,KAAO;oBACN,SAAS,OAAO;;;gBAGpB,OAAO,SAAS;;;;YAIpB,OAAO;;;GAIhB,SAAS;ACjOZ,CAAC,UAAA,SAAW;IACR;;IAEA,QAAQ,OAAO,eAAe,oBAAO,UAAS,UAAU;QACpD,SAAS,UAAU,mCAAe,UAAU,WAAW,IAAI;;;;;;;;;YASvD,UAAU,YAAY,UAAS,OAAO,UAAU,QAAQ;gBACpD,IAAI,WAAW;gBACf,QAAQ,QAAQ,OAAO,UAAA,MAAQ;oBAC3B,SAAS,KAAK,UAAU,aAAa,OAAO,MAAM;;gBAEtD,GAAG,IAAI,UAAU,KAAK,YAAM;oBACxB;mBACD,UAAA,KAAO;oBACN,SAAS;;;YAGjB,UAAU,UAAU,mBAAmB;;YAEvC,OAAO;;;GAIhB,SAAS;AC9BZ,CAAC,UAAA,SAAW;IACR;;IAEA,QAAQ,OAAO,eAAe,oBAAO,UAAS,UAAU;QACpD,SAAS,UAAU,mCAAe,UAAU,WAAW,IAAI;;;;;;;;;YASvD,UAAU,WAAW,UAAS,OAAO,UAAU,QAAQ;gBACnD,IAAI,WAAW;gBACf,QAAQ,QAAQ,OAAO,UAAA,MAAQ;oBAC3B,SAAS,KAAK,UAAU,aAAa,MAAM,MAAM;;gBAErD,GAAG,IAAI,UAAU,KAAK,YAAM;oBACxB;mBACD,UAAA,KAAO;oBACN,SAAS;;;YAGjB,UAAU,SAAS,mBAAmB;;YAEtC,OAAO;;;GAIhB,SAAS;AC9BZ,CAAC,UAAA,SAAW;IACR;;IAEA,QAAQ,OAAO,eAAe,oBAAO,UAAS,UAAU;QACpD,SAAS,UAAU,8DAAe,UAAU,WAAW,gBAAgB,IAAI,OAAO;;;;;;;;;YAS9E,UAAU,kBAAkB,UAAS,OAAO,UAAU,QAAQ;gBAC1D,IAAI,WAAW;oBACX,aAAa,UAAU;;gBAE3B,QAAQ,QAAQ,OAAO,UAAA,KAAO;oBAC1B,IAAI,WAAW,GAAG;oBAClB,SAAS,KAAK,SAAS;oBACvB,MAAM,IAAI,KAAK,QAAQ,QAAQ,UAAA,MAAQ;wBACnC,IAAG,QAAQ,SAAS,SAAS,KAAK,SAAS,GAAG;4BAC1C,QAAQ,QAAQ,QAAQ,QAAQ,OAAO,UAAA,MAAQ;gCAC3C,IAAG,KAAK,aAAa,YAAY,KAAK,SAAS,oBAAoB;oCAC/D,eAAe,IAAI,KAAK,IAAI,KAAK;;;;wBAI7C,IAAG,QAAQ,YAAY,WAAW,IAAI,OAAO;4BACzC,WAAW,IAAI,KAAK;;wBAExB,SAAS;uBACV,MAAM,UAAS,KAAK;wBACnB,SAAS,OAAO,IAAI,MAAK,mCAAmC,MAAG,QAAQ;;;gBAG/E,OAAO,GAAG,IAAI,UAAU,KAAK,YAAM;oBAC/B;mBACD,UAAA,KAAO;oBACN,SAAS;;;YAGjB,UAAU,gBAAgB,mBAAmB;;YAE7C,OAAO;;;GAIhB,SAAS;AChDZ;AACA,IAAG,CAAC,MAAM,UAAU,SAAS;QACzB,MAAM,UAAU,UAAU,UAAS,eAAe,WAAW;gBACzD,IAAI;;;;gBAIJ,IAAG,QAAQ,MAAM;wBACb,MAAM,IAAI,UAAU;;;gBAGxB,IAAI,IAAI,OAAO;;;;;gBAKf,IAAI,MAAM,EAAE,WAAW;;;gBAGvB,IAAG,QAAQ,GAAG;wBACV,OAAO,CAAC;;;;;gBAKZ,IAAI,IAAI,CAAC,aAAa;;gBAEtB,IAAG,KAAK,IAAI,OAAO,UAAU;wBACzB,IAAI;;;;gBAIR,IAAG,KAAK,KAAK;wBACT,OAAO,CAAC;;;;;;gBAMZ,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,MAAM,KAAK,IAAI,IAAI;;;gBAG7C,OAAM,IAAI,KAAK;;;;;;;;;;;;;wBAaX,IAAG,KAAK,KAAK,EAAE,OAAO,eAAe;gCACjC,OAAO;;wBAEX;;gBAEJ,OAAO,CAAC;;CAEf", "file": "ocLazyLoad.js", "sourcesContent": ["((angular, window) => {\r\n    'use strict';\r\n\r\n    var regModules = ['ng', 'oc.lazyLoad'],\r\n        regInvokes = {},\r\n        regConfigs = [],\r\n        modulesToLoad = [],\r\n        recordDeclarations = [],\r\n        broadcast = angular.noop,\r\n        runBlocks = {},\r\n        justLoaded = [];\r\n\r\n    var ocLazyLoad = angular.module('oc.lazyLoad', ['ng']);\r\n\r\n    ocLazyLoad.provider('$ocLazyLoad', function($controllerProvider, $provide, $compileProvider, $filterProvider, $injector, $animateProvider) {\r\n        var modules = {},\r\n            providers = {\r\n                $controllerProvider: $controllerProvider,\r\n                $compileProvider: $compileProvider,\r\n                $filterProvider: $filterProvider,\r\n                $provide: $provide, // other things (constant, decorator, provider, factory, service)\r\n                $injector: $injector,\r\n                $animateProvider: $animateProvider\r\n            },\r\n            debug = false,\r\n            events = false,\r\n            moduleCache = [];\r\n\r\n        moduleCache.push = function(value) {\r\n            if(this.indexOf(value) === -1) {\r\n                Array.prototype.push.apply(this, arguments);\r\n            }\r\n        };\r\n\r\n        this.config = function(config) {\r\n            // If we want to define modules configs\r\n            if(angular.isDefined(config.modules)) {\r\n                if(angular.isArray(config.modules)) {\r\n                    angular.forEach(config.modules, moduleConfig => {\r\n                        modules[moduleConfig.name] = moduleConfig;\r\n                    });\r\n                } else {\r\n                    modules[config.modules.name] = config.modules;\r\n                }\r\n            }\r\n\r\n            if(angular.isDefined(config.debug)) {\r\n                debug = config.debug;\r\n            }\r\n\r\n            if(angular.isDefined(config.events)) {\r\n                events = config.events;\r\n            }\r\n        };\r\n\r\n        /**\r\n         * Get the list of existing registered modules\r\n         * @param element\r\n         */\r\n        this._init = function _init(element) {\r\n            // this is probably useless now because we override angular.bootstrap\r\n            if(modulesToLoad.length === 0) {\r\n                var elements = [element],\r\n                    names = ['ng:app', 'ng-app', 'x-ng-app', 'data-ng-app'],\r\n                    NG_APP_CLASS_REGEXP = /\\sng[:\\-]app(:\\s*([\\w\\d_]+);?)?\\s/,\r\n                    append = function append(elm) {\r\n                        return (elm && elements.push(elm));\r\n                    };\r\n\r\n                angular.forEach(names, name => {\r\n                    names[name] = true;\r\n                    append(document.getElementById(name));\r\n                    name = name.replace(':', '\\\\:');\r\n                    if(typeof(element[0]) !== 'undefined' && element[0].querySelectorAll) {\r\n                        angular.forEach(element[0].querySelectorAll(`.${ name }`), append);\r\n                        angular.forEach(element[0].querySelectorAll(`.${ name }\\\\:`), append);\r\n                        angular.forEach(element[0].querySelectorAll(`[${ name }]`), append);\r\n                    }\r\n                });\r\n\r\n                angular.forEach(elements, elm => {\r\n                    if(modulesToLoad.length === 0) {\r\n                        var className = ` ${ element.className } `;\r\n                        var match = NG_APP_CLASS_REGEXP.exec(className);\r\n                        if(match) {\r\n                            modulesToLoad.push((match[2] || '').replace(/\\s+/g, ','));\r\n                        } else {\r\n                            angular.forEach(elm.attributes, attr => {\r\n                                if(modulesToLoad.length === 0 && names[attr.name]) {\r\n                                    modulesToLoad.push(attr.value);\r\n                                }\r\n                            });\r\n                        }\r\n                    }\r\n                });\r\n            }\r\n\r\n            if(modulesToLoad.length === 0 && !((window.jasmine || window.mocha) && angular.isDefined(angular.mock))) {\r\n                console.error('No module found during bootstrap, unable to init ocLazyLoad. You should always use the ng-app directive or angular.boostrap when you use ocLazyLoad.');\r\n            }\r\n\r\n            var addReg = function addReg(moduleName) {\r\n                if(regModules.indexOf(moduleName) === -1) {\r\n                    // register existing modules\r\n                    regModules.push(moduleName);\r\n                    var mainModule = angular.module(moduleName);\r\n\r\n                    // register existing components (directives, services, ...)\r\n                    _invokeQueue(null, mainModule._invokeQueue, moduleName);\r\n                    _invokeQueue(null, mainModule._configBlocks, moduleName); // angular 1.3+\r\n\r\n                    angular.forEach(mainModule.requires, addReg);\r\n                }\r\n            };\r\n\r\n            angular.forEach(modulesToLoad, moduleName => {\r\n                addReg(moduleName);\r\n            });\r\n\r\n            modulesToLoad = []; // reset for next bootstrap\r\n            recordDeclarations.pop(); // wait for the next lazy load\r\n        };\r\n\r\n        /**\r\n         * Like JSON.stringify but that doesn't throw on circular references\r\n         * @param obj\r\n         */\r\n        var stringify = function stringify(obj) {\r\n            var cache = [];\r\n            return JSON.stringify(obj, (key, value) => {\r\n                if(angular.isObject(value) && value !== null) {\r\n                    if(cache.indexOf(value) !== -1) {\r\n                        // Circular reference found, discard key\r\n                        return;\r\n                    }\r\n                    // Store value in our collection\r\n                    cache.push(value);\r\n                }\r\n                return value;\r\n            });\r\n        };\r\n\r\n        var hashCode = function hashCode(str) {\r\n            var hash = 0, i, chr, len;\r\n            if(str.length == 0) {\r\n                return hash;\r\n            }\r\n            for(i = 0, len = str.length; i < len; i++) {\r\n                chr = str.charCodeAt(i);\r\n                hash = (hash << 5) - hash + chr;\r\n                hash |= 0; // Convert to 32bit integer\r\n            }\r\n            return hash;\r\n        };\r\n\r\n        function _register(providers, registerModules, params) {\r\n            if(registerModules) {\r\n                var k, moduleName, moduleFn, tempRunBlocks = [];\r\n                for(k = registerModules.length - 1; k >= 0; k--) {\r\n                    moduleName = registerModules[k];\r\n                    if(!angular.isString(moduleName)) {\r\n                        moduleName = getModuleName(moduleName);\r\n                    }\r\n                    if(!moduleName || justLoaded.indexOf(moduleName) !== -1) {\r\n                        continue;\r\n                    }\r\n                    var newModule = regModules.indexOf(moduleName) === -1;\r\n                    moduleFn = ngModuleFct(moduleName);\r\n                    if(newModule) { // new module\r\n                        regModules.push(moduleName);\r\n                        _register(providers, moduleFn.requires, params);\r\n                    }\r\n                    if(moduleFn._runBlocks.length > 0) {\r\n                        // new run blocks detected! Replace the old ones (if existing)\r\n                        runBlocks[moduleName] = [];\r\n                        while(moduleFn._runBlocks.length > 0) {\r\n                            runBlocks[moduleName].push(moduleFn._runBlocks.shift());\r\n                        }\r\n                    }\r\n                    if(angular.isDefined(runBlocks[moduleName]) && (newModule || params.rerun)) {\r\n                        tempRunBlocks = tempRunBlocks.concat(runBlocks[moduleName]);\r\n                    }\r\n                    _invokeQueue(providers, moduleFn._invokeQueue, moduleName, params.reconfig);\r\n                    _invokeQueue(providers, moduleFn._configBlocks, moduleName, params.reconfig); // angular 1.3+\r\n                    broadcast(newModule ? 'ocLazyLoad.moduleLoaded' : 'ocLazyLoad.moduleReloaded', moduleName);\r\n                    registerModules.pop();\r\n                    justLoaded.push(moduleName);\r\n                }\r\n                // execute the run blocks at the end\r\n                var instanceInjector = providers.getInstanceInjector();\r\n                angular.forEach(tempRunBlocks, fn => {\r\n                    instanceInjector.invoke(fn);\r\n                });\r\n            }\r\n        }\r\n\r\n        function _registerInvokeList(args, moduleName) {\r\n            var invokeList = args[2][0],\r\n                type = args[1],\r\n                newInvoke = false;\r\n            if(angular.isUndefined(regInvokes[moduleName])) {\r\n                regInvokes[moduleName] = {};\r\n            }\r\n            if(angular.isUndefined(regInvokes[moduleName][type])) {\r\n                regInvokes[moduleName][type] = {};\r\n            }\r\n            var onInvoke = function(invokeName, signature) {\r\n                if(!regInvokes[moduleName][type].hasOwnProperty(invokeName)) {\r\n                    regInvokes[moduleName][type][invokeName] = [];\r\n                }\r\n                if(regInvokes[moduleName][type][invokeName].indexOf(signature) === -1) {\r\n                    newInvoke = true;\r\n                    regInvokes[moduleName][type][invokeName].push(signature);\r\n                    broadcast('ocLazyLoad.componentLoaded', [moduleName, type, invokeName]);\r\n                }\r\n            };\r\n\r\n            function signature(data) {\r\n                if(angular.isArray(data)) { // arrays are objects, we need to test for it first\r\n                    return hashCode(data.toString());\r\n                } else if(angular.isObject(data)) { // constants & values for example\r\n                    return hashCode(stringify(data));\r\n                } else {\r\n                    if(angular.isDefined(data) && data !== null) {\r\n                        return hashCode(data.toString());\r\n                    } else { // null & undefined constants\r\n                        return data;\r\n                    }\r\n                }\r\n            }\r\n\r\n            if(angular.isString(invokeList)) {\r\n                onInvoke(invokeList, signature(args[2][1]));\r\n            } else if(angular.isObject(invokeList)) {\r\n                angular.forEach(invokeList, function(invoke, key) {\r\n                    if(angular.isString(invoke)) { // decorators for example\r\n                        onInvoke(invoke, signature(invokeList[1]));\r\n                    } else { // components registered as object lists {\"componentName\": function() {}}\r\n                        onInvoke(key, signature(invoke));\r\n                    }\r\n                });\r\n            } else {\r\n                return false;\r\n            }\r\n            return newInvoke;\r\n        }\r\n\r\n        function _invokeQueue(providers, queue, moduleName, reconfig) {\r\n            if(!queue) {\r\n                return;\r\n            }\r\n\r\n            var i, len, args, provider;\r\n            for(i = 0, len = queue.length; i < len; i++) {\r\n                args = queue[i];\r\n                if(angular.isArray(args)) {\r\n                    if(providers !== null) {\r\n                        if(providers.hasOwnProperty(args[0])) {\r\n                            provider = providers[args[0]];\r\n                        } else {\r\n                            throw new Error(`unsupported provider ${ args[0] }`);\r\n                        }\r\n                    }\r\n                    var isNew = _registerInvokeList(args, moduleName);\r\n                    if(args[1] !== 'invoke') {\r\n                        if(isNew && angular.isDefined(provider)) {\r\n                            provider[args[1]].apply(provider, args[2]);\r\n                        }\r\n                    } else { // config block\r\n                        var callInvoke = function(fct) {\r\n                            var invoked = regConfigs.indexOf(`${ moduleName }-${ fct }`);\r\n                            if(invoked === -1 || reconfig) {\r\n                                if(invoked === -1) {\r\n                                    regConfigs.push(`${ moduleName }-${ fct }`);\r\n                                }\r\n                                if(angular.isDefined(provider)) {\r\n                                    provider[args[1]].apply(provider, args[2]);\r\n                                }\r\n                            }\r\n                        };\r\n                        if(angular.isFunction(args[2][0])) {\r\n                            callInvoke(args[2][0]);\r\n                        } else if(angular.isArray(args[2][0])) {\r\n                            for(var j = 0, jlen = args[2][0].length; j < jlen; j++) {\r\n                                if(angular.isFunction(args[2][0][j])) {\r\n                                    callInvoke(args[2][0][j]);\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        function getModuleName(module) {\r\n            var moduleName = null;\r\n            if(angular.isString(module)) {\r\n                moduleName = module;\r\n            } else if(angular.isObject(module) && module.hasOwnProperty('name') && angular.isString(module.name)) {\r\n                moduleName = module.name;\r\n            }\r\n            return moduleName;\r\n        }\r\n\r\n        function moduleExists(moduleName) {\r\n            if(!angular.isString(moduleName)) {\r\n                return false;\r\n            }\r\n            try {\r\n                return ngModuleFct(moduleName);\r\n            } catch(e) {\r\n                if(/No module/.test(e) || e.message.indexOf('$injector:nomod') > -1) {\r\n                    return false;\r\n                }\r\n            }\r\n        }\r\n\r\n        this.$get = function($log, $rootElement, $rootScope, $cacheFactory, $q) {\r\n            var instanceInjector,\r\n                filesCache = $cacheFactory('ocLazyLoad');\r\n\r\n            if(!debug) {\r\n                $log = {};\r\n                $log['error'] = angular.noop;\r\n                $log['warn'] = angular.noop;\r\n                $log['info'] = angular.noop;\r\n            }\r\n\r\n            // Make this lazy because when $get() is called the instance injector hasn't been assigned to the rootElement yet\r\n            providers.getInstanceInjector = function() {\r\n                return instanceInjector ? instanceInjector : (instanceInjector = ($rootElement.data('$injector') || angular.injector()));\r\n            };\r\n\r\n            broadcast = function broadcast(eventName, params) {\r\n                if(events) {\r\n                    $rootScope.$broadcast(eventName, params);\r\n                }\r\n                if(debug) {\r\n                    $log.info(eventName, params);\r\n                }\r\n            };\r\n\r\n            function reject(e) {\r\n                var deferred = $q.defer();\r\n                $log.error(e.message);\r\n                deferred.reject(e);\r\n                return deferred.promise;\r\n            }\r\n\r\n            return {\r\n                _broadcast: broadcast,\r\n\r\n                _$log: $log,\r\n\r\n                /**\r\n                 * Returns the files cache used by the loaders to store the files currently loading\r\n                 * @returns {*}\r\n                 */\r\n                _getFilesCache: function getFilesCache() {\r\n                    return filesCache;\r\n                },\r\n\r\n                /**\r\n                 * Let the service know that it should monitor angular.module because files are loading\r\n                 * @param watch boolean\r\n                 */\r\n                toggleWatch: function(watch) {\r\n                    if(watch) {\r\n                        recordDeclarations.push(true);\r\n                    } else {\r\n                        recordDeclarations.pop();\r\n                    }\r\n                },\r\n\r\n                /**\r\n                 * Let you get a module config object\r\n                 * @param moduleName String the name of the module\r\n                 * @returns {*}\r\n                 */\r\n                getModuleConfig: function(moduleName) {\r\n                    if(!angular.isString(moduleName)) {\r\n                        throw new Error('You need to give the name of the module to get');\r\n                    }\r\n                    if(!modules[moduleName]) {\r\n                        return null;\r\n                    }\r\n                    return angular.copy(modules[moduleName]);\r\n                },\r\n\r\n                /**\r\n                 * Let you define a module config object\r\n                 * @param moduleConfig Object the module config object\r\n                 * @returns {*}\r\n                 */\r\n                setModuleConfig: function(moduleConfig) {\r\n                    if(!angular.isObject(moduleConfig)) {\r\n                        throw new Error('You need to give the module config object to set');\r\n                    }\r\n                    modules[moduleConfig.name] = moduleConfig;\r\n                    return moduleConfig;\r\n                },\r\n\r\n                /**\r\n                 * Returns the list of loaded modules\r\n                 * @returns {string[]}\r\n                 */\r\n                getModules: () => regModules,\r\n\r\n                /**\r\n                 * Let you check if a module has been loaded into Angular or not\r\n                 * @param modulesNames String/Object a module name, or a list of module names\r\n                 * @returns {boolean}\r\n                 */\r\n                isLoaded: function(modulesNames) {\r\n                    var moduleLoaded = function(module) {\r\n                        var isLoaded = regModules.indexOf(module) > -1;\r\n                        if(!isLoaded) {\r\n                            isLoaded = !!moduleExists(module);\r\n                        }\r\n                        return isLoaded;\r\n                    };\r\n                    if(angular.isString(modulesNames)) {\r\n                        modulesNames = [modulesNames];\r\n                    }\r\n                    if(angular.isArray(modulesNames)) {\r\n                        var i, len;\r\n                        for(i = 0, len = modulesNames.length; i < len; i++) {\r\n                            if(!moduleLoaded(modulesNames[i])) {\r\n                                return false;\r\n                            }\r\n                        }\r\n                        return true;\r\n                    } else {\r\n                        throw new Error('You need to define the module(s) name(s)');\r\n                    }\r\n                },\r\n\r\n                /**\r\n                 * Given a module, return its name\r\n                 * @param module\r\n                 * @returns {String}\r\n                 */\r\n                _getModuleName: getModuleName,\r\n\r\n                /**\r\n                 * Returns a module if it exists\r\n                 * @param moduleName\r\n                 * @returns {module}\r\n                 */\r\n                _getModule: function getModule(moduleName) {\r\n                    try {\r\n                        return ngModuleFct(moduleName);\r\n                    } catch(e) {\r\n                        // this error message really suxx\r\n                        if(/No module/.test(e) || e.message.indexOf('$injector:nomod') > -1) {\r\n                            e.message = `The module \"${ stringify(moduleName) }\" that you are trying to load does not exist. ${ e.message }`;\r\n                        }\r\n                        throw e;\r\n                    }\r\n                },\r\n\r\n                /**\r\n                 * Check if a module exists and returns it if it does\r\n                 * @param moduleName\r\n                 * @returns {boolean}\r\n                 */\r\n                moduleExists: moduleExists,\r\n\r\n                /**\r\n                 * Load the dependencies, and might try to load new files depending on the config\r\n                 * @param moduleName (String or Array of Strings)\r\n                 * @param localParams\r\n                 * @returns {*}\r\n                 * @private\r\n                 */\r\n                _loadDependencies: function _loadDependencies(moduleName, localParams) {\r\n                    var loadedModule,\r\n                        requires,\r\n                        diff,\r\n                        promisesList = [],\r\n                        self = this;\r\n\r\n                    moduleName = self._getModuleName(moduleName);\r\n\r\n                    if(moduleName === null) {\r\n                        return $q.when();\r\n                    } else {\r\n                        try {\r\n                            loadedModule = self._getModule(moduleName);\r\n                        } catch(e) {\r\n                            return reject(e);\r\n                        }\r\n                        // get unloaded requires\r\n                        requires = self.getRequires(loadedModule);\r\n                    }\r\n\r\n                    angular.forEach(requires, requireEntry => {\r\n                        // If no configuration is provided, try and find one from a previous load.\r\n                        // If there isn't one, bail and let the normal flow run\r\n                        if(angular.isString(requireEntry)) {\r\n                            var config = self.getModuleConfig(requireEntry);\r\n                            if(config === null) {\r\n                                moduleCache.push(requireEntry); // We don't know about this module, but something else might, so push it anyway.\r\n                                return;\r\n                            }\r\n                            requireEntry = config;\r\n                        }\r\n\r\n                        // Check if this dependency has been loaded previously\r\n                        if(self.moduleExists(requireEntry.name)) {\r\n                            // compare against the already loaded module to see if the new definition adds any new files\r\n                            diff = requireEntry.files.filter(n => self.getModuleConfig(requireEntry.name).files.indexOf(n) < 0);\r\n\r\n                            // If the module was redefined, advise via the console\r\n                            if(diff.length !== 0) {\r\n                                self._$log.warn('Module \"', moduleName, '\" attempted to redefine configuration for dependency. \"', requireEntry.name, '\"\\n Additional Files Loaded:', diff);\r\n                            }\r\n\r\n                            // Push everything to the file loader, it will weed out the duplicates.\r\n                            if(angular.isDefined(self.filesLoader)) { // if a files loader is defined\r\n                                promisesList.push(self.filesLoader(requireEntry, localParams).then(() => self._loadDependencies(requireEntry)));\r\n                            } else {\r\n                                return reject(new Error(`Error: New dependencies need to be loaded from external files (${requireEntry.files}), but no loader has been defined.`));\r\n                            }\r\n                            return;\r\n                        } else if(angular.isArray(requireEntry)) {\r\n                            requireEntry = {\r\n                                files: requireEntry\r\n                            };\r\n                        } else if(angular.isObject(requireEntry)) {\r\n                            if(requireEntry.hasOwnProperty('name') && requireEntry['name']) {\r\n                                // The dependency doesn't exist in the module cache and is a new configuration, so store and push it.\r\n                                self.setModuleConfig(requireEntry);\r\n                                moduleCache.push(requireEntry['name']);\r\n                            }\r\n                        }\r\n\r\n                        // Check if the dependency has any files that need to be loaded. If there are, push a new promise to the promise list.\r\n                        if(angular.isDefined(requireEntry.files) && requireEntry.files.length !== 0) {\r\n                            if(angular.isDefined(self.filesLoader)) { // if a files loader is defined\r\n                                promisesList.push(self.filesLoader(requireEntry, localParams).then(() => self._loadDependencies(requireEntry)));\r\n                            } else {\r\n                                return reject(new Error(`Error: the module \"${requireEntry.name}\" is defined in external files (${requireEntry.files}), but no loader has been defined.`));\r\n                            }\r\n                        }\r\n                    });\r\n\r\n                    // Create a wrapper promise to watch the promise list and resolve it once everything is done.\r\n                    return $q.all(promisesList);\r\n                },\r\n\r\n                /**\r\n                 * Inject new modules into Angular\r\n                 * @param moduleName\r\n                 * @param localParams\r\n                 */\r\n                inject: function(moduleName, localParams = {}) {\r\n                    var self = this,\r\n                        deferred = $q.defer();\r\n                    if(angular.isDefined(moduleName) && moduleName !== null) {\r\n                        if(angular.isArray(moduleName)) {\r\n                            var promisesList = [];\r\n                            angular.forEach(moduleName, module => {\r\n                                promisesList.push(self.inject(module));\r\n                            });\r\n                            return $q.all(promisesList);\r\n                        } else {\r\n                            self._addToLoadList(self._getModuleName(moduleName), true);\r\n                        }\r\n                    }\r\n                    if(modulesToLoad.length > 0) {\r\n                        var res = modulesToLoad.slice(); // clean copy\r\n                        var loadNext = function loadNext(moduleName) {\r\n                            moduleCache.push(moduleName);\r\n                            self._loadDependencies(moduleName, localParams).then(function success() {\r\n                                try {\r\n                                    justLoaded = [];\r\n                                    _register(providers, moduleCache, localParams);\r\n                                } catch(e) {\r\n                                    self._$log.error(e.message);\r\n                                    deferred.reject(e);\r\n                                    return;\r\n                                }\r\n\r\n                                if(modulesToLoad.length > 0) {\r\n                                    loadNext(modulesToLoad.shift()); // load the next in list\r\n                                } else {\r\n                                    deferred.resolve(res); // everything has been loaded, resolve\r\n                                }\r\n                            }, function error(err) {\r\n                                deferred.reject(err);\r\n                            });\r\n                        };\r\n\r\n                        // load the first in list\r\n                        loadNext(modulesToLoad.shift());\r\n                    } else {\r\n                        deferred.resolve();\r\n                    }\r\n                    return deferred.promise;\r\n                },\r\n\r\n                /**\r\n                 * Get the list of required modules/services/... for this module\r\n                 * @param module\r\n                 * @returns {Array}\r\n                 */\r\n                getRequires: function getRequires(module) {\r\n                    var requires = [];\r\n                    angular.forEach(module.requires, requireModule => {\r\n                        if(regModules.indexOf(requireModule) === -1) {\r\n                            requires.push(requireModule);\r\n                        }\r\n                    });\r\n                    return requires;\r\n                },\r\n\r\n                /**\r\n                 * Invoke the new modules & component by their providers\r\n                 * @param providers\r\n                 * @param queue\r\n                 * @param moduleName\r\n                 * @param reconfig\r\n                 * @private\r\n                 */\r\n                _invokeQueue: _invokeQueue,\r\n\r\n                /**\r\n                 * Check if a module has been invoked and registers it if not\r\n                 * @param args\r\n                 * @param moduleName\r\n                 * @returns {boolean} is new\r\n                 */\r\n                _registerInvokeList: _registerInvokeList,\r\n\r\n                /**\r\n                 * Register a new module and loads it, executing the run/config blocks if needed\r\n                 * @param providers\r\n                 * @param registerModules\r\n                 * @param params\r\n                 * @private\r\n                 */\r\n                _register: _register,\r\n\r\n                /**\r\n                 * Add a module name to the list of modules that will be loaded in the next inject\r\n                 * @param name\r\n                 * @param force\r\n                 * @private\r\n                 */\r\n                _addToLoadList: _addToLoadList\r\n            };\r\n        };\r\n\r\n        // Let's get the list of loaded modules & components\r\n        this._init(angular.element(window.document));\r\n    });\r\n\r\n    var bootstrapFct = angular.bootstrap;\r\n    angular.bootstrap = function(element, modules, config) {\r\n        // we use slice to make a clean copy\r\n        angular.forEach(modules.slice(), module => {\r\n            _addToLoadList(module, true);\r\n        });\r\n        return bootstrapFct(element, modules, config);\r\n    };\r\n\r\n    var _addToLoadList = function _addToLoadList(name, force) {\r\n        if((recordDeclarations.length > 0 || force) && angular.isString(name) && modulesToLoad.indexOf(name) === -1) {\r\n            modulesToLoad.push(name);\r\n        }\r\n    };\r\n\r\n    var ngModuleFct = angular.module;\r\n    angular.module = function(name, requires, configFn) {\r\n        _addToLoadList(name);\r\n        return ngModuleFct(name, requires, configFn);\r\n    };\r\n\r\n    // CommonJS package manager support:\r\n    if(typeof module !== 'undefined' && typeof exports !== 'undefined' && module.exports === exports) {\r\n        module.exports = 'oc.lazyLoad';\r\n    }\r\n\r\n})(angular, window);\r\n", "(angular => {\r\n    'use strict';\r\n\r\n    angular.module('oc.lazyLoad').directive('ocLazyLoad', function($ocLazyLoad, $compile, $animate, $parse) {\r\n        return {\r\n            restrict: 'A',\r\n            terminal: true,\r\n            priority: 1000,\r\n            compile: function(element, attrs) {\r\n                // we store the content and remove it before compilation\r\n                var content = element[0].innerHTML;\r\n                element.html('');\r\n\r\n                return function($scope, $element, $attr) {\r\n                    var model = $parse($attr.ocLazyLoad);\r\n                    $scope.$watch(() => {\r\n                        return model($scope) || $attr.ocLazyLoad; // it can be a module name (string), an object, an array, or a scope reference to any of this\r\n                    }, moduleName => {\r\n                        if(angular.isDefined(moduleName)) {\r\n                            $ocLazyLoad.load(moduleName).then(() => {\r\n                                $animate.enter(content, $element);\r\n                                let contents = element.contents();\r\n                                angular.forEach(contents, content => {\r\n                                    if(content.nodeType !== 3) { // 3 is a text node\r\n                                        $compile(content)($scope);\r\n                                    }\r\n                                });\r\n                            });\r\n                        }\r\n                    }, true);\r\n                };\r\n            }\r\n        };\r\n    });\r\n\r\n})(angular);\r\n", "(angular => {\n    'use strict';\n\n    angular.module('oc.lazyLoad').config($provide => {\n        $provide.decorator('$ocLazyLoad', function($delegate, $q, $window, $interval) {\n            var uaCssChecked = false,\n                useCssLoadPatch = false,\n                anchor = $window.document.getElementsByTagName('head')[0] || $window.document.getElementsByTagName('body')[0];\n\n            /**\n             * Load a js/css file\n             * @param type\n             * @param path\n             * @param params\n             * @returns promise\n             */\n            $delegate.buildElement = function buildElement(type, path, params) {\n                var deferred = $q.defer(),\n                    el,\n                    loaded,\n                    filesCache = $delegate._getFilesCache(),\n                    cacheBuster = function cacheBuster(url) {\n                        var dc = new Date().getTime();\n                        if(url.indexOf('?') >= 0) {\n                            if(url.substring(0, url.length - 1) === '&') {\n                                return `${ url }_dc=${ dc }`;\n                            }\n                            return `${ url }&_dc=${ dc }`;\n                        } else {\n                            return `${ url }?_dc=${ dc }`;\n                        }\n                    };\n\n                // Store the promise early so the file load can be detected by other parallel lazy loads\n                // (ie: multiple routes on one page) a 'true' value isn't sufficient\n                // as it causes false positive load results.\n                if(angular.isUndefined(filesCache.get(path))) {\n                    filesCache.put(path, deferred.promise);\n                }\n\n                // Switch in case more content types are added later\n                switch(type) {\n                    case 'css':\n                        el = $window.document.createElement('link');\n                        el.type = 'text/css';\n                        el.rel = 'stylesheet';\n                        el.href = params.cache === false ? cacheBuster(path) : path;\n                        break;\n                    case 'js':\n                        el = $window.document.createElement('script');\n                        el.src = params.cache === false ? cacheBuster(path) : path;\n                        break;\n                    default:\n                        filesCache.remove(path);\n                        deferred.reject(new Error(`Requested type \"${ type }\" is not known. Could not inject \"${ path }\"`));\n                        break;\n                }\n                el.onload = el['onreadystatechange'] = function(e) {\n                    if((el['readyState'] && !/^c|loade/.test(el['readyState'])) || loaded) return;\n                    el.onload = el['onreadystatechange'] = null;\n                    loaded = 1;\n                    $delegate._broadcast('ocLazyLoad.fileLoaded', path);\n                    deferred.resolve();\n                };\n                el.onerror = function() {\n                    filesCache.remove(path);\n                    deferred.reject(new Error(`Unable to load ${ path }`));\n                };\n                el.async = params.serie ? 0 : 1;\n\n                var insertBeforeElem = anchor.lastChild;\n                if(params.insertBefore) {\n                    var element = angular.element(angular.isDefined(window.jQuery) ? params.insertBefore : document.querySelector(params.insertBefore));\n                    if(element && element.length > 0) {\n                        insertBeforeElem = element[0];\n                    }\n                }\n                insertBeforeElem.parentNode.insertBefore(el, insertBeforeElem);\n\n                /*\n                 The event load or readystatechange doesn't fire in:\n                 - iOS < 6       (default mobile browser)\n                 - Android < 4.4 (default mobile browser)\n                 - Safari < 6    (desktop browser)\n                 */\n                if(type == 'css') {\n                    if(!uaCssChecked) {\n                        var ua = $window.navigator.userAgent.toLowerCase();\n\n                        // iOS < 6\n                        if(/iP(hone|od|ad)/.test($window.navigator.platform)) {\n                            var v = ($window.navigator.appVersion).match(/OS (\\d+)_(\\d+)_?(\\d+)?/);\n                            var iOSVersion = parseFloat([parseInt(v[1], 10), parseInt(v[2], 10), parseInt(v[3] || 0, 10)].join('.'));\n                            useCssLoadPatch = iOSVersion < 6;\n                        } else if(ua.indexOf(\"android\") > -1) { // Android < 4.4\n                            var androidVersion = parseFloat(ua.slice(ua.indexOf(\"android\") + 8));\n                            useCssLoadPatch = androidVersion < 4.4;\n                        } else if(ua.indexOf('safari') > -1) {\n                            var versionMatch = ua.match(/version\\/([\\.\\d]+)/i);\n                            useCssLoadPatch = (versionMatch && versionMatch[1] && parseFloat(versionMatch[1]) < 6);\n                        }\n                    }\n\n                    if(useCssLoadPatch) {\n                        var tries = 1000; // * 20 = 20000 miliseconds\n                        var interval = $interval(() => {\n                            try {\n                                el.sheet.cssRules;\n                                $interval.cancel(interval);\n                                el.onload();\n                            } catch(e) {\n                                if(--tries <= 0) {\n                                    el.onerror();\n                                }\n                            }\n                        }, 20);\n                    }\n                }\n\n                return deferred.promise;\n            };\n\n            return $delegate;\n        })\n    });\n\n})(angular);\n", "(angular => {\n    'use strict';\n\n    angular.module('oc.lazyLoad').config(function($provide) {\n        $provide.decorator('$ocLazyLoad', function($delegate, $q) {\n            /**\n             * The function that loads new files\n             * @param config\n             * @param params\n             * @returns {*}\n             */\n            $delegate.filesLoader = function filesLoader(config, params = {}) {\n                var cssFiles = [],\n                    templatesFiles = [],\n                    jsFiles = [],\n                    promises = [],\n                    cachePromise = null,\n                    filesCache = $delegate._getFilesCache();\n\n                $delegate.toggleWatch(true); // start watching angular.module calls\n\n                angular.extend(params, config);\n\n                var pushFile = function(path) {\n                    var file_type = null, m;\n                    if(angular.isObject(path)) {\n                        file_type = path.type;\n                        path = path.path;\n                    }\n                    cachePromise = filesCache.get(path);\n                    if(angular.isUndefined(cachePromise) || params.cache === false) {\n\n                        // always check for requirejs syntax just in case\n                        if((m = /^(css|less|html|htm|js)?(?=!)/.exec(path)) !== null) { // Detect file type using preceding type declaration (ala requireJS)\n                            file_type = m[1];\n                            path = path.substr(m[1].length + 1, path.length);  // Strip the type from the path\n                        }\n\n                        if(!file_type) {\n                            if((m = /[.](css|less|html|htm|js)?((\\?|#).*)?$/.exec(path)) !== null) {  // Detect file type via file extension\n                                file_type = m[1];\n                            } else if(!$delegate.jsLoader.hasOwnProperty('ocLazyLoadLoader') && $delegate.jsLoader.hasOwnProperty('load')) { // requirejs\n                                file_type = 'js';\n                            } else {\n                                $delegate._$log.error(`File type could not be determined. ${ path }`);\n                                return;\n                            }\n                        }\n\n                        if((file_type === 'css' || file_type === 'less') && cssFiles.indexOf(path) === -1) {\n                            cssFiles.push(path);\n                        } else if((file_type === 'html' || file_type === 'htm') && templatesFiles.indexOf(path) === -1) {\n                            templatesFiles.push(path);\n                        } else if(file_type === 'js' || jsFiles.indexOf(path) === -1) {\n                            jsFiles.push(path);\n                        } else {\n                            $delegate._$log.error(`File type is not valid. ${ path }`);\n                        }\n\n                    } else if(cachePromise) {\n                        promises.push(cachePromise);\n                    }\n                };\n\n                if(params.serie) {\n                    pushFile(params.files.shift());\n                } else {\n                    angular.forEach(params.files, path => {\n                        pushFile(path);\n                    });\n                }\n\n                if(cssFiles.length > 0) {\n                    var cssDeferred = $q.defer();\n                    $delegate.cssLoader(cssFiles, err => {\n                        if(angular.isDefined(err) && $delegate.cssLoader.hasOwnProperty('ocLazyLoadLoader')) {\n                            $delegate._$log.error(err);\n                            cssDeferred.reject(err);\n                        } else {\n                            cssDeferred.resolve();\n                        }\n                    }, params);\n                    promises.push(cssDeferred.promise);\n                }\n\n                if(templatesFiles.length > 0) {\n                    var templatesDeferred = $q.defer();\n                    $delegate.templatesLoader(templatesFiles, err => {\n                        if(angular.isDefined(err) && $delegate.templatesLoader.hasOwnProperty('ocLazyLoadLoader')) {\n                            $delegate._$log.error(err);\n                            templatesDeferred.reject(err);\n                        } else {\n                            templatesDeferred.resolve();\n                        }\n                    }, params);\n                    promises.push(templatesDeferred.promise);\n                }\n\n                if(jsFiles.length > 0) {\n                    var jsDeferred = $q.defer();\n                    $delegate.jsLoader(jsFiles, err => {\n                        if(angular.isDefined(err) && $delegate.jsLoader.hasOwnProperty('ocLazyLoadLoader')) {\n                            $delegate._$log.error(err);\n                            jsDeferred.reject(err);\n                        } else {\n                            jsDeferred.resolve();\n                        }\n                    }, params);\n                    promises.push(jsDeferred.promise);\n                }\n\n                if(promises.length === 0) {\n                    let deferred = $q.defer(),\n                        err = \"Error: no file to load has been found, if you're trying to load an existing module you should use the 'inject' method instead of 'load'.\";\n                    $delegate._$log.error(err);\n                    deferred.reject(err);\n                    return deferred.promise;\n                } else if(params.serie && params.files.length > 0) {\n                    return $q.all(promises).then(() => $delegate.filesLoader(config, params));\n                } else {\n                    return $q.all(promises).finally(res => {\n                        $delegate.toggleWatch(false); // stop watching angular.module calls\n                        return res;\n                    });\n                }\n            };\n\n            /**\n             * Load a module or a list of modules into Angular\n             * @param module Mixed the name of a predefined module config object, or a module config object, or an array of either\n             * @param params Object optional parameters\n             * @returns promise\n             */\n            $delegate.load = function(originalModule, originalParams = {}) {\n                var self = this,\n                    config = null,\n                    deferredList = [],\n                    deferred = $q.defer(),\n                    errText;\n\n                // clean copy\n                var module = angular.copy(originalModule);\n                var params = angular.copy(originalParams);\n\n                // If module is an array, break it down\n                if(angular.isArray(module)) {\n                    // Resubmit each entry as a single module\n                    angular.forEach(module, m => {\n                        deferredList.push(self.load(m, params));\n                    });\n\n                    // Resolve the promise once everything has loaded\n                    $q.all(deferredList).then(res => {\n                        deferred.resolve(res);\n                    }, err => {\n                        deferred.reject(err);\n                    });\n\n                    return deferred.promise;\n                }\n\n                // Get or Set a configuration depending on what was passed in\n                if(angular.isString(module)) {\n                    config = self.getModuleConfig(module);\n                    if(!config) {\n                        config = {\n                            files: [module]\n                        };\n                    }\n                } else if(angular.isObject(module)) {\n                    // case {type: 'js', path: lazyLoadUrl + 'testModule.fakejs'}\n                    if(angular.isDefined(module.path) && angular.isDefined(module.type)) {\n                        config = {\n                            files: [module]\n                        };\n                    } else {\n                        config = self.setModuleConfig(module);\n                    }\n                }\n\n                if(config === null) {\n                    var moduleName = self._getModuleName(module);\n                    errText = `Module \"${ moduleName || 'unknown' }\" is not configured, cannot load.`;\n                    $delegate._$log.error(errText);\n                    deferred.reject(new Error(errText));\n                    return deferred.promise;\n                } else {\n                    // deprecated\n                    if(angular.isDefined(config.template)) {\n                        if(angular.isUndefined(config.files)) {\n                            config.files = [];\n                        }\n                        if(angular.isString(config.template)) {\n                            config.files.push(config.template);\n                        } else if(angular.isArray(config.template)) {\n                            config.files.concat(config.template);\n                        }\n                    }\n                }\n\n                var localParams = angular.extend({}, params, config);\n\n                // if someone used an external loader and called the load function with just the module name\n                if(angular.isUndefined(config.files) && angular.isDefined(config.name) && $delegate.moduleExists(config.name)) {\n                    return $delegate.inject(config.name, localParams);\n                }\n\n                $delegate.filesLoader(config, localParams).then(() => {\n                    $delegate.inject(null, localParams).then(res => {\n                        deferred.resolve(res);\n                    }, err => {\n                        deferred.reject(err);\n                    });\n                }, err => {\n                    deferred.reject(err);\n                });\n\n                return deferred.promise;\n            };\n\n            // return the patched service\n            return $delegate;\n        });\n    });\n\n})(angular);\n", "(angular => {\r\n    'use strict';\r\n\r\n    angular.module('oc.lazyLoad').config(function($provide) {\r\n        $provide.decorator('$ocLazyLoad', function ($delegate, $q) {\r\n            /**\r\n             * cssLoader function\r\n             * @type Function\r\n             * @param paths array list of css files to load\r\n             * @param callback to call when everything is loaded. We use a callback and not a promise\r\n             * @param params object config parameters\r\n             * because the user can overwrite cssLoader and it will probably not use promises :(\r\n             */\r\n            $delegate.cssLoader = function(paths, callback, params) {\r\n                var promises = [];\r\n                angular.forEach(paths, path => {\r\n                    promises.push($delegate.buildElement('css', path, params));\r\n                });\r\n                $q.all(promises).then(() => {\r\n                    callback();\r\n                }, err => {\r\n                    callback(err);\r\n                });\r\n            };\r\n            $delegate.cssLoader.ocLazyLoadLoader = true;\r\n\r\n            return $delegate;\r\n        })\r\n    });\r\n\r\n})(angular);\r\n", "(angular => {\r\n    'use strict';\r\n\r\n    angular.module('oc.lazyLoad').config(function($provide) {\r\n        $provide.decorator('$ocLazyLoad', function ($delegate, $q) {\r\n            /**\r\n             * jsLoader function\r\n             * @type Function\r\n             * @param paths array list of js files to load\r\n             * @param callback to call when everything is loaded. We use a callback and not a promise\r\n             * @param params object config parameters\r\n             * because the user can overwrite jsLoader and it will probably not use promises :(\r\n             */\r\n            $delegate.jsLoader = function(paths, callback, params) {\r\n                var promises = [];\r\n                angular.forEach(paths, path => {\r\n                    promises.push($delegate.buildElement('js', path, params));\r\n                });\r\n                $q.all(promises).then(() => {\r\n                    callback();\r\n                }, err => {\r\n                    callback(err);\r\n                });\r\n            };\r\n            $delegate.jsLoader.ocLazyLoadLoader = true;\r\n\r\n            return $delegate;\r\n        })\r\n    });\r\n\r\n})(angular);\r\n", "(angular => {\r\n    'use strict';\r\n\r\n    angular.module('oc.lazyLoad').config(function($provide) {\r\n        $provide.decorator('$ocLazyLoad', function ($delegate, $templateCache, $q, $http) {\r\n            /**\r\n             * templatesLoader function\r\n             * @type Function\r\n             * @param paths array list of css files to load\r\n             * @param callback to call when everything is loaded. We use a callback and not a promise\r\n             * @param params object config parameters for $http\r\n             * because the user can overwrite templatesLoader and it will probably not use promises :(\r\n             */\r\n            $delegate.templatesLoader = function(paths, callback, params) {\r\n                var promises = [],\r\n                    filesCache = $delegate._getFilesCache();\r\n\r\n                angular.forEach(paths, url => {\r\n                    var deferred = $q.defer();\r\n                    promises.push(deferred.promise);\r\n                    $http.get(url, params).success(data => {\r\n                        if(angular.isString(data) && data.length > 0) {\r\n                            angular.forEach(angular.element(data), node => {\r\n                                if(node.nodeName === 'SCRIPT' && node.type === 'text/ng-template') {\r\n                                    $templateCache.put(node.id, node.innerHTML);\r\n                                }\r\n                            });\r\n                        }\r\n                        if(angular.isUndefined(filesCache.get(url))) {\r\n                            filesCache.put(url, true);\r\n                        }\r\n                        deferred.resolve();\r\n                    }).error(function(err) {\r\n                        deferred.reject(new Error(`Unable to load template file \"${ url }\": ${ err }`));\r\n                    });\r\n                });\r\n                return $q.all(promises).then(() => {\r\n                    callback();\r\n                }, err => {\r\n                    callback(err);\r\n                });\r\n            };\r\n            $delegate.templatesLoader.ocLazyLoadLoader = true;\r\n\r\n            return $delegate;\r\n        })\r\n    });\r\n\r\n})(angular);\r\n", "// Array.indexOf polyfill for IE8\r\nif(!Array.prototype.indexOf) {\r\n    Array.prototype.indexOf = function(searchElement, fromIndex) {\r\n        var k;\r\n\r\n        // 1. Let O be the result of calling ToObject passing\r\n        //    the this value as the argument.\r\n        if(this == null) {\r\n            throw new TypeError('\"this\" is null or not defined');\r\n        }\r\n\r\n        var O = Object(this);\r\n\r\n        // 2. Let lenValue be the result of calling the Get\r\n        //    internal method of O with the argument \"length\".\r\n        // 3. Let len be ToUint32(lenValue).\r\n        var len = O.length >>> 0;\r\n\r\n        // 4. If len is 0, return -1.\r\n        if(len === 0) {\r\n            return -1;\r\n        }\r\n\r\n        // 5. If argument fromIndex was passed let n be\r\n        //    ToInteger(fromIndex); else let n be 0.\r\n        var n = +fromIndex || 0;\r\n\r\n        if(Math.abs(n) === Infinity) {\r\n            n = 0;\r\n        }\r\n\r\n        // 6. If n >= len, return -1.\r\n        if(n >= len) {\r\n            return -1;\r\n        }\r\n\r\n        // 7. If n >= 0, then Let k be n.\r\n        // 8. Else, n<0, Let k be len - abs(n).\r\n        //    If k is less than 0, then let k be 0.\r\n        k = Math.max(n >= 0 ? n : len - Math.abs(n), 0);\r\n\r\n        // 9. Repeat, while k < len\r\n        while(k < len) {\r\n            // a. Let Pk be ToString(k).\r\n            //   This is implicit for LHS operands of the in operator\r\n            // b. Let kPresent be the result of calling the\r\n            //    HasProperty internal method of O with argument Pk.\r\n            //   This step can be combined with c\r\n            // c. If kPresent is true, then\r\n            //    i.  Let elementK be the result of calling the Get\r\n            //        internal method of O with the argument ToString(k).\r\n            //   ii.  Let same be the result of applying the\r\n            //        Strict Equality Comparison Algorithm to\r\n            //        searchElement and elementK.\r\n            //  iii.  If same is true, return k.\r\n            if(k in O && O[k] === searchElement) {\r\n                return k;\r\n            }\r\n            k++;\r\n        }\r\n        return -1;\r\n    };\r\n}\r\n"], "sourceRoot": "/source/"}