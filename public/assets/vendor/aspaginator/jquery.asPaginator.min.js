/*! jQuery asPaginator - v0.2.1 - 2015-03-17
* https://github.com/amazingSurge/jquery-asPaginator
* Copyright (c) 2015 amazingSurge; Licensed GPL */
!function(a){"use strict";var b=a.asPaginator=function(c,d,e){this.element=c,this.$element=a(c).empty(),this.options=a.extend({},b.defaults,e),this.namespace=this.options.namespace,this.currentPage=this.options.currentPage||1,this.itemsPerPage=this.options.itemsPerPage,this.totalItems=d,this.totalPages=this.getTotalPages(),this.isOutOfBounds()&&(this.currentPage=this.totalPages),this.initialized=!1,this.components=a.extend(!0,{},this.components),this.$element.addClass(this.namespace),this.options.skin&&this.$element.addClass(this.options.skin),this.classes={disabled:this.options.disabledClass,active:this.options.activeClass},this.disabled=!1,this._trigger("init"),this.init()};b.prototype={constructor:b,components:{},init:function(){var b=this;b.visible=b.getVisible(),a.each(this.options.components,function(a,c){return null===c||c===!1?!1:void b.components[a].init.call(b.components[a],b)}),b.createHtml(),b.bindEvents(),b.goTo(b.currentPage),b.initialized=!0,"number"!=typeof this.options.visibleNum&&a(window).on("resize",this._throttle(function(){b.resize.call(b)},this.options.resizeTime)),this._trigger("ready")},createHtml:function(){var b,c=this;c.contents=c.options.tpl();for(var d,e=c.contents.match(/\{\{([^\}]+)\}\}/g).length,f=0;e>f;f++)d=c.contents.match(/\{\{([^\}]+)\}\}/),"namespace"!==d[1]?this.options.components[d[1]]&&(b=c.components[d[1]].opts.tpl.call(c),c.contents=c.contents.replace(d[0],b)):c.contents=c.contents.replace(d[0],c.namespace);c.$element.append(a(c.contents))},bindEvents:function(){var b=this;a.each(this.options.components,function(a,c){return null===c||c===!1?!1:void b.components[a].bindEvents.call(b.components[a],b)})},unbindEvents:function(){var b=this;a.each(this.options.components,function(a,c){return null===c||c===!1?!1:void b.components[a].unbindEvents.call(b.components[a],b)})},resize:function(){var b=this;b._trigger("resize"),b.goTo(b.currentPage),b.visible=b.getVisible(),a.each(this.options.components,function(a,c){return null===c||c===!1?!1:void("undefined"!=typeof b.components[a].resize&&b.components[a].resize.call(b.components[a],b))})},_throttle:function(a,b){var c,d,e,f=Date.now||function(){return(new Date).getTime()},g=null,h=0,i=function(){h=f(),g=null,e=a.apply(c,d),c=d=null};return function(){var j=f(),k=b-(j-h);return c=this,d=arguments,0>=k?(clearTimeout(g),g=null,h=j,e=a.apply(c,d),c=d=null):g||(g=setTimeout(i,k)),e}},getVisible:function(){var b=a("body, html").width(),c=0;return"number"!=typeof this.options.visibleNum?a.each(this.options.visibleNum,function(a,d){b>a&&(c=d)}):c=this.options.visibleNum,c},calculate:function(a,b,c){var d=1,e=1;return c+2>=a&&(d=0),a+c+1>=b&&(e=0),{left:d,right:e}},_trigger:function(a){var b=Array.prototype.slice.call(arguments,1),c=[this].concat(b);this.$element.trigger("asPaginator::"+a,c),a=a.replace(/\b\w+\b/g,function(a){return a.substring(0,1).toUpperCase()+a.substring(1)});var d="on"+a;"function"==typeof this.options[d]&&this.options[d].apply(this,b)},goTo:function(a){return a=Math.max(1,Math.min(a,this.totalPages)),a===this.currentPage&&this.initialized===!0?!1:(this.$element.find("."+this.classes.disabled).removeClass(this.classes.disabled),a===this.totalPages&&(this.$element.find("."+this.namespace+"-next").addClass(this.classes.disabled),this.$element.find("."+this.namespace+"-last").addClass(this.classes.disabled)),1===a&&(this.$element.find("."+this.namespace+"-prev").addClass(this.classes.disabled),this.$element.find("."+this.namespace+"-first").addClass(this.classes.disabled)),this.currentPage=a,void(this.initialized&&this._trigger("change",a)))},prev:function(){return this.hasPreviousPage()?(this.goTo(this.getPreviousPage()),!0):!1},next:function(){return this.hasNextPage()?(this.goTo(this.getNextPage()),!0):!1},goFirst:function(){return this.goTo(1)},goLast:function(){return this.goTo(this.totalPages)},update:function(a,b){var c={};"string"==typeof a?c[a]=b:c=a;for(var d in c)switch(d){case"totalItems":this.totalItems=c[d];break;case"itemsPerPage":this.itemsPerPage=c[d];break;case"currentPage":this.currentPage=c[d]}this.totalPages=this.totalPages()},isOutOfBounds:function(){return this.currentPage>this.totalPages},getItemsPerPage:function(){return this.itemsPerPage},getTotalItems:function(){return this.totalItems},getTotalPages:function(){return this.totalPages=Math.ceil(this.totalItems/this.itemsPerPage),this.lastPage=this.totalPages,this.totalPages},getCurrentPage:function(){return this.currentPage},hasPreviousPage:function(){return this.currentPage>1},getPreviousPage:function(){return this.hasPreviousPage()?this.currentPage-1:!1},hasNextPage:function(){return this.currentPage<this.totalPages},getNextPage:function(){return this.hasNextPage()?this.currentPage+1:!1},enable:function(){this.disabled&&(this.disabled=!1,this.$element.removeClass(this.classes.disabled),this.bindEvents())},disable:function(){this.disabled!==!0&&(this.disabled=!0,this.$element.addClass(this.classes.disabled),this.unbindEvents())},destory:function(){this.$element.removeClass(this.classes.disabled),this.unbindEvents(),this.$element.data("asPaginator",null),this._trigger("destory")}},b.defaults={namespace:"asPaginator",currentPage:1,itemsPerPage:10,visibleNum:5,resizeThrottle:250,disabledClass:"asPaginator_disable",activeClass:"asPaginator_active",tpl:function(){return"<ul>{{first}}{{prev}}{{lists}}{{next}}{{last}}</ul>"},skin:null,components:{first:!0,prev:!0,next:!0,last:!0,lists:!0},onInit:null,onReady:null,onChange:null},b.registerComponent=function(a,c){b.prototype.components[a]=c},b.registerComponent("prev",{defaults:{tpl:function(){return'<li class="'+this.namespace+'-prev"><a>Prev</a></li>'}},init:function(b){var c=a.extend({},this.defaults,b.options.components.prev);this.opts=c},bindEvents:function(b){this.$prev=b.$element.find("."+b.namespace+"-prev"),this.$prev.on("click.asPaginator",a.proxy(b.prev,b))},unbindEvents:function(){this.$prev.off("click.asPaginator")}}),b.registerComponent("next",{defaults:{tpl:function(){return'<li class="'+this.namespace+'-next"><a>Next</a></li>'}},init:function(b){var c=a.extend({},this.defaults,b.options.components.next);this.opts=c},bindEvents:function(b){this.$next=b.$element.find("."+b.namespace+"-next"),this.$next.on("click.asPaginator",a.proxy(b.next,b))},unbindEvents:function(){this.$next.off("click.asPaginator")}}),b.registerComponent("first",{defaults:{tpl:function(){return'<li class="'+this.namespace+'-first"><a>First</a></li>'}},init:function(b){var c=a.extend({},this.defaults,b.options.components.first);this.opts=c},bindEvents:function(b){this.$first=b.$element.find("."+b.namespace+"-first"),this.$first.on("click.asPaginator",a.proxy(b.goFirst,b))},unbindEvents:function(){this.$first.off("click.asPaginator")}}),b.registerComponent("last",{defaults:{tpl:function(){return'<li class="'+this.namespace+'-last"><a>Last</a></li>'}},init:function(b){var c=a.extend({},this.defaults,b.options.components.last);this.opts=c},bindEvents:function(b){this.$last=b.$element.find("."+b.namespace+"-last"),this.$last.on("click.asPaginator",a.proxy(b.goLast,b))},unbindEvents:function(){this.$last.off("click.asPaginator")}}),b.registerComponent("lists",{defaults:{tpl:function(){var a="",b=this.currentPage>=this.visible?this.currentPage%this.visible:this.currentPage;b=0===b?this.visible:b;for(var c=1;b>c;c++)a+='<li class="'+this.namespace+'-items" data-value="'+(this.currentPage-b+c)+'"><a href="#">'+(this.currentPage-b+c)+"</a></li>";a+='<li class="'+this.namespace+"-items "+this.classes.active+'" data-value="'+this.currentPage+'"><a href="#">'+this.currentPage+"</a></li>";for(var d=this.currentPage+1,e=d+this.visible-b-1>this.totalPages?this.totalPages:d+this.visible-b-1;e>=d;d++)a+='<li class="'+this.namespace+'-items" data-value="'+d+'"><a href="#">'+d+"</a></li>";return a}},init:function(b){var c=a.extend({},this.defaults,b.options.components.lists);this.opts=c,b.itemsTpl=this.opts.tpl.call(b)},bindEvents:function(b){var c=this;this.$items=b.$element.find("."+b.namespace+"-items"),b.$element.on("click",this.$items,function(c){var d=a(c.target).parent().data("value")||a(c.target).data("value");return void 0===d?!1:""===d?!1:void b.goTo(d)}),c.render(b),b.$element.on("asPaginator::change",function(){c.render(b)})},unbindEvents:function(a){a.$element.off("click",this.$items)},resize:function(a){this.render(a)},render:function(b){var c,d=b.currentPage,e=this,f=this.$items.removeClass(b.classes.active);a.each(f,function(e,f){return a(f).data("value")===d?(a(f).addClass(b.classes.active),c=!1,!1):void 0}),(c!==!1||this.visibleBefore!==b.visible)&&(this.visibleBefore=b.visible,a.each(f,function(c,d){0===c?a(d).replaceWith(e.opts.tpl.call(b)):a(d).remove()}),this.$items=b.$element.find("."+b.namespace+"-items"))}}),a.fn.asPaginator=function(c,d){if("string"==typeof d){var e=d,f=Array.prototype.slice.call(arguments,1);return this.each(function(){var b=a.data(this,"asPaginator");"function"==typeof b[e]&&b[e].apply(b,f)})}return"undefined"==typeof c?this:this.each(function(){a.data(this,"asPaginator")||a.data(this,"asPaginator",new b(this,c,d))})}}(jQuery);