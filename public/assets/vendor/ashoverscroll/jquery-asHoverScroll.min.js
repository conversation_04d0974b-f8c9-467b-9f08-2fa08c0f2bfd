/*! jQuery asHoverScroll - v0.2.0 - 2015-05-29
* https://github.com/amazingSurge/jquery-asHoverScroll
* Copyright (c) 2015 amazingSurge; Licensed GPL */
!function(a){"use strict";var b="asHoverScroll",c=0,d=a[b]=function(b,e){this.element=b,this.$element=a(b),this.options=a.extend({},d.defaults,e,this.$element.data()),this.$list=a(this.options.list,this.$element),this.classes={disabled:this.options.namespace+"-disabled"},"vertical"===this.options.direction?this.attributes={page:"pageY",axis:"Y",position:"top",length:"height",offset:"offsetTop",client:"clientY",clientLength:"clientHeight"}:"horizontal"===this.options.direction&&(this.attributes={page:"pageX",axis:"X",position:"left",length:"width",offset:"offsetLeft",client:"clientX",clientLength:"clientWidth"}),this._states={},this._scroll={time:null,pointer:null},this.instanceId=++c,this._trigger("init"),this.init()};d.defaults={namespace:b,list:"> ul",item:"> li",exception:null,direction:"vertical",fixed:!1,mouseMove:!0,touchScroll:!0,pointerScroll:!0,useCssTransforms:!0,useCssTransforms3d:!0,boundary:10,throttle:20,onEnter:function(){a(this).siblings().removeClass("is-active"),a(this).addClass("is-active")},onLeave:function(){a(this).removeClass("is-active")}};var e={};d.support=e,function(b){function c(b,c){var d=!1,g=b.charAt(0).toUpperCase()+b.slice(1);return a.each((b+" "+f.join(g+" ")+g).split(" "),function(a,b){return void 0!==e[b]?(d=c?b:!0,!1):void 0}),d}function d(a){return c(a,!0)}var e=a("<support>").get(0).style,f=["webkit","Moz","O","ms"],g={transition:{end:{WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd",transition:"transitionend"}},animation:{end:{WebkitAnimation:"webkitAnimationEnd",MozAnimation:"animationend",OAnimation:"oAnimationEnd",animation:"animationend"}}},h={csstransforms:function(){return!!c("transform")},csstransforms3d:function(){return!!c("perspective")},csstransitions:function(){return!!c("transition")},cssanimations:function(){return!!c("animation")}};h.csstransitions()&&(b.transition=new String(d("transition")),b.transition.end=g.transition.end[b.transition]),h.cssanimations()&&(b.animation=new String(d("animation")),b.animation.end=g.animation.end[b.animation]),h.csstransforms()&&(b.transform=new String(d("transform")),b.transform3d=h.csstransforms3d()),b.touch="ontouchstart"in window||window.DocumentTouch&&document instanceof window.DocumentTouch?!0:!1,b.pointer=window.PointerEvent||window.MSPointerEvent?!0:!1,b.convertMatrixToArray=function(a){return a&&"matrix"==a.substr(0,6)?a.replace(/^.*\((.*)\)$/g,"$1").replace(/px/g,"").split(/, +/):!1},b.prefixPointerEvent=function(a){return window.MSPointerEvent?"MSPointer"+a.charAt(9).toUpperCase()+a.substr(10):a}}(e),d.prototype={constructor:d,init:function(){this.initPosition(),this.updateLength(),this.bindEvents()},bindEvents:function(){var b=this,c=["enter"],d=[];this.options.mouseMove&&(this.$element.on(this.eventName("mousemove"),a.proxy(this.onMove,this)),c.push("mouseenter"),d.push("mouseleave")),this.options.touchScroll&&e.touch&&(this.$element.on(this.eventName("touchstart"),a.proxy(this.onScrollStart,this)),this.$element.on(this.eventName("touchcancel"),a.proxy(this.onScrollEnd,this))),this.options.pointerScroll&&e.pointer&&(this.$element.on(this.eventName(e.prefixPointerEvent("pointerdown")),a.proxy(this.onScrollStart,this)),this.$element.on(this.eventName(e.prefixPointerEvent("pointercancel")),a.proxy(this.onScrollEnd,this))),this.$list.on(this.eventName(c.join(" ")),this.options.item,function(){b.options.onEnter.call(this)}),this.$list.on(this.eventName(d.join(" ")),this.options.item,function(){b.options.onLeave.call(this)}),a(window).on(this.eventNameWithId("orientationchange"),function(){b.update.call(b)}),a(window).on(this.eventNameWithId("resize"),this.throttle(function(){b.update.call(b)},this.options.throttle))},unbindEvents:function(){this.$element.off(this.eventName()),this.$list.off(this.eventName()),a(window).off(this.eventNameWithId())},onScrollStart:function(b){var c=this;if(3!==b.which){this._scroll.time=(new Date).getTime(),this._scroll.pointer=this.pointer(b),this._scroll.start=this.getPosition();var d=function(){c.enter("scrolling"),c._trigger("scroll")};this.options.touchScroll&&e.touch&&(a(document).on(c.eventName("touchend"),a.proxy(this.onScrollEnd,this)),a(document).one(c.eventName("touchmove"),a.proxy(function(){a(document).on(c.eventName("touchmove"),a.proxy(this.onScrollMove,this)),d()},this))),this.options.pointerScroll&&e.pointer&&(a(document).on(c.eventName(e.prefixPointerEvent("pointerup")),a.proxy(this.onScrollEnd,this)),a(document).one(c.eventName(e.prefixPointerEvent("pointermove")),a.proxy(function(){a(document).on(c.eventName(e.prefixPointerEvent("pointermove")),a.proxy(this.onScrollMove,this)),d()},this))),a(document).on(c.eventName("blur"),a.proxy(this.onScrollEnd,this)),b.preventDefault()}},onScrollMove:function(a){this._scroll.updated=this.pointer(a);var b=this.distance(this._scroll.pointer,this._scroll.updated);if(this.is("scrolling")){a.preventDefault();var c=this._scroll.start+b;this.canScroll()&&(c>0?c=0:c<this.containerLength-this.listLength&&(c=this.containerLength-this.listLength),this.updatePosition(c))}},onScrollEnd:function(b){a(document).off(this.eventName("touchmove touchend blur")),this.is("scrolling")&&(this.leave("scrolling"),this._trigger("scrolled"),a(b.target).trigger("enter"))},pointer:function(a){var b={x:null,y:null};return a=this.getEvent(a),a.pageX&this.options.fixed?(b.x=a.pageX,b.y=a.pageY):(b.x=a.clientX,b.y=a.clientY),b},getEvent:function(a){return a=a.originalEvent||a||window.event,a=a.touches&&a.touches.length?a.touches[0]:a.changedTouches&&a.changedTouches.length?a.changedTouches[0]:a},distance:function(a,b){return"vertical"===this.options.direction?b.y-a.y:b.x-a.x},onMove:function(a){if(a=this.getEvent(a),this.isMatchScroll(a)){var b,c,d;b=a[this.attributes.page]&this.options.fixed?a[this.attributes.page]:a[this.attributes.client],d=b-this.element[this.attributes.offset],d<this.options.boundary?c=0:(c=(d-this.options.boundary)*this.multiplier,c>this.listLength-this.containerLength&&(c=this.listLength-this.containerLength)),this.updatePosition(-c)}},isMatchScroll:function(b){return!this.is("disabled")&&this.canScroll()?this.options.exception?0===a(b.target).closest(this.options.exception).length?!0:!1:!0:!1},canScroll:function(){return this.listLength>this.containerLength},getContainerLength:function(){return this.element[this.attributes.clientLength]},getListhLength:function(){return this.$list[0][this.attributes.clientLength]},updateLength:function(){this.containerLength=this.getContainerLength(),this.listLength=this.getListhLength(),this.multiplier=(this.listLength-this.containerLength)/(this.containerLength-2*this.options.boundary)},initPosition:function(){var a=this.makePositionStyle(0);this.$list.css(a)},getPosition:function(){var a;if(this.options.useCssTransforms&&e.transform){if(a=e.convertMatrixToArray(this.options.useCssTransforms3d&&e.transform3d?this.$list.css(e.transform):this.$list.css(e.transform)),!a)return 0;a="X"===this.attributes.axis?a[12]||a[4]:a[13]||a[5]}else a=this.$list.css(this.attributes.position);return parseFloat(a.replace("px",""))},makePositionStyle:function(a){var b,c="0px",d="0px";this.options.useCssTransforms&&e.transform?("X"===this.attributes.axis?c=a+"px":d=a+"px",b=e.transform.toString(),a=this.options.useCssTransforms3d&&e.transform3d?"translate3d("+c+","+d+",0px)":"translate("+c+","+d+")"):b=this.attributes.position;var f={};return f[b]=a,f},updatePosition:function(a){var b=this.makePositionStyle(a);this.$list.css(b)},update:function(){this.is("disabled")||(this.updateLength(),this.canScroll()||this.initPosition())},eventName:function(a){if("string"!=typeof a||""===a)return"."+b;a=a.split(" ");for(var c=a.length,d=0;c>d;d++)a[d]=a[d]+"."+b;return a.join(" ")},eventNameWithId:function(a){if("string"!=typeof a||""===a)return this.options.namespace+"-"+this.instanceId;a=a.split(" ");for(var b=a.length,c=0;b>c;c++)a[c]=a[c]+"."+this.options.namespace+"-"+this.instanceId;return a.join(" ")},_trigger:function(a){var c=Array.prototype.slice.call(arguments,1),d=[this].concat(c);this.$element.trigger(b+"::"+a,d),a=a.replace(/\b\w+\b/g,function(a){return a.substring(0,1).toUpperCase()+a.substring(1)});var e="on"+a;"function"==typeof this.options[e]&&this.options[e].apply(this,c)},is:function(a){return this._states[a]&&this._states[a]>0},enter:function(a){void 0===this._states[a]&&(this._states[a]=0),this._states[a]++},leave:function(a){this._states[a]--},throttle:function(a,b){var c,d,e,f=Date.now||function(){return(new Date).getTime()},g=null,h=0,i=function(){h=f(),g=null,e=a.apply(c,d),c=d=null};return function(){var j=f(),k=b-(j-h);return c=this,d=arguments,0>=k?(clearTimeout(g),g=null,h=j,e=a.apply(c,d),c=d=null):g||(g=setTimeout(i,k)),e}},enable:function(){this.is("disabled")&&(this.leave("disabled"),this.$element.removeClass(this.classes.disabled),this.bindEvents())},disable:function(){this.is("disabled")||(this.enter("disabled"),this.initPosition(),this.$element.addClass(this.classes.disabled),this.unbindEvents())},destory:function(){this.$element.removeClass(this.classes.disabled),this.unbindEvents(),this.$element.data(b,null),this._trigger("destory")}},a.fn[b]=function(c){if("string"!=typeof c)return this.each(function(){a.data(this,b)||a.data(this,b,new d(this,c))});var e=c,f=Array.prototype.slice.call(arguments,1);if(/^\_/.test(e))return!1;if(!/^(get)/.test(e))return this.each(function(){var c=a.data(this,b);c&&"function"==typeof c[e]&&c[e].apply(c,f)});var g=this.first().data(b);return g&&"function"==typeof g[e]?g[e].apply(g,f):void 0}}(jQuery);