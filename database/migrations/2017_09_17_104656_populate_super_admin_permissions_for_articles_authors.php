<?php

use App\Models\AccessLevel;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class PopulateSuperAdminPermissionsForArticlesAuthors extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $superAdmin = AccessLevel::where('name', 'superAdmin')->first();
        $admin = AccessLevel::where('name', 'admin')->first();

        $superAdminPermissions = \App\Models\Permission::whereIn('name', ['see_articles', 'manage_articles', 'delete_articles', 'see_authors', 'manage_authors', 'delete_authors'])
            ->pluck('id')
            ->toArray();
        $adminPermissions = \App\Models\Permission::whereIn('name', ['see_articles', 'manage_articles', 'delete_articles', 'see_authors', 'manage_authors', 'delete_authors'])
            ->pluck('id')
            ->toArray();

        $superAdmin->permissions()->syncWithoutDetaching($superAdminPermissions);
        $admin->permissions()->syncWithoutDetaching($adminPermissions);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
