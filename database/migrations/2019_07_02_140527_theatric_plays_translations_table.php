<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class TheatricPlaysTranslationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('theatric_plays_translations', function(Blueprint $table) {
            $table->increments('id');
            $table->integer('theatric_play_id')->unsigned();
            $table->string('locale')->index();
            $table->string('title', 255);
            $table->text('synopsis');
            $table->text('storyline');
            $table->text('extra_info');
            $table->text('extra_ongoing_info');

            $table->unique(['theatric_play_id', 'locale']);
            $table->foreign('theatric_play_id')->references('id')->on('theatric_plays')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('theatric_plays_translations');
    }
}
