<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreatePersonEventsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('person_events',
            function (Blueprint $table) {
                $table->increments('id');
                $table->integer('user_id')->index();
                $table->integer('person_id')->index();
                $table->string('category');
                $table->string('action');
                $table->integer('value');
                $table->timestamps();
            });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('person_events');
    }
}
