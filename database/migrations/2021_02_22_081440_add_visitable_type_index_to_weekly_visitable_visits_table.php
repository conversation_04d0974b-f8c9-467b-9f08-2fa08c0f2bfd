<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddVisitableTypeIndexToWeeklyVisitableVisitsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('weekly_visitable_visits', function (Blueprint $table) {
            $table->index('visitable_type', 'weekly_visitable_visits_visitable_type_index');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('weekly_visitable_visits', function (Blueprint $table) {
            $table->dropIndex('weekly_visitable_visits_visitable_type_index');
        });
    }
}
