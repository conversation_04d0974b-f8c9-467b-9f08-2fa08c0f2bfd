<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddProcessedFieldInDailyPersonVisitsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('daily_person_visits', function (Blueprint $table) {
            $table->boolean('processed')
                ->after('total')
                ->default(false)
                ->index();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('daily_person_visits', function (Blueprint $table) {
            $table->dropColumn('processed');
        });
    }
}
