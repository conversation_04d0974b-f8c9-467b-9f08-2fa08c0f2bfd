<?php

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AlterAllTableToHaveCaseSensitiveSlug extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE people CHANGE slug slug VARCHAR(255) BINARY NOT NULL;');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE genres CHANGE slug slug VARCHAR(255) BINARY NOT NULL;');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE theatric_plays CHANGE slug slug VARCHAR(255) BINARY NOT NULL;');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE theatres CHANGE slug slug VARCHAR(255) BINARY NOT NULL;');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
