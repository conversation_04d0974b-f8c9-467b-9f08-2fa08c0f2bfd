<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateEmailMessageActionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('email_message_actions', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('email_message_id')->nullable();
            $table->foreign('email_message_id')
                ->references('id')
                ->on('email_messages')
                ->onDelete('set null');

            $table->enum('action', ['unknown', 'send', 'delivery', 'open', 'click'])->default('unknown');
            $table->string('ip')->nullable();
            $table->string('user_agent')->nullable();
            $table->string('link')->nullable();
            $table->json('details')->nullable();
            $table->timestamp('date')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('email_message_actions');
    }
}
