<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddIndicesToRankingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('rankings', function (Blueprint $table) {
            $table->index('rankable_id', 'index_rankable_id');
            $table->index('rankable_type', 'index_rankable_type');
            $table->index('date', 'index_date');
            $table->index('rank', 'index_rank');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('rankings', function (Blueprint $table) {
            $table->dropIndex('index_rankable_id');
            $table->dropIndex('index_rankable_type');
            $table->dropIndex('index_date');
            $table->dropIndex('index_rank');
        });
    }
}
