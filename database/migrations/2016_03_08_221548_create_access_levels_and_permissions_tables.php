<?php

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateAccessLevelsAndPermissionsTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('access_levels', function (Blueprint $table)
        {
            $table->increments('id');
            $table->string('name');
            $table->string('label')->nullable();
            $table->timestamps();
        });
        Schema::create('permissions', function (Blueprint $table)
        {
            $table->increments('id');
            $table->string('name');
            $table->string('label')->nullable();
            $table->timestamps();
        });
        Schema::create('access_level_permission', function (Blueprint $table)
        {
            $table->integer('permission_id')->unsigned();
            $table->integer('access_level_id')->unsigned();
            $table->foreign('permission_id')
                  ->references('id')
                  ->on('permissions')
                  ->onDelete('cascade');
            $table->foreign('access_level_id')
                  ->references('id')
                  ->on('access_levels')
                  ->onDelete('cascade');
            $table->primary(['permission_Id', 'access_level_id']);
        });
        Schema::create('access_level_user', function (Blueprint $table)
        {
            $table->integer('access_level_id')->unsigned();
            $table->integer('user_id')->unsigned();
            $table->foreign('access_level_id')
                  ->references('id')
                  ->on('access_levels')
                  ->onDelete('cascade');
            $table->foreign('user_id')
                  ->references('id')
                  ->on('users')
                  ->onDelete('cascade');
            $table->primary(['access_level_id', 'user_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
