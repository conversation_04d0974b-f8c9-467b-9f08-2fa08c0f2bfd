<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('endeavours', function (Blueprint $table) {
            $table->unsignedBigInteger('tv_channel_id')->after('old_title')->nullable();
            $table->foreign('tv_channel_id')
                ->references('id')
                ->on('tv_channels')
                ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('endeavours', function (Blueprint $table) {
            $table->dropForeign(['tv_channel_id']);
            $table->dropColumn('tv_channel_id');
        });
    }
};
