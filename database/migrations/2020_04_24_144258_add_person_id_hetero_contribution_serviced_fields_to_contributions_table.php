<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddPersonIdHeteroContributionServicedFieldsToContributionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('contributions', function (Blueprint $table) {
            $table->integer('person_id')->unsigned()->nullable()->after('contributor_phone');
            $table->boolean('hetero_contribution')->default(false)->after('contributor_phone');
            $table->boolean('serviced')->default(false)->after('contributor_phone');

            $table->foreign('person_id')
                ->references('id')
                ->on('people')
                ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     *
     *
     * @return void
     */
    public function down()
    {
        Schema::table('contributions', function (Blueprint $table) {
            $table->dropForeign(['person_id']);
            $table->dropColumn('person_id');
            $table->dropColumn('hetero_contribution');
            $table->dropColumn('serviced');
        });
    }
}
