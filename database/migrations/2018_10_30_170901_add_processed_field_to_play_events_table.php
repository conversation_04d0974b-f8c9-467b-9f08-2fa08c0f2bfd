<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddProcessedFieldToPlayEventsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('play_events', function (Blueprint $table)
        {
            $table->boolean('processed')
                ->after('value')
                ->default(false)
                ->index();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('play_events', function (Blueprint $table)
        {
            $table->dropColumn('processed');
        });
    }
}
