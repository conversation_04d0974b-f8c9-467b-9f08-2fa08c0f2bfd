<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class RemoveReviewRelatedFieldsFromRatingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('ratings', function (Blueprint $table) {
            $table->dropColumn('review');
            $table->dropColumn('spoiler');
            $table->dropColumn('banned');
            $table->dropColumn('moderated');
            $table->dropColumn('review_created_at');
            $table->dropColumn('review_updated_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('ratings', function (Blueprint $table) {
            $table->text('review');
            $table->boolean('spoiler')->default(false);
            $table->boolean('banned')->default(false);
            $table->boolean('moderated')->default(false);
            $table->timestamp('review_created_at')->nullable();
            $table->timestamp('review_updated_at')->nullable();
        });
    }
}
