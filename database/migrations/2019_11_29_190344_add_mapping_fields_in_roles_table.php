<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddMappingFieldsInRolesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('roles', function (Blueprint $table) {
            $table->boolean('for_plays')->default(true)->after('description');
            $table->boolean('for_movies')->default(false)->after('for_plays');
            $table->boolean('for_tv')->default(false)->after('for_movies');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('roles', function (Blueprint $table) {
            $table->dropColumn('for_plays');
            $table->dropColumn('for_movies');
            $table->dropColumn('for_tv');
        });
    }
}
