<?php

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class MigrateRoleMusicComposerToMusic extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $musicComposer = \App\Models\Role::find(25);
        $music = \App\Models\Role::find(20);

        foreach (\App\Models\PersonPlayRole::where('role_id', $musicComposer->id)->get() as $ppl)
        {
            $personAlsoExistsWithMusicRole = \App\Models\PersonPlayRole::where('role_id', $music->id)
                                                                       ->where('play_id', $ppl->play_id)
                                                                       ->where('person_id', $ppl->person_id)
                                                                       ->first();

            if ( ! $personAlsoExistsWithMusicRole)
            {
                $ppl->role_id = $music->id;

            } else
            {
                $ppl->delete();
            }

            $ppl->save();
        }

        DB::table('roles')->delete($musicComposer->id);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
