<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddOnlineTicketsFieldsToTheatricPlaysTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('theatric_plays', function (Blueprint $table) {
            $table->string('online_tickets_1');
            $table->string('online_tickets_2');
            $table->string('online_tickets_3');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('theatric_plays', function (Blueprint $table) {
            $table->dropColumn('online_tickets_1');
            $table->dropColumn('online_tickets_2');
            $table->dropColumn('online_tickets_3');
        });
    }
}
