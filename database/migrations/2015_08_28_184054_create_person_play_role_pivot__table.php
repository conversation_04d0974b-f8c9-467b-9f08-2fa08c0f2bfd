<?php

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class CreatePersonPlayRolePivotTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement('SET foreign_key_checks = 0');
        Schema::dropIfExists('person_play_role');

        Schema::create('person_play_role', function (Blueprint $table)
        {
            $table->integer('play_id')->unsigned();
            $table->foreign('play_id')
                  ->references('id')
                  ->on('theatric_plays')
                  ->onDelete('cascade');
            $table->integer('person_id')->unsigned();
            $table->foreign('person_id')
                  ->references('id')
                  ->on('people')
                  ->onDelete('cascade');
            $table->integer('role_id')->unsigned()->nullable();
            $table->foreign('role_id')
                  ->references('id')
                  ->on('roles')
                  ->onDelete('set null');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::statement('SET foreign_key_checks = 0');
        Schema::drop('person_play_role');
    }
}
