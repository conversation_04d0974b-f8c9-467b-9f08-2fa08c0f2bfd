<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('endeavour_person_role', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('endeavour_id');
            $table->unsignedInteger('person_id');
            $table->unsignedInteger('role_id')->nullable();
            $table->string('character')->nullable();
            $table->timestamps();

            $table->unique(['endeavour_id', 'person_id', 'role_id']);
            $table->foreign('endeavour_id')
                ->references('id')
                ->on('endeavours')
                ->onDelete('cascade');
            $table->foreign('person_id')
                ->references('id')
                ->on('people')
                ->onDelete('cascade');
            $table->foreign('role_id')
                ->references('id')
                ->on('roles')
                ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('endeavour_person_role');
    }
};
