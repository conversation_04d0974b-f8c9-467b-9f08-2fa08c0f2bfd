<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddInternalRatingFieldsToTheatricPlaysTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('theatric_plays', function (Blueprint $table) {
            $table->float('internal_rating', 10, 8)
                ->default(0.0);
            $table->dateTime('internally_rated_at')->default('1970-01-01 00:00:00');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('theatric_plays', function (Blueprint $table) {
            $table->dropColumn('internal_rating');
            $table->dropColumn('internally_rated_at');
        });
    }
}
