<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateSlugRedirectsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('slug_redirects', function (Blueprint $table) {
            $table->bigIncrements('id');

            $table->string('old_slug')->index();
            $table->string('new_slug');
            $table->enum('model_type', ['person', 'play', 'movie', 'tv_show', 'article', 'streaming', 'theatre', 'festival'])
                ->default('person');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('slug_redirects');
    }
}
