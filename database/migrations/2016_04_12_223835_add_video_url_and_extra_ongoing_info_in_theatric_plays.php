<?php

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

class AddVideoUrlAndExtraOngoingInfoInTheatricPlays extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('theatric_plays', function (Blueprint $table)
        {
            $table->text('extra_ongoing_info');
            $table->text('video_url');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('theatric_plays', function (Blueprint $table)
        {
            $table->dropColumn(['extra_ongoing_info', 'video_url']);
        });
    }
}
