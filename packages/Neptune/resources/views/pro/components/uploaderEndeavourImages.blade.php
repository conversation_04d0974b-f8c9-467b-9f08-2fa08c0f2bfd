{{--@php $dropzoneId = isset($dz_id) ? $dz_id : str_random(8); @endphp--}}
@php $dropzoneId = isset($dz_id) ? $dz_id :  md5(rand()); @endphp

<div id="{{$dropzoneId}}" class="dropzone">

    <div class="dz-default dz-message">
        <div class="dropify-message">
            <br/><i data-feather="upload-cloud"></i><br/>
            <span>Κάνε click εδώ για ανέβασμα φωτογραφιών</span>
            <br>
            <small>Μέγιστο μέγεθος {{ config('neptune.dropzone.max_size', 0) / 1000 }} MB</small>
            <div><small>Επιτρεπόμενα αρχεία: {{ config('neptune.dropzone.allowed')}}</small></div>
            <br>
        </div>
    </div>
</div>
<div id="tpl" style="display: none;">

    <div class="dz-preview dz-file-preview">
        <div class="dz-error-message"><span data-dz-errormessage=""></span></div>
        <div class="dz-container">
            <div class="dz-preview-main">
                <div class="dz-image"><img data-dz-thumbnail=""></div>
                {{--                <div class="dz-filename"><span data-dz-name></span></div>--}}
                <div class="dz-progress"><span class="dz-upload" data-dz-uploadprogress=""></span></div>

                <div class="dz-success-mark">
                    <svg width="54px" height="54px" viewBox="0 0 54 54" version="1.1" xmlns="http://www.w3.org/2000/svg"
                         xmlns:xlink="http://www.w3.org/1999/xlink"
                         xmlns:sketch="http://www.bohemiancoding.com/sketch/ns">
                        <!-- Generator: Sketch 3.2.1 (9971) - http://www.bohemiancoding.com/sketch -->
                        <title>Check</title>
                        <desc>Created with Sketch.</desc>
                        <defs></defs>
                        <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"
                           sketch:type="MSPage">
                            <path
                                d="M23.5,31.8431458 L17.5852419,25.9283877 C16.0248253,24.3679711 13.4910294,24.366835 11.9289322,25.9289322 C10.3700136,27.4878508 10.3665912,30.0234455 11.9283877,31.5852419 L20.4147581,40.0716123 C20.5133999,40.1702541 20.6159315,40.2626649 20.7218615,40.3488435 C22.2835669,41.8725651 24.794234,41.8626202 26.3461564,40.3106978 L43.3106978,23.3461564 C44.8771021,21.7797521 44.8758057,19.2483887 43.3137085,17.6862915 C41.7547899,16.1273729 39.2176035,16.1255422 37.6538436,17.6893022 L23.5,31.8431458 Z M27,53 C41.3594035,53 53,41.3594035 53,27 C53,12.6405965 41.3594035,1 27,1 C12.6405965,1 1,12.6405965 1,27 C1,41.3594035 12.6405965,53 27,53 Z"
                                id="Oval-2" stroke-opacity="0.198794158" stroke="#747474" fill-opacity="0.816519475"
                                fill="#2cc56f" sketch:type="MSShapeGroup"></path>
                        </g>
                    </svg>
                </div>

                <div class="dz-error-mark">
                    <svg width="54px" height="54px" viewBox="0 0 54 54" version="1.1" xmlns="http://www.w3.org/2000/svg"
                         xmlns:xlink="http://www.w3.org/1999/xlink"
                         xmlns:sketch="http://www.bohemiancoding.com/sketch/ns">
                        <!-- Generator: Sketch 3.2.1 (9971) - http://www.bohemiancoding.com/sketch -->
                        <title>error</title>
                        <desc>Created with Sketch.</desc>
                        <defs></defs>
                        <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"
                           sketch:type="MSPage">
                            <g id="Check-+-Oval-2" sketch:type="MSLayerGroup" stroke="#747474"
                               stroke-opacity="0.198794158" fill="#F00F00" fill-opacity="0.816519475">
                                <path
                                    d="M32.6568542,29 L38.3106978,23.3461564 C39.8771021,21.7797521 39.8758057,19.2483887 38.3137085,17.6862915 C36.7547899,16.1273729 34.2176035,16.1255422 32.6538436,17.6893022 L27,23.3431458 L21.3461564,17.6893022 C19.7823965,16.1255422 17.2452101,16.1273729 15.6862915,17.6862915 C14.1241943,19.2483887 14.1228979,21.7797521 15.6893022,23.3461564 L21.3431458,29 L15.6893022,34.6538436 C14.1228979,36.2202479 14.1241943,38.7516113 15.6862915,40.3137085 C17.2452101,41.8726271 19.7823965,41.8744578 21.3461564,40.3106978 L27,34.6568542 L32.6538436,40.3106978 C34.2176035,41.8744578 36.7547899,41.8726271 38.3137085,40.3137085 C39.8758057,38.7516113 39.8771021,36.2202479 38.3106978,34.6538436 L32.6568542,29 Z M27,53 C41.3594035,53 53,41.3594035 53,27 C53,12.6405965 41.3594035,1 27,1 C12.6405965,1 1,12.6405965 1,27 C1,41.3594035 12.6405965,53 27,53 Z"
                                    id="Oval-2" sketch:type="MSShapeGroup"></path>
                            </g>
                        </g>
                    </svg>
                </div>
            </div>
        </div>
    </div>

</div>
<!-- Dropzone {{ $dropzoneId }} -->
@push('scripts')
    <script>
        var encparams = {!! isset($params) ? json_encode($params) : '{}'  !!};
        var slug = encparams.person_slug;
        var id = encparams.endeavour_id;
        // Turn off auto discovery
        Dropzone.autoDiscover = false;
        var file_up_names = [];
        var uploadedFiles{{$dropzoneId}}= [];
        $(function () {
            // Attach dropzone on element
            $("#{{ $dropzoneId }}").dropzone({
                url: "{{ route('neptune.pro.endeavour.images.store') }}",
                maxFilesize: {{ isset($maxFileSize) ? $maxFileSize : config('neptune.dropzone.max_size', 1000) / 1000 }},
                acceptedFiles: "{!! isset($acceptedFiles) ? $acceptedFiles : config('neptune.dropzone.allowed') !!}",
                headers: {'X-CSRF-TOKEN': "{{ csrf_token() }}"},
                addRemoveLinks: true,
                resizeWidth: 1600,
                resizeHeight: 1800,
                resizeQuality: 0.9,
                dictRemoveFileConfirmation: 'Διαγραφή φωτογραφίας;',
                dictRemoveFile: 'Αφαίρεση',
                dictCancelUpload: 'Ακύρωση',
                previewTemplate: document.querySelector('#tpl').innerHTML,
                params: {!! isset($params) ? json_encode($params) : '{}'  !!},
                init: function () {
                    this.on("complete", function (file) {
                        if (this.getUploadingFiles().length === 0 && this.getQueuedFiles().length === 0 && this.getErroredFiles().length === 0) {
                            $('.dropzone').removeClass('dz-started');
                        }
                    });
                    this.on("success", function (file, responseText) {
                        // console.log(responseText);
                        $('.table').prepend('<div id="image_' + responseText.id + '" class="table-row">\n' +
                            '<div class="table-cell">\n' +
                            '<img class="galleryImage"\n' +
                            'src="' + APP_URL + '/' + responseText.filename + '">\n' +
                            '</div>\n' +
                            '<div class="table-cell table-cell--full">\n' +
                            '<textarea  data-id="' + responseText.id + '" type="text" placeholder="Πληκτρολόγησε λεζάντα"  class="form-control caption"></textarea>' +
                            '<div class="captionCont"></div>' +
                            '</div>\n' +
                            '<div class="table-cell">'+
                                '<input type="radio" class="mainImage" name="mainImage" value="' + responseText.id + '"    data-toggle="tooltip" data-placement="top"  title="Ορισμός κύριας φωτογραφίας">'+
                            '</div>'+
                            '<div class="table-cell">\n' +
                            '<a href="#" data-url="' + APP_URL + '/proapp/endeavour/images/'+responseText.id+'/destroy"'+
                            'data-person_slug="'+slug+'"'+
                               'class="deleteResource">'+
                               ' <i data-feather="trash" class="actionIcon"></i>'+
                            '</a>'+
                            '</div>\n' +
                            '</div>')
                        feather.replace();
                        $('[data-toggle="tooltip"]').tooltip();
                        file_up_names.push(responseText);
                        file.previewElement.innerHTML = "";
                        file.previewElement.parentNode.removeChild(file.previewElement);
                        // $('.dz-preview').remove();
                        // if (file.previewElement) {
                        //     for (node of file.previewElement.querySelectorAll("[data-dz-caption]")) {
                        //         node.id = responseText.slug;
                        //     }
                        // }
                    });

                    // Handle added file
                    // this.on('addedfile', function (file) {
                    //     var thumb = getIconFromFilename(file);
                    //     $(file.previewElement).find(".dz-image img").attr("src", thumb);
                    // })
                    // handle remove file to delete on server
                    this.on("removedfile", function (file) {
                        if (this.getUploadingFiles().length === 0 && this.getQueuedFiles().length === 0 && this.getErroredFiles().length === 0){
                            $('.dropzone').removeClass('dz-started');
                        }
                        var obj = JSON.parse(file.xhr.response);
                        for (var i = 0; i < file_up_names.length; ++i) {
                            if (file_up_names[i].id == obj.id) {
                                var found = file_up_names[i];
                            }
                        }
                        // If got the file lets make a delete request by id
                        if (found) {
                            $.ajax({
                                url: APP_URL + "/proapp/endeavour/images/" + found.id + '/destroy',
                                type: 'DELETE',
                                headers: {
                                    'X-CSRF-TOKEN': "{{ csrf_token() }}"
                                },
                                data: {
                                    id: found.id,person_slug:slug
                                },
                                success: function (response) {
                                    $('#image_' + found.id).hide();

                                }
                            });
                        }
                    });
                    // Handle errors
                    this.on('error', function (file, response) {
                        var errMsg = response;
                        if (response.message) errMsg = response.message;
                        if (response.file) errMsg = response.file[0];
                        $(file.previewElement).find('.dz-error-message').text(errMsg);
                    });
                }
            });
        })
        Dropzone.prototype.getErroredFiles = function () {
            var file, _i, _len, _ref, _results;
            _ref = this.files;
            _results = [];
            for (_i = 0, _len = _ref.length; _i < _len; _i++) {
                file = _ref[_i];
                if (file.status === Dropzone.ERROR) {
                    _results.push(file);
                }
            }
            return _results;
        };

    </script>
@endpush
