<div class="  " id="sidebar-wrapper">
    <div class="sidebar-heading">
        <a href="{{ route('neptune.pro.home') }}"><img src="{{asset('proAssets/img/unstagepro_white.svg')}}"/></a>
    </div>
    @if(isset($logged_user))
        <div class="sidebar-user">
            @if($logged_user->proPeople()->count() == 1)
                <div class="personSelected">
                    @if (isset($selected_person->mainImage))
                        <img src="{!! asset(Croppa::url($selected_person->mainImage->filename,50,null)) !!}">
                    @else
                        <img src="{{asset('proAssets/img/profile_grey.png')}}">
                    @endif
                    {{ $selected_person->fullName }}
                </div>
            @else
                <div class="ui floating labeled icon dropdown button uiselect">
                    <div class="personSelected">
                        @if (isset($selected_person->mainImage))
                            <img src="{!! asset(Croppa::url($selected_person->mainImage->filename,50,null)) !!}">
                        @else
                            <img src="{{asset('proAssets/img/profile_grey.png')}}">
                        @endif
                        @if(isset($selected_person->fullName)){{ $selected_person->fullName }}@endif
                        <i class="dropdownChevron" data-feather="chevron-down"></i>
                    </div>

                    <div class="menu">
                        @foreach($logged_user->proPeople()->with('mainImage')->get() as $person)
                            @if($person->isPro())
                                <a class="item" href="{{ route('neptune.pro.dashboard.show', $person->slug) }}">
                                    @if (isset($person->mainImage))
                                        <img src="{!! asset(Croppa::url($person->mainImage->filename,50,null)) !!}"/>
                                    @else
                                        <img src="{{asset('proAssets/img/profile_grey.png')}}">
                                    @endif
                                    {{ $person->fullName }}
                                </a>
                            @endif
                        @endforeach
                    </div>
                </div>
            @endif
        </div>
    @endif
    <div class="list-group list-group-flush">
        @if(isset($selected_person))
            <a href="{{route('neptune.pro.dashboard.show', $selected_person->slug)}}"
               class="menu-item {{ ((Request::is('*dashboard*')  ) ? 'active' : '') }}">
                <i class="menuIcon" data-feather="activity"></i>
                {{ trans('neptune::pro/common.navigation.dashboard') }}
            </a>
            <a href="{{ route('neptune.pro.bio.edit', $selected_person->slug) }}"
               class="  menu-item {{ ((Request::is('*bio*')  ) ? 'active' : '') }}">
                <i class="menuIcon" data-feather="edit-2"></i>
                {{ trans('neptune::pro/common.navigation.bio') }}
            </a>
            <a href="{{route('neptune.pro.info.edit', $selected_person->slug)}}"
               class="menu-item @if(\Route::current()->getName() == 'neptune.pro.info.edit') active @endif">
                <i class="menuIcon" data-feather="info"></i>
                {{ trans('neptune::pro/common.navigation.info') }}
            </a>
            <a href="{{ route('neptune.pro.plays.index', $selected_person->slug) }}"
               class="  menu-item {{ ((Request::is('*plays*')  ) ? 'active' : '') }}">
                <i class="menuIcon" data-feather="star"></i>
                {{ trans('neptune::pro/common.navigation.plays') }}
            </a>
            <a href="{{ route('neptune.pro.movies.index', $selected_person->slug) }}"
               class="  menu-item {{ ((Request::is('*movies*')  ) ? 'active' : '') }}">
                <i class="menuIcon" data-feather="film"></i>
                {{ trans('neptune::pro/common.navigation.movies') }}
            </a>
            <a href="{{ route('neptune.pro.tvShows.index', $selected_person->slug) }}"
               class="  menu-item {{ ((Request::is('*tvShows*')  ) ? 'active' : '') }}">
                <i class="menuIcon" data-feather="tv"></i>
                {{ trans('neptune::pro/common.navigation.tvShows') }}
            </a>
            <a href="{{ route('neptune.pro.endeavours.index', $selected_person->slug) }}"
               class="  menu-item {{ ((Request::is('*simmetoxes*')  ) ? 'active' : '') }}">
                <i class="menuIcon" data-feather="loader"></i>
                {{ trans('neptune::pro/common.navigation.endeavours') }}
            </a>
            <a href="{{ route('neptune.pro.images.index', $selected_person->slug) }}"
               class="menu-item @if(\Route::current()->getName() == 'neptune.pro.images.index') active @endif">
                <i class="menuIcon" data-feather="image"></i>
                {{ trans('neptune::pro/common.navigation.images') }}
            </a>
            <a href="{{ route('neptune.pro.videos.index', $selected_person->slug) }}"
               class="  menu-item {{ ((Request::is('*videos*')  ) ? 'active' : '') }}">
                <i class="menuIcon" data-feather="youtube"></i>
                {{ trans('neptune::pro/common.navigation.videos') }}
            </a>
            <a href="{{ route('neptune.pro.seminars.index', $selected_person->slug) }}"
               class="  menu-item {{ ((Request::is('*seminars*')  ) ? 'active' : '') }}">
                <i class="menuIcon" data-feather="book-open"></i>
                {{ trans('neptune::pro/common.navigation.seminars') }}
            </a>
            <a href="{{ route('neptune.pro.publications.index', $selected_person->slug) }}"
               class="  menu-item {{ ((Request::is('*publications*')  ) ? 'active' : '') }}">
                <i class="menuIcon" data-feather="flag"></i>
                {{ trans('neptune::pro/common.navigation.references') }}
            </a>
            <a href="{{ route('people.show', $selected_person->slug) }}" target="_blank"
               class="  menu-item">
                <i class="menuIcon" data-feather="eye"></i>
                {{ trans('neptune::pro/common.navigation.preview') }}
            </a>
        @endif
        @if($logged_user->adminised)
            <a href="{{ route('neptune.pro.adminised.plays.index') }}"
               class="  menu-item {{ ((Request::is('*plays*')  ) ? 'active' : '') }}">
                <i class="menuIcon" data-feather="star"></i>
                {{ trans('neptune::pro/common.navigation.plays') }}
            </a>
        @endif
        <a class="  menu-item" href="{{route('logout')}}">
            <i class="menuIcon" data-feather="log-out"></i>
            Έξοδος
        </a>
    </div>
</div>
