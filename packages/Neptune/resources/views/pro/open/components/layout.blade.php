<!DOCTYPE html>
<html lang="el">
<head prefix="og: http://ogp.me/ns# fb: http://ogp.me/ns/fb#">
    @include('scripts.googleTagManagerHead')

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, user-scalable=no">
    {!! SEO::generate() !!}

    <link rel="apple-touch-icon" sizes="180x180" href="{{unstageAsset('apple-touch-icon.png')}}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{unstageAsset('favicon-32x32.png')}}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{unstageAsset('favicon-16x16.png')}}">
    <link rel="manifest" href="{{unstageAsset('site.webmanifest')}}">
    <link rel="mask-icon" href="{{unstageAsset('safari-pinned-tab.svg')}}" color="#020202">
    <link rel="shortcut icon" href="{{unstageAsset('favicon.ico')}}">
    <meta name="msapplication-TileColor" content="#000000">
    <meta name="theme-color" content="#ffffff">

    <meta property="fb:app_id" content="155195791749213"/>

    <script type="text/javascript">
        document.documentElement.addEventListener('touchstart', function (event) {
            if (event.touches.length > 1) {
                event.preventDefault();
            }
        }, false);
        var APP_URL = {!! json_encode(url('/')) !!};
    </script>
    <meta name="GOOGLE_MAPS_KEY" content="{{ env('GOOGLE_MAPS_KEY') }}"/>
    <meta name="csrf-token" content="{{ csrf_token() }}"/>
    {!! Minify::stylesheet(
    array(
    '/css/bootstrap/css/bootstrap4.css',
    '/css/flash.css',
    '/css/pro/main.css'
     ))->withFullUrl()  !!}

    <link type="text/css" rel="stylesheet" href="//fast.fonts.net/cssapi/a3afa447-fb81-478f-8114-f00194aa4a2b.css?v=1"/>

            <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
    {!! Minify::javascript(
    array(
    '/js/frontend/html5shiv.min.js',
    '/js/frontend/respond.min.js',
    )) !!}
    <![endif]-->
@yield('header')
</head>
<body>
@include('scripts.googleTagManagerBody')
{{--@include('neptune::auth.modalLogin')--}}
<div class="wrapper">
    <div class="flex-header">
        @include('neptune::pro.open.components.header')
    </div>
    <div class="flex-body">
        @yield('content')
        @include('components.messages.flash')
    </div>
    <div class="flex-footer">
        @include('neptune::pro.open.components.footer')
    </div>
</div>
<!-- Scripts -->
{!! Minify::javascript(
    array(
        '/js/jquery.js',
        '/css/bootstrap/js/bootstrap4.js',
        '/js/frontend/modernizr-custom.js',
        '/js/blazy/blazy.min.js',
        '/js/owlCarousel/owl.carousel.js',
        '/js/pro/main.js'
))->withFullUrl() !!}
@yield('footer')
</body>
</html>
