
@extends('neptune::pro.open.components.layout')

@section('content')
    <div class="proHero" style="background-image:url('{{unstageAsset('img/prohero1.jpg')}}')">
        <div class="container">
            <div class="row">
                <div class="col-md-6 offset-md-3">
                    <h1 class="proHero__title text-center">
                        Το εργαλείο της πλατφόρμας unstage για επαγγελματίες στο χώρο της ψυχαγωγίας
                    </h1>
                </div>
            </div>
        </div>
    </div>
    <div class="container">
        <div class="row">
            <div class="col-md-6  offset-md-3 col-lg-4 offset-lg-4 col-sm-10 offset-sm-1">
                <div class="proAuthLogin">
                    <h1 class="proAuth__title">{!! trans('neptune::pro/landings.login.heading') !!}</h1>
                    <form role="form" method="POST" action="{{ route('neptune.pro.login.handle') }}">
                        {!! csrf_field() !!}
                        <div class="form-group has-error">
                            <label for="email">{!! trans('neptune::pro/landings.login.email') !!}*</label>
                            <input type="text" class="form-control" id="email" name="email"
                                   value="{{ old('email') }}" required/>
                            <span class="auth-error">{!! $errors->first('email') !!}</span>
{{--                            {!!  $errors->first('email', '<span id="email-feedback" class="invalid-feedback">:message</span>') !!}--}}
                        </div>
                        <div class="form-group">
                            <label for="phone">{!! trans('neptune::pro/landings.login.password') !!}*</label>
                            <input type="password" class="form-control" id="password" name="password"
                                   value="{{ old('password') }}" required/>
                        </div>


                        <button type="submit"
                                class="proButton proButton--block">{!! trans('neptune::pro/landings.login.submit') !!}</button>
                    </form>
                    <div class="auth-link  ">
                        Κάνοντας είσοδο αποδέχεσαι τους <a target='_blank' href="{{ route('terms.index') }}"> Όρους Χρήσης και Πολιτική Απορρήτου</a> μας.
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop
