@extends('neptune::pro.open.components.layout')

@section('content')
    <div class="proHero b-lazy" data-src="{{unstageAsset('img/prohero1.jpg')}}">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <h1 class="proHero__title">
                        {{ trans('neptune::pro/landings.home.heading') }}
                    </h1>
                    <a href="{{ route('neptune.pro.register.show') }}"
                       class="proButton">{{ trans('neptune::pro/landings.home.register_cta') }}</a>
                </div>
            </div>
        </div>
    </div>
    <div class="proSection">
        <div class="container">
            <div class="row">
                <div class="col-md-12 text-center">
                    <h2 class="proSection__title">{{ trans('neptune::pro/landings.home.subheading') }}</h2>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12  ">
                    <img class="b-lazy"
                         src=data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7
                         data-src="{{unstageAsset('img/unpro1.png')}}"
                         width="100%"/>
                </div>
            </div>
            <div class="row">
                <?php $strengths = trans('neptune::pro/landings.home.unstage_pro'); ?>
                @foreach($strengths as $strength)
                <div class="col-md-4    ">
                        <div class="proSectionItem">
                            <div class="proSectionItem__title">
                                {{ $strength['title'] }}
                            </div>
                            <div class="proSectionItem__text">
                                {{ $strength['text'] }}
                            </div>
                        </div>
                </div>
                    @endforeach
            </div>
        </div>
    </div>
    <div class="homeCta">
        <div class="container">
            <div class="row">
                <div class="col-md-7 offset-md-2 col-lg-7 offset-lg-2">
                    <div class="homeCta__title">
                        {{ trans('neptune::pro/landings.home.teaser_text') }}
                    </div>
                    <a href="{{ route('neptune.pro.register.show') }}"
                       class="proButton">{{ trans('neptune::pro/landings.home.register_cta') }}</a>
                </div>
            </div>
        </div>
    </div>
@stop
