@extends('neptune::pro.components.layout')
@section('header')
@endsection
@section('content')
    <div class="subheader">
        <div class="row">
            <div class="col-md-12">
                <h1>{{ trans('neptune::pro/movies.edit_notes.heading') }}</h1>
                <div class="explainText">
                    {{ sprintf(trans('neptune::pro/movies.edit_notes.intro_text'), $movie->title) }}
                </div>
            </div>
        </div>
    </div>
    @include('neptune::pro.movies._moviesNav')
    {{ Form::model($movie, ['method' => 'PUT', 'route' => ['neptune.pro.movies.updateNotes', 'person_slug' => $selected_person->slug, 'movie_slug' => $movie->slug]]) }}
    @include('neptune::pro.movies._moviesNotesForm')
    {{ Form::close() }}
@stop
