@extends('neptune::pro.components.layout')
@section('header')
@endsection
@section('content')
    <div class="subheader">
        <div class="row">
            <div class="col-md-12">
                <h1>{{ trans('neptune::pro/movies.edit_published.heading') }}</h1>
                <div class="explainText">
                    {{ sprintf(trans('neptune::pro/movies.edit_published.intro_text'), $movie->title) }}
                </div>
            </div>
        </div>
    </div>
    @include('neptune::pro.movies._moviesNav')
    {{ Form::model($movie, ['method' => 'PUT', 'route' => ['neptune.pro.movies.updatePublished', 'person_slug' => $selected_person->slug, 'movie_slug' => $movie->slug]]) }}
    <div class="row">
        <div class="col-lg-12 col-xl-10 col-md-12">
            <div class="pageCont">
                <div class="explainText">
                    <h5>{{ trans('neptune::pro/movies.edit_published.submit_title') }}</h5>
                    <p>{{ trans('neptune::pro/movies.edit_published.submit_text') }}</p>
                </div>
                @if ($errors->any())
                    <div class="alert alert--danger" role="alert" >
                        <button type="button" class="button close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">×</span>
                            <span class="visually-hidden">Close</span>
                        </button>
                        @foreach ($errors->all() as $error)
                            <p>{{ $error }}</p>
                        @endforeach
                    </div>
                @endif
                <div class="form-group">
                    {{Form::submit(trans('neptune::pro/movies.edit_published.submit_cta'), ['class="btn btn-primary"'])}}
                </div>
            </div>
        </div>
    </div>
    {{ Form::close() }}
@stop
