
<ul class="nav nav-pills nav-plays">
    <li class="nav-item">
        <a class="nav-link @if(\Route::current()->getName() == 'neptune.pro.movies.editInfo') active @endif" href="{{route('neptune.pro.movies.editInfo', [$selected_person->slug, $movie->slug])}}">ΠΛΗΡΟΦΟΡΙΕΣ</a>
    </li>
    <li class="nav-item">
        <a class="nav-link @if(\Route::current()->getName() == 'neptune.pro.movies.editImages') active @endif" href="{{route('neptune.pro.movies.editImages', [$selected_person->slug, $movie->slug])}}">ΦΩΤΟΓΡΑΦΙΕΣ</a>
    </li>
    <li class="nav-item">
        <a class="nav-link @if(\Route::current()->getName() == 'neptune.pro.movies.editRoles') active @endif" href="{{route('neptune.pro.movies.editRoles', [$selected_person->slug, $movie->slug])}}">ΣΥΝΤΕΛΕΣΤΕΣ</a>
    </li>
    <li class="nav-item">
        <a class="nav-link @if(\Route::current()->getName() == 'neptune.pro.movies.editNotes') active @endif" href="{{route('neptune.pro.movies.editNotes', [$selected_person->slug, $movie->slug])}}">ΣΗΜΕΙΩΣΕΙΣ</a>
    </li>
    <li class="nav-item">
        <a class="nav-link @if(\Route::current()->getName() == 'neptune.pro.movies.editPublished') active @endif" href="{{route('neptune.pro.movies.editPublished', [$selected_person->slug, $movie->slug])}}">ΥΠΟΒΟΛΗ</a>
    </li>
</ul>
