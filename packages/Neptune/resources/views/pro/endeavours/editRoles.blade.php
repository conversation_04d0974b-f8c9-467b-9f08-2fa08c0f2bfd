@extends('neptune::pro.components.layout')
@section('header')
@endsection
@section('content')
    <div class="subheader">
        <div class="row">
            <div class="col-md-12">
                <h1>{{ trans('neptune::pro/endeavours.edit_roles.heading') }} {{ mb_strtolower($endeavour->variety->name) }} "{{ $endeavour->title }}"</h1>
                <div class="explainText">
                    {!! sprintf(trans('neptune::pro/endeavours.edit_roles.intro_text'), route('neptune.pro.endeavours.editNotes', [$selected_person->slug, $endeavour->slug])) !!}
                </div>
            </div>
        </div>
    </div>
    @include('neptune::pro.endeavours._endeavoursNav')
    @include('neptune::pro.endeavours._endeavoursRolesForm', ['submitButtonText'=>'Αποθήκευση'])
@stop
