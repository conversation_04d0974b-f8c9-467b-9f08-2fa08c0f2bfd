@extends('neptune::pro.components.layout')
@section('header')
@endsection
@section('content')
    <div class="subheader">
        <div class="row">
            <div class="col-md-12">
                <h1>{{ trans('neptune::pro/endeavours.edit_images.heading') }} {{ mb_strtolower($endeavour->variety->name) }} "{{ $endeavour->title }}"</h1>
                <div class="explainText">
                    {{ trans('neptune::pro/endeavours.edit_images.intro_text') }}
                </div>
            </div>
        </div>
    @include('neptune::pro.endeavours._endeavoursNav')
    @include('neptune::pro.endeavours._endeavoursImagesForm', ['submitButtonText'=>'Αποθήκευση'])
@stop
