<div class="row">
    <div class="col-lg-12 col-xl-10 col-md-12">
        <div class="pageCont">
            @include('neptune::pro.components.messages.errors')

            @foreach( $roles as $role )
                <div class="mainRole" data-play-id="{!! $play->id !!}" data-role-id="{!! $role->id !!}"
                     data-destroy-url="{!! route('neptune.pro.personPlayRole.destroy') !!}"
                     data-store-url="{!! route('neptune.pro.personPlayRole.store') !!}">
                    <label for="role_{{ $role->id }}">{{$role->description.':'}}</label>
                    <select name="role_{{ $role->id }}" id="role_{{ $role->id }}" class="form-control_ selectRoles"
                            multiple data-url="{!! route('neptune.pro.people.personList') !!}">
                    </select>
                </div>
                <br>
            @endforeach
        </div>
    </div>
</div>

@section('footer')
    @parent
    <script src="{{asset('js/pro/_playsRolesForm.js')}}" id="select2WithPeopleSearch"
            data-url="{!! route('neptune.pro.people.personList') !!}"></script>
    <script>

        @foreach($play->roles as $playRole)
            select2WithPeopleSearch('role_{!! $playRole->id   !!}', {!! json_encode($playRole->selectizedPeopleIdFullname) !!}, {!! json_encode($playRole->selectizedPeopleIdFullname) !!});
        @endforeach
        @foreach($rolesWithoutActors as $role)
            select2WithPeopleSearch('role_{!! $role->id   !!}', []);
        @endforeach
    </script>
@stop
