@extends('neptune::pro.components.layout')
@section('header')
@endsection
@section('content')
    <div class="subheader">
        <div class="row">
            <div class="col-md-12">
                <h1>{{ trans('neptune::pro/plays.create.heading') }}</h1>
                <div class="explainText">
                    {{ trans('neptune::pro/plays.create.intro_text') }}
                </div>
            </div>
        </div>
    </div>
    {{ Form::open(['method' => 'POST', 'route' => 'neptune.pro.adminised.plays.store']) }}
    <div class="row">
        <div class="col-lg-10 col-xl-8">
            <div class="pageCont">
                @include('neptune::pro.components.messages.errors')
                <div class="form-group {!! $errors->first('title') ? ' has-error' : '' !!}">
                    {{Form::label('title', trans('neptune::pro/adminised/plays.create.title_label'))}}
                    {{Form::text('title', null, ['class' => 'form-control'])}}
                    <div class="input-error">{{ $errors->first('title') }}</div>
                </div>
                <div class="form-group {!! $errors->first('year') ? ' has-error' : '' !!}">
                    {{Form::label('year', trans('neptune::pro/adminised/plays.create.year_label'))}}
                    {{Form::text('year', null, ['class' => 'form-control'])}}
                    <div class="input-error">{{ $errors->first('year') }}</div>
                </div>
                <div class="form-group">
                    {{Form::submit(trans('neptune::pro/adminised/plays.create.submit_cta'), ['class="btn btn-primary"'])}}
                </div>
            </div>
        </div>
    </div>
    {{ Form::close() }}
@stop

@section('footer')
    @parent
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <script>
        $('#date').bootstrapMaterialDatePicker({
            time: false,
            format: "DD-MM-YYYY"
        });
    </script>
@stop
