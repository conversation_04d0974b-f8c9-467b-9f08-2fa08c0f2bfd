<div class="row">
    <div class="col-lg-12 col-xl-10 col-md-12">
        <div class="pageCont">
            @include('neptune::pro.components.messages.errors')
            <div class="form-group {!! $errors->first('user_notes') ? ' has-error' : '' !!}">
                {{Form::label('user_notes', trans('neptune::pro/plays.edit_notes.user_notes_label'))}}
                <div class="explainForm">{{ trans('neptune::pro/plays.edit_notes.user_notes_explain') }}</div>
                {{Form::textarea('user_notes', null, ['class' => 'form-control summernote'])}}
                <div class="input-error">{{ $errors->first('user_notes') }}</div>
            </div>
            <div class="form-group">
                {{Form::submit(trans('neptune::pro/plays.edit_notes.submit_cta'), ['class="btn btn-primary"'])}}
            </div>
        </div>
    </div>
</div>
