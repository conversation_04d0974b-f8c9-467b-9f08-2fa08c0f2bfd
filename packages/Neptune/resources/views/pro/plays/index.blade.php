@extends('neptune::pro.components.layout')
@section('content')
    <div class="subheader">
        <div class="row">
            <div class="col-md-8">
                <h1>{{ trans('neptune::pro/plays.index.heading') }}</h1>
                <div class="explainText">
                    {{ sprintf(trans('neptune::pro/plays.index.intro_text'), $selected_person->fullName) }}
                </div>
            </div>
            <div class="col-md-4 text-right">
                <a href="{{ route('neptune.pro.plays.create', $selected_person->slug) }}"
                   class="btn btn-primary">{{ trans('neptune::pro/plays.index.add_cta') }}</a>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="pageCont">
                @include('neptune::pro.components.messages.errors')
                <div class="table-responsive">
                    <div class="table">
                        @forelse($plays as $play)
                            <div class="table-row">
                                <div class="table-cell featherSmall">
                                    @if($play->published)
                                    <a title="{{ trans('neptune::pro/plays.index.preview_title_cta') }}" target="_blank" href="{{ route('plays.show', $play->slug) }}">
                                        <i class="actionIcon1" data-feather="external-link"></i>
                                    </a>
                                    @endif
                                </div>
                                <div class="table-cell table-cell--full ">
                                    <p>
                                    @if($play->moderated)
                                        {{ $play->fullTitle }}
                                    @else
                                        <a title="{{ trans('neptune::pro/plays.index.edit_title_cta') }}" href="{{ route('neptune.pro.plays.editInfo', [$selected_person->slug, $play->slug]) }}">{{ $play->fullTitle }}</a>
                                    @endif
                                    </p>
                                </div>
                                <div class="table-cell">
                                @if($play->published)
                                        <div class="stat_icon--success">
                                            {{ trans('neptune::pro/plays.index.published_label') }}
                                            <i class="statusIcon" data-feather="eye" data-toggle="tooltip" title="{{ trans('neptune::pro/plays.index.published_label') }}"></i>
                                        </div>
                                @else
                                    <div>
                                        {{ trans('neptune::pro/plays.index.unpublished_label') }}
                                        <i class="statusIcon" data-feather="eye-off" data-toggle="tooltip" title="{{ trans('neptune::pro/plays.index.unpublished_label') }}"></i>
                                    </div>
                                @endif
                                </div>
                                <div class="table-cell">
                                @if($play->moderated)
                                    <div class="stat_icon--success">
                                        {{ trans('neptune::pro/plays.index.moderated_label') }}
                                        <i class="statusIcon" data-feather="check-circle" data-toggle="tooltip" title="{{ trans('neptune::pro/plays.index.moderated_label') }}"></i>
                                    </div>
                                @else
                                    <div>
                                        {{ trans('neptune::pro/plays.index.unmoderated_label') }}
                                        <i class="statusIcon" data-feather="pause-circle" data-toggle="tooltip" title="{{ trans('neptune::pro/plays.index.unmoderated_label') }}"></i>
                                    </div>
                                @endif
                                </div>
                                <div class="table-cell">
                                @if( !$play->moderated )
                                    <a title="{{ trans('neptune::pro/plays.index.edit_title_cta') }}" href="{{ route('neptune.pro.plays.editInfo', [$selected_person->slug, $play->slug]) }}">
                                        <i class="actionIcon" data-feather="edit-3" data-toggle="tooltip" title="{{ trans('neptune::pro/plays.index.edit_title_cta') }}"></i>
                                    </a>
                                @endif
                                </div>
                            </div>
                        @empty
                            <div class="table-row">
                                <div class="table-cell">
                                    {{ trans('neptune::pro/plays.index.none_found') }} <a href="{{ route('neptune.pro.plays.create', $selected_person->slug) }}">{{ trans('neptune::pro/plays.index.add_first_cta') }}</a>
                                </div>
                            </div>
                        @endforelse
                    </div>
                </div>
                {{$plays->links()}}
            </div>
        </div>
    </div>
@stop
@section('footer')
    @parent
@stop
