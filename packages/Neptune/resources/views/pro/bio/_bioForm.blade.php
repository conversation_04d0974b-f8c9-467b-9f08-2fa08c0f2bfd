<div class="row">
    <div class="col-12">
        <div class="pageCont">
            @include('neptune::pro.components.messages.errors')
            {{--<ul class="nav nav-pills  justify-content-end" id="pills-tab" role="tablist">--}}
                {{--<li class="nav-item">--}}
                    {{--<a class="nav-link active" id="pills-el-tab" data-toggle="pill" href="#pills-el" role="tab"--}}
                       {{--aria-controls="pills-el" aria-selected="true">Ελληνικά</a>--}}
                {{--</li>--}}
                {{--<li class="nav-item">--}}
                    {{--<a class="nav-link" id="pills-en-tab" data-toggle="pill" href="#pills-en" role="tab"--}}
                       {{--aria-controls="pills-en" aria-selected="false">English</a>--}}
                {{--</li>--}}
            {{--</ul>--}}
            <div class="tab-content" id="pills-tabContent">
                <div class="tab-pane fade show active" id="pills-el" role="tabpanel" aria-labelledby="pills-el-tab">
                    <div class="form-group">
                        <div class="d-flex justify-content-between align-items-center">
                            {{Form::label('user_bio_el', trans('neptune::pro/bio.bio_label'), ['class' => 'mb-0'])}}
                            <div class="bio-limits-info">
                                <span class="badge badge-info"
                                      data-toggle="tooltip"
                                      data-placement="top"
                                      title="{{ trans('neptune::pro/bio.character_limit_tooltip', ['max_words' => 200]) }}">
                                    <i class="fas fa-info-circle"></i> {{ trans('neptune::pro/bio.character_limit_tooltip', ['max_words' => 200]) }}
                                </span>
                            </div>
                        </div>
                        {{Form::textarea('user_bio_el',  $selected_person->user_bio , ['class' => 'form-control summernote bio-editor', 'id' => 'user_bio_el', 'data-bio-editor' => 'true'])}}

                        <!-- Word count display -->
                        <div class="bio-counter-container mt-2">
                            <div class="row">
                                <div class="col-12 text-center">
                                    <small class="text-muted">
                                        <span id="word-count-el">0</span>/200 λέξεις
                                    </small>
                                </div>
                                <!-- Hidden character counter for silent validation -->
                                <span id="char-count-el" style="display: none;">0</span>
                            </div>
                            <div class="progress mt-1" style="height: 3px;">
                                <div class="progress-bar" id="progress-bar-el" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                            <div id="limit-warning-el" class="alert alert-warning mt-2" style="display: none;">
                                <small><i class="fas fa-exclamation-triangle"></i> {{ trans('neptune::pro/bio.limit_warning') }}</small>
                            </div>
                            <div id="limit-exceeded-el" class="alert alert-danger mt-2" style="display: none;">
                                <small><i class="fas fa-times-circle"></i> {{ trans('neptune::pro/bio.limit_exceeded') }}</small>
                            </div>
                        </div>

                        {{ $errors->first('user_bio_el', '<span class="alert alert-danger d-block mt-2">:message</span>') }}
                    </div>
                </div>
                <div class="tab-pane fade" id="pills-en" role="tabpanel" aria-labelledby="pills-en-tab">
                    <div class="form-group">
                        <div class="d-flex justify-content-between align-items-center">
                            {{Form::label('user_bio_en', 'Bio', ['class' => 'mb-0'])}}
                            <div class="bio-limits-info">
                                <span class="badge badge-info"
                                      data-toggle="tooltip"
                                      data-placement="top"
                                      title="{{ trans('neptune::pro/bio.character_limit_tooltip', ['max_words' => 200]) }}">
                                    <i class="fas fa-info-circle"></i> {{ trans('neptune::pro/bio.character_limit_tooltip', ['max_words' => 200]) }}
                                </span>
                            </div>
                        </div>
                        {{Form::textarea('user_bio_en', $selected_person->hasTranslation('en') ? $selected_person->translate('en')->user_bio : "", ['class' => 'form-control summernote bio-editor', 'id' => 'user_bio_en', 'data-bio-editor' => 'true'])}}

                        <!-- Word count display -->
                        <div class="bio-counter-container mt-2">
                            <div class="row">
                                <div class="col-12 text-center">
                                    <small class="text-muted">
                                        <span id="word-count-en">0</span>/200 words
                                    </small>
                                </div>
                                <!-- Hidden character counter for silent validation -->
                                <span id="char-count-en" style="display: none;">0</span>
                            </div>
                            <div class="progress mt-1" style="height: 3px;">
                                <div class="progress-bar" id="progress-bar-en" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                            <div id="limit-warning-en" class="alert alert-warning mt-2" style="display: none;">
                                <small><i class="fas fa-exclamation-triangle"></i> Warning: Approaching the limit!</small>
                            </div>
                            <div id="limit-exceeded-en" class="alert alert-danger mt-2" style="display: none;">
                                <small><i class="fas fa-times-circle"></i> Limit exceeded!</small>
                            </div>
                        </div>

                        {{ $errors->first('user_bio_en', '<span class="alert alert-danger d-block mt-2">:message</span>') }}
                    </div>
                </div>
            </div>
            <div class="form-group">
                {{Form::submit(trans('neptune::pro/bio.submit_cta'), ['class="btn btn-primary"'])}}
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// PersonBio Character Counter - Only runs on bio edit page
window.PersonBioCounter = {
    init: function() {
        // Only run on PersonBio page
        if (!$('#user_bio_el').length && !$('#user_bio_en').length) {
            return;
        }

        // console.log('PersonBio Counter: Initializing...');

        // Initialize tooltips
        $('[data-toggle="tooltip"]').tooltip();

        // Configure Summernote for bio editors
        this.configureSummernote();

        this.setupCounters();
    },

    // Configure Summernote with restricted formatting and paste sanitization
    configureSummernote: function() {
        const bioConfig = {
            height: 300,
            toolbar: [
                ['style', ['style']],
                ['font', ['bold', 'italic', 'underline']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['insert', ['link']],
                ['view', ['codeview']]
            ],
            styleTags: [
                'p', 'h4', 'h5', 'h6'
            ],
            callbacks: {
                onPaste: function(e) {
                    // Prevent default paste behavior
                    e.preventDefault();

                    // Get clipboard data
                    const clipboardData = e.originalEvent.clipboardData || window.clipboardData;
                    let pastedData = clipboardData.getData('text/html') || clipboardData.getData('text/plain');

                    // Sanitize the pasted content
                    const sanitized = PersonBioCounter.sanitizeContent(pastedData);

                    // Insert sanitized content
                    $(this).summernote('pasteHTML', sanitized);
                },
                onChange: function(contents, $editable) {
                    // Sanitize content on change as well
                    const sanitized = PersonBioCounter.sanitizeContent(contents);
                    if (sanitized !== contents) {
                        $(this).summernote('code', sanitized);
                    }
                }
            }
        };

        // Apply configuration to bio editors
        $('.bio-editor[data-bio-editor="true"]').each(function() {
            const $editor = $(this);
            if ($editor.data('summernote')) {
                // If already initialized, destroy and reinitialize
                $editor.summernote('destroy');
            }
            $editor.summernote(bioConfig);
        });
    },

    // Sanitize content to remove custom styling and restrict to platform styles
    sanitizeContent: function(content) {
        if (!content) return '';

        // Create a temporary div to parse HTML
        const $temp = $('<div>').html(content);

        // Remove all style attributes
        $temp.find('*').removeAttr('style');

        // Remove class attributes (except allowed ones)
        $temp.find('*').each(function() {
            const $el = $(this);
            const classes = $el.attr('class');
            if (classes) {
                // Only keep specific platform classes if needed
                const allowedClasses = [];
                const currentClasses = classes.split(' ');
                const filteredClasses = currentClasses.filter(cls => allowedClasses.includes(cls));

                if (filteredClasses.length > 0) {
                    $el.attr('class', filteredClasses.join(' '));
                } else {
                    $el.removeAttr('class');
                }
            }
        });

        // Remove unwanted attributes
        const unwantedAttrs = ['id', 'data-*', 'onclick', 'onload', 'onerror', 'background', 'bgcolor', 'color', 'face', 'size'];
        unwantedAttrs.forEach(attr => {
            if (attr.includes('*')) {
                // Remove data attributes
                $temp.find('*').each(function() {
                    const element = this;
                    Array.from(element.attributes).forEach(attribute => {
                        if (attribute.name.startsWith('data-')) {
                            element.removeAttribute(attribute.name);
                        }
                    });
                });
            } else {
                $temp.find('*').removeAttr(attr);
            }
        });

        // Only allow specific HTML tags
        const allowedTags = ['p', 'br', 'strong', 'b', 'em', 'i', 'u', 'ul', 'ol', 'li', 'h4', 'h5', 'h6', 'a'];

        $temp.find('*').each(function() {
            const tagName = this.tagName.toLowerCase();
            if (!allowedTags.includes(tagName)) {
                // Replace disallowed tags with their content
                $(this).replaceWith($(this).html());
            }
        });

        // Clean up links - only allow href attribute
        $temp.find('a').each(function() {
            const $link = $(this);
            const href = $link.attr('href');

            // Remove all attributes
            Array.from(this.attributes).forEach(attr => {
                this.removeAttribute(attr.name);
            });

            // Add back only href if it's a valid URL
            if (href && (href.startsWith('http://') || href.startsWith('https://') || href.startsWith('mailto:'))) {
                $link.attr('href', href);
                $link.attr('target', '_blank');
                $link.attr('rel', 'noopener noreferrer');
            } else {
                // Remove invalid links but keep content
                $link.replaceWith($link.html());
            }
        });

        // Remove empty paragraphs and normalize whitespace
        $temp.find('p').each(function() {
            if ($(this).text().trim() === '' && $(this).find('br').length === 0) {
                $(this).remove();
            }
        });

        return $temp.html();
    },

    updateCounter: function(editorId, lang) {
        let content = '';
        const $editor = $(editorId);

        try {
            // Multiple ways to get content depending on Summernote state
            if ($editor.data('summernote')) {
                content = $editor.summernote('code');
            } else if ($editor.next('.note-editor').length) {
                content = $editor.next('.note-editor').find('.note-editable').html() || '';
            } else {
                content = $editor.val() || '';
            }
        } catch (e) {
            content = $editor.val() || '';
        }

        const plainText = $('<div>').html(content).text();
        const charCount = plainText.length;
        const wordCount = plainText.trim() === '' ? 0 : plainText.trim().split(/\s+/).length;

        // Update counters
        $('#char-count-' + lang).text(charCount);
        $('#word-count-' + lang).text(wordCount);

        // Calculate progress (based on word count primarily)
        const wordProgress = (wordCount / 200) * 100;
        const charProgress = (charCount / 5000) * 100; // Silent character limit for ~300 words
        const maxProgress = Math.max(wordProgress, charProgress);

        // Update progress bar
        const progressBar = $('#progress-bar-' + lang);
        progressBar.css('width', Math.min(maxProgress, 100) + '%');

        // Update progress bar color based on usage
        progressBar.removeClass('bg-success bg-warning bg-danger');
        if (maxProgress < 70) {
            progressBar.addClass('bg-success');
        } else if (maxProgress < 90) {
            progressBar.addClass('bg-warning');
        } else {
            progressBar.addClass('bg-danger');
        }

        // Show/hide warnings
        const warningEl = $('#limit-warning-' + lang);
        const exceededEl = $('#limit-exceeded-' + lang);

        if (wordCount > 200 || charCount > 5000) {
            warningEl.hide();
            exceededEl.show();
        } else if (wordCount > 180 || charCount > 4500) { // 90% threshold
            warningEl.show();
            exceededEl.hide();
        } else {
            warningEl.hide();
            exceededEl.hide();
        }

        // console.log('PersonBio Counter: Updated', lang, 'Words:', wordCount, 'Chars:', charCount);
    },

    setupCounters: function() {
        const self = this;

        // Setup for Greek editor
        if ($('#user_bio_el').length) {
            this.setupSingleEditor('#user_bio_el', 'el');
        }

        // Setup for English editor
        if ($('#user_bio_en').length) {
            this.setupSingleEditor('#user_bio_en', 'en');
        }

        // Retry setup after delays to catch late Summernote initialization
        setTimeout(() => {
            this.setupSingleEditor('#user_bio_el', 'el');
            this.setupSingleEditor('#user_bio_en', 'en');
        }, 1000);

        setTimeout(() => {
            this.setupSingleEditor('#user_bio_el', 'el');
            this.setupSingleEditor('#user_bio_en', 'en');
        }, 2000);
    },

    setupSingleEditor: function(editorId, lang) {
        const $editor = $(editorId);
        const self = this;

        if (!$editor.length) return;

        // Remove any existing event listeners to avoid duplicates
        $editor.off('.biocounter');

        // Method 1: Hook into Summernote events if available
        if ($editor.data('summernote')) {
            // console.log('PersonBio Counter: Hooking into existing Summernote for', editorId);
            $editor.on('summernote.change.biocounter', function() {
                self.updateCounter(editorId, lang);
            });
            $editor.on('summernote.keyup.biocounter', function() {
                self.updateCounter(editorId, lang);
            });
        }

        // Method 2: Listen on the note-editable div if Summernote is initialized
        const $noteEditable = $editor.next('.note-editor').find('.note-editable');
        if ($noteEditable.length) {
            // console.log('PersonBio Counter: Hooking into note-editable for', editorId);
            $noteEditable.off('.biocounter');
            $noteEditable.on('keyup.biocounter paste.biocounter input.biocounter', function() {
                setTimeout(() => self.updateCounter(editorId, lang), 50);
            });
        }

        // Method 3: Fallback to textarea events
        $editor.on('keyup.biocounter paste.biocounter input.biocounter change.biocounter', function() {
            setTimeout(() => self.updateCounter(editorId, lang), 50);
        });

        // Initial count
        this.updateCounter(editorId, lang);
    }
};

$(document).ready(function() {
    PersonBioCounter.init();
});

// Also initialize when window loads (in case of late script loading)
$(window).on('load', function() {
    setTimeout(() => PersonBioCounter.init(), 500);
});
</script>
@endpush

