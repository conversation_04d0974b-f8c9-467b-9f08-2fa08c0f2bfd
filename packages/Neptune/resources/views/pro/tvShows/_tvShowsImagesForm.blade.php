<div class="row">
    <div class="col-lg-12 col-xl-10 col-md-12">
        <div class="pageCont_">
            @include('neptune::pro.components.messages.errors')

            <div class="row">
                <div class="col-12">
                    <div class="dropify-wrapper">
                        @component('neptune::pro.components.uploaderTvShowImages', [
                   'title' => 'Upload Movie Images',
                   'params' => [
                       'person_slug' => $selected_person->slug,
                       'tvShow_id' => $tvShow->id
                   ],
                   'uploadedFiles' =>    []
               ])
                        @endcomponent
                    </div>
                    <div class="pageCont">
                        @include('neptune::pro.components.messages.errors')
                        <div class="table-responsive">
                            <div class="table">
                                @forelse($images = $logged_user->imagesUploaded($tvShow)->paginate(10) as $image)
                                    <div class="table-row">
                                        <div class="table-cell">
                                            <img class="galleryImage"
                                                 src="{!! asset(Croppa::url($image->filename,200,null)) !!}">
                                        </div>
                                        <div class="table-cell table-cell--full">
                                    <textarea data-id="{{$image->id}}"
                                              placeholder="Πληκτρολόγησε λεζάντα"
                                              class="form-control caption">{{$image->description}}</textarea>
                                            <div class="captionCont"></div>
                                        </div>
                                        <div class="table-cell  ">
                                        <input type="radio" class="mainImage" name="mainImage" value="{{$image->id}}" @if($image->main) checked @endif  data-toggle="tooltip" data-placement="top"  title="Ορισμός κύριας φωτογραφίας">
                                        </div>
                                        <div class="table-cell">
                                            <a href="#" data-url="{{route('neptune.pro.tvShow.images.destroy', [ $image->id])}}"
                                               data-person_slug="{{$selected_person->slug}}"
                                               class="deleteResource">
                                                <i data-feather="trash" class="actionIcon"></i>
                                            </a>
                                        </div>
                                        <div class="table-cell">
                                            @if($image->banned == true)
                                                <i data-toggle="tooltip" data-placement="top"  title="Υπήρξε πρόβλημα με τη φωτογραφία. Για περισσότερες πληροφορίες επικοινώνησε <EMAIL>" class="menuIcon" data-feather="alert-triangle"></i>
                                            @endif
                                        </div>
                                    </div>
                                @empty
                                @endforelse
                            </div>
                            <div class="pagination justify-content-end">
                                {{$images->links()}}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section('footer')
    @parent
    <script>
        $(document).on('click', '.mainImage', function () {
            var id = $(this).val();
           var person_slug = '{{$selected_person->slug}}';
            $.ajax({
                type: 'POST',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                url: APP_URL + '/proapp/tvShow/images/setMainImage',
                data: {id, person_slug},
                dataType: 'json',
                context: this,
                success: function (data) {
                    if (data.error == false) {
                        toastr.success(data.message);
                    } else {
                        toastr.success(data.message);
                    }
                }
            });
        });


        $(document).on('focusout', '.caption', function () {
            var id = $(this).attr('data-id');
            var description = $(this).val();
            var person_slug = '{{$selected_person->slug}}';
            $.ajax({
                type: 'POST',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                url: APP_URL + '/proapp/tvShow/images/saveCaption',
                data: {id, description, person_slug},
                dataType: 'json',
                context: this,
                success: function (data) {
                    if (data.error == false) {
                        $(this).addClass('captionSaved');
                        toastr.success(data.message);
                    } else {
                        toastr.error(data.message);
                    }
                    setTimeout(function () {
                        $('.caption').removeClass('captionSaved');
                    }, 2000);
                }
            });
        });
    </script>
@stop
