@extends('neptune::pro.components.layout')
@section('content')
    <div class="subheader">
        <div class="row">
            <div class="col-md-8">
                <h1>{{ trans('neptune::pro/tvShows.index.heading') }}</h1>
                <div class="explainText">
                    {{ sprintf(trans('neptune::pro/tvShows.index.intro_text'), $selected_person->fullName) }}
                </div>
            </div>
            <div class="col-md-4 text-right">
                <a href="{{ route('neptune.pro.tvShows.create', $selected_person->slug) }}"
                   class="btn btn-primary">{{ trans('neptune::pro/tvShows.index.add_cta') }}</a>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="pageCont">
                @include('neptune::pro.components.messages.errors')
                <div class="table-responsive">
                    <div class="table">
                        @forelse($tvShows as $tvShow)
                            <div class="table-row">
                                <div class="table-cell featherSmall">
                                    @if($tvShow->published)
                                    <a title="{{ trans('neptune::pro/tvShows.index.preview_title_cta') }}" target="_blank" href="{{ route('tvShows.show', $tvShow->slug) }}">
                                        <i class="actionIcon1" data-feather="external-link"></i>
                                    </a>
                                    @endif
                                </div>
                                <div class="table-cell table-cell--full ">
                                    <p>
                                        @if($tvShow->moderated)
                                            {{ $tvShow->fullTitle }}
                                        @else
                                            <a title="{{ trans('neptune::pro/tvShows.index.edit_title_cta') }}" href="{{ route('neptune.pro.tvShows.editInfo', [$selected_person->slug, $tvShow->slug]) }}">{{ $tvShow->fullTitle }}</a>
                                        @endif
                                    </p>
                                </div>
                                <div class="table-cell">
                                    @if($tvShow->published)
                                        <div class="stat_icon--success">
                                            {{ trans('neptune::pro/tvShows.index.published_label') }}
                                            <i class="statusIcon" data-feather="eye" data-toggle="tooltip" title="{{ trans('neptune::pro/tvShows.index.published_label') }}"></i>
                                        </div>
                                    @else
                                        <div>
                                            {{ trans('neptune::pro/tvShows.index.unpublished_label') }}
                                            <i class="statusIcon" data-feather="eye-off" data-toggle="tooltip" title="{{ trans('neptune::pro/tvShows.index.unpublished_label') }}"></i>
                                        </div>
                                    @endif
                                </div>
                                <div class="table-cell">
                                    @if($tvShow->moderated)
                                        <div class="stat_icon--success">
                                            {{ trans('neptune::pro/tvShows.index.moderated_label') }}
                                            <i class="statusIcon" data-feather="check-circle" data-toggle="tooltip" title="{{ trans('neptune::pro/tvShows.index.moderated_label') }}"></i>
                                        </div>
                                    @else
                                        <div>
                                            {{ trans('neptune::pro/tvShows.index.unmoderated_label') }}
                                            <i class="statusIcon" data-feather="pause-circle" data-toggle="tooltip" title="{{ trans('neptune::pro/tvShows.index.unmoderated_label') }}"></i>
                                        </div>
                                    @endif
                                </div>
                                <div class="table-cell">
                                    @if( !$tvShow->moderated )
                                        <a title="{{ trans('neptune::pro/tvShows.index.edit_title_cta') }}" href="{{ route('neptune.pro.tvShows.editInfo', [$selected_person->slug, $tvShow->slug]) }}">
                                            <i class="actionIcon" data-feather="edit-3"></i>
                                        </a>
                                    @endif
                                </div>
                            </div>
                        @empty
                            <div class="table-row">
                                <div class="table-cell">
                                    {{ trans('neptune::pro/tvShows.index.none_found') }} <a href="{{ route('neptune.pro.tvShows.create', $selected_person->slug) }}">{{ trans('neptune::pro/tvShows.index.add_first_cta') }}</a>
                                </div>
                            </div>
                        @endforelse
                    </div>
                </div>
                {{$tvShows->links()}}
            </div>
        </div>
    </div>
@stop
@section('footer')
    @parent
@stop
