
<ul class="nav nav-pills nav-plays">
    <li class="nav-item">
        <a class="nav-link @if(\Route::current()->getName() == 'neptune.pro.tvShows.editInfo') active @endif" href="{{route('neptune.pro.tvShows.editInfo', [$selected_person->slug, $tvShow->slug])}}">ΠΛΗΡΟΦΟΡΙΕΣ</a>
    </li>
    <li class="nav-item">
        <a class="nav-link @if(\Route::current()->getName() == 'neptune.pro.tvShows.editImages') active @endif" href="{{route('neptune.pro.tvShows.editImages', [$selected_person->slug, $tvShow->slug])}}">ΦΩΤΟΓΡΑΦΙΕΣ</a>
    </li>
    <li class="nav-item">
        <a class="nav-link @if(\Route::current()->getName() == 'neptune.pro.tvShows.editRoles') active @endif" href="{{route('neptune.pro.tvShows.editRoles', [$selected_person->slug, $tvShow->slug])}}">ΣΥΝΤΕΛΕΣΤΕΣ</a>
    </li>
    <li class="nav-item">
        <a class="nav-link @if(\Route::current()->getName() == 'neptune.pro.tvShows.editNotes') active @endif" href="{{route('neptune.pro.tvShows.editNotes', [$selected_person->slug, $tvShow->slug])}}">ΣΗΜΕΙΩΣΕΙΣ</a>
    </li>
    <li class="nav-item">
        <a class="nav-link @if(\Route::current()->getName() == 'neptune.pro.tvShows.editPublished') active @endif" href="{{route('neptune.pro.tvShows.editPublished', [$selected_person->slug, $tvShow->slug])}}">ΥΠΟΒΟΛΗ</a>
    </li>
</ul>
