@extends('neptune::pro.components.layout')
@section('header')
@endsection
@section('content')
    <div class="subheader">
        <div class="row">
            <div class="col-md-12">
                <h1>{{ trans('neptune::pro/tvShows.edit_images.heading') }} "{{ $tvShow->title }}"</h1>
                <div class="explainText">
                    {{ trans('neptune::pro/tvShows.edit_images.intro_text') }}
                </div>
            </div>
        </div>
    @include('neptune::pro.tvShows._tvShowsNav')
    @include('neptune::pro.tvShows._tvShowsImagesForm', ['submitButtonText'=>'Αποθήκευση'])
@stop
