@extends('neptune::pro.components.layout')
@section('content')
    <div class="subheader">
        <div class="row">
            <div class="col-md-8">
                <h1>{{ trans('neptune::pro/references.index.heading') }}</h1>
                <div class="explainText">
                    {{ sprintf(trans('neptune::pro/references.index.intro_text'), $selected_person->fullName) }}
                </div>
            </div>
            <div class="col-md-4 text-right">
                <a href="{{ route('neptune.pro.publications.create', $selected_person->slug) }}"
                   class="btn btn-primary">{{ trans('neptune::pro/references.index.add_cta') }}</a>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="pageCont">
                @include('neptune::pro.components.messages.errors')
                <div class="table-responsive">
                    <div class="table">
                        @forelse($references = $logged_user->referencesUploaded($selected_person)->paginate(10) as $reference)
                            <div class="table-row">
                                <div class="table-cell featherSmall">
                                    <a title="{{ trans('neptune::pro/references.index.preview_title_cta') }}" target="_blank" href="{{ $reference->url }}">
                                        <i class="actionIcon1" data-feather="external-link"></i>
                                    </a>
                                </div>
                                <div class="table-cell  ">
                                    <a title="{{ trans('neptune::pro/references.index.preview_title_cta') }}" target="_blank" href="{{ $reference->url }}">
                                    {{ $reference->medium }}
                                    </a>
                                </div>
                                <div class="table-cell  ">
                                    {{ $reference->type }}
                                </div>
                                <div class="table-cell  table-cell--full ">
                                    {{ $reference->author }}
                                </div>
                                <div class="table-cell">
                                    <a title="{{ trans('neptune::pro/references.index.edit_title_cta') }}" href="{{ route('neptune.pro.publications.edit', [$selected_person->slug, $reference->id]) }}">
                                        <i class="actionIcon" data-feather="edit-3"></i>
                                    </a>
                                </div>
                                <div class="table-cell">
                                    <a title="{{ trans('neptune::pro/references.index.delete_title_cta') }}" data-url="{{route('neptune.pro.publications.destroy', [$selected_person->slug, $reference->id])}}"
                                       href="" class="deleteResource">
                                        <i class="actionIcon actionIcon--danger" data-feather="trash"></i>
                                    </a>
                                </div>
                            </div>
                        @empty
                            <div class="table-row">
                                <div class="table-cell">
                                    {{ trans('neptune::pro/references.index.none_found') }} <a href="{{ route('neptune.pro.publications.create', $selected_person->slug) }}">{{ trans('neptune::pro/references.index.add_first_cta') }}</a>
                                </div>
                            </div>
                        @endforelse
                    </div>
                </div>
                {{$references->links()}}
            </div>
        </div>
    </div>
@stop
@section('footer')
    @parent
@stop
