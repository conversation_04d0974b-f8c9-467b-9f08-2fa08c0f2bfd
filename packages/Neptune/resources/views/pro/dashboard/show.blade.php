@extends('neptune::pro.components.layout')
@section('header')
@endsection
@section('content')
    <div class="subheader">
        <div class="row">
            <div class="col-md-8">
                <h1>{{ trans('neptune::pro/dashboard.heading') }}</h1>
                <div class="explainText">
                    {{ trans('neptune::pro/dashboard.intro_text') }}
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="proCard">
                <div class="stat">
                    @if( empty(strip_tags($selected_person->user_bio)) )
                        <div class="stat_icon stat_icon--alert">
                            <i  data-feather="alert-circle"></i>
                        </div>
                        <div class="stat_text">{{ trans('neptune::pro/dashboard.no_bio') }}</div>
                        <div class="stat_link">
                            <a href="{{ route('neptune.pro.bio.edit', $selected_person->slug) }}">{{ trans('neptune::pro/dashboard.fill_bio_cta') }}</a>
                        </div>
                    @else
                        <div class="stat_icon stat_icon--success">
                            <i  data-feather="check-square"></i>
                        </div>
                        <div class="stat_text">
                            {{ trans('neptune::pro/dashboard.have_bio') }}
                        </div>
                        <div class="stat_link">
                            <a href="{{ route('neptune.pro.bio.edit', $selected_person->slug) }}">{{ trans('neptune::pro/dashboard.edit_bio_cta') }}</a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="proCard">
                <div class="stat">
                    <div class="stat_number">{{ $uploaded_images_count }}</div>
                    <div class="stat_text">{{ trans('neptune::pro/dashboard.user_images') }}</div>
                    <div class="stat_link">
                    @if($uploaded_images_count > 0)
                        <a href="{{ route('neptune.pro.images.index', $selected_person->slug) }}">{{ trans('neptune::pro/dashboard.all_images_anchor') }}</a>
                    @else
                        <a href="{{ route('neptune.pro.images.index', $selected_person->slug) }}">{{ trans('neptune::pro/dashboard.add_now_anchor') }}</a>
                    @endif
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="proCard">
                <div class="stat">
                    <div class="stat_number">{{ $created_plays_count }}</div>
                    <div class="stat_text">{{ trans('neptune::pro/dashboard.user_plays') }}</div>
                    <div class="stat_link">
                    @if($created_plays_count > 0)
                        <a href="{{ route('neptune.pro.plays.index', $selected_person->slug) }}">{{ trans('neptune::pro/dashboard.all_plays_anchor') }}</a>
                    @else
                        <a href="{{ route('neptune.pro.plays.create', $selected_person->slug) }}">{{ trans('neptune::pro/dashboard.add_now_anchor') }}</a>
                    @endif
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="proCard">
                <div class="stat">
                    <div class="stat_number">{{ $created_movies_count }}</div>
                    <div class="stat_text">{{ trans('neptune::pro/dashboard.user_movies') }}</div>
                    <div class="stat_link">
                    @if($created_movies_count > 0)
                        <a href="{{ route('neptune.pro.movies.index', $selected_person->slug) }}">{{ trans('neptune::pro/dashboard.all_movies_anchor') }}</a>
                    @else
                        <a href="{{ route('neptune.pro.movies.create', $selected_person->slug) }}">{{ trans('neptune::pro/dashboard.add_now_anchor') }}</a>
                    @endif
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="proCard">
                <div class="stat">
                    <div class="stat_number">{{ $created_tvShows_count }}</div>
                    <div class="stat_text">{{ trans('neptune::pro/dashboard.user_tvShows') }}</div>
                    <div class="stat_link">
                    @if($created_tvShows_count > 0)
                        <a href="{{ route('neptune.pro.tvShows.index', $selected_person->slug) }}">{{ trans('neptune::pro/dashboard.all_tvShows_anchor') }}</a>
                    @else
                        <a href="{{ route('neptune.pro.tvShows.create', $selected_person->slug) }}">{{ trans('neptune::pro/dashboard.add_now_anchor') }}</a>
                    @endif
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="proCard">
                <div class="stat">
                    <div class="stat_number">{{ $created_endeavours_count }}</div>
                    <div class="stat_text">{{ trans('neptune::pro/dashboard.user_endeavours') }}</div>
                    <div class="stat_link">
                        @if($created_endeavours_count > 0)
                            <a href="{{ route('neptune.pro.endeavours.index', $selected_person->slug) }}">{{ trans('neptune::pro/dashboard.all_endeavours_anchor') }}</a>
                        @else
                            <a href="{{ route('neptune.pro.endeavours.create', $selected_person->slug) }}">{{ trans('neptune::pro/dashboard.add_now_anchor') }}</a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="proCard">
                <div class="stat">
                    <div class="stat_number">{{ $user_submitted_videos }}</div>
                    <div class="stat_text">{{ trans('neptune::pro/dashboard.user_videos') }}</div>
                    <div class="stat_link">
                    @if($user_submitted_videos > 0)
                        <a href="{{ route('neptune.pro.videos.index', $selected_person->slug) }}">{{ trans('neptune::pro/dashboard.all_videos_anchor') }}</a>
                    @else
                        <a href="{{ route('neptune.pro.videos.create', $selected_person->slug) }}">{{ trans('neptune::pro/dashboard.add_now_anchor') }}</a>
                    @endif
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="proCard">
                <div class="stat">
                    <div class="stat_number">{{ $user_submitted_references }}</div>
                    <div class="stat_text">{{ trans('neptune::pro/dashboard.user_references') }}</div>
                    <div class="stat_link">
                    @if($user_submitted_references > 0)
                        <a href="{{ route('neptune.pro.publications.index', $selected_person->slug) }}">{{ trans('neptune::pro/dashboard.all_references_anchor') }}</a>
                    @else
                        <a href="{{ route('neptune.pro.publications.create', $selected_person->slug) }}">{{ trans('neptune::pro/dashboard.add_now_anchor') }}</a>
                    @endif
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="proCard">
                <div class="stat">
                    <div class="stat_number">{{ $uploaded_seminars_count }}</div>
                    <div class="stat_text">{{ trans('neptune::pro/dashboard.user_seminars') }}</div>
                    <div class="stat_link">
                    @if($uploaded_seminars_count > 0)
                        <a href="{{ route('neptune.pro.seminars.index', $selected_person->slug) }}">{{ trans('neptune::pro/dashboard.all_seminars_anchor') }}</a>
                    @else
                        <a href="{{ route('neptune.pro.seminars.create', $selected_person->slug) }}">{{ trans('neptune::pro/dashboard.add_now_anchor') }}</a>
                    @endif
                    </div>
                </div>
            </div>
        </div>
{{--        <div class="col-md-6 col-lg-4 col-xl-3">--}}
{{--            <div class="proCard">--}}
{{--                <div class="stat">--}}
{{--                    <div class="stat_number">{{ $all_followers }}</div>--}}
{{--                    <div class="stat_text">{{ trans('neptune::pro/dashboard.all_followers') }}</div>--}}
{{--                </div>--}}
{{--            </div>--}}
{{--        </div>--}}
    </div>
@stop
@section('footer')
    @parent
@stop
