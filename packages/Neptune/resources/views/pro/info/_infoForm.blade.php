<div class="row">
    <div class="col-lg-10 col-xl-6">
        @include('neptune::pro.components.messages.errors')
        <div class="proCard">
            <div class="form-group {!! $errors->first('user_height') ? ' has-error' : '' !!}">
                {{Form::label('user_height', trans('neptune::pro/info.height_label'))}}
                {{Form::text('user_height', null, ['class' => 'form-control inputmask', 'data-inputmask' => "'mask': '9{2,3}'", 'data-inputmask-clearmaskonlostfocus' => 'false'])}}
                <div class="input-error">{{ $errors->first('user_height') }}</div>
            </div>
            {{Form::label('user_acting_age_from', trans('neptune::pro/info.age_label'))}}
            <div class="row">
                <div class="col-6">
                    <div class="form-group {!! $errors->first('user_acting_age_from') ? ' has-error' : '' !!}">
                        {{Form::label('user_acting_age_from', trans('neptune::pro/info.from_label'))}}
                        {{Form::text('user_acting_age_from', null, ['class' => 'form-control inputmask', 'data-inputmask' => "'mask': '9{1,2}'", 'data-inputmask-clearmaskonlostfocus' => 'false'])}}
                        <div class="input-error">{{ $errors->first('user_acting_age_from') }}</div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="form-group {!! $errors->first('user_acting_age_to') ? ' has-error' : '' !!}">
                        {{Form::label('user_acting_age_to', trans('neptune::pro/info.to_label'))}}
                        {{Form::text('user_acting_age_to', null, ['class' => 'form-control inputmask', 'data-inputmask' => "'mask': '9{1,2}'", 'data-inputmask-clearmaskonlostfocus' => 'false'])}}
                        <div class="input-error">{{ $errors->first('user_acting_age_to') }}</div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-6">
                    <div class="form-group {!! $errors->first('user_eye_colour') ? ' has-error' : '' !!}">
                        {{Form::label('user_eye_colour', trans('neptune::pro/info.eye_colour_label'))}}
                        {{Form::text('user_eye_colour', null, ['class' => 'form-control'])}}
                        <div class="input-error">{{ $errors->first('user_eye_colour') }}</div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="form-group {!! $errors->first('user_hair_colour') ? ' has-error' : '' !!}">
                        {{Form::label('user_hair_colour', trans('neptune::pro/info.hair_colour_label'))}}
                        {{Form::text('user_hair_colour', null, ['class' => 'form-control'])}}
                        <div class="input-error">{{ $errors->first('user_hair_colour') }}</div>
                    </div>
                </div>
            </div>
            <div class="form-group {!! $errors->first('user_facebook') ? ' has-error' : '' !!}">
                {{Form::label('user_facebook', trans('neptune::pro/info.facebook_label'))}}
                <div class="explainForm">{{ trans('neptune::pro/info.url_explain') }}</div>
                {{Form::text('user_facebook', null, ['class' => 'form-control'])}}
                <div class="input-error">{{ $errors->first('user_facebook') }}</div>
            </div>
            <div class="form-group {!! $errors->first('user_facebook_page') ? ' has-error' : '' !!}">
                {{Form::label('user_facebook_page', trans('neptune::pro/info.facebook_page_label'))}}
                <div class="explainForm">{{ trans('neptune::pro/info.url_explain') }}</div>
                {{Form::text('user_facebook_page', null, ['class' => 'form-control'])}}
                <div class="input-error">{{ $errors->first('user_facebook_page') }}</div>
            </div>
            <div class="form-group {!! $errors->first('user_instagram') ? ' has-error' : '' !!}">
                {{Form::label('user_instagram', trans('neptune::pro/info.instagram_label'))}}
                <div class="explainForm">{{ trans('neptune::pro/info.url_explain') }}</div>
                {{Form::text('user_instagram', null, ['class' => 'form-control'])}}
                <div class="input-error">{{ $errors->first('user_instagram') }}</div>
            </div>
            <div class="form-group {!! $errors->first('user_imdb') ? ' has-error' : '' !!}">
                {{Form::label('user_imdb', trans('neptune::pro/info.imdb_label'))}}
                <div class="explainForm">{{ trans('neptune::pro/info.url_explain') }}</div>
                {{Form::text('user_imdb', null, ['class' => 'form-control'])}}
                <div class="input-error">{{ $errors->first('user_imdb') }}</div>
            </div>
            <div class="form-group {!! $errors->first('user_website') ? ' has-error' : '' !!}">
                {{Form::label('user_website', trans('neptune::pro/info.website_label'))}}
                <div class="explainForm">{{ trans('neptune::pro/info.url_explain') }}</div>
                {{Form::text('user_website', null, ['class' => 'form-control'])}}
                <div class="input-error">{{ $errors->first('user_website') }}</div>
            </div>
            <div class="form-group {!! $errors->first('user_twitter') ? ' has-error' : '' !!}">
                {{Form::label('user_twitter', trans('neptune::pro/info.twitter_label'))}}
                <div class="explainForm">{{ trans('neptune::pro/info.url_explain') }}</div>
                {{Form::text('user_twitter', null, ['class' => 'form-control'])}}
                <div class="input-error">{{ $errors->first('user_twitter') }}</div>
            </div>
            <div class="form-group {!! $errors->first('user_youtube') ? ' has-error' : '' !!}">
                {{Form::label('user_youtube', trans('neptune::pro/info.youtube_label'))}}
                <div class="explainForm">{{ trans('neptune::pro/info.url_explain') }}</div>
                {{Form::text('user_youtube', null, ['class' => 'form-control'])}}
                <div class="input-error">{{ $errors->first('user_youtube') }}</div>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-md-12"><br/>
        <div class="form-group">
            <button type="submit" class="btn btn-primary">{{ trans('neptune::pro/info.submit_cta') }}</button>
        </div>
    </div>
</div>
@section('footer')
    @parent
@stop


