<div class="row">
    <div class="col-lg-10 col-xl-8">
        <div class="pageCont">
            @include('neptune::pro.components.messages.errors')
            <div class="form-group {!! $errors->first('title') ? ' has-error' : '' !!}">
                {{Form::label('title', trans('neptune::pro/seminars.create.title_label'))}}
                <div class="explainForm">{{ trans('neptune::pro/seminars.create.title_explain') }}</div>
                {{Form::text('title', null, ['class' => 'form-control'])}}
                <div class="input-error">{{ $errors->first('title') }}</div>
            </div>
            <div class="form-group {!! $errors->first('additional_info') ? ' has-error' : '' !!}">
                {{Form::label('additional_info', trans('neptune::pro/seminars.create.additional_info_label'))}}
                <div class="explainForm">{{ trans('neptune::pro/seminars.create.additional_info_explain') }}</div>
                {{Form::text('additional_info', null, ['class' => 'form-control'])}}
                <div class="input-error">{{ $errors->first('additional_info') }}</div>
            </div>
            {{Form::label('year_from', trans('neptune::pro/seminars.create.years_label'))}}
            <div class="row">
                <div class="col-6">
                    <div class="form-group {!! $errors->first('year_from') ? ' has-error' : '' !!}">
                        {{Form::label('year_from', trans('neptune::pro/seminars.create.from_label'))}}
                        {{Form::text('year_from', null, ['class' => 'form-control inputmask', 'data-inputmask' => "'mask': '9{0,4}'", 'data-inputmask-clearmaskonlostfocus' => 'false'])}}
                        <div class="input-error">{{ $errors->first('year_from') }}</div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="form-group {!! $errors->first('year_to') ? ' has-error' : '' !!}">
                        {{Form::label('year_to', trans('neptune::pro/seminars.create.to_label'))}}
                        {{Form::text('year_to', null, ['class' => 'form-control inputmask', 'data-inputmask' => "'mask': '9{0,4}'", 'data-inputmask-clearmaskonlostfocus' => 'false'])}}
                        <div class="input-error">{{ $errors->first('year_to') }}</div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                {{Form::submit(trans('neptune::pro/seminars.create.submit_cta'), ['class="btn btn-primary"'])}}
            </div>
        </div>
    </div>
</div>

@section('footer')
    @parent
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
@stop
