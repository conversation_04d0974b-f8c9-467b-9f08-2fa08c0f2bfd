@extends('neptune::pro.components.layout')
@section('content')
    <div class="subheader">
        <div class="row">
            <div class="col-md-9">
                <h1>{{ trans('neptune::pro/seminars.index.heading') }}</h1>
                <div class="explainText">
                    {{ sprintf(trans('neptune::pro/seminars.index.intro_text'), $selected_person->fullName) }}
                </div>
            </div>
            <div class="col-md-3 text-right">
                <a href="{{ route('neptune.pro.seminars.create', $selected_person->slug) }}"
                   class="btn btn-primary">{{ trans('neptune::pro/seminars.index.add_cta') }}</a>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="pageCont">
                @include('neptune::pro.components.messages.errors')
                <div class="table-responsive">
                    <div class="table">
                        @forelse($seminars = $logged_user->seminarsUploaded($selected_person)->paginate(10) as $seminar)
                            <div class="table-row">
                                <div class="table-cell table-cell--full ">
                                    <a href="{{ route('neptune.pro.seminars.edit', [$selected_person->slug, $seminar->id]) }}">{{ $seminar->title }}</a>
                                    <br>
                                    {{ $seminar->additional_info }}
                                </div>
                                <div class="table-cell  ">
                                    {{ $seminar->type }}
                                </div>
                                <div class="table-cell">
                                    <a title="{{ trans('neptune::pro/seminars.index.edit_title_cta') }}" href="{{ route('neptune.pro.seminars.edit', [$selected_person->slug, $seminar->id]) }}">
                                        <i  class="actionIcon" data-feather="edit-3"></i>
                                    </a>
                                </div>
                                <div class="table-cell">
                                    <a title="{{ trans('neptune::pro/seminars.index.delete_title_cta') }}" data-url="{{route('neptune.pro.seminars.destroy', [$selected_person->slug, $seminar->id])}}"
                                       href="" class="deleteResource">
                                        <i class="actionIcon actionIcon--danger" data-feather="trash"></i>
                                    </a>
                                </div>
                                <div class="table-cell">
                                    @if($seminar->banned == true)
                                        <i data-toggle="tooltip" data-placement="top"  title="Υπήρξε πρόβλημα με το σεμινάριο. Για περισσότερες πληροφορίες επικοινώνησε <EMAIL>" class="menuIcon" data-feather="alert-triangle"></i>
                                    @endif
                                </div>
                            </div>
                        @empty
                            <div class="table-row">
                                <div class="table-cell">
                                    {{ trans('neptune::pro/seminars.index.none_found') }}  <a href="{{ route('neptune.pro.seminars.create', $selected_person->slug) }}">{{ trans('neptune::pro/seminars.index.add_first_cta') }}</a>
                                </div>
                            </div>
                        @endforelse
                    </div>
                </div>
                {{$seminars->links()}}
            </div>
        </div>
    </div>
@stop
@section('footer')
    @parent
@stop
