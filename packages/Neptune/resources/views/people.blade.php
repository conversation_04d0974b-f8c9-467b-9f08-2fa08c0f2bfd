@extends('components.layout')
@section('header')
    @endsection
@section('content')

    <div class="bck    padding-large    ">
        <div class="container  ">
            <div class="row   ">
                <div class="col-md-12  ">
                    @include('neptune::components.header')
                </div>
            </div>
        </div>
        <div class="container  ">
            <div class="row   ">
                <div class="col-md-6 col-md-offset-3   ">
                    <div class="empty-state text-center">
                        <i class="pe-7s-cloud"></i>
                        <h1>Δεν έχετε επιλέξει να ακολουθείτε κάποιον ηθοποιο ακόμα. </h1>
                    </div>
                    <div class="table">
                        <div class="table-row user-list-item">
                            <div class="table-cell  table-cell-image  ">
                                <a href="#">
                                    <img class="circle  circle-medium    b-lazy_"
                                         src="https://www.unstage.gr/images/thumbs/people/enke-fezollari-MK2f8v-200x_.jpeg?token=e93a7895568132e50398f9aca34c9875"
                                         style="background-size:cover; background-position:center;"/>
                                </a>
                            </div>
                            <div class="table-cell align left">
                                <h4 class="text margin-none   h5     ">
                                    <a href="#">Ένκε Φεζολλάρι</a>
                                    <div class="margin-top-small small links">Συμμετέχει: <a href="#" >Εξηνταβελόνης</a>, <a href="#" >Εξηνταβελόνης</a></div>
                                </h4>
                            </div>
                            <div class="table-cell text-right">
                                <i class="removeItem pe-7s-close-circle"  data-theater="1" data-user="1" ></i>
                            </div>
                        </div>
                        <div class="table-row user-list-item">
                            <div class="table-cell  table-cell-image  ">
                                <a href="#">
                                    <img class="circle  circle-medium    b-lazy_"
                                         src="https://www.unstage.gr/images/thumbs/people/enke-fezollari-MK2f8v-200x_.jpeg?token=e93a7895568132e50398f9aca34c9875"
                                         style="background-size:cover; background-position:center;"/>
                                </a>
                            </div>
                            <div class="table-cell align left">
                                <h4 class="text margin-none   h5     ">
                                    <a href="#">Ένκε Φεζολλάρι</a>
                                </h4>
                            </div>
                            <div class="table-cell text-right">
                                <i class="removeItem pe-7s-close-circle"  data-theater="1" data-user="1" ></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop
@section('footer')
    @parent
    <script type="text/javascript">
        var token = $('meta[name=csrf-token]').attr("content");

        $(".removeItem").click(function (e) {
            e.preventDefault();
            var url = ''; // ajax post url
            var theater = $(this).data('theater');
            var user = $(this).data('user'); // if needed
            var element = this;

            $.ajax({
                type: 'DELETE',
                url: url,
                dataType: 'json',
                headers: {
                    "x-csrf-token": token
                },
                data: {
                    theater: theater,
                    user: user
                }
            }).success(function (data) {
                if (data.status) {
                    $(element).closest('.user-list-item').hide('fast');
                }
                else // status 0
                {

                }
            }).error(function (data) {
                $(element).closest('.user-list-item').hide('fast');
            })
        });
    </script>
@stop