@extends('components.layout')
@section('header')
    @endsection
@section('content')

    <div class="     padding-large    ">
        <div class="container  ">
            <div class="row   ">
                <div class="col-md-12  ">
                    @include('neptune::components.header')
                </div>
            </div>
        </div>
        <div class="container  ">
            <div class="row   ">
                <div class="col-md-6 col-md-offset-3   ">
                    <div class="table">
                        <div class="table-row user-list-item">
                            <div class="table-cell align left">
                                <h4 class="text margin-none   h5     ">
                                    <a href="#">Bar theatre</a>
                                </h4>
                            </div>
                            <div class="table-cell text-right">
                                <i class="pe-7s-plus"></i>
                            </div>
                        </div>
                        <div class="table-row user-list-item">
                            <div class="table-cell align left">
                                <h4 class="text margin-none   h5     ">
                                    <a href="#">Musical</a>
                                </h4>
                            </div>
                            <div class="table-cell text-right">
                                <i class="removeItem pe-7s-check" data-genre="1" data-user="1"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop
@section('footer')
    @parent
    <script type="text/javascript">
        var token = $('meta[name=csrf-token]').attr("content");

        $(".removeItem").click(function (e) {
            e.preventDefault();
            var url = ''; // ajax post url
            var genre = $(this).data('genre');
            var user = $(this).data('user'); // if needed
            var element = this;

            $.ajax({
                type: 'DELETE',
                url: url,
                dataType: 'json',
                headers: {
                    "x-csrf-token": token
                },
                data: {
                    genre: genre,
                    user: user
                }
            }).success(function (data) {
                if (data.status) {
                    $(element).removeClass('pe-7s-check');
                    $(element).addClass('pe-7s-plus');

                }
                else // status 0
                {

                }
            }).error(function (data) {
                $(element).removeClass('pe-7s-check');
                $(element).addClass('pe-7s-plus');
            })
        });
    </script>
@stop