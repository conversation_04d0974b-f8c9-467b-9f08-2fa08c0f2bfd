@extends('components.layout')

@section('content')

    <section id="login-content">
        <div class="container">
            <div class="row">
                {{--<div class="col-md-4 col-md-offset-0 col-sm-6 col-sm-offset-3 col-lg-3 col-lg-offset-0 col-ms-8 col-ms-offset-2">--}}
                <div class="col-xs-12 col-ms-8 col-ms-offset-2 col-sm-6    col-sm-offset-3 col-lg-4 col-lg-offset-4 ">
                    <div class="auth-container">
                        <h1 class="h3">{!! trans('neptune::register.page_title') !!}</h1>
                        <p>{!! trans('neptune::register.header_text') !!}</p>
{{--                        <a class="login-socials login-facebook"--}}
{{--                           href="{{ route('neptune.redirect_to_provider', ['provider' => 'facebook']) }}">--}}
{{--                            Εγγραφή με facebook<i class="fa fa-facebook"></i>--}}
{{--                        </a>--}}
{{--                        <a class="login-socials login-twitter"--}}
{{--                           href="{{ route('neptune.redirect_to_provider', ['provider' => 'twitter']) }}">--}}
{{--                            Εγγραφή με twitter <i class="fa fa-twitter"></i>--}}
{{--                        </a>--}}
                        <a class="login-socials login-google"
                           href="{{ route('neptune.redirect_to_provider', ['provider' => 'google']) }}">
                            Εγγραφή με google<i class="fa fa-google"></i>
                        </a>
                        <div class="or"> ή</div>

                        <form role="form" method="POST" action="{{ route('register.handle') }}">
                            {{ csrf_field() }}


                            <div class="form-email">
                                <div class="form-group{{ $errors->has('email') ? ' has-error' : '' }}">
                                    <input id="email" type="email" class="form-control" name="email"
                                           placeholder="{!! trans('neptune::register.label_email') !!}"
                                           value="{{ old('email') }}">
                                    @if ($errors->has('email'))
                                        <span class="help-block">
                                        <strong>{{ $errors->first('email') }}</strong>
                                    </span>
                                    @endif
                                </div>
                            </div>
                            <div class="form-password">
                                <div class="form-group{{ $errors->has('password') ? ' has-error' : '' }}">
                                    <input id="password" type="password" class="form-control" name="password"
                                           placeholder="{!! trans('neptune::register.label_password') !!}">
                                    @if ($errors->has('password'))
                                        <span class="help-block">
                                        <strong>{{ $errors->first('password') }}</strong>
                                    </span>
                                    @endif
                                </div>
                            </div>
                            <div class="form-password">
                                <div class="form-group{{ $errors->has('password_confirmation') ? ' has-error' : '' }}">
                                    <input id="password-confirm" type="password" class="form-control"
                                           name="password_confirmation" placeholder="{!! trans('neptune::register.label_password_confirm') !!}">
                                    @if ($errors->has('password_confirmation'))
                                        <span class="help-block">
                                        <strong>{{ $errors->first('password_confirmation') }}</strong>
                                    </span>
                                    @endif
                                </div>
                            </div>


                            <div class="margin-top-medium">
                                <button class="btn btn-submit">Εγγραφή</button>
                            </div>
                            <div class="auth-link  ">
                                Έχεις ήδη λογαριασμό; <a href="{{ url('/login') }}"> Είσοδος εδώ.</a>
                            </div>
                            <div class="auth-link  ">
                                Κάνοντας εγγραφή αποδέχεσαι τους <a target='_blank' href="{{ route('terms.index') }}"> Όρους Χρήσης και Πολιτική Απορρήτου</a> μας.
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- END / FEATURED REQUEST TEACHER -->

@endsection
