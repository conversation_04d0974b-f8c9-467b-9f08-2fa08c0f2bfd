<div class="modal fade"
     @if((isset($errors) && ($errors->has('email') || $errors->has('password')) && !Request::is('epikoinonia')  && !Request::is('sinergasies') && !Request::is('login') && !Request::is('join') && !Request::is('*reset*')))
     data-show="true"
     @endif
     id="modalLogin" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog--auth" role="document">
        <div class="modal-content">
            <div class="modal-header modal-header--alt">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="myModalLabel">&nbsp</h4>
            </div>
            <div class="modal-body modal-body--alt">
                <div class="auth-container">
                    <h1 class="h3"></h1>

{{--                    <a class="login-socials login-facebook"--}}
{{--                       href="{{ route('neptune.redirect_to_provider', ['provider' => 'facebook']) }}">--}}
{{--                        Είσοδος με facebook <i class="fa fa-facebook"></i>--}}
{{--                    </a>--}}
{{--                    <a class="login-socials login-twitter"--}}
{{--                       href="{{ route('neptune.redirect_to_provider', ['provider' => 'twitter']) }}">--}}
{{--                        Είσοδος με twitter <i class="fa fa-twitter"></i>--}}
{{--                    </a>--}}
                    <a class="login-socials login-google"
                       href="{{ route('neptune.redirect_to_provider', ['provider' => 'google']) }}">
                        Είσοδος με google <i class="fa fa-google"></i>
                    </a>
                    <div class="or"> ή</div>
                    <div class="form-login">
                        <form role="form" method="POST" action="{{ route('login.handle') }}">
                            {{ csrf_field() }}
                            <div class="form-email">
                                <div class="form-group{{ (isset($errors) && $errors->has('email')) ? ' has-error' : '' }}">
                                    <input id="emailModal" type="email" class="form-control" name="email"
                                           placeholder="Email"
                                           value="{{ old('email') }}">
                                    @if(isset($errors))
                                        {!!  $errors->first('email', '<span class="help-block">:message</span>') !!}
                                    @endif
                                </div>
                            </div>
                            <div class="form-password">
                                <div class="form-group{{ (isset($errors) && $errors->has('password')) ? ' has-error' : '' }}">
                                    <input id="passwordModal" type="password" class="form-control" name="password"
                                           placeholder="Κωδικός">
                                    @if(isset($errors))
                                        {!!  $errors->first('password', '<span class="help-block">:message</span>') !!}
                                    @endif
                                </div>
                            </div>
                            {{--<div class="form-check">--}}
                            {{--<input type="checkbox" id="check" name="remember">--}}
                            {{--<label for="check">--}}
                            {{--<i class="icon md-check-2"></i>--}}
                            {{--Να με θυμάσαι</label>--}}
                            {{--</div>--}}
                            <div class="margin-top-medium">
                                <button class="btn btn-submit">Είσοδος</button>
                            </div>
                            <div class="auth-link">
                                Δεν έχεις λογαριασμό;
                                <a href="{{ url('/join') }}">
                                    Εγγραφή εδώ.
                                </a>
                            </div>
                            <div class="auth-link">
                                <a href="{{ url('/password/reset') }}">Ξεχάσες τον κωδικό σου;</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
