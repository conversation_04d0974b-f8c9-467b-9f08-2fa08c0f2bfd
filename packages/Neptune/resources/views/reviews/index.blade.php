@extends('components.layout')

@section('content')
    @include('neptune::components.header')
    <div>
        <div class="container  ">
            <div class="row   ">
                <div class="col-md-8 col-md-offset-2  ">
                    @forelse($reviews as $play)
                        <div class="table">
                            <div class="table-row user-list-item">
                                <div class="table-cell  table-cell-image table-cell__noborder  ">
                                    <a href="{!! route('plays.show', ['slug' => $play->slug]) !!}">
                                        <div class="play-image-small  b-lazy   "
                                             data-src="{{ $play->mainImage ? asset(Croppa::url($play->mainImage->filename,800,null)):'' }}">
                                            &nbsp;
                                        </div>
                                    </a>
                                </div>
                                <div class="table-cell table-cell--middle text-left table-cell-full table-cell__noborder ">
                                    <a href="{!! route('plays.show', ['slug' => $play->slug]) !!}"
                                       class="item-title">{{ $play->title }}</a>
                                    <p class="text small item-descr ">
                                        @if(count($play->genres) > 0)
                                            <span class="vertical-seperator">
                                            <span class="comma-seperated">
                                            @foreach($play->genres as $genre)
                                                    <span>{{ $genre->name }}</span>
                                                @endforeach
                                            </span>
                                           </span>
                                        @endif
                                        @if($play->year !='')
                                            <span class="vertical-seperator">{{$play->year}}</span>
                                        @endif
                                    <div class="  userCritic__rating">
                                        <i class="fa fa-star"></i>
                                        {{ auth('users')->user()->getRating($play->id) }}<span class=" userCritic__rating--total">/10</span>
                                    </div>
                                    </p>
                                </div>
                                <div class="table-cell   table-cell--middle table-cell__noborder">
                                    <div class="rating rating--small user-comment__actions--color">
                                        <a href="{{ route('neptune.reviews.create', $play->slug) }}"><i class="fa fa-pencil" aria-hidden="true"></i></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="user-reviews__item">
                            <div>
                                {!! $play->pivot->review !!}
                            </div>
                            <div class="user-comment__actions user-comment__actions--me ">
                                <i class="fa fa-thumbs-o-up" aria-hidden="true"></i>{{ $play->pivot->upvotes }}&nbsp;&nbsp;
                            </div>
                        </div>
                    @empty
                        <div class="table">
                            <div class="table-cell text-center  table-cell-middle  ">
                                <div class="empty-state">
                                    <img src="{{unstageAsset('web-icons/star.svg')}}" width="120"/><br><br>
                                    <h4>Δε βρέθηκαν κριτικές</h4>
                                    <p class="empty-state_subtitle">Μοιράσου τη γνώμη σου για τις παραστάσεις που έχεις
                                        δει με τα υπόλοιπα μέλη του unstage</p>
                                    <br>
                                    <a href="{{ route('plays.index') }}" class="btn  btn-submit btn-submit--inline">Βρες
                                        παραστάσεις</a>
                                </div>
                            </div>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>
    </div>
@stop
@section('footer')
    @parent
@stop
