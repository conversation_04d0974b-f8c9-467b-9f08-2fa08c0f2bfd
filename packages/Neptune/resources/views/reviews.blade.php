@extends('components.layout')

@section('content')

    <div class="bck    padding-large    ">
        <div class="container  ">
            <div class="row   ">
                <div class="col-md-12  ">
                    @include('neptune::components.header')
                </div>
            </div>
        </div>
        <div class="container  ">
            <div class="row   ">
                <div class="col-md-8 col-md-offset-2   ">
                    <div class="empty-state text-center hide">
                        <i class="pe-7s-cloud"></i>
                        <h1>Δεν έχετε επιλέξει να ακολουθείτε κάποιον ηθοποιο ακόμα. </h1>
                    </div>
                    <table  >
                        <tr class="table-row_ user-list-item">
                            <td class="table-cell  table-cell-image align-top ">
                                <div class="rating">8</div>
                            </td>
                            <td class="table-cell_ align left">
                                <h4 class="text margin-none   h5     ">
                                    <a href="#">Εξηνταβελόνης</a>
                                </h4>
                            </td>
                            <td class="table-cell_ text-right align-top">
                                <i class="pe-7s-pen"></i>
                                <i class="removeItem pe-7s-close-circle" data-theater="1" data-user="1"></i>
                            </td>
                        </tr>
                        <tr class="border-bottom">
                            <td colspan="3">
                                <p class="text small padding-top-small">Στο θέατρο Βράχων το σκηνικό είναι λιτό, όπως υπήρξε και ολόκληρος ο βίος του
                                    Σωκράτη: μια καρέκλα και μια κλεψύδρα, μέσα από την οποία ο ήχος της σταγόνας
                                    συνοδεύει ως μοναδικό εφέ τον τελευταίο λόγο του φιλόσοφου, την περίφημη
                                    «Απολογία» του.

                                    Η παράσταση δεν αποτελεί ακριβώς μονόλογο, είναι περισσότερο μια αναπαράσταση
                                    της δίκης που έλαβε χώρα το 399 π.Χ. στο δικαστήριο της Ηλιαίας με αποτέλεσμα τη
                                    θανατική καταδίκη. Οι κατηγορίες με τις οποίες ήρθε αντιμέτωπος ο Σωκράτης ήταν
                                    η ασέβεια προς τους θεούς, η εισαγωγή καινών δαιμόνιων και η διαφθορά των νέων,
                                    ενώ οι κατήγοροί του ήταν ο Άνυτος, ο Λύκωνας και κυρίως ο Μέλητος. Το κείμενο
                                    παρουσιάζεται με υπέρτιτλους μεταφρασμένο στα νέα Ελληνικά και στα Αγγλικά.

                                    Με βάση τη σωκρατική απολογία και με γνώμονα τον «Λόγο» μαθαίνουμε εις βάθος τη
                                    φιλοσοφία και τη βιοθεωρία ενός ανθρώπου που την ακολούθησε πιστά κι αμετακίνητα
                                    μέχρι το τέλος.

                                    Για τον φιλόσοφο, τον άνθρωπο δεν πρέπει να απασχολεί το αν θα ζήσει ή αν θα
                                    πεθάνει, αλλά ο έλεγχος των πράξεων και της ηθικής του. Αν κάποιος αφοσιωθεί σε
                                    έναν ανώτερο σκοπό, δεν λογαριάζει και δεν φοβάται τον θάνατο. Είναι ντροπή να
                                    συσσωρεύουμε υλικά αγαθά αφού δεν βρίσκεται εκεί η ευτυχία, παρά μόνο στην
                                    αληθινή γνώση και τον αυτοπροσδιορισμό. Το υπαρξιακό αυτό ερώτημα είναι μέχρι
                                    σήμερα επίκαιρο - και μάλλον διαχρονικά αναπάντητο - αφού παρά τα άλματα προόδου
                                    σε ζητήματα της επιστήμης και της τεχνολογίας, ο άνθρωπος παραμένει θλιβερά
                                    ανεπαρκής όσον αφορά τη ενσυνείδητη κατανόηση του εαυτού του.</p>
                            </td>
                    </table>
                </div>
            </div>
        </div>
    </div>
@stop
@section('footer')
    @parent
    <script type="text/javascript">
        var token = $('meta[name=csrf-token]').attr("content");

        $(".removeItem").click(function (e) {
            e.preventDefault();
            var url = ''; // ajax post url
            var theater = $(this).data('theater');
            var user = $(this).data('user'); // if needed
            var element = this;

            $.ajax({
                type: 'DELETE',
                url: url,
                dataType: 'json',
                headers: {
                    "x-csrf-token": token
                },
                data: {
                    theater: theater,
                    user: user
                }
            }).success(function (data) {
                if (data.status) {
                    $(element).closest('.user-list-item').hide('fast');
                }
                else // status 0
                {

                }
            }).error(function (data) {
                $(element).closest('.user-list-item').hide('fast');
            })
        });
    </script>
@stop