<?php

// =======================================================================//
//                              Landing pages
// =======================================================================//
Route::get('/', 'Pro\Open\LandingsController@show')->name('neptune.pro.landing');
Route::get('/join', 'Pro\Open\LandingsController@showRegister')->name('neptune.pro.register.show');
Route::post('/join', 'Pro\Open\LandingsController@handleRegister')->name('neptune.pro.register.handle');
Route::get('login', '\Packages\Neptune\app\Http\Controllers\Auth\LoginProController@showLoginForm')->name('neptune.pro.login.show');
Route::post('login', '\Packages\Neptune\app\Http\Controllers\Auth\LoginProController@login')->name('neptune.pro.login.handle');
