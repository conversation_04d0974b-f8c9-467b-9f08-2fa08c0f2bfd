<?php

namespace Packages\Neptune\app\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\MailMessage;
use Packages\Neptune\app\Models\User;
use App\Models\Play;

class WatchlistReminder extends Notification implements ShouldQueue
{
    use Queueable;

    protected $user;
    protected $remindersStarting;
    protected $remindersEnding;

    /**
     * Create a notification instance.
     *
     * @param  string $token
     * @return void
     */
    public function __construct(User $user, $remindersStarting, $remindersEnding)
    {
        $this->user = $user;
    	$this->remindersStarting	= $remindersStarting;
    	$this->remindersEnding		= $remindersEnding;
    }

    /**
     * Get the notification's channels.
     *
     * @param  mixed $notifiable
     * @return array|string
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Build the mail representation of the notification.
     *
     * @param  mixed $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {

        $startingPlays = Play::with([
            'mainImage',
            'theatre',
            'genres',
        ])->whereIn('id',   $this->remindersStarting)->get();
        $endingPlays = Play::with([
            'mainImage',
            'theatre',
            'genres',
        ])->whereIn('id', $this->remindersEnding)->get();

        $data['startingPlays'] = $startingPlays;
        $data['endingPlays'] = $endingPlays;
        $data['user'] = $this->user;



    	$message = (new MailMessage)
            ->success()
            ->view('hermes::neptune.watchlist-reminder', $data)
            ->subject('Μη χάσεις τις επιλεγμένες σου παραστάσεις!');
        return $message;
    }
}
