<?php

namespace Packages\Neptune\app\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ReviewRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'rating'    => 'required|integer|between:1,10',
            'review'    => 'required',
            'play_id'   => 'required|integer|exists:theatric_plays,id',
        ];
    }
}
