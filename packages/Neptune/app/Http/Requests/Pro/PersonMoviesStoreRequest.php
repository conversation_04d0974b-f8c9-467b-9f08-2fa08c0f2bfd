<?php

namespace Packages\Neptune\app\Http\Requests\Pro;

use Illuminate\Foundation\Http\FormRequest;

class PersonMoviesStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'title'             => 'required',
            'year'              => 'required|numeric',
        ];
    }
}
