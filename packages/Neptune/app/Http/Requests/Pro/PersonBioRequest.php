<?php

namespace Packages\Neptune\app\Http\Requests\Pro;

use App\Helpers\ContentSanitizer;
use Illuminate\Foundation\Http\FormRequest;

class PersonBioRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Clean up empty fields and sanitize content
        $bioFields = [
            "user_bio_el",
            "user_bio_en",
        ];

        $sanitizedData = [];

        foreach($bioFields as $field) {
            if ($this->has($field)) {
                $value = $this->$field;
                if ($value === null) {
                    $sanitizedData[$field] = '';
                } else {
                    // Sanitize the content to remove custom styling
                    $sanitizedData[$field] = ContentSanitizer::sanitizeUserBio($value);
                }
            }
        }

        if (!empty($sanitizedData)) {
            $this->merge($sanitizedData);
        }
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'user_bio_el' => [
                'nullable',
                'string',
                'max:15000', // Character limit for MySQL TEXT with utf8mb4 safety margin
                function ($attribute, $value, $fail) {
                    if ($value && $this->countWords($value) > 200) {
                        $fail(trans('neptune::pro/bio.validation.max_words', ['max' => 200]));
                    }
                },
            ],
            'user_bio_en' => [
                'nullable',
                'string',
                'max:15000', // Character limit for MySQL TEXT with utf8mb4 safety margin
                function ($attribute, $value, $fail) {
                    if ($value && $this->countWords($value) > 200) {
                        $fail(trans('neptune::pro/bio.validation.max_words', ['max' => 200]));
                    }
                },
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'user_bio_el.max' => trans('neptune::pro/bio.validation.max_characters', ['max' => 15000]),
            'user_bio_en.max' => trans('neptune::pro/bio.validation.max_characters', ['max' => 15000]),
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'user_bio_el' => trans('neptune::pro/bio.bio_label'),
            'user_bio_en' => 'Bio',
        ];
    }



    /**
     * Count words in text, handling HTML content from rich text editor
     *
     * @param string $text
     * @return int
     */
    private function countWords($text)
    {
        if (empty($text)) {
            return 0;
        }

        // Strip HTML tags to get plain text for word counting
        $plainText = strip_tags($text);

        // Remove extra whitespace and normalize
        $plainText = preg_replace('/\s+/', ' ', trim($plainText));

        if (empty($plainText)) {
            return 0;
        }

        // Count words - split by whitespace
        return count(preg_split('/\s+/', $plainText, -1, PREG_SPLIT_NO_EMPTY));
    }
}
