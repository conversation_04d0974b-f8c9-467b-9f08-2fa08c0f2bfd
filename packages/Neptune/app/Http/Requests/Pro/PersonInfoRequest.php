<?php

namespace Packages\Neptune\app\Http\Requests\Pro;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class PersonInfoRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'user_height'           => 'nullable|integer',
            'user_acting_age_from'  => 'nullable|integer',
            'user_acting_age_to'    => 'nullable|integer',
            'user_facebook'         => 'nullable|url',
            'user_facebook_page'    => 'nullable|url',
            'user_instagram'        => 'nullable|url',
            'user_website'          => 'nullable|url',
            'user_youtube'          => 'nullable|url',
            'user_imdb'             => 'nullable|url',
            'user_twitter'          => 'nullable|url',
        ];
    }
}
