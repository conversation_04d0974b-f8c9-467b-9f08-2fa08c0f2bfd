<?php

namespace Packages\Neptune\app\Http\Requests;

class RegisterRequest extends ProfileRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = $this->common_rules;
        // add extra validation rules
        $rules['password'] = 'required|min:6|confirmed';
        $rules['email'] = 'required|email|max:255|unique:users';

        return $rules;
    }
}
