<?php

namespace Packages\Neptune\app\Http\Controllers\Pro;

use App\Components\Sanitizer;
use App\Models\Image;
use App\Models\Movie;
use App\Models\Person;
use App\Trak\Cache\InvalidatorPersonCache;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Croppa;
use Illuminate\Support\Str;

class MovieImagesController extends ProController
{

    public function __construct(InvalidatorPersonCache $cacheInvalidator)
    {
        parent::__construct($cacheInvalidator);

        // redirect to proper page if adminised
        $this->middleware(function ($request, $next) {
            // $this->logged_user is filled in in the ProController constructor
            if($this->logged_user->adminised)
            {
                return redirect()->route('neptune.pro.adminised.plays.index');
            }

            return $next($request);
        });
    }

    /**
     * Show the form for previewing movie images
     *
     * @param  $person_slug
     */
    public function index($person_slug)
    {
        // get the person
        try {
            $person = Person::where('slug', $person_slug)->with('mainImage')->firstOrFail();
        } catch (ModelNotFoundException $e) {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title'  => 'An error occured',
                    'body'   => 'Person not found',
                ]
            ]);
        }

        // throws ProException
        $this->personChecks($person);

        $this->view_data['selected_person'] = $person;

        return view('neptune::pro.images.index', $this->view_data);
    }


    /**
     * Store the specified resource in storage.
     *
     * @param Request $request
     * @param $person_slug
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'file'        => 'required|file|max:50000|mimes:' . $this->getAllowedFileTypes(),
            'movie_id' => 'required|exists:movies,id',
            'person_slug' => 'required|exists:people,slug',
        ]);

        // get the person
        try {
            $person = Person::where('slug', $request->get('person_slug'))->with('mainImage')->firstOrFail();
            $movie = Movie::where('id', $request->get('movie_id'))->firstOrFail();
        } catch (ModelNotFoundException $e) {
            return response(['message' => 'Person not found'], 400);
        }

        // throws ProException
        if (!$person->isPro()) {
            return response(['message' => $person->fullName . ' is not registered in unstagePro'], 400);
        }

        // if the person is not connected to current user, abort
        if (!$this->logged_user->proPeople()->get()->contains($person)) {
            return response(['message' => 'Insufficient permissions to edit ' . $person->fullName], 400);
        }

        // perform basic security checks
        if($movie->user_id != $this->logged_user->id)
        {
            return response()->json([
                'error'   => true,
                'message' => trans('neptune::pro/movies.insufficient_priviledges')
            ], 200);
        }

        $file = $request->file;

        $fileExtension = $file->guessExtension();

        $imageNameWithoutExtension = substr($file->getClientOriginalName(), 0,
            strrpos($file->getClientOriginalName(), "."));

        $image_name = Sanitizer::handle($imageNameWithoutExtension, true) . '-' . Str::random(6) . '.' . $fileExtension;
        $image_name = string_to_greeklish($image_name);

        // prepare the storage folder name
        $storageFolder = 'images/pro/movies';

        // prepare path name
        $path = public_path($storageFolder);

        // move the file from the temporary uploaded location
        // to a more permanent one
        $file->move($path, $image_name);

        // image model actions
        $image_attributes = [
            'filename'    => $storageFolder . '/' . $image_name,
            'movie_id'    => $movie->id,
            'user_id'     => $this->logged_user->id,
            'description' => ''
        ];

        // create new model
        $image = Image::create($image_attributes);

        return $image;
    }


    /**
     * Remove the specified resource from storage.
     *
     * @param $image_id
     * @return JsonResponse
     */
    public function destroy($image_id, Request $request)
    {
        // get the image and the person
        try {
            $image = Image::whereNotNull('movie_id')
                ->where('id', $image_id)
                ->where('user_id', $this->logged_user->id)
                ->firstOrFail();

            $person = Person::where('slug', $request->get('person_slug'))->firstOrFail();

        } catch (ModelNotFoundException $e) {
            return response()->json([
                'error'   => true,
                'message' => trans('neptune::pro/images.delete.unsuccessful_delete')
            ], 200);
        }

        // check that person is Pro enabled
        if (!$person->isPro()) {
            return response()->json([
                'error'   => true,
                'message' => trans('neptune::pro/images.delete.unsuccessful_delete')
            ], 200);
        }

        // if the person is not connected to current user, abort
        if (!$this->logged_user->proPeople()->get()->contains($person)) {
            return response()->json([
                'error'   => true,
                'message' => trans('neptune::pro/images.delete.unsuccessful_delete')
            ], 200);
        }

        // perform basic security checks
        // get the movie
        $movie = $image->movie()->first();
        if($movie->user_id != $this->logged_user->id)
        {
            return response()->json([
                'error'   => true,
                'message' => trans('neptune::pro/movies.insufficient_priviledges')
            ], 200);
        }

        try {
            if (File::exists(public_path() . '/' . $image->filename)) {
//                unlink(public_path() . '/' . $image->filename);
                Croppa::delete(public_path() . '/' . $image->filename);
            }
            $deleted = Image::destroy($image_id);
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'error'   => true,
                'message' => trans('neptune::pro/images.delete.unsuccessful_delete')
            ], 200);
        }
        $status = $deleted ? 1 : 0;
        $message = $deleted ? trans('neptune::pro/images.delete.successful_delete') : trans('neptune::pro/images.delete.unsuccessful_delete');
        return response()->json([
            'status'  => $status,
            'message' => $message,
        ]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     *
     * Update specified image's caption (description)
     */
    public function saveCaption(Request $request)
    {
        // get the Image and the Person
        try {
            $image = Image::whereNotNull('movie_id')
                ->where('id', $request->get('id'))
                ->where('user_id', $this->logged_user->id)
                ->firstOrFail();

            $person = Person::where('slug', $request->get('person_slug'))->firstOrFail();
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'error'   => true,
                'message' => trans('neptune::pro/images.image_caption.unsuccessful_save')
            ], 200);
        }

        // check that person is Pro enabled
        if (!$person->isPro()) {
            return response()->json([
                'error'   => true,
                'message' => trans('neptune::pro/images.image_caption.unsuccessful_save')
            ], 200);
        }

        // if the person is not connected to current user, abort
        if (!$this->logged_user->proPeople()->get()->contains($person)) {
            return response()->json([
                'error'   => true,
                'message' => trans('neptune::pro/images.image_caption.unsuccessful_save')
            ], 200);
        }

        // perform basic security checks
        // get the movie
        $movie = $image->movie()->first();
        if($movie->user_id != $this->logged_user->id)
        {
            return response()->json([
                'error'   => true,
                'message' => trans('neptune::pro/movies.insufficient_priviledges')
            ], 200);
        }


        if ($image->update(['description' => $request->get('description')])) {
            return response()->json([
                'error'   => false,
                'message' => trans('neptune::pro/images.image_caption.successful_save')
            ], 200);
        } else {
            return response()->json([
                'error'   => true,
                'message' => trans('neptune::pro/images.image_caption.unsuccessful_save')
            ], 200);
        }
    }


    /**
     * @param Request $request
     * @return JsonResponse
     *
     * Update specified image's caption (description)
     */
    public function setMainImage(Request $request)
    {
        // get the Image and the Person
        try {
            $image = Image::whereNotNull('movie_id')
                ->where('id', $request->get('id'))
                ->where('user_id', $this->logged_user->id)
                ->firstOrFail();

            $person = Person::where('slug', $request->get('person_slug'))->firstOrFail();
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'error'   => true,
                'message' => trans('neptune::pro/images.image_main.unsuccessful_save')
            ], 200);
        }

        // check that person is Pro enabled
        if (!$person->isPro()) {
            return response()->json([
                'error'   => true,
                'message' => trans('neptune::pro/images.image_main.unsuccessful_save')
            ], 200);
        }

        // if the person is not connected to current user, abort
        if (!$this->logged_user->proPeople()->get()->contains($person)) {
            return response()->json([
                'error'   => true,
                'message' => trans('neptune::pro/images.image_main.unsuccessful_save')
            ], 200);
        }

        // perform basic security checks
        // get the movie
        $movie = $image->movie()->first();
        if($movie->user_id != $this->logged_user->id)
        {
            return response()->json([
                'error'   => true,
                'message' => trans('neptune::pro/movies.insufficient_priviledges')
            ], 200);
        }

        // resets all images of specific person to have column main as false
        Image::where('movie_id', $image->movie_id)
            ->update(['main' => 0]);

        // ...and then sets the given image as main (column main as true)
        if ($image->update(['main' => 1])) {
            return response()->json([
                'error'   => false,
                'message' => trans('neptune::pro/images.image_main.successful_save')
            ], 200);
        } else {
            return response()->json([
                'error'   => true,
                'message' => trans('neptune::pro/images.image_main.unsuccessful_save')
            ], 200);
        }
    }


    /**
     * Remove . prefix so laravel validator can use allowed files
     *
     * @return string
     */
    private function getAllowedFileTypes()
    {
        return str_replace('.', '', config('neptune.dropzone.allowed', ''));
    }

}
