<?php

namespace Packages\Neptune\app\Http\Controllers\Pro;

use App\Models\Person;
use App\Trak\Cache\InvalidatorPersonCache;
use Artesaos\SEOTools\Facades\SEOMeta;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class PersonDashboardController extends ProController
{
    public function __construct(InvalidatorPersonCache $cacheInvalidator)
    {
        parent::__construct($cacheInvalidator);

        // redirect to proper page if adminised
        $this->middleware(function ($request, $next) {
            // $this->logged_user is filled in in the ProController constructor
            if($this->logged_user->adminised)
            {
                return redirect()->route('neptune.pro.adminised.plays.index');
            }

            return $next($request);
        });
    }

    public function show($person_slug)
    {
        // get the person
        try
        {
            $person = Person::where('slug', $person_slug)->with('mainImage')->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Person not found',
                ]
            ]);
        }

        // throws ProException
        $this->personChecks($person);

        $this->view_data['selected_person'] = $person;

        $this->view_data['user_submitted_references']   = $this->logged_user->referencesUploaded($person)->count();
        $this->view_data['user_submitted_videos']       = $person->videos()->where('user_id', $this->logged_user->id)->count();
        $this->view_data['all_followers']               = $person->users()->count();
        $this->view_data['uploaded_images_count']       = $this->logged_user->imagesUploaded($person)->uninspected()->count();
        $this->view_data['uploaded_seminars_count']     = $this->logged_user->seminarsUploaded($person)->count();
        $this->view_data['created_plays_count']         = $this->logged_user->playsCreated()->count();
        $this->view_data['created_movies_count']        = $this->logged_user->moviesCreated()->count();
        $this->view_data['created_endeavours_count']    = $this->logged_user->endeavoursCreated()->count();
        $this->view_data['created_tvShows_count']       = $this->logged_user->tvShowsCreated()->count();

        return view('neptune::pro.dashboard.show', $this->view_data);
    }

}
