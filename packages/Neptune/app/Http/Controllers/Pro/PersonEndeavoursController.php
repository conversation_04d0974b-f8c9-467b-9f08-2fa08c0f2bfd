<?php

namespace Packages\Neptune\app\Http\Controllers\Pro;

use App\Exceptions\Pro\ProException;
use App\Mail\ProPublishMessage;
use App\Models\Endeavour;
use App\Models\EndeavourPersonRole;
use App\Models\Person;
use App\Models\Variety;
use App\Trak\Cache\InvalidatorPersonCache;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Packages\Neptune\app\Http\Requests\Pro\PersonEndeavoursStoreRequest;
use Packages\Neptune\app\Http\Requests\Pro\PersonEndeavoursUpdateRequest;

class PersonEndeavoursController extends ProController
{

    public function __construct(InvalidatorPersonCache $cacheInvalidator)
    {
        parent::__construct($cacheInvalidator);

        // redirect to proper page if adminised
        $this->middleware(function ($request, $next) {
            // $this->logged_user is filled in in the ProController constructor
            if($this->logged_user->adminised)
            {
                return redirect()->route('neptune.pro.adminised.plays.index');
            }

            return $next($request);
        });
    }

    /**
     * Show the form for previewing person endeavours
     *
     * @param  $person_slug
     */
    public function index($person_slug)
    {
        // get the person
        try
        {
            $person = Person::where('slug', $person_slug)->with('mainImage')->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Person not found',
                ]
            ]);
        }

        // throws ProException
        $this->personChecks($person);

        $this->view_data['selected_person'] = $person;
        $this->view_data['endeavours']      = $this->logged_user->endeavoursCreated()->orderBy('created_at', 'desc')->paginate(10);

        return view('neptune::pro.endeavours.index', $this->view_data);
    }

    /**
     * Show the form for creating the pro person endeavour
     *
     * @param  $person_slug
     */
    public function create($person_slug)
    {
        // get the person
        try
        {
            $person = Person::where('slug', $person_slug)->with('mainImage')->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Person not found',
                ]
            ]);
        }

        // throws ProException
        $this->personChecks($person);

        $this->view_data['selected_person'] = $person;
        $this->view_data['varieties']       = Variety::orderBy('name')->pluck('name', 'id');

        return view('neptune::pro.endeavours.create', $this->view_data);
    }


    /**
     * Store the specified resource in storage.
     *
     * @param PersonEndeavoursStoreRequest $request
     * @param $person_slug
     * @throws PersonProException
     */
    public function store(PersonEndeavoursStoreRequest $request, $person_slug)
    {
        // get the person
        try
        {
            $person = Person::where('slug', $person_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Person not found',
                ]
            ]);
        }

        // throws ProException
        $this->personChecks($person);

        $endeavour = new Endeavour;

        $data = [
            'old_title'     => trim($request['title']), // initially created nontranslatable in the endeavours table
            'year'          => trim($request['year']),
            'variety_id'    => $request['variety_id'],
            'el'            => [
                'title'         => trim($request['title']),
            ],
        ];

        $endeavour = $endeavour->create($data);

        // ...set the user_id attribute for the endeavour
        $endeavour->user_id = $this->logged_user->id;

        // ...set the moderated attribute to false
        $endeavour->moderated = false;

        // ...set the published attribute to false
        $endeavour->published = false;

        // ...and finaly save the model to persist the changes
        $endeavour->save();

        return redirect()->route('neptune.pro.endeavours.editInfo', [$person->slug, $endeavour->slug])
            ->with('success', trans('neptune::pro/endeavours.create.successful_creation'));
    }

    /**
     * Show the form for editing the pro person endeavour info
     *
     * @param  $person_slug
     * @param  $endeavour_slug
     * @throws PersonProException|ProException
     */
    public function editInfo($person_slug, $endeavour_slug)
    {
        // get the person
        try
        {
            $person = Person::where('slug', $person_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Person not found',
                ]
            ]);
        }

        // throws ProExcpetion
        $this->personChecks($person);

        // get the endeavour
        try
        {
            $endeavour = Endeavour::where('slug', $endeavour_slug)
                ->firstOrFail();
//                ->load('filmGenres');
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Item was not found',
                ]
            ]);
        }

        // throws ProException
        $this->endeavourChecks($person, $endeavour);

        $this->view_data['selected_person'] = $person;
        $this->view_data['endeavour']       = $endeavour;
        $this->view_data['roles']           = [];
//        $this->view_data['filmGenres']      = [];

        // get all roles for endeavour variety
        $this->view_data['roles'] = $endeavour->variety->roles->pluck('description', 'id');

        // get all filmGenres
//        foreach(FilmGenre::orderBy('name')->get() as $filmGenre)
//        {
//            $this->view_data['filmGenres'][$filmGenre->id] = $filmGenre->name;
//        }

        // get role ids for the given endeavour for the given person
        $role_ids = EndeavourPersonRole::where('person_id', $person->id)
            ->where('endeavour_id', $endeavour->id)
            ->pluck('role_id');
        $this->view_data['selected_roles'] = $role_ids;

        return view('neptune::pro.endeavours.editInfo', $this->view_data);
    }

    /**
     * Show the form for editing the pro person endeavour images
     *
     * @param  $person_slug
     * @param  $endeavour_slug
     * @throws PersonProException
     */
    public function editImages($person_slug, $endeavour_slug)
    {
        // get the person
        try
        {
            $person = Person::where('slug', $person_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Person not found',
                ]
            ]);
        }

        // throws ProExcpetion
        $this->personChecks($person);

        // get the endeavour
        try
        {
            $endeavour = Endeavour::where('slug', $endeavour_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Item was not found',
                ]
            ]);
        }

        // throws ProException
        $this->endeavourChecks($person, $endeavour);

        $this->view_data['selected_person'] = $person;
        $this->view_data['endeavour']       = $endeavour;

        return view('neptune::pro.endeavours.editImages', $this->view_data);
    }

    /**
     * Show the form for editing the pro endeavour person roles
     *
     * @param  $person_slug
     * @param  $endeavour_slug
     * @throws PersonProException
     */
    public function editRoles($person_slug, $endeavour_slug)
    {
        // get the person
        try
        {
            $person = Person::where('slug', $person_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Person not found',
                ]
            ]);
        }

        // throws ProException
        $this->personChecks($person);

        // get the endeavour
        try
        {
            $endeavour = Endeavour::where('slug', $endeavour_slug)
                ->with('roles')
                ->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Item was not found',
                ]
            ]);
        }

        // throws ProException
        $this->endeavourChecks($person, $endeavour);

        $endeavour->load([
            'roles.endeavourPeople' => function ($query) use ($endeavour) {
                $query->wherePivot('endeavour_id', '=', $endeavour->id);
            },
        ]);

        $rolesWithoutActors = [];
        // Workaround to get list of roles for which this endeavour
        // doesn't have any person
        $rolesIdsWithActors = [];
        foreach ($endeavour->roles as $endeavourRole)
        {
            $rolesIdsWithActors[] = $endeavourRole->id;
        }
        foreach ($endeavour->variety->roles as $role)
        {
            if ( ! in_array($role->id, $rolesIdsWithActors))
            {
                $rolesWithoutActors[] = $role;
            }
        }

        $this->view_data['selected_person']     = $person;
        $this->view_data['endeavour']           = $endeavour;
        $this->view_data['roles']               = $endeavour->variety->roles()->orderBy('sort_order_frontend')->get();
        $this->view_data['rolesWithoutActors']  = $rolesWithoutActors;

        return view('neptune::pro.endeavours.editRoles', $this->view_data);
    }

    /**
     * Show the form for editing the pro person endeavour private note
     *
     * @param  $person_slug
     * @param  $endeavour_slug
     * @throws PersonProException
     */
    public function editNotes($person_slug, $endeavour_slug)
    {
        // get the person
        try
        {
            $person = Person::where('slug', $person_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Person not found',
                ]
            ]);
        }

        // throws ProExcpetion
        $this->personChecks($person);

        // get the endeavour
        try
        {
            $endeavour = Endeavour::where('slug', $endeavour_slug)
                ->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Item was not found',
                ]
            ]);
        }

        // throws ProException
        $this->endeavourChecks($person, $endeavour);

        $this->view_data['selected_person'] = $person;
        $this->view_data['endeavour']       = $endeavour;

        return view('neptune::pro.endeavours.editNotes', $this->view_data);
    }

    /**
     * Show the form for finalising and publishing the pro person endeavour
     *
     * @param  $person_slug
     * @param  $endeavour_slug
     * @throws PersonProException
     */
    public function editPublished($person_slug, $endeavour_slug)
    {
        // get the person
        try
        {
            $person = Person::where('slug', $person_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Person not found',
                ]
            ]);
        }

        // throws ProExcpetion
        $this->personChecks($person);

        // get the endeavour
        try
        {
            $endeavour = Endeavour::where('slug', $endeavour_slug)
                ->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Item was not found',
                ]
            ]);
        }

        // throws ProException
        $this->endeavourChecks($person, $endeavour);

        $this->view_data['selected_person'] = $person;
        $this->view_data['endeavour']       = $endeavour;

        // check if the person has given his own role in the endeavour
        $existing_roles = EndeavourPersonRole::where('person_id', $person->id)
            ->where('endeavour_id', $endeavour->id)
            ->get();

        if($existing_roles->isEmpty())
        {
            return view('neptune::pro.endeavours.editPublished', $this->view_data)
                ->withErrors(['own_role_status' => trans('neptune::pro/endeavours.edit_published.own_role_error_msg')]);
        }

        return view('neptune::pro.endeavours.editPublished', $this->view_data);
    }


    /**
     * Update the specified resource in storage.
     *
     */
    public function updatePublished(Request $request, $person_slug, $endeavour_slug)
    {
        // get the person
        try
        {
            $person = Person::where('slug', $person_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Person not found',
                ]
            ]);
        }

        // throws ProExcpetion
        $this->personChecks($person);

        // get the endeavour
        try
        {
            $endeavour = Endeavour::where('slug', $endeavour_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Item was not found',
                ]
            ]);
        }

        // throws ProException
        $this->endeavourChecks($person, $endeavour);

        // check if the person has given his own role in the endeavour
        $existing_roles = EndeavourPersonRole::where('person_id', $person->id)
            ->where('endeavour_id', $endeavour->id)
            ->get();

        if($existing_roles->isEmpty())
        {
            return redirect()->route('neptune.pro.endeavours.editPublished', [$person->slug, $endeavour->slug])
                ->withErrors(['own_role_status' => trans('neptune::pro/endeavours.edit_published.own_role_error_msg')]);
        }

        // manually set the fields
        $endeavour->moderated = true;
        $endeavour->published = true;

        $endeavour->save();

        try {
            // send notification email
            $input = [
                'person_id'         => $person->id,
                'person_name'       => $person->fullName,
                'person_slug'       => $person->slug,
                'user_id'           => $this->logged_user->id,
                'user_email'        => $this->logged_user->email,
                'endeavour_type'    => $endeavour->variety->name,
                'endeavour_id'      => $endeavour->id,
                'endeavour_title'   => $endeavour->title,
                'endeavour_slug'    => $endeavour->slug,
            ];
            Mail::to(config('mail_addresses.contact'))->send(new ProPublishMessage($input));
        } catch (\Exception $exception) {
            report($exception);
        }

        return redirect()->route('neptune.pro.endeavours.index', [$person->slug, $endeavour->slug])
            ->with('success', trans('neptune::pro/endeavours.edit_published.successful_publish'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param PersonEndeavoursUpdateRequest $request
     */
    public function updateInfo(PersonEndeavoursUpdateRequest $request, $person_slug, $endeavour_slug)
    {
        // get the person
        try
        {
            $person = Person::where('slug', $person_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Person not found',
                ]
            ]);
        }

        // throws ProExcpetion
        $this->personChecks($person);

        // get the endeavour
        try
        {
            $endeavour = Endeavour::where('slug', $endeavour_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Item was not found',
                ]
            ]);
        }

        // throws ProException
        $this->endeavourChecks($person, $endeavour);

        // manually set the editable fields
        // synopsis
        if($request->has('synopsis') && ! empty($request->input('synopsis')))
        {
            $endeavour->synopsis = trim($request->input('synopsis'));
        }

        // ...we also want to tag the endeavour fer moderation
        // ...(ideally on the condition that some data changed)
        if($endeavour->isDirty())
        {
            $endeavour->moderated = false;
        }

        $endeavour->save();

        // handle filmGenres
        // code to filter out the empty string value submitted because of the placeholder of the dropdown
//        $genre_collection = new Collection($request->input('filmGenre'));
//        $filtered_genre_collection = $genre_collection->filter(function ($value, $key) {
//            return $value != '';
//        });
//        $endeavour->filmGenres()->sync($filtered_genre_collection);

        // handle own roles
        // firstly clear out any roles fer this endeavour fer this person
        $existing_roles = EndeavourPersonRole::where('person_id', $person->id)
            ->where('endeavour_id', $endeavour->id)
            ->delete();

        // ...and then create the submitted roles
        foreach($request->input('own_role') as $role_id)
        {
            if( ! empty($role_id) )
            {
                // character handling for actor role
                if($role_id == 1 && $request->has('character') && ! empty($request->input('character')))
                {
                    $data = [
                        'role_id'       => $role_id,
                        'endeavour_id'  => $endeavour->id,
                        'person_id'     => $person->id,
                        'character'     => trim($request->input('character')),
                    ];
                }
                else
                {
                    $data = [
                        'role_id'       => $role_id,
                        'endeavour_id'  => $endeavour->id,
                        'person_id'     => $person->id,
                    ];
                }

                EndeavourPersonRole::create($data);
            }
        }

        return redirect()->route('neptune.pro.endeavours.editInfo', [$person->slug, $endeavour->slug])
            ->with('success', trans('neptune::pro/endeavours.edit_info.successful_update'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     */
    public function updateNotes(Request $request, $person_slug, $endeavour_slug)
    {
        // get the person
        try
        {
            $person = Person::where('slug', $person_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Person not found',
                ]
            ]);
        }

        // throws ProExcpetion
        $this->personChecks($person);

        // get the endeavour
        try
        {
            $endeavour = Endeavour::where('slug', $endeavour_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Item was not found',
                ]
            ]);
        }

        // throws ProException
        $this->endeavourChecks($person, $endeavour);

        // manually set the editable fields
        // user note
        if($request->has('user_notes') && ! empty($request->input('user_notes')))
        {
            $endeavour->user_notes = trim($request->input('user_notes'));
        }

        // ...we also want to tag the endeavour fer moderation
        // ...(on the condition that some data changed)
        if($endeavour->isDirty())
        {
            $endeavour->moderated = false;
        }

        $endeavour->save();

        return redirect()->route('neptune.pro.endeavours.editNotes', [$person->slug, $endeavour->slug])
            ->with('success', trans('neptune::pro/endeavours.edit_notes.successful_update'));
    }


    /**
     * @param Person $person
     * @param Endeavour $endeavour
     * @throws ProException
     */
    protected function endeavourChecks(Person $person, Endeavour $endeavour)
    {
        // if the endeavour is not created by the logged in user, abort
        if( $this->logged_user->id != $endeavour->user_id )
        {
            throw new ProException('Insufficient permissions to edit');
        }

        // if the endeavour is moderated, abort
        if( $endeavour->moderated )
        {
            throw new ProException('Insufficient permissions to edit');
        }
    }
}
