<?php

namespace Packages\Neptune\app\Http\Controllers\Pro;

use App\Components\Sanitizer;
use App\Models\Image;
use App\Models\Person;
use App\Models\Play;
use App\Trak\Cache\InvalidatorPersonCache;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Croppa;
use Illuminate\Support\Str;

class AdminisedPlayImagesController extends ProController
{

    public function __construct(InvalidatorPersonCache $cacheInvalidator)
    {
        parent::__construct($cacheInvalidator);

        // redirect to proper page if NOT adminised
        $this->middleware(function ($request, $next) {
            // $this->logged_user is filled in in the ProController constructor
            if( ! $this->logged_user->adminised )
            {
                return redirect()->route('neptune.pro.home');
            }

            return $next($request);
        });
    }

    /**
     * Show the form for previewing person images
     *
     * @param  $person_slug
     */
    public function index($person_slug)
    {
        // get the person
        try {
            $person = Person::where('slug', $person_slug)->with('mainImage')->firstOrFail();
        } catch (ModelNotFoundException $e) {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title'  => 'An error occured',
                    'body'   => 'Person not found',
                ]
            ]);
        }

        // throws ProException
        $this->personChecks($person);

        $this->view_data['selected_person'] = $person;

        return view('neptune::pro.images.index', $this->view_data);
    }


    /**
     * Store the specified resource in storage.
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'file'    => 'required|file|max:50000|mimes:' . $this->getAllowedFileTypes(),
            'play_id' => 'required|exists:theatric_plays,id',
        ]);

        // perform basic security checks
        // get the Play
        $play = Play::where('id', $request->get('play_id'))
            ->firstOrFail();
        if($play->user_id != $this->logged_user->id)
        {
            return response()->json([
                'error'   => true,
                'message' => trans('neptune::pro/plays.insufficient_priviledges')
            ], 200);
        }

        $file = $request->file;

        $fileExtension = $file->guessExtension();

        $imageNameWithoutExtension = substr($file->getClientOriginalName(), 0,
            strrpos($file->getClientOriginalName(), "."));

        $image_name = Sanitizer::handle($imageNameWithoutExtension, true) . '-' . Str::random(6) . '.' . $fileExtension;
        $image_name = string_to_greeklish($image_name);

        // prepare the storage folder name
        $storageFolder = 'images/pro/plays';

        // prepare path name
        $path = public_path($storageFolder);

        // move the file from the temporary uploaded location
        // to a more permanent one
        $file->move($path, $image_name);

        // image model actions
        $image_attributes = [
            'filename'          => $storageFolder . '/' . $image_name,
            'theatric_play_id'  => $play->id,
            'user_id'           => $this->logged_user->id,
            'description'       => '',
        ];

        // create new model
        $image = Image::create($image_attributes);

        return $image;
    }


    /**
     * Remove the specified resource from storage.
     *
     * @param $image_id
     * @return JsonResponse
     */
    public function destroy($image_id, Request $request)
    {
        // get the image
        try {
            $image = Image::whereNotNull('theatric_play_id')
                ->where('id', $image_id)
                ->where('user_id', $this->logged_user->id)
                ->firstOrFail();
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'error'   => true,
                'message' => trans('neptune::pro/images.delete.unsuccessful_delete')
            ], 200);
        }

        // perform basic security checks
        // get the Play
        $play = $image->play()->first();
        if($play->user_id != $this->logged_user->id)
        {
            return response()->json([
                'error'   => true,
                'message' => trans('neptune::pro/plays.insufficient_priviledges')
            ], 200);
        }

        try {
            if (File::exists(public_path() . '/' . $image->filename)) {
//                unlink(public_path() . '/' . $image->filename);
                Croppa::delete(public_path() . '/' . $image->filename);
            }
            $deleted = Image::destroy($image_id);
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'error'   => true,
                'message' => trans('neptune::pro/images.delete.unsuccessful_delete')
            ], 200);
        }

        $status = $deleted ? 1 : 0;
        $message = $deleted ? trans('neptune::pro/images.delete.successful_delete') : trans('neptune::pro/images.delete.unsuccessful_delete');

        return response()->json([
            'status'  => $status,
            'message' => $message,
        ]);
    }


    /**
     * @param Request $request
     * @return JsonResponse
     *
     * Update specified image's caption (description)
     */
    public function saveCaption(Request $request)
    {
        // get the Image
        try {
            $image = Image::whereNotNull('theatric_play_id')
                ->where('id', $request->get('id'))
                ->where('user_id', $this->logged_user->id)
                ->firstOrFail();
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'error'   => true,
                'message' => trans('neptune::pro/images.image_caption.unsuccessful_save')
            ], 200);
        }

        // perform basic security checks
        // get the Play
        $play = $image->play()->first();
        if($play->user_id != $this->logged_user->id)
        {
            return response()->json([
                'error'   => true,
                'message' => trans('neptune::pro/plays.insufficient_priviledges')
            ], 200);
        }

        if ($image->update(['description' => $request->get('description')])) {
            return response()->json([
                'error'   => false,
                'message' => trans('neptune::pro/images.image_caption.successful_save')
            ], 200);
        } else {
            return response()->json([
                'error'   => true,
                'message' => trans('neptune::pro/images.image_caption.unsuccessful_save')
            ], 200);
        }
    }


    /**
     * @param Request $request
     * @return JsonResponse
     *
     * Update specified image's caption (description)
     */
    public function setMainImage(Request $request)
    {
        // get the Image
        try {
            $image = Image::whereNotNull('theatric_play_id')
                ->where('id', $request->get('id'))
                ->where('user_id', $this->logged_user->id)
                ->firstOrFail();
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'error'   => true,
                'message' => trans('neptune::pro/images.image_main.unsuccessful_save')
            ], 200);
        }

        // perform basic security checks
        // get the Play
        $play = $image->play()->first();
        if($play->user_id != $this->logged_user->id)
        {
            return response()->json([
                'error'   => true,
                'message' => trans('neptune::pro/plays.insufficient_priviledges')
            ], 200);
        }

        // resets all images of specific person to have column main as false
        Image::where('theatric_play_id', $image->theatric_play_id)
            ->update(['main' => 0]);

        // ...and then sets the given image as main (column main as true)
        if ($image->update(['main' => 1])) {
            return response()->json([
                'error'   => false,
                'message' => trans('neptune::pro/images.image_main.successful_save')
            ], 200);
        } else {
            return response()->json([
                'error'   => true,
                'message' => trans('neptune::pro/images.image_main.unsuccessful_save')
            ], 200);
        }
    }

    /**
     * Remove . prefix so laravel validator can use allowed files
     *
     * @return string
     */
    private function getAllowedFileTypes()
    {
        return str_replace('.', '', config('neptune.dropzone.allowed', ''));
    }
}
