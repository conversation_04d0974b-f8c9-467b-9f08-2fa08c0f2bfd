<?php

namespace Packages\Neptune\app\Http\Controllers\Pro;

use App\Trak\Cache\InvalidatorPersonCache;

class HomeController extends ProController
{

    public function __construct(InvalidatorPersonCache $cacheInvalidator)
    {
        parent::__construct($cacheInvalidator);

        // redirect to proper page if adminised
        $this->middleware(function ($request, $next) {
            // $this->logged_user is filled in in the ProController constructor
            if($this->logged_user->adminised)
            {
                return redirect()->route('neptune.pro.adminised.plays.index');
            }

            return $next($request);
        });
    }

    public function show()
    {
        return view('neptune::pro.home', $this->view_data);
    }
}
