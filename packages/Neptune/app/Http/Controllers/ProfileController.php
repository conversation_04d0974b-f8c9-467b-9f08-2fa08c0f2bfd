<?php

namespace Packages\Neptune\app\Http\Controllers;

use Packages\Neptune\app\Http\Requests\ProfileRequest;
use App\Models\Traits\DatabaseToken;

class ProfileController extends Controller
{
	use DatabaseToken;

    public function edit()
    {
        $data['user'] = auth('users')->user();

        return view('neptune::profile', $data);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param ProfileRequest $request
     * @param $id
     * @return Response
     * @internal param $id
     * @internal param int $id
     */
    public function update(ProfileRequest $request)
    {
        $input = array_map('trim', $request->all());

        // checkboxes handle
        // watchlist notifs
        if(!$request->has('watchlist_notifications'))
        {
            $input['watchlist_notifications'] = false;
        }
        else
        {
            $input['watchlist_notifications'] = true;
        }
        // recommendation notifs
        if(!$request->has('recommendation_notifications'))
        {
            $input['recommendation_notifications'] = false;
        }
        else
        {
            $input['recommendation_notifications'] = true;
        }

        auth('users')->user()->update($input);

        return redirect()->route('neptune.profile.edit')
            ->with('success', 'Το προφίλ σου ενημερώθηκε');
    }

    public function resendEmailVerification()
    {
    	$user = auth('users')->user();

        $token = $this->databaseToken()->create($user);

        $user->sendEmailVerificationNotification($token);

        return redirect()->route('neptune.profile.edit')
            ->with('emailVerificationSent', 'Σου στείλαμε μήνυμα email για να επιβεβαιώσεις τη διεύθυνση email σου.');
    }
}
