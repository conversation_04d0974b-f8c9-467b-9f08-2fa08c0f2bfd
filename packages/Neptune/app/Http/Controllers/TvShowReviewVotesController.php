<?php

namespace Packages\Neptune\app\Http\Controllers;

use App\Models\TvShowReview;
use Exception;

class TvShowReviewVotesController extends Controller
{

    /**
     * Toggles a given review vote on/off fer the logged in user
     *
     * @param $review_id
     * @return mixed
     */
    public function toggle($review_id)
    {
        try
        {
            $vote = auth('users')->user()->tvShowReviewVotes()->toggle($review_id);

            // fetch review
            $review = TvShowReview::find($review_id);
            // set review upvote count field
            $review->setUpvoteCount();
        }
        catch (Exception $e)
        {
            return $this->jsonError($e);
        }

        // todo: return proper
        return response()->json([
            'status'   => 'success',
            'upvotes'  => $review->upvotes,
            'attached' => empty($vote['attached']) ? '' : $vote['attached'][0],
            'detached' => empty($vote['detached']) ? '' : $vote['detached'][0],
        ]);
    }
}
