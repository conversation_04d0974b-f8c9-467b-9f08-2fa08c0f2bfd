<?php

namespace Packages\Neptune\app\Http\Controllers;

use App\Events\PersonFollowed;
use App\Events\PersonUnfollowed;
use App\Models\Supergenre;
use App\Models\Theatre;
use Exception;

class FollowablesController extends Controller
{
    /**
     * Display the followable theatres for a user.
     *
     * @return Response
     */
    public function theatreIndex()
    {
        $theatres = Theatre::with('translations')
            ->followable()
            ->nonGeneric() // scope to bring only actual theatres
            ->orderBy('name', 'asc')
            ->get();

        $user = auth('users')->user();

        $followed = auth('users')->user()->followedTheatres()->pluck('id')->toArray();

        return view('neptune::followables.theatreIndex', compact('theatres', 'user', 'followed'));
    }

    /**
     * Toggles a given theatre on/off the logged in user's follow list
     *
     * @param $theatre_id
     * @return mixed
     */
    public function theatreToggle($theatre_id)
    {
        try
        {
            $ret = auth('users')->user()->followedTheatres()->toggle($theatre_id);
        }
        catch (Exception $e)
        {
            return $this->jsonError($e);
        }

        // manually fire events
//        $event_data = [
//            'play_id'   => $play_id,
//            'user_id'   => auth('users')->user()->id,
//        ];
//        if (!empty($ret['attached']))
//        {
//            event(new WatchlistItemAdded($event_data));
//        }
//        elseif (!empty($ret['detached']))
//        {
//            event(new WatchlistItemRemoved($event_data));
//        }

        // todo: return proper
        return response()->json([
            'status'   => 'success',
            'attached' => empty($ret['attached']) ? '' : $ret['attached'][0],
            'detached' => empty($ret['detached']) ? '' : $ret['detached'][0],
        ]);
    }

    /**
     * Display the followable people for a user.
     *
     * @return Response
     */
    public function peopleIndex()
    {
        $followed_people = auth('users')->user()
            ->followedPeople()
            ->with('mainImage', 'attendingPlays')
            ->orderBy('last_name', 'asc')
            ->get();

        return view('neptune::followables.peopleIndex', compact('followed_people'));
    }

    /**
     * Toggles a given person on/off the logged in user's follow list
     *
     * @param $person_id
     * @return mixed
     */
    public function personToggle($person_id)
    {
        try
        {
            $ret = auth('users')->user()->followedPeople()->toggle($person_id);
        }
        catch (Exception $e)
        {
            return $this->jsonError($e);
        }

        // manually fire events
        $event_data = [
            'person_id'   => $person_id,
            'user_id'   => auth('users')->user()->id,
        ];
        if (!empty($ret['attached']))
        {
            event(new PersonFollowed($event_data));
        }
        elseif (!empty($ret['detached']))
        {
            event(new PersonUnfollowed($event_data));
        }

        // todo: return proper
        return response()->json([
            'status'   => 'success',
            'attached' => empty($ret['attached']) ? '' : $ret['attached'][0],
            'detached' => empty($ret['detached']) ? '' : $ret['detached'][0],
        ]);
    }

    /**
     * Display the followable people for a user.
     *
     * @return Response
     */
    public function supergenreIndex()
    {
        $supergenres = Supergenre::orderBy('name', 'asc')
            ->get();

        $user = auth('users')->user();

        return view('neptune::followables.supergenreIndex', compact('supergenres', 'user'));
    }

    /**
     * Toggles a given person on/off the logged in user's follow list
     *
     * @param $person_id
     * @return mixed
     */
    public function supergenreToggle($supergenre_id)
    {
        try
        {
            $ret = auth('users')->user()->followedSupergenres()->toggle($supergenre_id);
        }
        catch (Exception $e)
        {
            return $this->jsonError($e);
        }

        // manually fire events
//        $event_data = [
//            'play_id'   => $play_id,
//            'user_id'   => auth('users')->user()->id,
//        ];
//        if (!empty($ret['attached']))
//        {
//            event(new WatchlistItemAdded($event_data));
//        }
//        elseif (!empty($ret['detached']))
//        {
//            event(new WatchlistItemRemoved($event_data));
//        }

        // todo: return proper
        return response()->json([
            'status'   => 'success',
            'attached' => empty($ret['attached']) ? '' : $ret['attached'][0],
            'detached' => empty($ret['detached']) ? '' : $ret['detached'][0],
        ]);
    }
}
