<?php

namespace Packages\Neptune\app\Models;

use App\Models\Endeavour;
use App\Models\PersonEvent;
use App\Models\Lista;
use App\Models\Person;
use App\Models\Play;
use App\Models\Movie;
use App\Models\TvShow;
use App\Models\Overrides\AuthenticatableModel;
use App\Models\PlayEvent;
use App\Models\Review;
use App\Models\Supergenre;
use App\Models\Theatre;
use App\Models\Traits\NonBlockable;
use App\Models\Traits\SocialAccountable;
use App\Models\Traits\Verifiable;
use Carbon\Carbon;
use Illuminate\Notifications\Notifiable;
use App\Models\Recommendation;
use Packages\Neptune\app\Models\UserMetadata;
use Packages\Neptune\app\Notifications\StreamingRecommendationNotification;
use Packages\Neptune\app\Notifications\StreamingRecommendationReminder;
use Packages\Neptune\app\Notifications\RecommendationNotification;
use Packages\Neptune\app\Notifications\RecommendationReminder;
use Packages\Neptune\app\Notifications\ResetPassword;
use Packages\Neptune\app\Notifications\VerifyEmail;
use Packages\Neptune\app\Notifications\WatchlistReminder;
use Packages\Neptune\app\Notifications\WatchlistNotification;
use Packages\Neptune\app\Notifications\WelcomeNewUser;
use Packages\Neptune\app\Notifications\Pro\WelcomeProUser;
use Illuminate\Support\Facades\DB;
use Packages\Neptune\app\Models\Traits\Relationships\UserRelationships;

class User extends AuthenticatableModel
{
    use Notifiable,
        Verifiable,
        NonBlockable,
        SocialAccountable,
        UserRelationships;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'first_name',
        'last_name',
        'description',
        'email',
        'verified',
        'email_verified',
        'gender',
        'watchlist_notifications',
        'recommendation_notifications',
        'blocked',
        'block_cause',
        'blocked_at',
        'email_suppressed',
        'email_suppress_cause',
        'email_suppressed_at',
        'adminised',
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $casts = [
        'blocked_at' => 'datetime',
        'email_suppressed_at' => 'datetime',
    ];

    // =======================================================================//
    //                         Accessors & Mutators
    // =======================================================================//

    /**
     * Get the first/last name combo in one go
     *
     * @return string
     */
    public function getFullNameAttribute()
    {
        return trim($this->first_name . ' ' . $this->last_name);
    }

    /**
     * Get the user avatar
     *
     * @return string
     */
    public function getAvatarAttribute()
    {
        if (!empty($this->full_name))
        {
            return "https://eu.ui-avatars.com/api/?background=00BCBE&color=fff&format=svg&name=" . $this->full_name;
        } else {
            return unstageAsset('img/member.svg');
        }
    }

    // =======================================================================//
    //                           Custom Functions
    // =======================================================================//

    /**
     * Check if user is verified
     *
     * @return bool
     */
    public function isVerified()
    {
        return $this->verified == 1 ? true : false;
    }


    /**
     * Check whether the given play exists in the current user's watchlist or not
     * @param $play_id
     * @return bool
     */
    public function inWatchlist($play_id)
    {
        return empty($this->watchlist()->where('play_id', $play_id)->first()) ? false : true;
    }


    /**
     * Check whether the given review has been voted by the current user or not
     * @param $review_id
     * @return bool
     */
    public function hasUpvoted($review_id)
    {
        return empty($this->reviewVotes()->where('review_id', $review_id)->first()) ? false : true;
    }

    /**
     * Check whether the given theatre exists in the current user's followed theatres list or not
     * @param $theatre_id
     * @return bool
     */
    public function inFollowedTheatres($theatre_id)
    {
        // Cache for 2 seconds for pages with a lot of Theatres
        $followedTheatres = cache()->remember('followedTheatres_' . $this->id, 2, function (){
            return $this->followedTheatres()->get()->pluck('id')->toArray();
        });
        return in_array($theatre_id, $followedTheatres);
//        return empty($this->followedTheatres()->where('theatre_id', $theatre_id)->first()) ? false : true;
    }

    /**
     * Check whether the given person exists in the current user's followed people list or not
     * @param $person_id
     * @return bool
     */
    public function inFollowedPeople($person_id)
    {
        // Cache for 2 seconds for pages with a lot of people
        $followedPeople = cache()->remember('followedPeople_' . $this->id, 2, function (){
            return $this->followedPeople()->get()->pluck('id')->toArray();
        });
        return in_array($person_id, $followedPeople);
//        return empty($this->followedPeople()->where('person_id', $person_id)->first()) ? false : true;
    }

    /**
     * Check whether the given supergenre exists in the current user's followed supergenres list or not
     * @param $supergenre_id
     * @return bool
     */
    public function inFollowedSupergenres($supergenre_id)
    {
        // Cache for 2 seconds for pages with a lot of Supergenres
        $followedSupergenres = cache()->remember('followedSupergenres_' . $this->id, 2, function (){
            return $this->followedSupergenres()->get()->pluck('id')->toArray();
        });
        return in_array($supergenre_id, $followedSupergenres);
//        return empty($this->followedSupergenres()->where('supergenre_id', $supergenre_id)->first()) ? false : true;
    }

    /**
     * Returns the rating score for the given play from the current user
     * In case the user has not rated the play, 0 is returned
     * @param $play_id
     */
    public function getRating($play_id)
    {
        $ret = $this->ratings()->where('play_id', $play_id)->first();

        if (!empty($ret)) {
            return $ret->pivot->rating;
        }
        return 0;
    }

    /**
     * Returns the rating score for the given movie from the current user
     * In case the user has not rated the movie, 0 is returned
     * @param $movie_id
     */
    public function getMovieRating($movie_id)
    {
        $ret = $this->movieRatings()->where('movie_id', $movie_id)->first();

        if (!empty($ret)) {
            return $ret->pivot->rating;
        }
        return 0;
    }

    /**
     * Returns the rating score for the given endeavour from the current user
     * In case the user has not rated the movie, 0 is returned
     * @param $endeavour_id
     */
    public function getEndeavourRating($endeavour_id)
    {
        $ret = $this->endeavourRatings()->where('endeavour_id', $endeavour_id)->first();

        if (!empty($ret)) {
            return $ret->pivot->rating;
        }
        return 0;
    }

    /**
     * Returns the rating score for the given tv show from the current user
     * In case the user has not rated the show, 0 is returned
     * @param $tvShow_id
     */
    public function getTvShowRating($tvShow_id)
    {
        $ret = $this->tvShowRatings()->where('tv_show_id', $tvShow_id)->first();

        if (!empty($ret)) {
            return $ret->pivot->rating;
        }
        return 0;
    }


    /**
     * Returns the review text for the given play from the current user
     * In case the user has not reviewd the play, null is returned
     * @param $play_id
     */
    public function getReview($play_id)
    {
        $rev = $this->reviews()
            ->where('play_id', $play_id)
            ->first();

        if (!empty($rev)) {
            return $rev->pivot->review;
        }

        return null;
    }


    /**
     * Returns the review id for the given play from the current user
     * In case the user has not reviewd the play, null is returned
     * @param $play_id
     */
    public function getReviewId($play_id)
    {
        $rev = $this->reviews()
            ->where('play_id', $play_id)
            ->first();

        if (!empty($rev)) {
            return $rev->pivot->id;
        }

        return null;
    }


    /**
     * Returns the spoiler status for the given play from the current user
     * In case the user has not reviewd the play, 0 is returned
     * @param $play_id
     */
    public function getSpoiler($play_id)
    {
        $rev = $this->reviews()
            ->where('play_id', $play_id)
            ->first();

        if (!empty($rev)) {
            return $rev->pivot->spoiler;
        }

        return 0;
    }

    /**
     * Returns the average rating for all ratings of said user (statistical usage)
     *
     * @return mixed
     */
    public function getAverageRatings()
    {
        return str_replace(',0', '', number_format($this->ratings()->avg('rating'), 1, ',', ''));
    }

    // =======================================================================//
    //                           Relationships (plain Lara way)
    // =======================================================================//

    /**
     * Get the watchlist of the user
     */
    public function watchlist()
    {
        return $this->belongsToMany(Play::class, 'watchlists', 'user_id', 'play_id')
            ->withTimestamps();
    }

    /**
     * Get the ratings of the user
     */
    public function ratings()
    {
        return $this->belongsToMany(Play::class, 'ratings', 'user_id', 'play_id')
            ->withPivot('rating')
            ->withTimestamps();
    }

    /**
     * Get the movie ratings of the user
     */
    public function movieRatings()
    {
        return $this->belongsToMany(Movie::class, 'movie_ratings', 'user_id', 'movie_id')
            ->withPivot('rating')
            ->withTimestamps();
    }

    /**
     * Get the endeavour ratings of the user
     */
    public function endeavourRatings()
    {
        return $this->belongsToMany(Endeavour::class, 'endeavour_ratings', 'user_id', 'endeavour_id')
            ->withPivot('rating')
            ->withTimestamps();
    }

    /**
     * Get the tv show ratings of the user
     */
    public function tvShowRatings()
    {
        return $this->belongsToMany(TvShow::class, 'tv_show_ratings', 'user_id', 'tv_show_id')
            ->withPivot('rating')
            ->withTimestamps();
    }

    /**
     * Get the reviews of the user
     */
    public function reviews()
    {
        return $this->belongsToMany(Play::class, 'reviews', 'user_id', 'play_id')
            ->withPivot('review', 'spoiler', 'upvotes', 'id', 'banned', 'moderated')
            ->withTimestamps();
    }

    /**
     * Get the review votes of the user
     */
    public function reviewVotes()
    {
        return $this->belongsToMany(Review::class, 'review_votes', 'user_id', 'review_id')
            ->withTimestamps();
    }

    /**
     * Get the ratings breakdown of the user
     */
    public function ratingsBreakdown($rating)
    {
        // if an invalid rating parametre value is given, return all ratings
        if (!ctype_digit(strval($rating))) {
            return $this->ratings();
        } else if ($rating < 1 || $rating > 10) {
            return $this->ratings();
        }

        return $this->ratings()
            ->where('rating', $rating)
            ->get();
    }

    /**
     * Get the followed theatres of the user
     */
    public function followedTheatres()
    {
        return $this->belongsToMany(Theatre::class, 'followed_theatres', 'user_id', 'theatre_id')
            ->withTimestamps();
    }

    /**
     * Get the followed people of the user
     */
    public function followedPeople()
    {
        return $this->belongsToMany(Person::class, 'followed_people', 'user_id', 'person_id')
            ->withTimestamps();
    }

    /**
     * Get the followed supergenres of the user
     */
    public function followedSupergenres()
    {
        return $this->belongsToMany(Supergenre::class, 'followed_supergenres', 'user_id', 'supergenre_id')
            ->withTimestamps();
    }

    /**
     * Get the listas for the author
     */
    public function listas()
    {
        return $this->hasMany(Lista::class);
    }

    /**
     * Get this user's play events in general
     *
     * @return mixed
     */
    public function playEvents()
    {
        return $this->hasMany(PlayEvent::class);
    }

    /**
     * Get this user's last days' play events
     *
     * @return mixed
     */
    public function playEventsLastDays($event_category = null, $number_of_days = 7)
    {
        if ($event_category === null) {
            return $this->playEvents()
                ->whereDate('created_at', '>=', Carbon::today()->subDays($number_of_days))
                ->get();
        }

        return $this->playEvents()
            ->where('category', $event_category)
            ->whereDate('created_at', '>=', Carbon::today()->subDays($number_of_days))
            ->get();
    }

    /**
     * Get this user's play events for play visits (category = play)
     *
     * @return mixed
     */
    public function playVisitEvents($action = null)
    {
        // if the action is not one of the defined values for this category return all events of category
        if ($action != 'main_visit' && $action != 'media_visit' && $action != 'references_visit') {
            return $this->playEvents()
                ->where('category', 'play')
                ->get();
        } else {
            return $this->playEvents()
                ->where('category', 'play')
                ->where('action', $action)
                ->get();
        }
    }

    /**
     * Get this user's last month's play events for play visits (category = play)
     *
     * @return mixed
     */
    public function playVisitEventsLastMonth()
    {
        return $this->playEvents()
            ->where('category', 'play')
            ->whereMonth('created_at', '=', Carbon::now()->subMonth()->month)
            ->get();
    }

    /**
     * Get this user's last week's play events for play visits (category = play)
     *
     * @return mixed
     */
    public function playVisitEventsLastDays($number_of_days = 7)
    {
        return $this->playEventsLastDays('play', $number_of_days);
    }

    /**
     * Get this user's play events for play ratings (category = rating)
     *
     * @return mixed
     */
    public function playRatingEvents($action = null)
    {
        // if the action is not one of the defined values for this category return all events of category
        if ($action != 'store' && $action != 'remove') {
            return $this->playEvents()
                ->where('category', 'rating')
                ->get();
        } else {
            return $this->playEvents()
                ->where('category', 'rating')
                ->where('action', $action)
                ->get();
        }
    }

    /**
     * Get this user's last month's play events for play ratings (category = rating)
     *
     * @return mixed
     */
    public function playRatingEventsLastMonth()
    {
        return $this->playEvents()
            ->where('category', 'rating')
            ->whereMonth('created_at', '=', Carbon::now()->subMonth()->month)
            ->get();
    }

    /**
     * Get this user's last week's play events for play ratings (category = rating)
     *
     * @return mixed
     */
    public function playRatingEventsLastDays($number_of_days = 7)
    {
        return $this->playEventsLastDays('rating', $number_of_days);
    }

    /**
     * Get this user's play events for play watchlist (category = watchlist)
     *
     * @return mixed
     */
    public function playWatchlistEvents($action = null)
    {
        // if the action is not one of the defined values for this category return all events of category
        if ($action != 'add' && $action != 'remove') {
            return $this->playEvents()
                ->where('category', 'watchlist')
                ->get();
        } else {
            return $this->playEvents()
                ->where('category', 'watchlist')
                ->where('action', $action)
                ->get();
        }
    }

    /**
     * Get this user's last month's play events for play watchlist (category = watchlist)
     *
     * @return mixed
     */
    public function playWatchlistEventsLastMonth()
    {
        return $this->playEvents()
            ->where('category', 'watchlist')
            ->whereMonth('created_at', '=', Carbon::now()->subMonth()->month)
            ->get();
    }

    /**
     * Get this user's last week's play events for play watchlist (category = watchlist)
     *
     * @return mixed
     */
    public function playWatchlistEventsLastDays($number_of_days = 7)
    {
        return $this->playEventsLastDays('watchlist', $number_of_days);
    }

    /**
     * Get this user's person events in general
     *
     * @return mixed
     */
    public function personEvents()
    {
        return $this->hasMany(PersonEvent::class);
    }

    /**
     * Get this user's last days' person events
     *
     * @return mixed
     */
    public function personEventsLastDays($event_category = null, $number_of_days = 7)
    {
        if ($event_category === null) {
            return $this->personEvents()
                ->whereDate('created_at', '>=', Carbon::today()->subDays($number_of_days))
                ->get();
        }

        return $this->personEvents()
            ->where('category', $event_category)
            ->whereDate('created_at', '>=', Carbon::today()->subDays($number_of_days))
            ->get();
    }

    /**
     * Get this user's person events for person visits (category = person)
     *
     * @return mixed
     */
    public function personVisitEvents($action = null)
    {
        // if the action is not one of the defined values for this category return all events of category
        if ($action != 'main_visit' && $action != 'media_visit' && $action != 'bio_visit') {
            return $this->personEvents()
                ->where('category', 'person')
                ->get();
        } else {
            return $this->personEvents()
                ->where('category', 'person')
                ->where('action', $action)
                ->get();
        }
    }

    /**
     * Get this user's last month's person events for person visits (category = person)
     *
     * @return mixed
     */
    public function personVisitEventsLastMonth()
    {
        return $this->personEvents()
            ->where('category', 'person')
            ->whereMonth('created_at', '=', Carbon::now()->subMonth()->month)
            ->get();
    }

    /**
     * Get this user's last week's person events for person visits (category = person)
     *
     * @return mixed
     */
    public function personVisitEventsLastDays($number_of_days = 7)
    {
        return $this->personEventsLastDays('person', $number_of_days);
    }


    /**
     * Send the password reset notification.
     *
     * @param string $token
     * @return void
     */
    public function sendPasswordResetNotification($token)
    {
        $this->notify((new ResetPassword($token))->onQueue(config('queue.emails.title')));
    }

    /**
     * Send email verification notification.
     *
     * @param string $token
     * @return void
     */
    public function sendEmailVerificationNotification($token)
    {
        $this->notify((new VerifyEmail($token))->onQueue(config('queue.emails.title')));
    }

    /**
     * Send watchlist reminder notification.
     *
     * @param string $token
     * @return void
     */
    public function sendWatchlistReminderNotification($remindersStarting, $remindersEnding)
    {
        $this->notify((new WatchlistReminder($this, $remindersStarting, $remindersEnding))->onQueue(config('queue.emails.title')));

        activity('sendWatchlistReminderNotification')->causedBy($this)->withProperties(['starting' => $remindersStarting, 'ending' => $remindersEnding])->log('');

        $notifyDB = $env = env('NOTIFY_DB', false);
        if ($notifyDB) {
            foreach ($remindersStarting as $plays) {
                $this->notify((new WatchlistNotification($plays, 'starting')));
            }
            foreach ($remindersEnding as $plays) {
                $this->notify((new WatchlistNotification($plays, 'ending')));
            }
        }
    }

    /**
     * Send recommendation reminder notification.
     *
     * @param string $token
     * @return void
     */
    public function sendRecommendationReminderNotification($reminders)
    {
        // send the mail notification containing the recommendations
        $this
            ->notify((new RecommendationReminder($this, $reminders))
                ->onQueue(config('queue.emails.title'))); //todo what/where is this?

        // log the notifications in order to not notify teh same user fer teh same play again
        // the keys of the reminders array are the play IDs that are going to be sent fer notification
        foreach (array_keys($reminders) as $play_id) {
            $recommendation = Recommendation::create(['user_id' => $this->id, 'play_id' => $play_id, 'type' => 'play']);
        }

        // log the activity of sending the recommendations
        activity('sendRecommendationReminderNotification')
            ->causedBy($this)
            ->withProperties(['plays' => $reminders])
            ->log('');

        $notifyDB = $env = env('NOTIFY_DB', false);
        if ($notifyDB) {
            foreach (array_keys($reminders) as $play_id) {
                $this->notify((new RecommendationNotification($play_id, 'recommendation')));
            }
        }
    }

    /**
     * Send streaming recommendation reminder notification.
     *
     * @param string $token
     * @return void
     */
    public function sendStreamingRecommendationReminderNotification($reminders)
    {
        // send the mail notification containing the recommendations
        $this
            ->notify((new StreamingRecommendationReminder($this, $reminders))
                ->onQueue(config('queue.emails.title'))); //todo what/where is this?

        // log the notifications in order to not notify teh same user fer teh same play again
        // the keys of the reminders array are the play IDs that are going to be sent fer notification
        foreach (array_keys($reminders) as $play_id) {
            $recommendation = Recommendation::create(['user_id' => $this->id, 'play_id' => $play_id, 'type' => 'streaming']);
        }

        // log the activity of sending the recommendations
        activity('sendStreamingRecommendationReminderNotification')
            ->causedBy($this)
            ->withProperties(['plays' => $reminders])
            ->log('');

        $notifyDB = $env = env('NOTIFY_DB', false);
        if ($notifyDB) {
            foreach (array_keys($reminders) as $play_id) {
                $this->notify((new StreamingRecommendationNotification($play_id, 'recommendation')));
            }
        }
    }

    /**
     * Send welcome email
     *
     * @return void
     */
    public function sendWelcomeEmail()
    {
        //send welcome email
        $this->notify((new WelcomeNewUser($this))
            ->onQueue(config('queue.emails.title'))); //todo what/where is this?

        // log the sending of the welcome email in order to not
        // welcome teh same user again
        $welcome_email = UserMetadata::updateOrCreate(
            ['user_id' => $this->id],
            ['welcome_email' => true]
        );
    }

    /**
     * Send unstagePro welcome email
     *
     * @return void
     */
    public function sendProWelcomeEmail()
    {
        //send unstagePro welcome email
        $this->notify((new WelcomeProUser($this))
            ->onQueue(config('queue.emails.title'))); //todo what/where is this?

        // log the sending of the welcome email in order to not
        // welcome teh same user again
        $pro_welcome_email = UserMetadata::updateOrCreate(
            ['user_id' => $this->id],
            ['pro_welcome_email' => true]
        );
    }

    /**
     * Check whether we have sent a welcome email to the specified user
     * @return bool
     */
    public function hasReceivedWelcomeEmail()
    {
        // check if user has already received the welcome email
        $welcome_email = DB::table('user_metadata')
            ->where('user_id', '=', $this->id)
            ->where('welcome_email', '=', true)
            ->get();

        return $welcome_email->isEmpty() ? false : true;
    }

    /**
     * Check whether we have sent a unstagePro welcome email to the specified user
     * @return bool
     */
    public function hasReceivedProWelcomeEmail()
    {
        // check if user has already received the unstagePro welcome email
        $pro_welcome_email = DB::table('user_metadata')
            ->where('user_id', '=', $this->id)
            ->where('pro_welcome_email', '=', true)
            ->get();

        return $pro_welcome_email->isEmpty() ? false : true;
    }

    /**
     *
     * Returns the pro registered people associated with a user
     */
    public function proPeople()
    {
        return $this->belongsToMany(Person::class, 'person_user', 'user_id', 'person_id')
            ->withPivot('type')
            ->withTimestamps();
    }

    /**
     * @return bool
     *
     * Returns whether the user has people that are proEnabled
     * connected to them
     */
    public function isPro()
    {
        // if user is adminised we count it as pro enabled
        // (hack to allow users to input content without being people representatives)
        if($this->adminised)
        {
            return true;
        }

        $people = $this->proPeople()->get();
        foreach ($people as $person) {
            if ($person->isPro()) {
                return true;
            }
        }

        return false;
    }

    /**
     * @param Person $person
     * @return bool
     *
     * Returns whether the user is related to the specified person who is ProEnabled
     */
    public function hasProPerson(Person $person)
    {
        // basic checks
        if (empty($person))
        {
            return false;
        }

        $ret = $this->proPeople()->where('person_id', $person->id)->first();

        if( ! empty($ret) )
        {
            return true;
        }

        return false;
    }
}
