<div class="row">
    <div class="col-sm-6 col-lg-10">
        <!-- Panel Static Lables -->
        <div class="panel">
            <div class="panel-body container-fluid">
                <div class="form-group row">
                    <div class="col-sm-12">
                        <label class="control-label" for="title">Από χρήστη:</label>
                        <p>
                        @if( ! $seminar->user )
                            ΟΧΙ
                        @else
                            <a href="{{ route('uranus.users.show', $seminar->user_id) }}" target="_blank">{{ $seminar->user->fullName }}, {{ $seminar->user->email }}</a>
                        </p>
                        @endif
                    </div>
                    <div class="col-sm-12">
                        <label class="control-label" for="title">Για συντελεστή:</label>
                        <p>
                        @if( ! $seminar->person )
                            ΟΧΙ
                        @else
                            <a href="{{ route('uranus.people.edit', $seminar->person_id) }}" target="_blank">{{ $seminar->person->fullName }}</a>
                        </p>
                        @endif
                    </div>
                    <div class="col-sm-12">
                        <label class="control-label">Τίτλος:</label>
                        <p>{{ $seminar->title }}</p>
                        <input type="text" class="form-control" name="title" value="{{ old('title',$seminar->title) }}">
                    </div>
                    <div class="col-sm-12">
                        <label class="control-label">Additional info:</label>
                        <p>{{ $seminar->additional_info }}</p>
                        <input type="text" class="form-control" name="additional_info" value="{{ old('additional_info',$seminar->additional_info) }}">
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-sm-12 col-lg-6">
                        <label class="control-label">Έτος από:</label>
                        <p>{{ $seminar->year_from }}</p>
                    </div>
                    <div class="col-sm-12 col-lg-6">
                        <label class="control-label">Έτος έως:</label>
                        <p>{{ $seminar->year_to }}</p>
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-sm-12 col-lg-6">
                        <label class="control-label">Updated at:</label>
                        <p>{{ $seminar->updated_at }}</p>
                    </div>
                    <div class="col-sm-12 col-lg-6">
                        <label class="control-label">Created at:</label>
                        <p>{{ $seminar->created_at }}</p>
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-sm-12 col-lg-6">
                        <label class="control-label" for="inspected">Inspected:</label>
                        <select name="inspected" id="inspected" class="form-control">
                            <option value="0" {{ $seminar->inspected ? 'selected': '' }}>Οχι</option>
                            <option value="1" {{ $seminar->inspected ? 'selected': '' }}>Ναι</option>
                        </select>
                    </div>
                    <div class="col-sm-12 col-lg-6">
                        <label class="control-label" for="banned">Banned:</label>
                        <select name="banned" id="banned" class="form-control">
                            <option value="0" {{ $seminar->banned ? 'selected': '' }}>Οχι</option>
                            <option value="1" {{ $seminar->banned ? 'selected': '' }}>Ναι</option>
                        </select>
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-sm-12 col-md-6">
                        <button type="submit" class="btn btn-block btn-success">{{ $submitButtonText }}</button>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Panel Static Lables -->
    </div>
</div>