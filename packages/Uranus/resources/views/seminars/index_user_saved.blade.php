@extends('uranus::layout')
@section('content')
        <!-- Page -->
<div class="page animsition">
    <div class="page-header">
        <h1 class="page-title">User saved σεμινάρια</h1>

        <div class="page-header-actions">
        </div>
    </div>
    <div class="page-content">
        <!-- Panel Basic -->
        <div class="panel">

            <div class="panel-body">
                <form action="{{ route('uranus.seminars.user_saved.index') }}" method="get">
                    <div class="form-group row">
                        <div class="col-sm-12 col-md-6">
                            <label for="q">Τίτλος / additional info / url</label>
                            <input type="text" id="search_query" name="q" value="{{ request('q') }}">
                        </div>
                        <div class="col-sm-12 col-md-6">
                            <label for="user_id">ID χρήστου καταχωρητού</label>
                            <input type="text" id="user_id" name="user_id" value="{{ request('user_id') }}">
                        </div>
                    </div>
                    <div class="form-group row">
                        <div class="col-sm-12 col-md-6">
                            <label for="inspected">Inspected</label>
                            <select name="inspected" id="inspected" class="form-control">
                                <option disabled selected value>Επίλεξε</option>
                                <option value="all" @if(request('inspected') == 'all') selected @endif>Όλα</option>
                                <option value="inspected" @if(request('inspected') == 'inspected') selected @endif>Inspected</option>
                                <option value="uninspected" @if(request('inspected') == 'uninspected') selected @endif>Uninspected</option>
                            </select>
                        </div>
                        <div class="col-sm-12 col-md-6">
                            <label for="banned">Banned</label>
                            <select name="banned" id="banned" class="form-control">
                                <option disabled selected value>Επίλεξε</option>
                                <option value="all" @if(request('banned') == 'all') selected @endif>Όλα</option>
                                <option value="banned" @if(request('banned') == 'banned') selected @endif>Banned</option>
                                <option value="unbanned" @if(request('banned') == 'unbanned') selected @endif>Unbanned</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group row">
                        <div class="col-sm-12 col-md-4">
                            <button type="submit">Αναζήτηση</button>
                            <a href="{{ route('uranus.seminars.user_saved.index') }}">Όλα</a>
                        </div>
                    </div>
                </form>

                <table class="table table-hover dataTable table-striped width-full" data-plugin=" ">
                    <thead>
                        <tr>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.seminars.user_saved.index','Ban','banned') !!}</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.seminars.user_saved.index','Insp','inspected') !!}</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.seminars.user_saved.index','Τίτλος','title') !!}</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.seminars.user_saved.index','Έτος από','year_from') !!}</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.seminars.user_saved.index','Έτος έως','year_to') !!}</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.seminars.user_saved.index','Τελευταία δραστηριότητα','updated_at') !!}</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.seminars.user_saved.index','Χρήστης','user_id') !!}</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($seminars as $seminar)
                            <tr>
                                <td>
                                @if($seminar->banned)
                                    <div class="text-center">
                                        <i class="icon wb-thumb-down red-600" aria-hidden="true" title="Banned"></i>
                                    </div>
                                @else
                                    <div class="text-center">
                                        <i class="icon wb-thumb-up green-600" aria-hidden="true" title="Unbanned"></i>
                                    </div>
                                @endif
                                </td>
                                <td>
                                @if($seminar->inspected)
                                    <div class="text-center">
                                        <i class="icon wb-check-circle green-600" aria-hidden="true" title="Inspected"></i>
                                    </div>
                                @else
                                    <div class="text-center">
                                        <i class="icon wb-alert-circle red-600" aria-hidden="true" title="Uninspected"></i>
                                    </div>
                                @endif
                                </td>
                                <td><a href="{!! route('uranus.seminars.edit', $seminar->id) !!}" target="_blank">{!! $seminar->title !!}</a></td>
                                <td>{!! $seminar->year_from !!}</td>
                                <td>{!! $seminar->year_to !!}</td>
                                <td>{!! $seminar->updated_at !!}</td>
                                <td>{!! $seminar->user_id !!}</td>
                                @admincan('delete_plays')
                                <td>
                                    <a data-url="{!! route('uranus.seminars.destroy', $seminar->id) !!}" href="" class="deleteResource">
                                        <button type="button" class="btn btn-icon btn-danger btn-outline" title="Διαγραφή">
                                            <i class="fa fa-times" aria-hidden="true"></i></button>
                                    </a>
                                </td>
                                @endadmincan
                            </tr>
                        @empty
                            <p>Δε βρέθηκαν παραστάσεις</p>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
        <!-- End Panel Basic -->
        {!! $seminars->render() !!}
    </div>
</div>
<!-- End Page -->
{!! csrf_field() !!}

@stop
@section('scripts')
    @parent
    {!! csrf_field() !!}
    <script src="{{ asset('js/admin/jquery.deleteResource.js') }}"></script>
    <script src="{{ asset('js/admin/jquery.mouseoverPopover.js') }}"></script>
@stop
