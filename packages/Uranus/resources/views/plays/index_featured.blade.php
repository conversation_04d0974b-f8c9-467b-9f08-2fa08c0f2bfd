@extends('uranus::layout')
@section('content')
        <!-- Page -->
<div class="page animsition">
    <div class="page-header">
        <h1 class="page-title">Featured παραστάσεις</h1>

        <div class="page-header-actions">
            <a href="{!! route('uranus.plays.create') !!}">
                <button type="button" class="btn btn-floating btn-danger">
                    <i class="icon wb-plus" aria-hidden="true"></i></button>
            </a>
        </div>
    </div>
    <div class="page-content">
        <!-- Panel Basic -->
        <div class="panel">

            <div class="panel-body">

                {{--Search bar--}}
                {{--<form action="{{ route('uranus.plays.featured.index') }}" method="get" class="search_form">--}}
                    {{--<input type="text" id="search_query" name="q">--}}
                    {{--<button type="submit">Αναζήτηση</button>--}}
                {{--</form>--}}

                <table class="table table-hover dataTable table-striped width-full" data-plugin=" ">
                    <thead>
                        <tr>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.plays.featured.index','Δημ','published') !!}</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.plays.featured.index','Τίτλος','title') !!}</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.plays.featured.index','Θέατρο','theatre_id') !!}</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.plays.featured.index','Έναρξη','start_date') !!}</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.plays.featured.index','Λήξη','end_date') !!}</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.plays.featured.index','Τελευταία δραστηριότητα','updated_at') !!}</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.plays.featured.index','Σημειώσεις','notes') !!}</th>
                            <th>Tick for Featured</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($plays as $play)
                            <tr>
                                <td>@if($play->published)
                                        <div class="text-center">
                                            <i class="icon wb-eye green-600" aria-hidden="true" title="Δημοσιευμένη"></i>
                                        </div>
                                    @else
                                        <div class="text-center">
                                            <i class="icon wb-eye-close red-600" aria-hidden="true" title="Μη δημοσιευμένη"></i>
                                        </div>
                                    @endif
                                </td>
                                <td class="showPopoverInfo" data-url="{!! route('uranus.plays.popoverInfo') !!}" data-id="{!! $play->id !!}">{!! link_to_route('uranus.plays.edit', $play->title, $play->id) !!}</td>
                                @if($play->theatre)
                                    <td>{!! link_to_route('uranus.theatres.edit', $play->theatre->name, $play->theatre->id) !!}</td>
                                @else
                                    <td></td>
                                @endif
                                <td>{!! $play->start_date !!}</td>
                                <td>{!! $play->end_date !!}</td>
                                <td>{!! $play->updated_at !!}</td>
                                <td>{!! $play->notes !!}</td>
                                <td>
                                    <input type="checkbox" name="featured" data-url="{!! route('uranus.plays.featured.update') !!}" data-theatricPlayId="{!! $play->id !!}" class="featuredTheatricPlay" {!! isset($play->featured) ? 'checked' : null !!}>
                                </td>
                            </tr>
                        @empty
                            <p>Δε βρέθηκαν έργα</p>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
        <!-- End Panel Basic -->
        {!! $plays->render() !!}
    </div>
</div>
<!-- End Page -->
{!! csrf_field() !!}

@stop
@section('scripts')
    @parent
    <script src="{{ asset('js/admin/jquery.mouseoverPopover.js') }}"></script>
    <script src="{{ asset('js/admin/jquery.theatricPlays.featuredUpdate.js') }}"></script>
@stop
