@extends('uranus::layout')
@section('head')
    <link type="text/css" rel="stylesheet"
          href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.10.2/themes/smoothness/jquery-ui.min.css"
          media="screen"/>
    <link rel="stylesheet" href="{{asset('assets/vendor/bootstrap-datepicker/bootstrap-datepicker.css')}}">
    @stop

    @section('content')
            <!-- Page -->
    <div class="page animsition">
        <div class="page-header">
            <div class="page-header-actions">
                <a href="{!! route('uranus.plays.edit', $play_id) !!}">
                    <button type="button" class="btn btn-floating btn-info"
                            title="{!! trans('uranus::plays.edit_play') !!}">
                        <i class="icon wb-pencil" aria-hidden="true"></i></button>
                </a>
            </div>
        </div>
        <div class="page-content">
            <div class="container-fluid">
                @include('uranus::messages.successfulSave')
                @include('uranus::errors.genericForm')
                <div class="row">
                    <div class="col-sm-12">
                        <!-- Panel Static Lables -->
                        <div class="panel">
                            <div class="panel-heading">
                                <h4 class="panel-title">Οι χαρακτήρες</h4>
                            </div>
                            <div class="panel-body ">
                                <div class="container-fluid">
                                    @forelse($PersonPlayRoles as $PersonPlayRole)
                                        <div class="form-group row">
                                            <form action="{{ route('uranus.plays.characters.update', $PersonPlayRole->id) }}"
                                                  method="post">
                                                {!! csrf_field() !!}
                                                {!! method_field('put') !!}
                                                <div class="form-group row">
                                                    <div class="col-sm-12 col-md-12">
                                                        <label class="control-label"
                                                               for="character_{{ $PersonPlayRole->id }}">{{ $PersonPlayRole->person->first_name }} {{ $PersonPlayRole->person->last_name }}</label>
                                                    </div>
                                                    <div class="col-sm-12 col-md-9 col-lg-8">
                                                        <input type="text" class="form-control" name="character"
                                                               id="character_{{ $PersonPlayRole->id }}"
                                                               value="{{ $PersonPlayRole->character }}">
                                                        <input type="hidden" name="play_id" value="{{ $play_id }}"/>
                                                    </div>
                                                    <div class="col-sm-12 col-md-3 col-lg-2">
                                                        <button type="submit" class="btn btn-block btn-success">
                                                            Αποθήκευση
                                                        </button>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                    @empty
                                        Δε βρέθηκαν χαρακτήρες
                                    @endforelse
                                </div>
                            </div>
                        </div>
                        <!-- End Panel Static Lables -->
                    </div>
                    <!-- End Panel Floating Lables -->
                </div>
            </div>
        </div>
    </div>
    <!-- End Page -->
@stop
@section('footer')
    @parent
    <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.10.2/jquery-ui.min.js"
            charset="UTF-8"></script>
    <script src="{{asset('assets/vendor/bootstrap-datepicker/bootstrap-datepicker.js')}}"></script>
    <script src="{{asset('assets/js/components/bootstrap-datepicker.js')}}"></script>
@stop
