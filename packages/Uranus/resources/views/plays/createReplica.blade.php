@extends('uranus::layout')
@section('head')
    <link rel="stylesheet" href="//code.jquery.com/ui/1.11.4/themes/smoothness/jquery-ui.css">
    <style>
        .ui-autocomplete {
            max-height: 200px;
            overflow-y: auto;
            /* prevent horizontal scrollbar */
            overflow-x: hidden;
        }

        /* IE 6 doesn't support max-height
         * we use height instead, but this forces the menu to always be this tall
         */
        * html .ui-autocomplete {
            height: 100px;
        }
    </style>
    @stop

    @section('content')
            <!-- Page -->
    <div class="page animsition">
        <div class="page-header">
            <h1 class="page-title">Αντιγραφή Παράστασης "{{ $play->title }}"</h1>
        </div>
        <div class="page-content">
            <div class="container-fluid">
                @include('uranus::messages.successfulSave')
                @include('uranus::errors.genericForm')
                <form action="{{ route('uranus.plays.replicate') }}" method="post">
                    {!! csrf_field() !!}
                    <input type="hidden" name="playId" value="{{ $play->id }}">
                    <div class="row">
                        <div class="col-sm-6 col-sm-offset-3">
                            <div class="panel">
                                <div class="panel-body container-fluid">
                                    <div class="form-group row">
                                        <div class="col-sm-8 col-sm-offset-2">
                                            <label for="title">Τίτλος:*</label>
                                            <input disabled type="text" name="title" id="title" class="form-control" value="{{ old('title', $play->title) }}">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <div class="col-sm-8 col-sm-offset-2">
                                            <label for="year" class="control-label">Έτος έναρξης:</label>
                                            <input type="number" name="year" min="1900" id="year" value="{{ old('year',$play->year) }}" class="form-control">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <div class="col-sm-4 col-sm-offset-2">
                                            <div class="checkbox-custom checkbox-primary">
                                                <input id="withPeople" name="withPeople" type="checkbox" value="1" checked>
                                                <label for="withPeople">Συντελεστές</label>
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <div class="checkbox-custom checkbox-primary">
                                                <input id="withImages" name="withImages" type="checkbox" value="1" checked>
                                                <label for="withImages">Φωτογραφίες</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <div class="col-sm-5 col-sm-offset-5">
                                            <button type="submit" class="btn btn-block btn-success">Αντιγραφή
                                                <i class="fa fa-arrow-right pull-right"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- End Page -->
@stop
