@extends('uranus::layout')
@section('head')
    <link type="text/css" rel="stylesheet"
            href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.10.2/themes/smoothness/jquery-ui.min.css"
            media="screen"/>
    <link rel="stylesheet" href="{{asset('assets/vendor/bootstrap-datepicker/bootstrap-datepicker.css')}}">
    @stop

    @section('content')
            <!-- Page -->
    <div class="page animsition">
        <div class="page-header">
            <h1 class="page-title">{{$play->title}}</h1>
            <div class="page-header-actions">
                <a href="{!! route('uranus.plays.edit', $play->id) !!}">
                    <button type="button" class="btn btn-floating btn-info"
                            title="{!! trans('uranus::plays.edit_play') !!}">
                        <i class="icon wb-pencil" aria-hidden="true"></i></button>
                </a>
            </div>
        </div>
        <div class="page-content">
            <div class="container-fluid">
                @include('uranus::messages.successfulSave')
                @include('uranus::errors.genericForm')
                <div class="row">
                    <div class="col-sm-12">
                        <!-- Panel Static Lables -->
                        <div class="panel">
                            <div class="panel-heading">
                                <h4 class="panel-title">Επιλογή Πατέρα</h4>
                            </div>
                            <div class="panel-body container-fluid">
                                <form action="{{route('uranus.plays.parent.update', $play->id)}}" method="post" enctype="multipart/form-data">
                                    {!! csrf_field() !!}
                                    {!! method_field('put') !!}
                                    <div data-play-id="{!! $play->id !!}">
                                        <label for="parent_id">Parent</label>
                                        <select name="parent_id" id="parent_id" class="form-control">
                                        </select>
                                    </div>
                                        <br>
                                    <div class="form-group row">
                                        <div class="col-sm-12 col-md-6 col-md-offset-3">
                                            <button type="submit" class="btn btn-block btn-success">Αποθήκευση</button>
                                        </div>
                                    </div>
                                </form>
                                <form action="{{route('uranus.plays.parent.remove', $play->id)}}" method="post">
                                    {!! csrf_field() !!}
                                    {!! method_field('put') !!}
                                    <div class="form-group row">
                                        <div class="col-sm-12">
                                            <button type="submit" class="pull-right">Διαγραφή πατέρα</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <!-- End Panel Static Lables -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- End Page -->
@stop
@section('footer')
    @parent
    <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.10.2/jquery-ui.min.js"
            charset="UTF-8"></script>

    <script src="{{asset('js/admin/jquery.plays.editParentId.js')}}" id="select2WithPlaysSearch" data-url="{!! route('uranus.plays.playList') !!}"></script>
    <script>
        select2WithPlaysSearch('parent_id', {!! json_encode($parent_title) !!});
    </script>



@stop
