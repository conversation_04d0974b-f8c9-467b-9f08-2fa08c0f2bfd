<div class="form-group row">
    <div class="col-sm-12">
        <label for="title" class="control-label">Τίτλος:*</label>
        <input type="text" class="form-control" name="title" id="title" value="{!! $play->title !!}" readonly="readonly">
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12">
        <a style="cursor:pointer" data-url="{!! route('uranus.plays.translation.suggest') !!}" href="" id="translate_title">Πρότεινε μετάφρασιν</a>
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12">
        <label for="title_en" class="control-label">Τίτλος [Αγγλικά]:*</label>
        <input type="text" class="form-control" name="title_en" id="title_en" value="{{ old('title_en',($play->hasTranslation('en') ? $play->translate('en')->title : "")) }}">
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12">
        <label for="synopsis" class="control-label">Σύνοψη:</label>
        <textarea name="synopsis" id="synopsis" class="form-control" rows="2" readonly="readonly">{{ strip_tags($play->synopsis) }}</textarea>
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12">
        <a style="cursor:pointer" data-url="{!! route('uranus.plays.translation.suggest') !!}" href="" id="translate_synopsis">Πρότεινε μετάφρασιν</a>
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12">
        <label for="synopsis_en" class="control-label">Σύνοψη [Αγγλικά]:</label>
        <textarea name="synopsis_en" id="synopsis_en" class="form-control" rows="2">{{ old('synopsis_en', ($play->hasTranslation('en') ? strip_tags($play->translate('en')->synopsis) : "")) }}</textarea>
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12">
        <label for="storyline" class="control-label">Περιγραφή:</label>
        <textarea name="storyline" id="storyline" class="form-control" readonly="readonly">{{ strip_tags($play->storyline) }}</textarea>
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12">
        <a style="cursor:pointer" data-url="{!! route('uranus.plays.translation.suggest') !!}" href="" id="translate_storyline">Πρότεινε μετάφρασιν</a>
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12">
        <label for="storyline_en" class="control-label">Περιγραφή [Αγγλικά]:</label>
        <textarea name="storyline_en" id="storyline_en" class="form-control">{{ old('storyline_en',($play->hasTranslation('en') ? strip_tags($play->translate('en')->storyline) : "")) }}</textarea>
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12">
        <label for="extra_info" class="control-label">Επιπλέον Πληροφορίες:</label>
        <textarea name="extra_info" id="extra_info" class="form-control" rows="2" readonly="readonly">{{ strip_tags($play->extra_info) }}</textarea>
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12">
        <a style="cursor:pointer" data-url="{!! route('uranus.plays.translation.suggest') !!}" href="" id="translate_extra_info">Πρότεινε μετάφρασιν</a>
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12">
        <label for="extra_info_en" class="control-label">Επιπλέον Πληροφορίες [Αγγλικά]:</label>
        <textarea name="extra_info_en" id="extra_info_en" class="form-control" rows="2">{{ old('extra_info_en',($play->hasTranslation('en') ? strip_tags($play->translate('en')->extra_info) : "")) }}</textarea>
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12">
        <label for="extra_ongoing_info" class="control-label">Επιπλέον Προσωρινες Πληροφορίες <br>(εμφανιζονται μονο αν η παρασταση παιζεται αυτη την περιοδο):</label>
        <textarea name="extra_ongoing_info" id="extra_ongoing_info" class="form-control" rows="2" readonly="readonly">{{ strip_tags($play->extra_ongoing_info) }}</textarea>
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12">
        <a style="cursor:pointer" data-url="{!! route('uranus.plays.translation.suggest') !!}" href="" id="translate_extra_ongoing_info">Πρότεινε μετάφρασιν</a>
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12">
        <label for="extra_ongoing_info_en" class="control-label">Επιπλέον Προσωρινες Πληροφορίες <br>(εμφανιζονται μονο αν η παρασταση παιζεται αυτη την περιοδο) [Αγγλικά]:</label>
        <textarea name="extra_ongoing_info_en" id="extra_ongoing_info_en" class="form-control" rows="2">{{ old('extra_ongoing_info_en',($play->hasTranslation('en') ? strip_tags($play->translate('en')->extra_ongoing_info) : "")) }}</textarea>
    </div>
</div>

<div class="form-group row">
    <div class="col-sm-12 col-md-6 col-md-offset-3">
        <button type="submit" class="btn btn-block btn-success">{{ $submitButtonText }}</button>
    </div>
</div>


