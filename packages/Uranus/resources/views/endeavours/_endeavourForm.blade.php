<div class="form-group row">
    <div class="col-sm-12">
        <div class="checkbox-custom checkbox-primary">
            <input id="published" name="published" type="checkbox" {{ old('published',$endeavour->published)?'checked="checked"':'' }}>
            <label for="published">Δημοσιευμένο</label>
        </div>
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12 orange-600">
        <label for="notes" class="control-label">Notes (εσωτερικής χρήσεως):</label>
        <textarea name="notes" class="form-control">{{ old('notes',$endeavour->notes) }}</textarea>
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12">
        <label for="title" class="control-label">Τίτλος:*</label>
        <input type="text" class="form-control" name="title" id="title" value="{{ old('title',$endeavour->title) }}">
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12 col-md-12">
        <label for="variety_id" class="control-label">Variety:</label>
        <select name="variety_id" class="form-control">
            @foreach($varieties as $variety)
                <option value="{{ $variety->id }}" {{ ($variety->id == $endeavour->variety_id) || $variety->id == old('variety_id') ? 'selected="selected"' : '' }}>{{ $variety->name }}</option>
            @endforeach
        </select>
    </div>
</div>
{{--<div class="form-group row">--}}
    {{--<div class="col-sm-12 col-md-12">--}}
        {{--<label for="genre_list" class="control-label">Είδος:</label>--}}
        {{--<select name="genre_list[]" id="genre_list" class="form-control" multiple>--}}
            {{--@foreach($genres as $genre)--}}
                {{--<option value="{{ $genre->id }}" {{in_array($genre->id,$endeavour->genresIds)||(old('genre_list') && in_array($genre->id,old('genre_list')))?'selected="selected"':''}}>{{ $genre->name }}</option>--}}
            {{--@endforeach--}}
        {{--</select>--}}
    {{--</div>--}}
{{--</div>--}}

<div class="form-group row">
    <div class="col-sm-12 col-md-12">
        <label for="theatre_list" class="control-label">Venues:</label>
        <select name="theatre_list[]" id="theatre_list" class="form-control" multiple>
            @foreach($theatres as $theatre)
                <option value="{{ $theatre->id }}" {{in_array($theatre->id,$endeavour->theatres()->pluck('theatres.id')->all())||(old('theatre_list') && in_array($theatre->id,old('theatre_list')))?'selected="selected"':''}}>{{ $theatre->name }}</option>
            @endforeach
        </select>
    </div>
</div>

<div class="form-group row">
    <div class="col-sm-12 col-md-12">
        <label for="tv_channel_id" class="control-label">Κανάλι:</label>
        <select name="tv_channel_id" id="tv_channel_list" class="form-control">
            <option selected value="">επιλογή καναλιού</option>
            @foreach($tv_channels as $tv_channel)
                <option value="{{ $tv_channel->id }}" {{($tv_channel->id==$endeavour->tv_channel_id)||$tv_channel->id==old('tv_channel_id')?'selected="selected"':''}}>{{ $tv_channel->name }}</option>
            @endforeach
        </select>
    </div>
</div>

<div class="form-group row">
    <div class="col-sm-12">
        <label for="synopsis" class="control-label">Σύνοψη:</label>
        <textarea name="synopsis" id="synopsis" class="form-control" rows="2">{!! old('synopsis',$endeavour->synopsis) !!}</textarea>
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12">
        <label for="description" class="control-label">Περιγραφή:</label>
        <textarea name="description" id="description" class="form-control wysiwyg">{!! old('description',$endeavour->description) !!}</textarea>
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12">
        <label for="extra_info" class="control-label">Επιπλέον Πληροφορίες:</label>
        <textarea name="extra_info" id="extra_info" class="form-control wysiwyg" rows="2">{!! old('extra_info',$endeavour->extra_info) !!}</textarea>
    </div>
</div>

{{--<div class="form-group row">--}}
    {{--<div class="col-sm-12">--}}
        {{--<label for="facebook" class="control-label">Facebook page:</label>--}}
        {{--<input type="text" class="form-control" name="facebook" id="facebook" value="{{ old('facebook',$endeavour->facebook) }}">--}}
    {{--</div>--}}
{{--</div>--}}
<br>
<h4>Υπόλοιπες πληροφορίες</h4>
<div class="form-group row">
    <div class="col-sm-12 col-md-6">
        <label for="year" class="control-label">Έτος:*</label>
        <input type="number" name="year" id="year" value="{{ old('year',$endeavour->year) }}" class="form-control">
    </div>
    <div class="col-sm-12 col-md-6">
        <label for="duration" class="control-label">Διάρκεια (mins):</label>
        <input type="number" name="duration" id="duration" value="{{ old('duration',$endeavour->duration) }}" class="form-control">
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12 col-md-6 margin-bottom-10">
        <label for="start_date" class="control-label">Ημ/νια έναρξης:</label>
        <input type="text" name="start_date" id="start_date" value="{{$endeavour->start_date}}" class="form-control" data-plugin="datepicker" data-date-format="dd/mm/yyyy" data-week-start=1>
    </div>
    <div class="col-sm-12 col-md-6 margin-bottom-10">
        <label for="end_date" class="control-label">Ημ/νια λήξης:</label>
        <input type="text" name="end_date" id="end_date" value="{{$endeavour->end_date}}" class="form-control" data-plugin="datepicker" data-date-format="dd/mm/yyyy" data-week-start=1>
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12">
        <label for="ticket_price" class="control-label">Τιμή εισιτηρίου:</label>
        <textarea name="ticket_price" id="ticket_price" class="form-control" rows="2">{!! old('ticket_price',$endeavour->ticket_price) !!}</textarea>
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12">
        <label for="online_tickets_1" class="control-label">Online tickets 1:</label>
        <input type="text" name="online_tickets_1" id="online_tickets_1" value="{{ old('online_tickets_1',$endeavour->online_tickets_1) }}" class="form-control">
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12">
        <label for="online_tickets_2" class="control-label">Online tickets 2:</label>
        <input type="text" name="online_tickets_2" id="online_tickets_2" value="{{ old('online_tickets_2',$endeavour->online_tickets_2) }}" class="form-control">
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12">
        <label for="online_tickets_3" class="control-label">Online tickets 3:</label>
        <input type="text" name="online_tickets_3" id="online_tickets_3" value="{{ old('online_tickets_3',$endeavour->online_tickets_3) }}" class="form-control">
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12 col-md-6">
        <label for="featured" class="control-label">Featured:</label>
        <select name="featured" id="featured" class="form-control">
            <option value="0" {{ $endeavour->featured ? 'selected': '' }}>Οχι</option>
            <option value="1" {{ $endeavour->featured ? 'selected': '' }}>Ναι</option>
        </select>
    </div>
    <div class="col-sm-12 col-md-6">
        <label for="handpicked" class="control-label">Handpicked:</label>
        <select name="handpicked" id="handpicked" class="form-control">
            <option value="0" {{ $endeavour->handpicked ? 'selected': '' }}>Οχι</option>
            <option value="1" {{ $endeavour->handpicked ? 'selected': '' }}>Ναι</option>
        </select>
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12 col-md-6">
        <button type="submit" class="btn btn-block btn-success">{{ $submitButtonText }}</button>
    </div>
</div>
<br>
@if($endeavour->user_id != null)
<h4>Πληροφορίες χρήστου καταχωρητού</h4>
<div class="form-group row">
    <div class="col-sm-12">
        <p class="control-label">User: {{ $endeavour->user->fullname }} (id: {{ $endeavour->user->id }})</p>
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12 orange-600">
        <label for="moderated" class="control-label">Moderated:</label>
        <select name="moderated" id="moderated" class="form-control">
            <option value="0" {{ $endeavour->moderated ? 'selected': '' }}>Οχι</option>
            <option value="1" {{ $endeavour->moderated ? 'selected': '' }}>Ναι</option>
        </select>
    </div>
</div>
{{--<div class="form-group row">--}}
{{--    <div class="col-sm-12">--}}
{{--        <label class="control-label">Αγγλικός τίτλος:</label>--}}
{{--        @if( !$endeavour->hasTranslation('en') )--}}
{{--        <a target="_blank" href="{{ route('uranus.plays.translatable.edit', $endeavour->id) }}">No (en) title</a>--}}
{{--        @else--}}
{{--        <p class="">{{ $endeavour->translate('en')->title }}</p>--}}
{{--        @endif--}}
{{--    </div>--}}
{{--</div>--}}
<div class="form-group row">
    <div class="col-sm-12 orange-600">
        <label class="control-label">User Notes (δεν αποθηκεύονται από εμάς):</label>
        <textarea disabled class="form-control wysiwyg">{!! $endeavour->user_notes !!}</textarea>
    </div>
</div>
@endif

@section('footer')
    @parent
    <script type="text/javascript" src="{{ asset("js/admin/tinymce/tinymce.min.js") }}"></script>
    <script type="text/javascript">
        tinymce.init({
            selector: ".wysiwyg",
            plugins: [
                'advlist autolink lists link charmap preview anchor',
                'paste code fullscreen hr'
            ],
            entity_encoding: "raw",
            paste_auto_cleanup_on_paste: true,
            paste_preprocess: function (pl, o)
            {
                // Content string containing the HTML from the clipboard
                o.content = o.content;
            },
            paste_postprocess: function (pl, o)
            {
                // Content DOM node containing the DOM structure of the clipboard
                o.node.innerHTML = o.node.innerHTML;
            }
        });
    </script>
    <script>
        $('#theatre_list').select2({
            placeholder: 'Choose a venue'
        });
    </script>

@stop
