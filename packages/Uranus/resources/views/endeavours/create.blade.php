@extends('uranus::layout')
@section('head')
    <link rel="stylesheet" href="https://code.jquery.com/ui/1.11.4/themes/smoothness/jquery-ui.css">
    <style>
        .ui-autocomplete {
            max-height: 200px;
            overflow-y: auto;
            /* prevent horizontal scrollbar */
            overflow-x: hidden;
        }

        /* IE 6 doesn't support max-height
         * we use height instead, but this forces the menu to always be this tall
         */
        * html .ui-autocomplete {
            height: 100px;
        }
    </style>
    @stop

@section('content')
        <!-- Page -->
<div class="page animsition">
    <div class="page-header">
        <h1 class="page-title">Καταχώρηση νέου Endeavour</h1>
    </div>
    <div class="page-content">
        <div class="container-fluid">
            @include('uranus::messages.successfulSave')
            @include('uranus::errors.genericForm')
            <form action="{{ route('uranus.endeavours.store') }}" method="post">
                {!! csrf_field() !!}
                <div class="row">
                    <div class="col-sm-6 col-sm-offset-3">
                        <div class="panel">
                            <div class="panel-body container-fluid">
                                <div class="form-group row">
                                    <div class="col-sm-8 col-sm-offset-2">
                                        <label for="title">Τίτλος:*</label>
                                        <input autofocus type="text" name="title" id="title" class="form-control" value="{{ old('title') }}">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-8 col-sm-offset-2">
                                        <label for="year" class="control-label">Ετος:*</label>
                                        <input type="number" name="year" min="1900" id="year" value="{{ old('year',\Carbon\Carbon::now()->year) }}" class="form-control">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-8 col-sm-offset-2">
                                        <label for="variety_id" class="control-label">Variety:*</label>
                                        <select name="variety_id" class="form-control">
                                            <option disabled selected>επίλεξε</option>
                                            @foreach($varieties as $variety)
                                                <option value="{{ $variety->id }}" {{ $variety->id == old('variety_id') ? 'selected="selected"' : ''}}>{{ $variety->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-5 col-sm-offset-5">
                                        <button type="submit" class="btn btn-block btn-success">Συνέχεια
                                            <i class="fa fa-arrow-right pull-right"></i></button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<!-- End Page -->
@stop
@section('footer')
    @parent
    <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.11.4/jquery-ui.min.js" charset="UTF-8"></script>
    <script>
        $(function() {
            var names = [];
            @foreach($endeavours as $endeavour)
                    names.push({!! htmlspecialchars(json_encode($endeavour), ENT_NOQUOTES, 'UTF-8') !!});
                    @endforeach

            var accentMap = {
                        "ά": "α",
                        "Ά": "α",
                        "έ": "ε",
                        "Έ": "ε",
                        "ό": "ο",
                        "Ό": "ο",
                        "Ί": "ι",
                        "ί": "ι",
                        "ή": "η",
                        "Ή": "η",
                        "ύ": "υ",
                        "Ύ": "υ",
                    };

            var normalize = function( term ) {
                var ret = "";
                for ( var i = 0; i < term.length; i++ ) {
                    ret += accentMap[ term.charAt(i) ] || term.charAt(i);
                }
                return ret;
            };

            $( "#title" ).autocomplete({
                source: function( request, response ) {
                    var matcher = new RegExp( $.ui.autocomplete.escapeRegex( request.term ), "i" );
                    response( $.grep( names, function( value ) {
                        value = value.label || value.value || value;
                        return matcher.test( value ) || matcher.test( normalize( value ) );
                    }) );
                },
                minLength: 3
            });

        });
    </script>
@stop
