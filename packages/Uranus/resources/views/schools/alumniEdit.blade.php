@extends('uranus::layout')
@section('content')
<!-- Page -->
<div class="page animsition">
    <div class="page-header">
        <h1 class="page-title">{{$school->name}}: Απόφοιτοι</h1>
        <div class="page-header-actions">
            <a title="Επεξεργασία School {{ $school->name }}" href="{!! route('uranus.schools.edit', $school->id) !!}">
                <button type="button" class="btn btn-floating btn-info"><i class="icon wb-pencil" aria-hidden="true"></i></button>
            </a>
            <a title="Δημιουργία Νέου school" href="{!! route('uranus.schools.create') !!}">
                <button type="button" class="btn btn-floating btn-danger"><i class="icon wb-plus" aria-hidden="true"></i></button>
            </a>
        </div>
    </div>
    <div class="page-content">
        <div class="container-fluid">
            <h3>Σύνολο αποφοίτων {{ $school->people()->count() }}</h3>
            @include('uranus::messages.successfulSave')
            @include('uranus::errors.genericForm')
            {{--Search bar--}}
            <form method="get" class="search_form" action="{{ route('uranus.schools.alumni.edit', $school->id) }}">
                <div class="form-group row">
                    <div class="col-sm-12  col-md-4">
                        <label for="year">Έτος</label>
                        <select name="year" id="search_query" class="form-control">
                            <option disabled selected value>Επίλεξε έτος</option>
                            <option value="0" @if(request('year') == '0') selected @endif>0</option>
                            <?php $current_year = \Carbon\Carbon::now()->year; ?>
                            @for($i = $current_year; $i >= 1900; $i--)
                                <option value="{{ $i }}" @if(request('year') == $i) selected @endif>{{ $i }}</option>
                            @endfor
                        </select>
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-sm-12 col-md-6">
                        <button type="submit">Αναζήτηση</button>
                        <a href="{{ route('uranus.schools.alumni.edit', $school->id) }}">Όλοι</a>
                    </div>
                </div>
            </form>
            <table class="table table-hover dataTable table-striped width-full" data-plugin=" ">
                <thead>
                <tr>
                    <th>Έτος</th>
                    <th>Είδος Αποφοίτησης</th>
                    <th>Ονοματεπώνυμο</th>
                    <th></th>
                </tr>
                </thead>
                <tbody>
                @forelse($alumni as $alumnus)
                    <tr>
                        <td><a target="_blank" href="{!! route('uranus.alumni.edit', $alumnus->id) !!}">{{ $alumnus->year }}</a></td>
                        <td><a target="_blank" href="{!! route('uranus.alumni.edit', $alumnus->id) !!}">{{ $alumnus->graduation_type }}</a></td>
                        <td><a target="_blank" href="{{ route('uranus.people.edit', $alumnus->person_id) }}">{{ $alumnus->person()->first()->fullName }}</a></td>
                        <td>
                            <a target="_blank" href="{!! route('uranus.alumni.edit', $alumnus->id) !!}">
                                <button type="button" class="btn btn-icon btn-default btn-outline" title="Επεξεργασία">
                                    <i class="icon wb-pencil" aria-hidden="true"></i></button>
                            </a>
                        </td>
                        @admincan('delete_theatres')
                        <td>
                            <a data-url="{!! route('uranus.alumni.destroy', $alumnus->id) !!}" href="" class="deleteResource">
                                <button type="button" class="btn btn-icon btn-danger btn-outline">
                                    <i class="fa fa-times" aria-hidden="true"></i></button>
                            </a>
                        </td>
                        @endadmincan
                    </tr>
                @empty
                    <p>Δε βρέθηκαν alumni</p>
                @endforelse
                </tbody>
            </table>
            {!! $alumni->render() !!}


            <div class="panel">
                <div class="panel-heading">
                    <h3 class="panel-title">Σύνδεση συντελεστών (επί τη επιλογή των)</h3>
                </div>
                <div class="panel-body container-fluid">
                    <div class="form-group row">
                        <div class="col-sm-12 col-md-4">
                            <div>
                                <label for="year">Έτος:</label>
                                <input type="number" min="1900" step="1" name="year" id="year_element" class="form-control" />
                            </div>
                        </div>
                    </div>
                    <div class="form-group row">
                        <div class="col-sm-12 col-md-12">
                            <div data-school-id="{!! $school->id !!}"
                                 data-destroy-url="{!! route('uranus.schoolPerson.destroy') !!}"
                                 data-store-url="{!! route('uranus.schoolPerson.store') !!}">
                                <label for="people_list">Συντελεστές:</label>
                                <select name="people_list" id="people_list" class="form-control" multiple>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            @include('uranus::_partials._personCreator')

        </div>
    </div>
</div>
@section('scripts')
    @parent
    {!! csrf_field() !!}
    <script src="{{ asset('js/admin/jquery.deleteResource.js') }}"></script>

    <script src="{{asset('js/admin/jquery.schools._schoolFormPeople.js')}}" id="select2WithPeopleSearch"
            data-url="{!! route('uranus.people.personList') !!}"></script>

    <script>
        select2WithPeopleSearch('people_list');
    </script>
@stop

@section('footer')
    @parent

    <script>
        $('#search_query').select2({
            placeholder: 'Choose a year'
        });
    </script>
@stop
