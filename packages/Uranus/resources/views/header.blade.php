<div class="site-menubar">
    <div class="site-menubar-body">
        <div>
            <div>
                <ul class="site-menu">
                    <li class="site-menu-category">Καλωσηλθες {!! auth('admin')->user()->username !!}</li>
                    <li class="site-menu-item ">
                        <a href="{!! route('uranus.dashboard') !!}">
                            <i class="site-menu-icon fa-dashboard" aria-hidden="true"></i>
                            <span class="site-menu-title">Αρχική</span>
                        </a>
                    </li>
                    @admincan('see_plays')
                    <li class="site-menu-item has-sub
                    {{ ((Request::is('uranus/plays')
                    || Request::is('*plays/featured')
                    || Request::is('*plays/unpublished')
                    || Request::is('*plays/handpicked')
                    || Request::is('*plays/repeated')
                    || Request::is('*plays/unfinalised')
                    || Request::is('*reviews')
                    || Request::is('*referralClicks')
                    ) ? 'active open' : '') }}
                            ">
                        <a href="javascript:void(0)">
                            <i class="site-menu-icon wb-book" aria-hidden="true"></i>
                            <span class="site-menu-title">Παραστάσεις</span>
                            <span class="site-menu-arrow"></span>
                        </a>
                        <ul class="site-menu-sub">
                            <li class="site-menu-item  {{ ((Request::is('uranus/plays')) ? 'active' : '') }} ">
                                <a href="{!! route('uranus.plays.index') !!}">
                                    <span class="site-menu-title">Όλες</span>
                                </a>
                            </li>
                            @admincan('manage_plays')
                            <li class="site-menu-item">
                                <a href="{!! route('uranus.plays.create') !!}">
                                    <span class="site-menu-title">Δημιουργία νέας</span>
                                </a>
                            </li>
                            @endadmincan
                            <li class="site-menu-item   {{ ((Request::is('*plays/featured')  ) ? 'active' : '') }}">
                                <a href="{!! route('uranus.plays.featured.index') !!}">
                                    <span class="site-menu-title">Featured</span>
                                </a>
                            </li>
                            <li class="site-menu-item {{ ((Request::is('*plays/unpublished')  ) ? 'active' : '') }}">
                                <a href="{!! route('uranus.plays.unpublished.index') !!}">
                                    <span class="site-menu-title">Unpublished</span>
                                </a>
                            </li>
                            <li class="site-menu-item   {{ ((Request::is('*plays/handpicked')  ) ? 'active' : '') }}">
                                <a href="{!! route('uranus.plays.handpicked.index') !!}">
                                    <span class="site-menu-title">Handpicked</span>
                                </a>
                            </li>
                            <li class="site-menu-item   {{ ((Request::is('*plays/repeated')  ) ? 'active' : '') }}">
                                <a href="{!! route('uranus.plays.repeated.index') !!}">
                                    <span class="site-menu-title">Επαναλήψεις</span>
                                </a>
                            </li>
                            <li class="site-menu-item {{ ((Request::is('*plays/unfinalised')  ) ? 'active' : '') }}">
                                <a href="{!! route('uranus.plays.unfinalised.index') !!}">
                                    <span class="site-menu-title">Unfinalised</span>
                                </a>
                            </li>
                            <li class="site-menu-item   {{ ((Request::is('*reviews')  ) ? 'active' : '') }}">
                                <a href="{!! route('uranus.reviews.index') !!}">
                                    <span class="site-menu-title">Reviews</span>
                                </a>
                            </li>
                            <li class="site-menu-item   {{ ((Request::is('*referralClicks')  ) ? 'active' : '') }}">
                                <a href="{!! route('uranus.referralClicks.index') !!}">
                                    <span class="site-menu-title">Referral Clicks</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    @endadmincan
                    @admincan('see_people')
                    <li class="site-menu-item has-sub
                    {{ ((Request::is('uranus/people/create')
                    || Request::is('uranus/people')
                    ) ? 'active open' : '') }}
                            ">
                        <a href="javascript:void(0)">
                            <i class="site-menu-icon wb-users" aria-hidden="true"></i>
                            <span class="site-menu-title">Συντελεστές</span>
                            <span class="site-menu-arrow"></span>
                        </a>
                        <ul class="site-menu-sub">
                            <li class="site-menu-item {{ (Request::is('uranus/people') ? 'active' : '') }}">
                                <a href="{!! route('uranus.people.index') !!}">
                                    <span class="site-menu-title">Όλοι</span>
                                </a>
                            </li>
                            @admincan('manage_people')
                            <li class="site-menu-item {{ ((Request::is('uranus/people/create')) ? 'active' : '') }}">
                                <a href="{!! route('uranus.people.create') !!}">
                                    <span class="site-menu-title">Δημιουργία νέου</span>
                                </a>
                            </li>
                            @endadmincan
                        </ul>
                    </li>
                    @endadmincan
                    @admincan('see_theatres')
                    <li class="site-menu-item has-sub
                    {{ ((Request::is('*theatres/*')
                    || Request::is('uranus/theatres')
                    || Request::is('*festivals*')
                         || Request::is('*roles*')
                          || Request::is('*uranus/genres*')
                          || Request::is('*uranus/supergenres*')
                          || Request::is('*uranus/schools*')
                          || Request::is('*uranus/awards*')
                          || Request::is('*uranus/albums*')
                          || Request::is('*uranus/books*')
                          || Request::is('*uranus/images-moderation*')
                    ) ? 'active open' : '') }}
                            ">
                        <a href="javascript:void(0)">
                            <i class="site-menu-icon wb-bookmark" aria-hidden="true"></i>
                            <span class="site-menu-title">Θεατρ. περιεχομενο</span>
                            <span class="site-menu-arrow"></span>
                        </a>
                        <ul class="site-menu-sub">
                            <li class="site-menu-item  {{ ((Request::is('*theatres/*') || Request::is('uranus/theatres') ) ? 'active' : '') }}">
                                <a href="{!! route('uranus.theatres.index') !!}">
                                    <span class="site-menu-title">Θέατρα</span>
                                </a>
                            </li>
                            <li class="site-menu-item   {{ ((Request::is('*festivals*')  ) ? 'active' : '') }}">
                                <a href="{!! route('uranus.festivals.index') !!}">
                                    <span class="site-menu-title">Φεστιβάλ</span>
                                </a>
                            </li>
                            <li class="site-menu-item   {{ ((Request::is('*roles*')  ) ? 'active' : '') }}">
                                <a href="{!! route('uranus.roles.index') !!}">
                                    <span class="site-menu-title">Επαγγέλματα</span>
                                </a>
                            </li>
                            <li class="site-menu-item  {{ ((Request::is('*uranus/schools*') ) ? 'active' : '') }}  ">
                                <a href="{!! route('uranus.schools.index') !!}">
                                    <span class="site-menu-title">Schools</span>
                                </a>
                            </li>
                            <li class="site-menu-item  {{ ((Request::is('*uranus/awards*') ) ? 'active' : '') }}  ">
                                <a href="{!! route('uranus.awards.index') !!}">
                                    <span class="site-menu-title">Awards</span>
                                </a>
                            </li>
                            <li class="site-menu-item  {{ ((Request::is('*uranus/images-moderation*') ) ? 'active' : '') }}  ">
                                <a href="{!! route('uranus.images.moderation.index',['type' => 'unmoderated', 'tagged' => 'untagged', 'related' => 'play']) !!}">
                                    <span class="site-menu-title">Images</span>
                                </a>
                            </li>
                            <li class="site-menu-item  {{ ((Request::is('*uranus/albums*') ) ? 'active' : '') }}  ">
                                <a href="{!! route('uranus.albums.index') !!}">
                                    <span class="site-menu-title">Albums</span>
                                </a>
                            </li>
                            <li class="site-menu-item  {{ ((Request::is('*uranus/books*') ) ? 'active' : '') }}  ">
                                <a href="{!! route('uranus.books.index') !!}">
                                    <span class="site-menu-title">Books</span>
                                </a>
                            </li>
                            <li class="site-menu-item  {{ ((Request::is('*uranus/genres*') && !Request::is('*supergenres*')) ? 'active' : '') }}  ">
                                <a href="{!! route('uranus.genres.index') !!}">
                                    <span class="site-menu-title">Θεατρικά Είδη</span>
                                </a>
                            </li>
                            <li class="site-menu-item  {{ ((Request::is('*uranus/supergenres*')  ) ? 'active' : '') }}  ">
                                <a href="{!! route('uranus.supergenres.index') !!}">
                                    <span class="site-menu-title">Κατηγορίες Παραστάσεων</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    @endadmincan
                    @admincan('see_contributor_articles')
                    <li class="site-menu-item has-sub
                    {{ ((Request::is('*interviews*')
                    || Request::is('*tags*')
                    || Request::is('*critiques*')
                    || Request::is('*userposts*')
                    || Request::is('*newsposts*')
                    || Request::is('*externals*')
                    || Request::is('*authors*')
                    || Request::is('*articles*')
                    ) ? 'active open' : '') }}
                            ">
                        <a href="javascript:void(0)">
                            <i class="site-menu-icon wb-library" aria-hidden="true"></i>
                            <span class="site-menu-title">Αρθογραφία</span>
                            <span class="site-menu-arrow"></span>
                        </a>
                        <ul class="site-menu-sub">
                            @admincan('see_articles')
                            <li class="site-menu-item  {{ ((Request::is('*tags*')  ) ? 'active' : '') }}  ">
                                <a href="{!! route('uranus.tags.index') !!}">
                                    <span class="site-menu-title">Tags</span>
                                </a>
                            </li>
                            <li class="site-menu-item  {{ ((Request::is('*interviews*')  ) ? 'active' : '') }}  ">
                                <a href="{!! route('uranus.interviews.index') !!}">
                                    <span class="site-menu-title">Συνεντεύξεις</span>
                                </a>
                            </li>
                            <li class="site-menu-item  {{ ((Request::is('*userposts*')  ) ? 'active' : '') }}  ">
                                <a href="{!! route('uranus.userposts.index') !!}">
                                    <span class="site-menu-title">Άρθρα χρηστών</span>
                                </a>
                            </li>
                            <li class="site-menu-item   {{ ((Request::is('*newsposts*')    ) ? 'active' : '') }}   ">
                                <a href="{!! route('uranus.newsposts.index') !!}">
                                    <span class="site-menu-title">Άρθρα</span>
                                </a>
                            </li>
                            <li class="site-menu-item   {{ ((Request::is('*externals*')  ) ? 'active' : '') }}    ">
                                <a href="{!! route('uranus.externals.index') !!}">
                                    <span class="site-menu-title">Εξωτερικά Άρθρα</span>
                                </a>
                            </li>
                            <li class="site-menu-item   {{ ((Request::is('*authors*')  ) ? 'active' : '') }}   ">
                                <a href="{!! route('uranus.authors.index') !!}">
                                    <span class="site-menu-title">Αρθρογράφοι</span>
                                </a>
                            </li>
                            <li class="site-menu-item  {{ ((Request::is('*critiques*')  ) ? 'active' : '') }}  ">
                                <a href="{!! route('uranus.critiques.index') !!}">
                                    <span class="site-menu-title">Κριτικές</span>
                                </a>
                            </li>
                            @endadmincan
                            <li class="site-menu-item  {{ ((Request::is('*myinterviews*')  ) ? 'active' : '') }}  ">
                                <a href="{!! route('uranus.myInterviews.index') !!}">
                                    <span class="site-menu-title">Συνεντεύξεις μου</span>
                                </a>
                            </li>
                            <li class="site-menu-item  {{ ((Request::is('*mynewsposts*')  ) ? 'active' : '') }}  ">
                                <a href="{!! route('uranus.myNewsposts.index') !!}">
                                    <span class="site-menu-title">Γενικά άρθρα μου</span>
                                </a>
                            </li>
                            <li class="site-menu-item  {{ ((Request::is('*mycritiques*')  ) ? 'active' : '') }}  ">
                                <a href="{!! route('uranus.myCritiques.index') !!}">
                                    <span class="site-menu-title">Κριτικές μου</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    @endadmincan
                    @admincan('see_movies')
                    <li class="site-menu-item has-sub
                    {{ ((Request::is('uranus/movies')
                    || Request::is('*movies/unfinalised*')
                    || Request::is('*movies/create')
                    || Request::is('*movieReviews*')
                    || Request::is('*filmGenres*')
                    ) ? 'active open' : '') }}
                            ">
                        <a href="javascript:void(0)">
                            <i class="site-menu-icon wb-camera" aria-hidden="true"></i>
                            <span class="site-menu-title">Κινηματογράφος</span>
                            <span class="site-menu-arrow"></span>
                        </a>
                        <ul class="site-menu-sub">
                            <li class="site-menu-item  {{ (Request::is('uranus/movies') ? 'active' : '') }} ">
                                <a href="{!! route('uranus.movies.index') !!}">
                                    <span class="site-menu-title">Ταινίες</span>
                                </a>
                            </li>
                            <li class="site-menu-item {{ ((Request::is('*movies/create')  ) ? 'active' : '') }}">
                                <a href="{!! route('uranus.movies.create') !!}">
                                    <span class="site-menu-title">Δημιουργία νέας</span>
                                </a>
                            </li>
                            <li class="site-menu-item {{ ((Request::is('*movies/unfinalised*')  ) ? 'active' : '') }}">
                                <a href="{!! route('uranus.movies.unfinalised.index') !!}">
                                    <span class="site-menu-title">Unfinalised</span>
                                </a>
                            </li>
                            <li class="site-menu-item  {{ ((Request::is('*uranus/filmGenres*') ) ? 'active' : '') }}  ">
                                <a href="{!! route('uranus.filmGenres.index') !!}">
                                    <span class="site-menu-title">Film Genres</span>
                                </a>
                            </li>
                            <li class="site-menu-item   {{ ((Request::is('*uranus/movieReviews')  ) ? 'active' : '') }}">
                                <a href="{!! route('uranus.movieReviews.index') !!}">
                                    <span class="site-menu-title">Movie Reviews</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    @endadmincan
                    @admincan('see_tv')
                    <li class="site-menu-item has-sub
                    {{ ((Request::is('uranus/tvShows')
                    || Request::is('*tvShows/unfinalised*')
                    || Request::is('*tvShows/create')
                    || Request::is('*tvShowReviews')
                    || Request::is('*tvChannels')
                    ) ? 'active open' : '') }}
                            ">
                        <a href="javascript:void(0)">
                            <i class="site-menu-icon wb-desktop" aria-hidden="true"></i>
                            <span class="site-menu-title">Τηλεόραση</span>
                            <span class="site-menu-arrow"></span>
                        </a>
                        <ul class="site-menu-sub">
                            <li class="site-menu-item  {{ (Request::is('uranus/tvShows') ? 'active' : '') }} ">
                                <a href="{!! route('uranus.tvShows.index') !!}">
                                    <span class="site-menu-title">Σειρές</span>
                                </a>
                            </li>
                            <li class="site-menu-item {{ (Request::is('*tvShows/create') ? 'active' : '') }} ">
                                <a href="{!! route('uranus.tvShows.create') !!}">
                                    <span class="site-menu-title">Δημιουργία νέας</span>
                                </a>
                            </li>
                            <li class="site-menu-item {{ ((Request::is('*tvShows/unfinalised*')  ) ? 'active' : '') }}">
                                <a href="{!! route('uranus.tvShows.unfinalised.index') !!}">
                                    <span class="site-menu-title">Unfinalised</span>
                                </a>
                            </li>
                            <li class="site-menu-item  {{ ((Request::is('*uranus/tvChannels*') ) ? 'active' : '') }}  ">
                                <a href="{!! route('uranus.tvChannels.index') !!}">
                                    <span class="site-menu-title">Τηλεοπτικά Κανάλια</span>
                                </a>
                            </li>
                            <li class="site-menu-item   {{ ((Request::is('*uranus/tvShowReviews')  ) ? 'active' : '') }}">
                                <a href="{!! route('uranus.tvShowReviews.index') !!}">
                                    <span class="site-menu-title">Tv Reviews</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    @endadmincan
                    @admincan('see_videos')
                    <li class="site-menu-item has-sub
                    {{ ((Request::is('uranus/videos')
                    || Request::is('uranus/videos/create')
                    ) ? 'active open' : '') }}
                            ">
                        <a href="javascript:void(0)">
                            <i class="site-menu-icon wb-play" aria-hidden="true"></i>
                            <span class="site-menu-title">Videos</span>
                            <span class="site-menu-arrow"></span>
                        </a>
                        <ul class="site-menu-sub">
                            <li class="site-menu-item  {{ (Request::is('uranus/videos') ? 'active' : '') }} ">
                                <a href="{!! route('uranus.videos.index') !!}">
                                    <span class="site-menu-title">Όλα</span>
                                </a>
                            </li>
                            <li class="site-menu-item {{ (Request::is('uranus/videos/create') ? 'active' : '') }} ">
                                <a href="{!! route('uranus.videos.create') !!}">
                                    <span class="site-menu-title">Δημιουργία νέου</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    @endadmincan
                    @admincan('see_plays')
                    <li class="site-menu-item has-sub
                    {{ ((Request::is('uranus/endeavours*')
                    || Request::is('*varieties*')
                    ) ? 'active open' : '') }}
                            ">
                        <a href="javascript:void(0)">
                            <i class="site-menu-icon wb-info-circle" aria-hidden="true"></i>
                            <span class="site-menu-title">Endeavours</span>
                            <span class="site-menu-arrow"></span>
                        </a>
                        <ul class="site-menu-sub">
                            <li class="site-menu-item {{ ((Request::is('uranus/endeavours')) ? 'active' : '') }} ">
                                <a href="{!! route('uranus.endeavours.index') !!}">
                                    <span class="site-menu-title">Endeavours</span>
                                </a>
                            </li>
                            @admincan('manage_plays')
                            <li class="site-menu-item">
                                <a href="{!! route('uranus.endeavours.create') !!}">
                                    <span class="site-menu-title">Νέο endeavour</span>
                                </a>
                            </li>
                            @endadmincan
                            <li class="site-menu-item  {{ ((Request::is('uranus/varieties')) ? 'active' : '') }} ">
                                <a href="{!! route('uranus.varieties.index') !!}">
                                    <span class="site-menu-title">Varieties</span>
                                </a>
                            </li>
                            @admincan('manage_plays')
                            <li class="site-menu-item {{ ((Request::is('uranus/varieties/create')) ? 'active' : '') }} "">
                            <a href="{!! route('uranus.varieties.create') !!}">
                                <span class="site-menu-title">Νέο variety</span>
                            </a>
                    </li>
                    @endadmincan
                </ul>
                </li>
                @endadmincan
                    @admincan('see_plays')
                    <li class="site-menu-item has-sub
                    {{ ((Request::is('*streamings/*')
                    || Request::is('uranus/streamings')
                    ) ? 'active open' : '') }}
                            ">
                        <a href="javascript:void(0)">
                            <i class="site-menu-icon wb-video" aria-hidden="true"></i>
                            <span class="site-menu-title">Streamings</span>
                            <span class="site-menu-arrow"></span>
                        </a>
                        <ul class="site-menu-sub">
                            <li class="site-menu-item  {{ (Request::is('uranus/streamings') ? 'active' : '') }} ">
                                <a href="{!! route('uranus.streamings.index') !!}">
                                    <span class="site-menu-title">Όλα</span>
                                </a>
                            </li>
                            <li class="site-menu-item">
                                <a href="{!! route('uranus.streamings.create') !!}">
                                    <span class="site-menu-title">Δημιουργία νέου</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    @endadmincan
                    @admincan('see_analytics')
                    <li class="site-menu-item has-sub
                    {{ ((Request::is('*followed/people')
                    || Request::is('*followed/theatres')
                    || Request::is('*followed/supergenres')
                    || Request::is('*rated/plays')
                    || Request::is('*watchlisted/plays')
                    ) ? 'active open' : '') }}
                            ">
                        <a href="javascript:void(0)">
                            <i class="site-menu-icon wb-eye" aria-hidden="true"></i>
                            <span class="site-menu-title">Followed</span>
                            <span class="site-menu-arrow"></span>
                        </a>
                        <ul class="site-menu-sub">
                            <li class="site-menu-item  {{ ((Request::is('*followed/people')  ) ? 'active' : '') }}">
                                <a href="{!! route('uranus.followed.people') !!}">
                                    <span class="site-menu-title">Συντελεστές</span>
                                </a>
                            </li>
                            <li class="site-menu-item  {{ ((Request::is('*followed/theatres')  ) ? 'active' : '') }}">
                                <a href="{!! route('uranus.followed.theatres') !!}">
                                    <span class="site-menu-title">Θέατρα</span>
                                </a>
                            </li>
                            <li class="site-menu-item  {{ ((Request::is('*followed/supergenres')  ) ? 'active' : '') }}">
                                <a href="{!! route('uranus.followed.supergenres') !!}">
                                    <span class="site-menu-title">Θεατρικές κατηγορίες</span>
                                </a>
                            </li>
                            <li class="site-menu-item  {{ ((Request::is('*rated/plays')  ) ? 'active' : '') }}">
                                <a href="{!! route('uranus.rated.plays') !!}">
                                    <span class="site-menu-title">Βαθμολογημένες παραστάσεις</span>
                                </a>
                            </li>
                            <li class="site-menu-item  {{ ((Request::is('*watchlisted/plays')  ) ? 'active' : '') }}">
                                <a href="{!! route('uranus.watchlisted.plays') !!}">
                                    <span class="site-menu-title">Παραστάσεις σε watchlist</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="site-menu-item has-sub
                    {{ ((Request::is('*analytics/users')
                       || Request::is('*analytics/people')
                       || Request::is('*analytics/plays')
                       || Request::is('*analytics/movies')
                       || Request::is('*analytics/tvShows')
                       || Request::is('*analytics/people/recent*')
                       || Request::is('/people/rankings*')
                       || Request::is('*analytics/plays/recent*')
                       || Request::is('*analytics/movies/recent*')
                       || Request::is('*analytics/tvShows/recent*')
                       || Request::is('*analytics/behaviour/users')
                       || Request::is('*analytics/behaviour/actions')
                       || Request::is('*analytics/behaviour/ratings*')
                       || Request::is('*analytics/behaviour/actions/recent*')
                       ) ? 'active open' : '') }}
                            ">
                        <a href="javascript:void(0)">
                            <i class="site-menu-icon wb-stats-bars" aria-hidden="true"></i>
                            <span class="site-menu-title">Analytics</span>
                            <span class="site-menu-arrow"></span>
                        </a>
                        <ul class="site-menu-sub">
                            <li class="site-menu-item  {{ ((Request::is('*analytics/users')  ) ? 'active' : '') }}">
                                <a href="{!! route('uranus.analytics.users') !!}">
                                    <span class="site-menu-title">Δημογραφικά Χρηστών</span>
                                </a>
                            </li>
                            <li class="site-menu-item  {{ ((Request::is('*analytics/behaviour/users')  ) ? 'active' : '') }}">
                                <a href="{!! route('uranus.analytics.behaviour.users') !!}">
                                    <span class="site-menu-title">Χρήστες με Ενέργειες</span>
                                </a>
                            </li>
                            <li class="site-menu-item  {{ ((Request::is('*analytics/behaviour/actions/recent*')  ) ? 'active' : '') }} ">
                                <a href="{!! route('uranus.analytics.behaviour.actions.recent', 3) !!}">
                                    <i class="site-menu-icon   fa-group" aria-hidden="true"></i>
                                    <span class="site-menu-title">Actions πρόσφατα</span>
                                </a>
                            </li>
                            <li class="site-menu-item  {{ ((Request::is('*analytics/behaviour/ratings*')  ) ? 'active' : '') }} ">
                                <a href="{!! route('uranus.analytics.behaviour.ratings', 3) !!}">
                                    <i class="site-menu-icon   fa-group" aria-hidden="true"></i>
                                    <span class="site-menu-title">Ratings πρόσφατα</span>
                                </a>
                            </li>
                            <li class="site-menu-item  {{ ((Request::is('*analytics/behaviour/actions')  ) ? 'active' : '') }}">
                                <a href="{!! route('uranus.analytics.behaviour.actions') !!}">
                                    <span class="site-menu-title">Ενέργειες ανά ημέρα</span>
                                </a>
                            </li>
                            <li class="site-menu-item {{ ((Request::is('*analytics/plays')  ) ? 'active' : '') }} ">
                                <a href="{!! route('uranus.analytics.plays') !!}">
                                    <span class="site-menu-title">Παραστάσεις</span>
                                </a>
                            </li>
                            <li class="site-menu-item {{ ((Request::is('*analytics/movies')  ) ? 'active' : '') }} ">
                                <a href="{!! route('uranus.analytics.movies') !!}">
                                    <span class="site-menu-title">Ταινίες</span>
                                </a>
                            </li>
                            <li class="site-menu-item {{ ((Request::is('*analytics/tvShows')  ) ? 'active' : '') }} ">
                                <a href="{!! route('uranus.analytics.tvShows') !!}">
                                    <span class="site-menu-title">Τηλεοπτικές σειρές</span>
                                </a>
                            </li>
                            <li class="site-menu-item {{ ((Request::is('*analytics/people')  ) ? 'active' : '') }} ">
                                <a href="{!! route('uranus.analytics.people') !!}">
                                    <span class="site-menu-title">Συντελεστές</span>
                                </a>
                            </li>
                            <li class="site-menu-item  {{ ((Request::is('*analytics/plays/recent*')  ) ? 'active' : '') }} ">
                                <a href="{!! route('uranus.analytics.plays.recent.days', 3) !!}">
                                    <i class="site-menu-icon fa-group" aria-hidden="true"></i>
                                    <span class="site-menu-title">Παραστάσεις πρόσφατα</span>
                                </a>
                            </li>
                            <li class="site-menu-item  {{ ((Request::is('*analytics/movies/recent*')  ) ? 'active' : '') }} ">
                                <a href="{!! route('uranus.analytics.movies.recent.days', 3) !!}">
                                    <i class="site-menu-icon fa-group" aria-hidden="true"></i>
                                    <span class="site-menu-title">Ταινίες πρόσφατα</span>
                                </a>
                            </li>
                            <li class="site-menu-item  {{ ((Request::is('*analytics/tvShows/recent*')  ) ? 'active' : '') }} ">
                                <a href="{!! route('uranus.analytics.tvShows.recent.days', 3) !!}">
                                    <i class="site-menu-icon fa-group" aria-hidden="true"></i>
                                    <span class="site-menu-title">Τηλεοπτικές σειρές πρόσφατα</span>
                                </a>
                            </li>
                            <li class="site-menu-item  {{ ((Request::is('*analytics/people/recent*')  ) ? 'active' : '') }} ">
                                <a href="{!! route('uranus.analytics.people.recent.days', 3) !!}">
                                    <i class="site-menu-icon fa-group" aria-hidden="true"></i>
                                    <span class="site-menu-title">Συντελεστές πρόσφατα</span>
                                </a>
                            </li>
                            <li class="site-menu-item  {{ ((Request::is('*/people/rankings*')  ) ? 'active' : '') }} ">
                                <a href="{!! route('uranus.people.rankings.index',['type' => 'unmoderated', 'tagged' => 'untagged', 'related' => 'play']) !!}">
                                    <i class="site-menu-icon fa-group" aria-hidden="true"></i>
                                    <span class="site-menu-title">Rankings</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    @endadmincan
                    <li class="site-menu-item has-sub
                    {{ ((Request::is('*quality*')
                       || Request::is('*plays/user-saved')
                       || Request::is('*movies/user-saved')
                       || Request::is('*endeavours/user-saved')
                       || Request::is('*tvShows/user-saved')
                       || Request::is('*seminars*')
                       || Request::is('*videos/user-saved')
                       || Request::is('*people/proEnabled')
                       || Request::is('*images-inspection*')
                       || Request::is('*slug-redirects*')
                    ) ? 'active open' : '') }}
                            ">
                        <a href="javascript:void(0)">
                            <i class="site-menu-icon wb-emoticon" aria-hidden="true"></i>
                            <span class="site-menu-title">Content Quality</span>
                            <span class="site-menu-arrow"></span>
                        </a>
                        <ul class="site-menu-sub">
                            @admincan('see_pro')
                            <li class="site-menu-item {{ ((Request::is('*people/proEnabled')  ) ? 'active' : '') }}">
                                <a href="{!! route('uranus.people.proEnabled.index') !!}">
                                    <span class="site-menu-title">unstagePro Peeps</span>
                                </a>
                            </li>
                            <li class="site-menu-item {{ ((Request::is('*endeavours/user-saved')  ) ? 'active' : '') }}">
                                <a href="{!! route('uranus.endeavours.user_saved.index') !!}">
                                    <span class="site-menu-title">unstagePro Endeavours</span>
                                </a>
                            </li>
                            <li class="site-menu-item {{ ((Request::is('*plays/user-saved')  ) ? 'active' : '') }}">
                                <a href="{!! route('uranus.plays.user_saved.index') !!}">
                                    <span class="site-menu-title">unstagePro Plays</span>
                                </a>
                            </li>
                            <li class="site-menu-item {{ ((Request::is('*movies/user-saved')  ) ? 'active' : '') }}">
                                <a href="{!! route('uranus.movies.user_saved.index') !!}">
                                    <span class="site-menu-title">unstagePro Movies</span>
                                </a>
                            </li>
                            <li class="site-menu-item {{ ((Request::is('*tvShows/user-saved')  ) ? 'active' : '') }}">
                                <a href="{!! route('uranus.tvShows.user_saved.index') !!}">
                                    <span class="site-menu-title">unstagePro TvShows</span>
                                </a>
                            </li>
                            <li class="site-menu-item  {{ ((Request::is('*uranus/images-inspection*') ) ? 'active' : '') }}  ">
                                <a href="{!! route('uranus.images.inspection.index',['type' => 'uninspected']) !!}">
                                    <span class="site-menu-title">unstagePro Images</span>
                                </a>
                            </li>
                            <li class="site-menu-item  {{ ((Request::is('*videos/user-saved') ) ? 'active' : '') }}  ">
                                <a href="{!! route('uranus.videos.user_saved.index') !!}">
                                    <span class="site-menu-title">unstagePro Videos</span>
                                </a>
                            </li>
                            <li class="site-menu-item  {{ ((Request::is('*seminars*') ) ? 'active' : '') }}  ">
                                <a href="{!! route('uranus.seminars.user_saved.index',['inspected' => 'uninspected']) !!}">
                                    <span class="site-menu-title">unstagePro Seminars</span>
                                </a>
                            </li>
                            @endadmincan
                            <li class="site-menu-item  {{ app('router')->currentRouteNamed('uranus.quality.index') ? 'active' : '' }}   ">
                                <a href="{!! route('uranus.quality.index') !!}">
                                    <span class="site-menu-title">Ποιοτητα Περιεχ.</span>
                                </a>
                            </li>
                            @admincan('delete_people')
                            <li class="site-menu-item  {{ ((Request::is('*slug-redirects*')  ) ? 'active' : '') }}">
                                <a href="{!! route('uranus.slugRedirects.index') !!}">
                                    <span class="site-menu-title">Slug Redirects</span>
                                </a>
                            </li>
                            @endadmincan
                        </ul>
                    </li>
                    <li class="site-menu-item has-sub
                    {{ ((Request::is('*subscriptions*')
                       || Request::is('*contacts*')
                       || Request::is('*collaborations*')
                       || Request::is('*email-messages*')
                       || Request::is('*email-message-actions*')
                       || Request::is('*proLeads*')
                       || Request::is('*contributions*')
                       || Request::is('*uranus/users*')
                       || Request::is('*activityLogs*')
                       ) ? 'active open' : '') }}
                            ">
                        <a href="javascript:void(0)">
                            <i class="site-menu-icon wb-briefcase" aria-hidden="true"></i>
                            <span class="site-menu-title">CRM</span>
                            <span class="site-menu-arrow"></span>
                        </a>
                        <ul class="site-menu-sub">
                            @admincan('see_users')
                            <li class="site-menu-item  {{ ((Request::is('*uranus/users*')  ) ? 'active' : '') }}">
                                <a href="{!! route('uranus.users.index') !!}">
                                    <span class="site-menu-title">Χρήστες (endusers)</span>
                                </a>
                            </li>
                            @endadmincan
                            @admincan('see_contacts')
                            <li class="site-menu-item  {{ ((Request::is('*contacts*')  ) ? 'active' : '') }} ">
                                <a href="{!! route('uranus.contacts.index') !!}">
                                    <span class="site-menu-title">Μηνύματα Επικοινωνίας</span>
                                </a>
                            </li>
                            <li class="site-menu-item  {{ ((Request::is('*collaborations*')  ) ? 'active' : '') }} ">
                                <a href="{!! route('uranus.collaborations.index') !!}">
                                    <span class="site-menu-title">Μηνύματα Συνεργασίας/Διαφήμισης</span>
                                </a>
                            </li>
                            <li class="site-menu-item  {{ ((Request::is('*email-messages*')  ) ? 'active' : '') }} ">
                                <a href="{!! route('uranus.emailMessages.index') !!}">
                                    <span class="site-menu-title">Μηνύματα Email</span>
                                </a>
                            </li>
                            <li class="site-menu-item  {{ ((Request::is('*email-message-actions*')  ) ? 'active' : '') }} ">
                                <a href="{!! route('uranus.emailMessageActions.index') !!}">
                                    <span class="site-menu-title">Email Actions</span>
                                </a>
                            </li>
                            @endadmincan
                            @admincan('see_pro')
                            <li class="site-menu-item  {{ ((Request::is('*contributions*')  ) ? 'active' : '') }} ">
                                <a href="{!! route('uranus.contributions.index') !!}">
                                    <span class="site-menu-title">Συνεισφορές</span>
                                </a>
                            </li>
                            <li class="site-menu-item  {{ ((Request::is('*proLeads*')  ) ? 'active' : '') }}">
                                <a href="{!! route('uranus.proLeads.index') !!}">
                                    <span class="site-menu-title">unstagePro Leads</span>
                                </a>
                            </li>
                            <li class="site-menu-item  {{ ((Request::is('*subscriptions*')  ) ? 'active' : '') }}">
                                <a href="{!! route('uranus.subscriptions.index') !!}">
                                    <span class="site-menu-title">Subscriptions</span>
                                </a>
                            </li>
                            <li class="site-menu-item  {{ ((Request::is('*activityLogs*')  ) ? 'active' : '') }}">
                                <a href="{!! route('uranus.activityLogs.index') !!}">
                                    <span class="site-menu-title">Δραστηριότητα χρηστών</span>
                                </a>
                            </li>
                            @endadmincan
                        </ul>
                    </li>
                    @admincan('see_admins')
                    <li class="site-menu-item has-sub
                    {{ ((Request::is('*admins*')
                    || Request::is('*accessLevels*')
                    ) ? 'active open' : '') }}
                            ">
                        <a href="javascript:void(0)">
                            <i class="site-menu-icon wb-settings" aria-hidden="true"></i>
                            <span class="site-menu-title">Διαχειριστές</span>
                            <span class="site-menu-arrow"></span>
                        </a>
                        <ul class="site-menu-sub">
                            <li class="site-menu-item  {{ ((Request::is('*admins*')  ) ? 'active' : '') }}">
                                <a href="{!! route('uranus.admins.index') !!}">
                                    <span class="site-menu-title">Όλοι</span>
                                </a>
                            </li>
                            <li class="site-menu-item  {{ ((Request::is('*accessLevels*')  ) ? 'active' : '') }}">
                                <a href="{!! route('uranus.accessLevels.index') !!}">
                                    <span class="site-menu-title">Δικαιώματα</span>
                                </a>
                            </li>
                            <li class="site-menu-item">
                                <a href="{!! route('log-viewer::dashboard') !!}">
                                    <span class="site-menu-title">Logs</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    @endadmincan
                </ul>
            </div>
        </div>
    </div>
    <div class="site-menubar-footer">
        <a href="{!! url('/') !!}"
        >
            <span class="icon wb-arrow-left" aria-hidden="true"></span>
        </a>
        <a href="{!! route('uranus.logout') !!}" data-placement="top" data-toggle="tooltip" data-original-title="Logout"
           onclick="event.preventDefault();document.getElementById('logout-form').submit();">
            <span class="icon wb-power" aria-hidden="true"></span>
        </a>
        <form id="logout-form" action="{{ route('uranus.logout') }}" method="POST" style="display: none;">
            {{ csrf_field() }}
        </form>
    </div>
</div>

<nav class="site-navbar navbar navbar-default navbar-fixed-top navbar-mega" role="navigation">
    <div class="navbar-header">
        <button type="button" class="navbar-toggle hamburger hamburger-close navbar-toggle-left hided"
                data-toggle="menubar">
            <span class="sr-only">Toggle navigation</span>
            <span class="hamburger-bar"></span>
        </button>
    </div>
</nav>
