@extends('uranus::layout')
@section('content')
    <!-- Page -->
    <div class="page animsition">
        <div class="page-header">
            <h1 class="page-title">Συντελεστές Χωρίς Έργα</h1>
        </div>
        <div class="page-content">
            <!-- Panel Basic -->
            <div class="panel">
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-12">
                            <table class="table table-hover dataTable table-striped width-full" data-plugin=" ">
                                <thead>
                                    <tr>
                                        <th>{!! \App\Components\LinkTo::sortableRoute('uranus.quality.people.withoutPlays','Ονοματεπώνυμο','last_name') !!}</th>
                                        <th>{!! \App\Components\LinkTo::sortableRoute('uranus.quality.people.withoutPlays','Ημερομηνία καταχώρησης','created_at') !!}</th>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($people as $person)
                                        <tr>
                                            <td>{!! link_to_route('uranus.people.edit', $person->fullName, $person->id) !!}</td>
                                            <td>{!! $person->created_at !!}</td>
                                            <td>
                                                <a href="{!! route('uranus.people.edit', $person->id) !!}">
                                                    <button type="button" class="btn btn-icon btn-default btn-outline">
                                                        <i class="icon wb-pencil" aria-hidden="true"></i></button>
                                                </a>
                                            </td>
                                            @admincan('delete_people')
                                                <td>
                                                    <a data-url="{!! route('uranus.people.destroy', $person->id) !!}" href="" class="deleteResource">
                                                        <button type="button" class="btn btn-icon btn-danger btn-outline">
                                                            <i class="fa fa-times" aria-hidden="true"></i></button>
                                                    </a>
                                                </td>
                                            @endadmincan
                                        </tr>
                                    @empty
                                        <p>Δε βρέθηκαν συντελεστές</p>
                                    @endforelse
                                </tbody>
                            </table>
                            {!! $people->render() !!}
                        </div>
                    </div>
                </div>
            </div>
            <!-- End Panel Basic -->
        </div>
    </div>
    <!-- End Page -->
@stop
@section('scripts')
    @parent
    {!! csrf_field() !!}
    <script src="{{ asset('js/admin/jquery.deleteResource.js') }}"></script>
@stop
