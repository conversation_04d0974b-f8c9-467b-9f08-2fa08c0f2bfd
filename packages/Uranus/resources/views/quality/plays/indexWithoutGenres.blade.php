@extends('uranus::layout')
@section('content')
    <!-- Page -->
    <div class="page animsition">
        <div class="page-header">
            <h1 class="page-title">Παραστάσεις Χωρίς είδος</h1>
        </div>
        <div class="page-content">
            <!-- Panel Basic -->
            <div class="panel">
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-12">
                            <table class="table table-hover dataTable table-striped width-full" data-plugin=" ">
                                <thead>
                                    <tr>
                                        <th>{!! \App\Components\LinkTo::sortableRoute('uranus.quality.plays.withoutGenres','Τίτλος','title') !!}</th>
                                        <th>{!! \App\Components\LinkTo::sortableRoute('uranus.quality.plays.withoutGenres','Τελευταία δραστηριότητα','updated_at') !!}</th>
                                        <th><div class="text-center">{!! \App\Components\LinkTo::sortableRoute('uranus.quality.plays.withoutGenres','Δημοσιευμένη','published') !!}</div></th>
                                        <th></th>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($plays as $play)
                                        <tr>
                                            <td class="showPopoverInfo" data-url="{!! route('uranus.plays.popoverInfo') !!}" data-id="{!! $play->id !!}">{!! link_to_route('uranus.plays.edit', $play->title, $play->id) !!}</td>
                                            <td>{{ $play->updated_at }}</td>
                                            <td>@if($play->published)
                                                    <div class="text-center">
                                                        <i class="icon wb-eye green-600" aria-hidden="true" title="Δημοσιευμένη"></i>
                                                    </div>
                                                @else
                                                    <div class="text-center">
                                                        <i class="icon wb-eye-close red-600" aria-hidden="true" title="Αδημοσίευτη"></i>
                                                    </div>
                                                @endif
                                            </td>
                                            <td>
                                                <a href="{!! route('uranus.plays.edit', $play->id) !!}">
                                                    <button type="button" class="btn btn-icon btn-default btn-outline" title="Επεξεργασία">
                                                        <i class="icon wb-pencil" aria-hidden="true"></i></button>
                                                </a>
                                            </td>
                                            @admincan('delete_plays')
                                                <td>
                                                    <a data-url="{!! route('uranus.plays.destroy', $play->id) !!}" href="" class="deleteResource">
                                                        <button type="button" class="btn btn-icon btn-danger btn-outline" title="Διαγραφή">
                                                            <i class="fa fa-times" aria-hidden="true"></i></button>
                                                    </a>
                                                </td>
                                            @endadmincan
                                        </tr>
                                    @empty
                                        <p>Δε βρέθηκαν έργα</p>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- End Panel Basic -->
            {!! $plays->render() !!}
        </div>
    </div>
    <!-- End Page -->
@stop
@section('scripts')
    @parent
    {!! csrf_field() !!}
    <script src="{{ asset('js/admin/jquery.deleteResource.js') }}"></script>
    <script src="{{ asset('js/admin/jquery.mouseoverPopover.js') }}"></script>
@stop
