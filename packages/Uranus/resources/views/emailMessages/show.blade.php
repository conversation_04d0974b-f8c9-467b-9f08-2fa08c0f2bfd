@extends('uranus::layout')
@section('head')
    <link type="text/css" rel="stylesheet" href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.10.2/themes/smoothness/jquery-ui.min.css" media="screen"/>
    @stop

@section('content')
            <!-- Page -->
    <div class="page animsition">
        <div class="page-header">
            <h1 class="page-title">Email Message</h1>
            <div class="page-header-actions">

            </div>
        </div>
        <div class="page-content">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-sm-12">
                        <!-- Panel Static Lables -->
                        <div class="panel">
                            <div class="panel-heading">
                                <h3 class="panel-title">Subject: {{ $message->subject }}</h3>
                            </div>
                            <div class="panel-body container-fluid">
                                <div class="form-group row">
                                    <div class="col-sm-12">
                                        <div class="row">
                                            <b class="col-sm-2">ID</b>
                                            <span>{!! $message->id !!}</span>
                                        </div>
                                        <div class="row">
                                            <b class="col-sm-2">Παραλήπτης</b>
                                            <span>{!! $message->to !!}</span>
                                        </div>
                                        <div class="row">
                                            <b class="col-sm-2">Ημερομηνία</b>
                                            <span>{!! $message->date !!}</span>
                                        </div>
                                        <div class="row">
                                            <b class="col-sm-2">Actions</b>
                                            <span>{!! $message->actions->count() !!}</span>
                                        </div>
                                        <div class="row">
                                            <b class="col-sm-2">AWS Msg ID</b>
                                            <span>{!! $message->message_id !!}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="panel-heading">
                                <h3 class="panel-title">Actions: {{ $message->subject }}</h3>
                            </div>
                            <div class="panel-body">
                                <table class="table table-hover dataTable table-striped width-full" data-plugin=" ">
                                    <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Action</th>
                                        <th>Link</th>
                                        <th>Ημερομηνία</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @forelse($actions as $action)
                                        <tr>
                                            <td><a target="_blank" href="{{ route('uranus.emailMessageActions.show', $action->id) }}">{{ $action->id }}</a></td>
                                            <td><a target="_blank" href="{{ route('uranus.emailMessageActions.show', $action->id) }}">{{ $action->action }}</a></td>
                                            <td>{!! $action->link !!}</td>
                                            <td>{!! $action->date !!}</td>
                                        </tr>
                                    @empty
                                        <p>Δε βρέθηκαν email actions</p>
                                    @endforelse
                                    </tbody>
                                </table>
                                {!! $actions->render() !!}
                            </div>
                            <div class="panel-body container-fluid">
                                <div class="form-group row">
                                    <div class="col-sm-12">
                                        <div class="row">
                                            <b class="col-sm-2">Details</b>
                                        </div>
                                        <div class="row">
                                        @if( !empty($message->details) )
                                            @foreach(Arr::flatten($message->details) as $value)
                                                <div class="col-sm-12">{{ $value }}</div>
                                            @endforeach
                                        @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- End Panel Static Lables -->
                    </div>
                    <!-- End Panel Floating Lables -->
                </div>
            </div>
        </div>
    </div>
    <!-- End Page -->
@stop
@section('footer')
    @parent
    <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.10.2/jquery-ui.min.js" charset="UTF-8"></script>
@stop
