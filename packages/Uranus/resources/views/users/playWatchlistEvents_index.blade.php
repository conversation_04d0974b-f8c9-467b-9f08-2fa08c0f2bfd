@extends('uranus::layout')
@section('content')
    <!-- Page -->
    <div class="page animsition">
        @include('uranus::users._userBreadcrumbs')
        <div class="page-header">
            <h1 class="page-title">Watchlist play actions: Χρήστης <a title="Επεξεργασία συντελεστή" href="{!! route('uranus.users.edit', $user_item->id) !!}">{!! $user_item->first_name !!} {!! $user_item->last_name !!} (id: {!! $user_item->id !!})</a></h1>
        </div>
        <div class="page-content">
            <!-- Panel Basic -->
            <div class="panel">
                <div class="panel-body">
                    <h4>Συνολική εικόνα</h4>
                    <p>{!! link_to_route('uranus.users.playWatchlistEvents', 'Πλήθος actions: ' . $user_item->playWatchlistEvents()->count(), ['id' =>$user_item->id, 'sortBy' => 'created_at', 'direction' => 'desc']) !!}</p>
                    <p>{!! link_to_route('uranus.users.playWatchlistEvents', 'Add: ' . $user_item->playWatchlistEvents('add')->count(), ['id' =>$user_item->id, 'filter' => 'add', 'sortBy' => 'created_at', 'direction' => 'desc']) !!}</p>
                    <p>{!! link_to_route('uranus.users.playWatchlistEvents', 'Remove: ' . $user_item->playWatchlistEvents('remove')->count(), ['id' =>$user_item->id, 'filter' => 'remove', 'sortBy' => 'created_at', 'direction' => 'desc']) !!}</p>
                </div>
            </div>
        </div>
        <div class="page-content">
            <!-- Panel Basic -->
            <div class="panel">
                <div class="panel-body">
                    <h4>Λίστα events</h4>
                    <table class="table table-hover dataTable table-striped width-full" data-plugin=" ">
                        <thead>
                            <tr>
                                <th>{!! \App\Components\LinkTo::sortableRoute('uranus.users.playWatchlistEvents','Event category', 'category', ['id' => $user_item->id]) !!}</th>
                                <th>{!! \App\Components\LinkTo::sortableRoute('uranus.users.playWatchlistEvents','Event action', 'action', ['id' => $user_item->id]) !!}</th>
                                <th>{!! \App\Components\LinkTo::sortableRoute('uranus.users.playWatchlistEvents','Παράσταση', 'play_id', ['id' => $user_item->id]) !!}</th>
                                <th>{!! \App\Components\LinkTo::sortableRoute('uranus.users.playWatchlistEvents','Event date', 'created_at', ['id' => $user_item->id]) !!}</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($events as $event)
                                <tr>
                                    <td>{!! $event->category !!}</td>
                                    <td>{!! $event->action !!}</td>
                                    <td>{!! $event->play()->first()->title !!}</td>
                                    <td>{!! $event->created_at !!}</td>
                                </tr>
                            @empty
                                <p>Δε βρέθηκαν events</p>
                            @endforelse
                        </tbody>
                    </table>
                    {!! $events->render() !!}
                </div>
            </div>
            <!-- End Panel Basic -->
        </div>
    </div>
    <!-- End Page -->
@stop