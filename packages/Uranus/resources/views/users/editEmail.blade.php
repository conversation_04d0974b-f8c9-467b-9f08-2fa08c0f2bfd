@extends('uranus::layout')
@section('content')
    <!-- Page -->
    <div class="page animsition">
        <div class="page-header">
            <h1 class="page-title">Επεξεργασία email χρήστη {{ $user_item->email }} (id: {{ $user_item->id }})</h1>
            <div class="page-header-actions">
                <a href="{!! route('uranus.users.show', $user_item->id) !!}">
                    <button title="Preview user in uranus" type="button" class="btn btn-floating btn-primary">
                        <i class="icon wb-eye" aria-hidden="true"></i></button>
                </a>
                <a href="{!! route('uranus.users.edit', $user_item->id) !!}">
                    <button title="Edit user in uranus" type="button" class="btn btn-floating btn-info">
                        <i class="icon wb-pencil" aria-hidden="true"></i></button>
                </a>
            </div>
        </div>
        <div class="page-content">
            <div class="container-fluid">
                @include('uranus::messages.successfulSave')
                @include('uranus::errors.genericForm')
                <form method="post" action="{{ route('uranus.users.updateEmail', $user_item->id) }}">
                    {!! csrf_field() !!}
                    {!! method_field('put') !!}
                    @include('uranus::users._userEmailEditForm')
                </form>
            </div>
        </div>
    </div>
    <!-- End Page -->
@stop