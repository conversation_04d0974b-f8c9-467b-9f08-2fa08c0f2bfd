@extends('uranus::layout')
@section('content')
    <!-- Page -->
    <div class="page animsition">
        @include('uranus::users._userBreadcrumbs')
        <div class="page-header">
            <h1 class="page-title">Χρήστης <a href="{!! route('uranus.users.edit', $user_item->id) !!}">{!! $user_item->first_name !!} {!! $user_item->last_name !!} (id: {!! $user_item->id !!})</a></h1>
            <h3>Followed συντελεστές</h3>
        </div>
        <div class="page-content">
            <!-- Panel Basic -->
            <div class="panel">
                <div class="panel-body">
                    <table class="table table-hover dataTable table-striped width-full" data-plugin=" ">
                        <thead>
                            <tr>
                                <th>Όνομα</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($people as $person)
                                <tr>
                                    <td>{!! link_to_route('uranus.people.edit', $person->first_name . ' ' . $person->last_name, $person->id) !!}</td>
                                </tr>
                            @empty
                                <p>Δε βρέθηκαν followed συντελεστές</p>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- End Panel Basic -->
        </div>
    </div>
    <!-- End Page -->
@stop