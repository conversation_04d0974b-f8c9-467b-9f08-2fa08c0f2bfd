@extends('uranus::layout')
@section('content')
    <!-- Page -->
    <div class="page animsition">
        <div class="page-header">
            <h1 class="page-title">Χρήστες με τις πιο πολλές ενέργειες</h1>
        </div>
        <div class="page-content">
            <!-- Panel Basic -->
            <div class="panel">
                <div class="panel-body">
                    <h2>Χρήστες με ενέργειες παραστάσεων (all-time)</h2><br>
                    <div>
                        <table class="table table-hover dataTable table-striped width-full" data-plugin=" ">
                            <thead>
                            <tr>
                                <th>Ενέργειες παραστάσεων</th>
                                <th>Email, Όνομα</th>
                            </tr>
                            </thead>
                            <tbody>
                        @foreach($view_data['most_actionable_users_plays'] as $the_user)
                            <tr>
                                <td>
                                    {{ $the_user->total }}
                                </td>
                                <td>
                                   {!! link_to_route('uranus.users.edit', $the_user->email . ', ' . $the_user->fullName, $the_user->id) !!}
                                </td>
                            </tr>
                        @endforeach
                            </tbody>
                        </table>
                    </div>
                    <h2>Χρήστες με ενέργειες παραστάσεων (30 ημέρες)</h2><br>
                    <div>
                        <table class="table table-hover dataTable table-striped width-full" data-plugin=" ">
                            <thead>
                            <tr>
                                <th>Ενέργειες παραστάσεων</th>
                                <th>Email, Όνομα</th>
                            </tr>
                            </thead>
                            <tbody>
                        @foreach($view_data['most_actionable_users_plays_recent'] as $the_user)
                            <tr>
                                <td>
                                    {{ $the_user->total }}
                                </td>
                                <td>
                                   {!! link_to_route('uranus.users.edit', $the_user->email . ', ' . $the_user->fullName, $the_user->id) !!}
                                </td>
                            </tr>
                        @endforeach
                            </tbody>
                        </table>
                    </div>
                    <h2>Χρήστες με ενέργειες συντελεστών (all-time)</h2><br>
                    <div>
                        <table class="table table-hover dataTable table-striped width-full" data-plugin=" ">
                            <thead>
                            <tr>
                                <th>Ενέργειες συντελεστών</th>
                                <th>Email, Όνομα</th>
                            </tr>
                            </thead>
                            <tbody>
                        @foreach($view_data['most_actionable_users_people'] as $the_user)
                            <tr>
                                <td>
                                    {{ $the_user->total }}
                                </td>
                                <td>
                                   {!! link_to_route('uranus.users.edit', $the_user->email . ', ' . $the_user->fullName, $the_user->id) !!}
                                </td>
                            </tr>
                        @endforeach
                            </tbody>
                        </table>
                    </div>
                    <h2>Χρήστες με ενέργειες συντελεστών (30 ημέρες)</h2><br>
                    <div>
                        <table class="table table-hover dataTable table-striped width-full" data-plugin=" ">
                            <thead>
                            <tr>
                                <th>Ενέργειες συντελεστών</th>
                                <th>Email, Όνομα</th>
                            </tr>
                            </thead>
                            <tbody>
                        @foreach($view_data['most_actionable_users_people_recent'] as $the_user)
                            <tr>
                                <td>
                                    {{ $the_user->total }}
                                </td>
                                <td>
                                   {!! link_to_route('uranus.users.edit', $the_user->email . ', ' . $the_user->fullName, $the_user->id) !!}
                                </td>
                            </tr>
                        @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <!-- End Panel Basic -->
        </div>
    </div>
    <!-- End Page -->
@stop