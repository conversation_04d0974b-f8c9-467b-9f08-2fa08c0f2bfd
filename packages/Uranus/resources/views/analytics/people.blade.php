@extends('uranus::layout')
@section('content')
    <!-- Page -->
    <div class="page animsition">
        <div class="page-header">
            <h1 class="page-title">Συντελεστές με πιο πολλές all-time επισκέψεις</h1>
        </div>
        <div class="page-content">
            <!-- Panel Basic -->
            <div class="panel">
                <div class="panel-body">
                    <h2>Συντελεστές</h2><br><br>
                    <div>
                        <table class="table table-hover dataTable table-striped width-full" data-plugin=" ">
                            <thead>
                            <tr>
                                <th>Επισκέψεις</th>
                                <th>Όνομα</th>
                                <th>Main image</th>
                                <th>Bday</th>
                                <th>Birth place</th>
                                <th>Auto bio</th>
                                <th>Trivia</th>
                                <th>Quotes</th>
                                <th>Refs</th>
                                <th>Manual bio</th>
                            </tr>
                            </thead>
                            <tbody>
                        @foreach($view_data['most_visited_people'] as $visit_counter)
                            <tr>
                                <td>
                                    {{ $visit_counter->visits }}
                                </td>
                                <td>
                                    <a target="_blank" href="{{ route('uranus.people.edit', $visit_counter->visitable->id) }}">{{ $visit_counter->visitable->fullName }}</a>
                                </td>
                                <td @if( empty($visit_counter->visitable->mainImage()->first())) style="color:#f00; font-weight:bold;"@endif>
                                    {{ empty($visit_counter->visitable->mainImage()->first()) ? 'Όχι' : 'Ναι'}}
                                </td>
                                <td>
                                    {{ empty($visit_counter->visitable->birthday) && empty($visit_counter->visitable->birth_year) ? 'Όχι' : 'Ναι'}}
                                </td>
                                <td>
                                    {{ empty($visit_counter->visitable->birth_place) ? 'Όχι' : 'Ναι'}}
                                </td>
                                <td @if( empty($visit_counter->visitable->auto_biography_text)) style="color:#f00; font-weight:bold;"@endif>
                                    {{ empty($visit_counter->visitable->auto_biography_text) ? 'Όχι' : 'Ναι'}}
                                </td>
                                <td>
                                    {{ $visit_counter->visitable->trivia()->get()->count() }}
                                </td>
                                <td>
                                    {{ $visit_counter->visitable->quotes()->get()->count() }}
                                </td>
                                <td>
                                    {{ $visit_counter->visitable->personReferences()->get()->count() }}
                                </td>
                                <td @if( empty($visit_counter->visitable->bio)) style="color:#f00; font-weight:bold;"@endif>
                                    {{ empty($visit_counter->visitable->bio) ? 'Όχι' : 'Ναι'}}
                                </td>
                            </tr>
                        @endforeach
                            </tbody>
                        </table>

                    </div>
                </div>
            </div>
            <!-- End Panel Basic -->
        </div>
    </div>
    <!-- End Page -->
@stop