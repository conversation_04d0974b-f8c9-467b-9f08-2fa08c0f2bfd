@extends('uranus::layout')
@section('content')
    <!-- Page -->
    <div class="page animsition">
        <div class="page-header">
            <h1 class="page-title">Συντελεστές με τις πιο πολλές πρόσφατες επισκέψεις</h1>
            <ul>
                <li>{!! link_to_route('uranus.analytics.people.recent.days', '30 ημέρες', 30) !!}</li>
                <li>{!! link_to_route('uranus.analytics.people.recent.days', '7 ημέρες', 7) !!}</li>
                <li>{!! link_to_route('uranus.analytics.people.recent.days', '3 ημέρες', 3) !!}</li>
            </ul>
        </div>
        <div class="page-content">
            <!-- Panel Basic -->
            <div class="panel">
                <div class="panel-body">
                    <h2>Συντελεστές</h2>
                    <div>
                        <table class="table table-hover dataTable table-striped width-full" data-plugin=" ">
                            <thead>
                            <tr>
                                <th>Όνομα</th>
                                <th># επισκέψεις</th>
                                <th>Main image</th>
                                <th>Bday</th>
                                <th>Birth place</th>
                                <th>Auto bio</th>
                                <th>Trivia</th>
                                <th>Quotes</th>
                                <th>Refs</th>
                                <th>Manual bio</th>
                            </tr>
                            </thead>
                            <tbody>
                                @forelse($view_data['people'] as $person)
                                    <tr>
                                        <td><a target="_blank" href="{{ route('uranus.people.edit', $person->id) }}">{{ $person->first_name . ' ' . $person->last_name }}</a></td>
                                        <td>{{ $person->counter }}</td>
                                        <td @if(empty($person->mainImage()->first())) style="color:#f00; font-weight:bold;"@endif>
                                            {{ empty($person->mainImage()->first()) ? 'Όχι' : 'Ναι'}}
                                        </td>
                                        <td @if(empty($person->birthday) && empty($person->birth_year)) style="color:#f00; font-weight:bold;"@endif>
                                            {{ empty($person->birthday)  && empty($person->birth_year) ? 'Όχι' : 'Ναι'}}
                                        </td>
                                        <td @if(empty($person->birth_place)) style="color:#f00; font-weight:bold;"@endif>
                                            {{ empty($person->birth_place) ? 'Όχι' : 'Ναι'}}
                                        </td>
                                        <td @if(empty($person->auto_biography_text)) style="color:#f00; font-weight:bold;"@endif>
                                            {{ empty($person->auto_biography_text) ? 'Όχι' : 'Ναι'}}
                                        </td>
                                        <td>{{ $person->trivia()->get()->count() }}</td>
                                        <td>{{ $person->quotes()->get()->count() }}</td>
                                        <td>{{ $person->personReferences()->get()->count() }}</td>
                                        <td>{{ empty($person->bio) ? 'Όχι' : 'Ναι'}}</td>
                                    </tr>
                                @empty
                                    <p>Δε βρέθηκαν συντελεστές με πρόσφατες επισκέψεις</p>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <!-- End Panel Basic -->
        </div>
    </div>
    <!-- End Page -->
@stop