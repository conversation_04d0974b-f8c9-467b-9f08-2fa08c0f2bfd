@extends('uranus::layout')
@section('content')
    <!-- Page -->
    <div class="page animsition">
        <div class="page-header">
            <h1 class="page-title">Τηλεοπτικές σειρές με τις πιο πολλές πρόσφατες επισκέψεις</h1>
            <ul>
                <li>{!! link_to_route('uranus.analytics.tvShows.recent.days', '30 ημέρες', 30) !!}</li>
                <li>{!! link_to_route('uranus.analytics.tvShows.recent.days', '7 ημέρες', 7) !!}</li>
                <li>{!! link_to_route('uranus.analytics.tvShows.recent.days', '3 ημέρες', 3) !!}</li>
            </ul>
        </div>
        <div class="page-content">
            <!-- Panel Basic -->
            <div class="panel">
                <div class="panel-body">
                    <h2>Τηλεοπτικές σειρές</h2>
                    <div>
                        <table class="table table-hover dataTable table-striped width-full" data-plugin=" ">
                            <thead>
                            <tr>
                                <th>Τίτλος (Έτος)</th>
                                <th># επισκέψεις</th>
                                <th>Main image</th>
                            </tr>
                            </thead>
                            <tbody>
                                @forelse($view_data['tvShows'] as $tvShow)
                                    <tr>
                                        <td><a target="_blank" href="{{ route('uranus.tvShows.edit', $tvShow->id) }}">{{ $tvShow->title . ' (' . $tvShow->year . ')'}}</a></td>
                                        <td>{{ $tvShow->counter }}</td>
                                        <td @if(empty($tvShow->mainImage()->first())) style="color:#f00; font-weight:bold;"@endif>
                                            {{ empty($tvShow->mainImage()->first()) ? 'Όχι' : 'Ναι'}}
                                        </td>
                                        <td>
                                            <a target="_blank" href="{!! route('tvShows.show', $tvShow->slug) !!}">
                                                <button type="button" class="btn btn-icon btn-default btn-outline" title="Preview">
                                                    <i class="icon wb-search" aria-hidden="true"></i></button>
                                            </a>
                                        </td>
                                    </tr>
                                @empty
                                    <p>Δε βρέθηκαν τηλεοπτικές σειρές με πρόσφατες επισκέψεις</p>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <!-- End Panel Basic -->
        </div>
    </div>
    <!-- End Page -->
@stop