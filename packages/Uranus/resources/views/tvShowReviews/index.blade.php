@extends('uranus::layout')
@section('content')
        <!-- Page -->
<div class="page animsition">
    <div class="page-header">
        <h1 class="page-title">Reviews τηλεοπτικών σειρών {{ $moderated_text }}</h1>
        <ul>
            <li><a href="{{ route('uranus.tvShowReviews.index', ['moderated' => 'all']) }}">Όλες</a></li>
            <li><a href="{{ route('uranus.tvShowReviews.index', ['moderated' => 'no']) }}">Only unmoderated</a></li>
            <li><a href="{{ route('uranus.tvShowReviews.index', ['moderated' => 'yes']) }}">Only moderated</a></li>
            <li><a href="{{ route('uranus.tvShowReviews.index', ['moderated' => 'banned']) }}">Only banned</a></li>
        </ul>
        <div class="page-header-actions">
        </div>
    </div>
    <div class="page-content">
        <!-- Panel Basic -->
        <div class="panel">
            <div class="panel-body">
                @include('uranus::messages.successfulSave')
                @include('uranus::errors.genericForm')
                <table class="table table-hover dataTable table-striped width-full" data-plugin="">
                    <thead>
                        <tr>
                            <th>id</th>
                            <th>Κείμενο</th>
                            <th>Τηλεοπτική σειρά</th>
                            <th>Χρήστης</th>
                            <th>Rating</th>
                            <th>Upvoted</th>
                            <th>Banned</th>
                            <th>Moderated</th>
                            <th>Updated_at</th>
                            <th></th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($reviews as $review)
                            <tr>
                                <td>{!! $review->id !!}</td>
                                <td>{!! $review->review !!}</td>
                                <td>
                                    <a target="_blank" href="{!! route('tvShows.show', $review->tvShow()->first()->slug) !!}">
                                        {{ $review->tvShow()->first()->title }}
                                        <i class="fa fa-external-link" aria-hidden="true"></i>
                                    </a>
                                </td>
                                <td><a target="_blank" href="{!! route('uranus.users.edit', $review->user()->first()->id) !!}">{{ $review->user()->first()->id }}: {{ $review->user()->first()->fullName }}</a></td>
                                <td><?php $rating = \App\Models\TvShowRating::where([['user_id', '=', $review->user_id], ['tv_show_id', '=', $review->tv_show_id]])->first() ?> {{ $rating->rating }}</td>
                                <td>{!! $review->upvotes !!}</td>
                                <td @if($review->banned) style="color:#f00; font-weight:bold;" @endif><span id="banned-{{$review->id}}">{!! $review->banned ? 'Ναι' : 'Όχι' !!}</span></td>
                                <td><span id="moderated-{{$review->id}}">{!! $review->moderated ? 'Ναι' : 'Όχι' !!}</span></td>
                                <td>{!! $review->updated_at !!}</td>
                                <td>
                                </td>
                                <td>
                                    <div class="allow"
                                         data-url="{{ route('uranus.tvShowReviews.update', $review->id) }}"
                                        data-banned="0">
                                        <button type="button" class="btn btn-icon btn-success btn-outline" title="Allow">
                                            <i class="icon wb-check" aria-hidden="true"></i></button>
                                    </div>
                                </td>
                                <td>
                                    <div class="allow"
                                         data-url="{{ route('uranus.tvShowReviews.update', $review->id) }}"
                                         data-banned="1">
                                        <button type="button" class="btn btn-icon btn-danger btn-outline" title="Block">
                                            <i class="fa fa-times" aria-hidden="true"></i></button>
                                    </div>

                                </td>
                            </tr>
                        @empty
                            <p>Δε βρέθηκαν reviews τηλεοπτικών σειρών</p>
                        @endforelse
                    </tbody>
                </table>
                {!! $reviews->render() !!}
            </div>
        </div>
        <!-- End Panel Basic -->
    </div>
</div>
<!-- End Page -->
@stop
@section('scripts')
    @parent
    {!! csrf_field() !!}
    <script src="{{ asset('js/admin/jquery.deleteResource.js') }}"></script>
    <script type="text/javascript">
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        })


        $('.allow').on('click', function (e) {
            e.preventDefault();
            var url = $(this).attr('data-url');
            var banned = $(this).attr('data-banned');
            var self = this;
            $.ajax({
                url: url,
                dataType: 'json',
                type: 'post',
                data:{'banned':banned},
                success: function (data, textStatus, jQxhr) {
                  if(data.status == 'success'){
                      if(data.banned == 0){
                          $('#banned-'+data.id).html('Όχι');
                      }
                      else{
                          $('#banned-'+data.id).html('Ναι');
                      }
                      $('#moderated-'+data.id).html('Ναι');

                  }
                },
                error: function (jqXhr, textStatus, errorThrown) {
                }
            });
        });
    </script>
@stop
