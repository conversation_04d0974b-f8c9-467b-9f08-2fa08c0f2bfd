@extends('uranus::layout')
@section('content')
    <!-- Page -->
    <div class="page animation">
        <div class="page-header">
            <h1 class="page-title">{{ $dpu->id }}: {{ $dpu->title }}</h1>
            <div class="page-header-actions">
            </div>
        </div>
        <div class="page-content">
            <div class="container-fluid">
                @include('uranus::messages.successfulSave')
                @include('uranus::errors.genericForm')
                <form method="post" action="{{ route('uranus.digital_platform_urls.update', $dpu->id) }}">
                    {!! csrf_field() !!}
                    {!! method_field('put') !!}
                    @include('uranus::digital_platform_urls._digitalPlatformUrlForm',['submitButtonText'=>'Αποθήκευση'])
                </form>
            </div>
        </div>
    </div>
    <!-- End Page -->
