@extends('uranus::layout')
@section('content')
    <!-- Page -->
    <div class="page animsition">
        <div class="page-header">
            <h1 class="page-title">Followed συντελεστές</h1>
        </div>
        <div class="page-content">
            <!-- Panel Basic -->
            <div class="panel">
                <div class="panel-body">
                    <table class="table table-hover dataTable table-striped width-full" data-plugin=" ">
                        <thead>
                            <tr>
                                <th>Όνομα</th>
                                <th># followers</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($people as $person)
                                <tr>
                                    <td>{!! link_to_route('uranus.people.edit', $person->first_name . ' ' . $person->last_name, $person->person_id) !!}</td>
                                    <td>{{ $person->counter }}</td>
                                </tr>
                            @empty
                                <p>Δε βρέθηκαν followed συντελεστές</p>
                            @endforelse
                        </tbody>
                    </table>
                    {!! $people->render() !!}
                </div>
            </div>
            <!-- End Panel Basic -->
        </div>
    </div>
    <!-- End Page -->
@stop