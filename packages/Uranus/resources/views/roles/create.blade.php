@extends('uranus::layout')
@section('content')
<!-- Page -->
<div class="page animsition">
  <div class="page-header">
    <h1 class="page-title">Καταχώρηση νέου θεατρικού επαγγέλματος</h1>
  </div>
  <div class="page-content">
    <div class="container-fluid">
        @include('uranus::messages.successfulSave')
      @include('uranus::errors.genericForm')
        <form action="{{ route('uranus.roles.store') }}" method="post">
            {!! csrf_field() !!}
            <div class="row">
                <div class="col-sm-6 col-sm-offset-3">
                    <div class="panel">
                        <div class="panel-body container-fluid">
                            <div class="form-group row">
                                <div class="col-sm-8 col-sm-offset-2">
                                    <label for="description">Περιγραφή:*</label>
                                    <input autofocus type="text" name="description" id="description" class="form-control">
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-3 col-sm-offset-7">
                                    <button type="submit" class="btn btn-block btn-success">Συνέχεια
                                        <i class="fa fa-arrow-right pull-right"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
  </div>
</div>
<!-- End Page -->
@stop