@extends('uranus::layout')
@section('content')
        <!-- Page -->
<div class="page animsition">
    <div class="page-header">
        <h1 class="page-title">Θεατρικά επαγγέλματα</h1>
        <div class="page-header-actions">
            @admincan('manage_theatres')
            <a href="{!! route('uranus.roles.create') !!}">
                <button type="button" class="btn btn-floating btn-danger">
                    <i class="icon wb-plus" aria-hidden="true"></i></button>
            </a>
            @endadmincan
        </div>
    </div>
    <div class="page-content">
        <!-- Panel Basic -->
        <div class="panel">
            <div class="panel-body">
                {{--Search bar--}}
                <form method="get" action="{{ route('uranus.roles.index') }}">
                    <div class="form-group row">
                        <div class="col-sm-12 col-md-4">
                            <label for="q">Τίτλος</label>
                            <input type="text" id="search_query" class="form-control" name="q" value="{{ request('q') }}">
                        </div>
                        <div class="col-sm-12  col-md-4">
                            <label for="characterable">Characterable</label>
                            <select name="characterable" id="characterable" class="form-control">
                                <option disabled selected value>Επίλεξε</option>
                                <option @if(request('characterable') == 'yes') selected @endif value="yes">Yes</option>
                                <option @if(request('characterable') == 'no') selected @endif value="no">No</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group row">
                        <div class="col-sm-12 col-md-6">
                            <button type="submit">Αναζήτηση</button>
                            <a href="{{ route('uranus.roles.index') }}">Όλα</a>
                        </div>
                    </div>
                </form>
                {{--Search bar--}}
                <br>
                <table class="table table-hover dataTable table-striped width-full" data-plugin="">
                    <thead>
                        <tr>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.roles.index','Τίτλος','title') !!}</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.roles.index','Περιγραφή','description') !!}</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.roles.index','Κατάταξη Uranus','sort_order') !!}</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.roles.index','Κατάταξη Frontend','sort_order_frontend') !!}</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.roles.index','Characterable','characterable') !!}</th>
                            <th>Varieties</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($roles as $role)
                            <tr>
                                <td><a target="_blank" href="{{ route('uranus.roles.edit', $role->id) }}">{{ $role->title }}</a></td>
                                <td>{{ $role->description }}</td>
                                <td>{{ $role->sort_order }}</td>
                                <td>{{ $role->sort_order_frontend }}</td>
                                <td>
                                    @if($role->characterable)
                                        <div class="text-center">
                                            <i class="icon wb-user-add green-600" aria-hidden="true" title="Characterable"></i>
                                        </div>
                                    @else
                                        <div class="text-center">
                                            <i class="icon wb-user-add red-600" aria-hidden="true" title="Non characterable"></i>
                                        </div>
                                    @endif
                                </td>
                                <td>{{ $role->varieties()->count() }}</td>
                                @admincan('manage_theatres')
                                <td>
                                    <a target="_blank" href="{!! route('uranus.roles.edit', $role->id) !!}">
                                        <button type="button" class="btn btn-icon btn-default btn-outline">
                                            <i class="icon wb-pencil" aria-hidden="true"></i></button>
                                    </a>
                                </td>
                                @endadmincan
                                @admincan('delete_theatres')
                                <td>
                                    <a data-url="{!! route('uranus.roles.destroy', $role->id) !!}" href="" class="deleteResource">
                                        <button type="button" class="btn btn-icon btn-danger btn-outline">
                                            <i class="fa fa-times" aria-hidden="true"></i></button>
                                    </a>
                                </td>
                                @endadmincan
                            </tr>
                        @empty
                            <p>Δε βρέθηκαν θεατρικά επαγγέλματα</p>
                        @endforelse
                    </tbody>
                </table>
                {!! $roles->render() !!}
            </div>
        </div>
        <!-- End Panel Basic -->
    </div>
</div>
<!-- End Page -->
@stop
@section('scripts')
    @parent
    {!! csrf_field() !!}
    <script src="{{ asset('js/admin/jquery.deleteResource.js') }}"></script>
@stop
