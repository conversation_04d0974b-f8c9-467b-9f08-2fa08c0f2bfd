<div class="row">
    <div class="col-sm-6">
        <!-- Panel Static Lables -->
        <div class="panel">
            <div class="panel-body container-fluid">
                <div class="form-group row">
                    <div class="col-sm-12">
                        <label class="control-label" for="title">Τίτλος*:</label>
                        <input type="text" class="form-control" name="title" id="title" value="{{ old('title', $role->title) }}">
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-sm-6">
                        <label class="control-label" for="sort_order_frontend">Κατάταξη frontend:</label>
                        <input type="number" class="form-control" name="sort_order_frontend" id="sort_order_frontend" value="{{ old('sort_order_frontend',$role->sort_order_frontend==255?0:$role->sort_order_frontend) }}">
                    </div>
                    <div class="col-sm-6">
                        <label class="control-label" for="sort_order">Κατάταξη uranus:</label>
                        <input type="number" class="form-control" name="sort_order" id="sort_order" value="{{ old('sort_order', $role->sort_order) }}">
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-sm-12">
                        <label class="control-label" for="description">Περιγραφή (unstage):</label>
                        <textarea class="form-control" name="description" id="description">{{ old('description', $role->description) }}</textarea>
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-sm-12">
                        <label class="control-label" for="biographable_description_female">Κείμενο στο βιογραφικό (θηλυκό):</label>
                        <textarea class="form-control" name="biographable_description_female" id="biographable_description_female">{{ old('biographable_description_female', $role->biographable_description_female) }}</textarea>
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-sm-12">
                        <label class="control-label" for="biographable_description_male">Κείμενο στο βιογραφικό (αρσενικό):</label>
                        <textarea class="form-control" name="biographable_description_male" id="biographable_description_male">{{ old('biographable_description_male', $role->biographable_description_male) }}</textarea>
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-sm-12">
                        <label class="control-label" for="biographable">Βιογραφίσιμο (biographable):</label>
                        <select name="biographable" id="biographable" class="form-control">
                            <option value="0" {{ $role->biographable ? 'selected': '' }}>Οχι</option>
                            <option value="1" {{ $role->biographable ? 'selected': '' }}>Ναι</option>
                        </select>
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-sm-12">
                        <label class="control-label" for="biographable">Χαρακτηρίσιμο (characterable):</label>
                        <select name="characterable" id="characterable" class="form-control">
                            <option value="0" {{ $role->characterable ? 'selected': '' }}>Οχι</option>
                            <option value="1" {{ $role->characterable ? 'selected': '' }}>Ναι</option>
                        </select>
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-sm-12">
                        <label class="control-label" for="for_plays">Σε παραστάσεις θεατρικές:</label>
                        <select name="for_plays" id="for_plays" class="form-control">
                            <option value="0" {{ $role->for_plays ? 'selected': '' }}>Οχι</option>
                            <option value="1" {{ $role->for_plays ? 'selected': '' }}>Ναι</option>
                        </select>
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-sm-12">
                        <label class="control-label" for="for_movies">Σε ταινίες κινηματογραφικές:</label>
                        <select name="for_movies" id="for_movies" class="form-control">
                            <option value="0" {{ $role->for_movies ? 'selected': '' }}>Οχι</option>
                            <option value="1" {{ $role->for_movies ? 'selected': '' }}>Ναι</option>
                        </select>
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-sm-12">
                        <label class="control-label" for="for_tv">Σε σειρές τηλεοπτικές:</label>
                        <select name="for_tv" id="for_tv" class="form-control">
                            <option value="0" {{ $role->for_tv ? 'selected': '' }}>Οχι</option>
                            <option value="1" {{ $role->for_tv ? 'selected': '' }}>Ναι</option>
                        </select>
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-sm-12 col-md-6">
                        <button type="submit" class="btn btn-block btn-success">{{ $submitButtonText }}</button>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Panel Static Labels -->
    </div>
    <div class="col-sm-6">
        <!-- Panel Static Lables -->
        <div class="panel">
            <div class="panel-body container-fluid">
                <div class="form-group row">
                    <div class="col-sm-12">
                        <label class="control-label" for="varieties">Varieties:</label>
                        <select name="varieties[]" id="varieties_list" class="form-control" multiple>
                        @foreach($varieties as $variety)
                            <option value="{{ $variety->id }}" {{ $role->varieties->contains($variety) ? 'selected' : '' }}>{{ $variety->name }}</option>
                        @endforeach
                        </select>
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-sm-12 col-md-6">
                        <button type="submit" class="btn btn-block btn-success">{{ $submitButtonText }}</button>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Panel Static Labels -->
    </div>
</div>

@section('footer')
    @parent
    <script>
        $('#varieties_list').select2({
            placeholder: 'Please choose'
        });
    </script>
@stop
