@extends('uranus::layout')
@section('head')
    <link rel="stylesheet" href="{{asset('assets/vendor/c3/c3.css')}}">
@stop
    @section('content')

            <!-- Page -->
    <div class="page animsition">
        <div class="page-header">
            <h1 class="page-title">Αρχική</h1>
        </div>
        <div class="page-content">
            @admincan('see_analytics')
            <div class="row">
                <div class="col-sm-12">
                    <!-- Panel Basic -->
                    <div class="panel">
                        <div class="panel-body">
                            <h4 class="example-title">ΕΜΦΑΝΙΣΕΙΣ ΣΕΛΙΔΩΝ - ΕΠΙΣΚΕΠΤΕΣ / ΜΕΡΑ</h4>

                            <div id="chart"></div>
                        </div>
                    </div>
                    <!-- End Panel Basic -->
                </div>
            </div>
            @endadmincan
            <div class="row">
                <div class="col-sm-12   col-xs-12">
                    <div class="widget">
                        <div class="widget-content padding-35 bg-white clearfix">
                            <div class="counter counter-md pull-left text-left">
                                <div class="counter-number-group">
                                    <span class="counter-number">{{$activeUsers}}</span>
                                    <span class="counter-number-related text-capitalize">{{$activeUsers === 1 ? "Επισκέπτης" : "Επισκέπτες"}}</span>
                                </div>
                                <div class="counter-label text-capitalize font-size-16">σήμερα</div>
                            </div>
                            <div class="pull-right white">
                                <i class="icon icon-circle icon-2x wb-users bg-blue-600" aria-hidden="true"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                @admincan('see_plays')
                <div class="col-md-4">
                    <a href="{{ route('uranus.plays.index') }}">
                        <div class="widget-content padding-35 bg-white clearfix">
                            <div class="counter counter-md pull-left text-left">
                                <div class="counter-number-group">
                                    <span class="counter-number">{{ number_format($totalPlays,0,',','.') }}</span>
                                    <span class="counter-number-related ">Θεατρικά Έργα</span>
                                </div>
                            </div>
                            <div class="pull-right white">
                                <i class="icon icon-circle icon-2x wb-camera bg-blue-600" aria-hidden="true"></i>
                            </div>
                        </div>
                    </a>
                </div>
                @endadmincan
                @admincan('see_people')
                <div class="col-md-4">
                    <a href="{{ route('uranus.people.index') }}">
                        <div class="widget-content padding-35 bg-white clearfix">
                            <div class="counter counter-md pull-left text-left">
                                <div class="counter-number-group">
                                    <span class="counter-number">{{ number_format($totalPeople,0,',','.') }}</span>
                                    <span class="counter-number-related ">Συντελεστές</span>
                                </div>
                            </div>
                            <div class="pull-right white">
                                <i class="icon icon-circle icon-2x wb-users bg-red-600" aria-hidden="true"></i>
                            </div>
                        </div>
                    </a>
                </div>
                @endadmincan
                @admincan('see_theatres')
                <div class="col-md-4">
                    <a href="{{ route('uranus.theatres.index') }}">
                        <div class="widget-content padding-35 bg-white clearfix">
                            <div class="counter counter-md pull-left text-left">
                                <div class="counter-number-group">
                                    <span class="counter-number">{{ number_format($totalTheatres,0,',','.') }}</span>
                                    <span class="counter-number-related ">Θέατρα</span>
                                </div>
                            </div>
                            <div class="pull-right white">
                                <i class="icon icon-circle icon-2x wb-home bg-green-600" aria-hidden="true"></i>
                            </div>
                        </div>
                    </a>
                </div>
                @endadmincan
            </div>
            <br>

            <div class="row">
                @admincan('see_plays')
                <div class="col-md-4">
                    <a href="{{ route('uranus.plays.index') }}">
                        <div class="widget-content padding-35 bg-white clearfix">
                            <div class="counter counter-md pull-left text-left">
                                <div class="counter-number-group">
                                    <span class="counter-number">{{ number_format($totalPlayRatings,0,',','.') }}</span>
                                    <span class="counter-number-related ">Ratings Παραστάσεων</span>
                                </div>
                            </div>
                            <div class="pull-right white">
                                <i class="icon icon-circle icon-2x wb-camera bg-blue-600" aria-hidden="true"></i>
                            </div>
                        </div>
                    </a>
                </div>
                @endadmincan
                @admincan('see_movies')
                <div class="col-md-4">
                    <a href="{{ route('uranus.movies.index') }}">
                        <div class="widget-content padding-35 bg-white clearfix">
                            <div class="counter counter-md pull-left text-left">
                                <div class="counter-number-group">
                                    <span class="counter-number">{{ number_format($totalMovieRatings,0,',','.') }}</span>
                                    <span class="counter-number-related ">Ratings ταινιών</span>
                                </div>
                            </div>
                            <div class="pull-right white">
                                <i class="icon icon-circle icon-2x wb-users bg-red-600" aria-hidden="true"></i>
                            </div>
                        </div>
                    </a>
                </div>
                @endadmincan
                @admincan('see_tv')
                <div class="col-md-4">
                    <a href="{{ route('uranus.tvShows.index') }}">
                        <div class="widget-content padding-35 bg-white clearfix">
                            <div class="counter counter-md pull-left text-left">
                                <div class="counter-number-group">
                                    <span class="counter-number">{{ number_format($totalTvShowRatings,0,',','.') }}</span>
                                    <span class="counter-number-related ">Ratings σειρών</span>
                                </div>
                            </div>
                            <div class="pull-right white">
                                <i class="icon icon-circle icon-2x wb-home bg-green-600" aria-hidden="true"></i>
                            </div>
                        </div>
                    </a>
                </div>
                @endadmincan
            </div>
            <br>

            <div class="row">
                @admincan('see_plays')
                <div class="col-md-4">
                    <a href="{{ route('uranus.plays.index') }}">
                        <div class="widget-content padding-35 bg-white clearfix">
                            <div class="counter counter-md pull-left text-left">
                                <div class="counter-number-group">
                                    <span class="counter-number">{{ number_format($totalPlayReviews,0,',','.') }}</span>
                                    <span class="counter-number-related ">Reviews Παραστάσεων</span>
                                </div>
                            </div>
                            <div class="pull-right white">
                                <i class="icon icon-circle icon-2x wb-camera bg-blue-600" aria-hidden="true"></i>
                            </div>
                        </div>
                    </a>
                </div>
                @endadmincan
                @admincan('see_movies')
                <div class="col-md-4">
                    <a href="{{ route('uranus.movies.index') }}">
                        <div class="widget-content padding-35 bg-white clearfix">
                            <div class="counter counter-md pull-left text-left">
                                <div class="counter-number-group">
                                    <span class="counter-number">{{ number_format($totalMovieReviews,0,',','.') }}</span>
                                    <span class="counter-number-related ">Reviews ταινιών</span>
                                </div>
                            </div>
                            <div class="pull-right white">
                                <i class="icon icon-circle icon-2x wb-users bg-red-600" aria-hidden="true"></i>
                            </div>
                        </div>
                    </a>
                </div>
                @endadmincan
                @admincan('see_tv')
                <div class="col-md-4">
                    <a href="{{ route('uranus.tvShows.index') }}">
                        <div class="widget-content padding-35 bg-white clearfix">
                            <div class="counter counter-md pull-left text-left">
                                <div class="counter-number-group">
                                    <span class="counter-number">{{ number_format($totalTvShowReviews,0,',','.') }}</span>
                                    <span class="counter-number-related ">Reviews σειρών</span>
                                </div>
                            </div>
                            <div class="pull-right white">
                                <i class="icon icon-circle icon-2x wb-home bg-green-600" aria-hidden="true"></i>
                            </div>
                        </div>
                    </a>
                </div>
                @endadmincan
            </div>
            <br>

            <div class="row">
                @admincan('see_contributor_articles')
                <div class="col-md-4">
                    <a href="{{ route('uranus.myCritiques.index') }}">
                        <div class="widget-content padding-35 bg-white clearfix">
                            <div class="counter counter-md pull-left text-left">
                                <div class="counter-number-group">
                                    <span class="counter-number">{{ number_format($totalMyCritiques,0,',','.') }}</span>
                                    <span class="counter-number-related ">Δημοσιευμένες κριτικές</span>
                                </div>
                            </div>
                            <div class="pull-right white">
                                <i class="icon icon-circle icon-2x wb-camera bg-blue-600" aria-hidden="true"></i>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-md-4">
                    <a href="{{ route('uranus.myInterviews.index') }}">
                        <div class="widget-content padding-35 bg-white clearfix">
                            <div class="counter counter-md pull-left text-left">
                                <div class="counter-number-group">
                                    <span class="counter-number">{{ number_format($totalMyInterviews,0,',','.') }}</span>
                                    <span class="counter-number-related ">Δημοσιευμένες συνεντεύξεις</span>
                                </div>
                            </div>
                            <div class="pull-right white">
                                <i class="icon icon-circle icon-2x wb-users bg-red-600" aria-hidden="true"></i>
                            </div>
                        </div>
                    </a>
                </div>
                @endadmincan
                @admincan('see_articles')
                <div class="col-md-4">
                    <a href="{{ route('uranus.userposts.index') }}">
                        <div class="widget-content padding-35 bg-white clearfix">
                            <div class="counter counter-md pull-left text-left">
                                <div class="counter-number-group">
                                    <span class="counter-number">{{ number_format($totalUserposts,0,',','.') }}</span>
                                    <span class="counter-number-related ">Δημοσιευμένα άρθρα χρηστών</span>
                                </div>
                            </div>
                            <div class="pull-right white">
                                <i class="icon icon-circle icon-2x wb-home bg-green-600" aria-hidden="true"></i>
                            </div>
                        </div>
                    </a>
                </div>
                @endadmincan
            </div>
            <br>


            <div class="row">
                @admincan('see_plays')
                <div class="col-lg-4">
                    @include('uranus::_dashboardRevisionsTable',[
                            'label'             => 'Μόλις ενημερώθηκαν',
                            'class'             => 'blue-600',
                            'title'             => 'Θεατρικά Έργα',
                            'resources'         => $latestUpdatedPlays,
                            'resourceTimestamp' => 'updated_at',
                            'revisionRelation' => 'latestRevision',
                            'resourceEditRoute' => 'uranus.plays.edit',
                            'resourceRevisionRoute' => 'uranus.play.revisions',
                            'resourceEditLabel' => 'title',
                    ])
                </div>
                @endadmincan
                @admincan('see_people')
                <div class="col-lg-4">
                    @include('uranus::_dashboardRevisionsTable',[
                             'label'             => 'Μόλις ενημερώθηκαν',
                             'class'             => 'red-600',
                             'title'             => 'Συντελεστές',
                             'resources'         => $latestUpdatedPeople,
                             'resourceTimestamp' => 'updated_at',
                              'revisionRelation' => 'latestRevision',
                             'resourceEditRoute' => 'uranus.people.edit',
                             'resourceRevisionRoute' => 'uranus.person.revisions',
                             'resourceEditLabel' => 'fullName',
                        ])
                </div>
                @endadmincan
                @admincan('see_theatres')
                <div class="col-lg-4">
                    @include('uranus::_dashboardRevisionsTable',[
                           'label'             => 'Μόλις ενημερώθηκαν',
                           'class'             => 'green-600',
                           'title'             => 'Θέατρα',
                           'resources'         => $latestUpdatedTheatres,
                           'resourceTimestamp' => 'updated_at',
                            'revisionRelation' => 'latestRevision',
                           'resourceEditRoute' => 'uranus.theatres.edit',
                           'resourceRevisionRoute' => 'uranus.theatre.revisions',
                           'resourceEditLabel' => 'name',
                        ])
                </div>
                @endadmincan

            </div>

            <div class="row">
                @admincan('see_plays')
                <div class="col-lg-4">
                    @include('uranus::_dashboardRevisionsTable',[
                           'label'             => 'Μόλις ανέβηκαν',
                           'class'             => 'blue-600',
                           'title'             => 'Θεατρικά Έργα',
                           'resources'         => $latestCreatedPlays,
                           'resourceTimestamp' => 'created_at',
                           'revisionRelation' => 'creationRevision',
                           'resourceEditRoute' => 'uranus.plays.edit',
                           'resourceRevisionRoute' => 'uranus.play.revisions',
                           'resourceEditLabel' => 'title',
                        ])
                </div>
                @endadmincan
                @admincan('see_people')
                <div class="col-lg-4">
                    @include('uranus::_dashboardRevisionsTable',[
                            'label'             => 'Μόλις ανέβηκαν',
                            'class'             => 'red-600',
                            'title'             => 'Συντελεστές',
                            'resources'         => $latestCreatedPeople,
                            'resourceTimestamp' => 'created_at',
                            'revisionRelation' => 'creationRevision',
                            'resourceEditRoute' => 'uranus.people.edit',
                            'resourceRevisionRoute' => 'uranus.person.revisions',
                            'resourceEditLabel' => 'fullName',
                         ])
                </div>
                @endadmincan
                @admincan('see_theatres')
                <div class="col-lg-4">
                    @include('uranus::_dashboardRevisionsTable',[
                            'label'             => 'Μόλις ανέβηκαν',
                            'class'             => 'green-600',
                            'title'             => 'Θέατρα',
                            'resources'         => $latestCreatedTheatres,
                            'resourceTimestamp' => 'created_at',
                            'revisionRelation' => 'creationRevision',
                            'resourceEditRoute' => 'uranus.theatres.edit',
                             'resourceRevisionRoute' => 'uranus.theatre.revisions',
                            'resourceEditLabel' => 'name',
                         ])
                </div>
                @endadmincan
            </div>

        </div>
    </div>
    <!-- End Page -->
@stop

@section('footer')
    @parent
    <script src="{{asset('assets/vendor/d3/d3.min.js')}}"></script>
    <script src="{{asset('assets/vendor/c3/c3.min.js')}}"></script>
    <script type="text/javascript">
        (function () {
            var time_series_chart = c3.generate({
                bindto: '#chart',
                data: {
                    x: 'x',
                    columns: [
                        ['x', {!! $days!!}],
                        ['visitors', {{$visitors}}],
                        ['pageviews', {{$pageViews}}]
                    ]
                },
                padding: {
                    right: 40
                },
                axis: {
                    x: {
                        type: 'timeseries',
                        tick: {
                            outer: false,
                            format: '%Y-%m-%d'
                        }
                    },
                    y: {
                        min: 0
                    }
                },
                grid: {
                    x: {
                        show: false
                    },
                    y: {
                        show: true
                    }
                }
            });

        })();
    </script>
@stop
