<div class="form-group row">
    <div class="col-sm-12">
        <label for="title" class="control-label">Τίτλος σειράς:*</label>
        <input type="text" class="form-control" name="title" id="title" value="{!! $tvShow->title !!}" readonly="readonly">
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12">
        <a style="cursor:pointer" data-url="{!! route('uranus.tvShows.translation.suggest') !!}" href="" id="translate_title">Πρότεινε μετάφρασιν</a>
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12">
        <label for="title_en" class="control-label">Τίτλος σειράς [Αγγλικά]:*</label>
        <input type="text" class="form-control" name="title_en" id="title_en" value="{{ old('title_en',($tvShow->hasTranslation('en') ? $tvShow->translate('en')->title : "")) }}">
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12">
        <label for="synopsis" class="control-label">Σύνοψη σειράς:</label>
        <textarea name="synopsis" id="synopsis" class="form-control" rows="4" readonly="readonly">{!! $tvShow->synopsis !!}</textarea>
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12">
        <a style="cursor:pointer" data-url="{!! route('uranus.tvShows.translation.suggest') !!}" href="" id="translate_synopsis">Πρότεινε μετάφρασιν</a>
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12">
        <label for="synopsis_en" class="control-label">Σύνοψη σειράς [Αγγλικά]:</label>
        <textarea name="synopsis_en" id="synopsis_en" class="form-control wysiwyg" rows="4">{!! old('synopsis_en', ($tvShow->hasTranslation('en') ? $tvShow->translate('en')->synopsis : "")) !!}</textarea>
    </div>
</div>

<div class="form-group row">
    <div class="col-sm-12 col-md-6 col-md-offset-3">
        <button type="submit" class="btn btn-block btn-success">{{ $submitButtonText }}</button>
    </div>
</div>
