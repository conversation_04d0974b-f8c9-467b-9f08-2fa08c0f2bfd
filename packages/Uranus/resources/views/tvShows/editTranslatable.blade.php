@extends('uranus::layout')
@section('head')
    <link type="text/css" rel="stylesheet"
            href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.10.2/themes/smoothness/jquery-ui.min.css"
            media="screen"/>
    @stop

    @section('content')
            <!-- Page -->
    <div class="page animsition">
        <div class="page-header">
            <h1 class="page-title">{{$tvShow->title}}</h1>
            <div class="page-header-actions">
                <a href="{!! route('uranus.tvShows.edit', $tvShow->id) !!}">
                    <button type="button" class="btn btn-floating btn-info"
                            title="{!! trans('uranus::tvShows.edit_tvShow') !!}">
                        <i class="icon wb-pencil" aria-hidden="true"></i></button>
                </a>
            </div>
        </div>

        <div class="page-content">
            <div class="container-fluid">
                @include('uranus::messages.successfulSave')
                @include('uranus::errors.genericForm')
                <div class="row">
                    <div class="col-sm-12">
                        <!-- Panel Static Lables -->
                        <div class="panel">
                            <div class="panel-heading">
                                <h4 class="panel-title">Μετάφραση</h4>
                            </div>
                            <div class="panel-body container-fluid">
                                <form action="{{route('uranus.tvShows.translatable.update', $tvShow->id)}}" method="post" enctype="multipart/form-data">
                                    {!! csrf_field() !!}
                                    {!! method_field('put') !!}
                                    @include('uranus::tvShows._tvShowFormTranslatable', ['submitButtonText'=>'Αποθήκευση'])
                                </form>
                            </div>
                        </div>
                        <!-- End Panel Static Lables -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- End Page -->
@stop
@section('footer')
    @parent
    <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.10.2/jquery-ui.min.js" charset="UTF-8"></script>
    {!! csrf_field() !!}
    <script src="{{ asset('js/admin/jquery.translateTvShowResource.js') }}"></script>
@stop
