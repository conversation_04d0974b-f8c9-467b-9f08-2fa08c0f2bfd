@extends('uranus::layouts.master')
@section('title', 'Επεξεργασία μεταφράσεων τηλεοπτικής σειράς')
@section('content')
    <!-- Page -->
    <div class="page">
        <div class="page-header">
            <h1 class="page-title">Επεξεργασία μεταφράσεων τηλεοπτικής σειράς</h1>
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('uranus.dashboard') }}">Αρχική</a></li>
                <li class="breadcrumb-item"><a href="{{ route('uranus.tvShows.index') }}">Τηλεοπτικές σειρές</a></li>
                <li class="breadcrumb-item"><a href="{{ route('uranus.tvShows.edit', $tvShow->id) }}">Επεξεργασία σειράς</a></li>
                <li class="breadcrumb-item active">Μεταφράσεις</li>
            </ol>
        </div>

        <div class="page-content">
            <div class="panel">
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-3">
                            <h4>Ενέργειες</h4>
                            <div class="list-group">
                                <a href="{{ route('uranus.tvShows.edit', $tvShow->id) }}" class="list-group-item">
                                    <i class="icon md-edit" aria-hidden="true"></i> Επεξεργασία βασικών στοιχείων
                                </a>
                                <a href="{{ route('uranus.tvShows.translatable.edit', $tvShow->id) }}" class="list-group-item active">
                                    <i class="icon md-translate" aria-hidden="true"></i> Επεξεργασία μεταφράσεων
                                </a>
                            </div>
                        </div>
                        <div class="col-md-9">
                            @include('uranus::errors.genericForm')
                            
                            <div class="panel">
                                <div class="panel-heading">
                                    <h4 class="panel-title">Μεταφράσεις τηλεοπτικής σειράς</h4>
                                </div>
                                <div class="panel-body container-fluid">
                                    <form action="{{route('uranus.tvShows.translatable.update', $tvShow->id)}}" method="post" enctype="multipart/form-data">
                                        {!! csrf_field() !!}
                                        {!! method_field('put') !!}
                                        @include('uranus::tvShows._tvShowFormTranslatable', ['submitButtonText'=>'Αποθήκευση'])
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- End Page -->
@stop
@section('footer')
    @parent
    <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.10.2/jquery-ui.min.js" charset="UTF-8"></script>
    {!! csrf_field() !!}
    <script src="{{ asset('js/admin/jquery.translateTvShowResource.js') }}"></script>
@stop
