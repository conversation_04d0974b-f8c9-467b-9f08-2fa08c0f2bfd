@extends('uranus::layout')
@section('head')
    <link type="text/css" rel="stylesheet"
            href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.10.2/themes/smoothness/jquery-ui.min.css"
            media="screen"/>
    <link rel="stylesheet" href="{{asset('assets/vendor/bootstrap-datepicker/bootstrap-datepicker.css')}}">
    @stop

    @section('content')
            <!-- Page -->
    <div class="page animsition">
        <div class="page-header">
            <h1 class="page-title">{{$tvShow->title}}</h1>
            <h4>Τηλεόραση</h4>
            <div class="page-header-actions">

                <a href="{{ route('uranus.tvShow.revisions',$tvShow->id) }}">
                    <button type="button" class="btn btn-floating btn-default" title="{!! trans('uranus::common.revisions_tooltip') !!}">
                        <i class="fa fa-lg fa-history blue-600" style="font-size: 24px"></i></button>
                </a>
                <a target="_blank" href="{!! route('tvShows.show', ['slug' => $tvShow->slug]) !!}">
                    <button type="button" class="btn btn-floating btn-primary" title="{!! trans('uranus::common.preview_tooltip') !!}">
                        <i class="icon wb-eye" aria-hidden="true"></i></button>
                </a>
                <a href="{!! route('uranus.images.moderation.index', ['tv_show_id' => $tvShow->id]) !!}">
                    <button type="button" class="btn btn-floating btn-success" title="{!! trans('uranus::tvShows.images') !!}"><i class="icon wb-image"
                                                                                                                                aria-hidden="true"></i></button>
                </a>
                <a href="{!! route('uranus.tvShows.create') !!}">
                    <button type="button" class="btn btn-floating btn-danger" title="{!! trans('uranus::tvShows.create_tvShow_tooltip') !!}"><i class="icon wb-plus"
                                aria-hidden="true"></i></button>
                </a>
            </div>
        </div>
        <div class="page-content">
            <div class="container-fluid">
                @include('uranus::messages.successfulSave')
                @include('uranus::errors.genericForm')
                <div class="row">
                    <div class="col-sm-6">
                        <!-- Panel Static Lables -->
                        <div class="panel">
                            <div class="panel-heading">
                                <h4 class="panel-title">Γενικές πληροφορίες σειράς</h4>
                            </div>
                            <div class="panel-body container-fluid">
                                <form action="{{route('uranus.tvShows.update', $tvShow->id)}}" method="post" enctype="multipart/form-data">
                                    {!! csrf_field() !!}
                                    {!! method_field('put') !!}
                                    @include('uranus::tvShows._tvShowForm', ['submitButtonText'=>'Αποθήκευση σειράς'])
                                </form>
                            </div>
                        </div>
                        <!-- End Panel Static Lables -->
                    </div>
                    <div class="col-sm-6">
                        <!-- Panel Floating Lables -->
                        <div class="panel">
                            @include('uranus::images.manage')
                        </div>
                        <!-- End Panel Floating Lables -->

                        <!-- Translation Panel -->
                        <div class="panel">
                            <div class="panel-heading">
                                <h3 class="panel-title">Μεταφράσεις</h3>
                            </div>
                            <div class="panel-body container-fluid">
                                <a href="{{ route('uranus.tvShows.translatable.edit', $tvShow->id) }}" class="btn btn-primary btn-block">
                                    <i class="icon md-translate" aria-hidden="true"></i> Επεξεργασία μεταφράσεων
                                </a>
                                @if($tvShow->hasTranslation('en'))
                                    <div class="alert alert-success mt-3" role="alert">
                                        <i class="icon md-check" aria-hidden="true"></i> Υπάρχουν αγγλικές μεταφράσεις
                                    </div>
                                @else
                                    <div class="alert alert-warning mt-3" role="alert">
                                        <i class="icon md-alert" aria-hidden="true"></i> Δεν υπάρχουν αγγλικές μεταφράσεις
                                    </div>
                                @endif
                            </div>
                        </div>
                        <!-- End Translation Panel -->

                        @include('uranus::_partials._personCreator')

                        <!-- Panel Floating Lables -->
                        <div class="panel">
                            <div class="panel-heading">
                                <h3 class="panel-title">Συντελεστές σειράς</h3>
                            </div>
                            <div class="panel-body container-fluid">
                                @include('uranus::tvShows._tvShowRolesSelects', ['roles' => config('roles')->where('for_tv', true)->sortBy('sort_order')])
                            </div>
                        </div>
                    </div>
                    <!-- End Panel Floating Lables -->
                </div>
            </div>
        </div>
    </div>
    <!-- End Page -->
@stop
@section('footer')
    @parent
    <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.10.2/jquery-ui.min.js"
            charset="UTF-8"></script>
    <script src="{{asset('assets/vendor/bootstrap-datepicker/bootstrap-datepicker.js')}}"></script>
    <script src="{{asset('assets/js/components/bootstrap-datepicker.js')}}"></script>
@stop
