@extends('uranus::layout')
@section('content')
        <!-- Page -->
<div class="page animsition">
    <div class="page-header">
        <h1 class="page-title">{{request('q')? 'Αποτελεσματα Αναζήτησης streaming για \''.request('q').'\'':'Streamings'}}</h1>
        <div class="page-header-actions">
            <a href="{!! route('uranus.streamings.create') !!}">
                <button type="button" class="btn btn-floating btn-danger">
                    <i class="icon wb-plus" aria-hidden="true"></i></button>
            </a>
        </div>
    </div>
    <div class="page-content">
        <!-- Panel Basic -->
        <div class="panel">
            <div class="panel-body">
                {{--Search bar--}}
                <form method="get" class="search_form" action="{{ route('uranus.streamings.index') }}">
                    <input type="text" id="search_query" name="q" value="{{ request('q') }}">
                    <button type="submit">Αναζήτηση σε Τίτλο / Url / Slug</button>
                </form>

                <table class="table table-hover dataTable table-striped width-full" data-plugin=" ">
                    <thead>
                        <tr>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.streamings.index','Δημ/νο','published') !!}</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.streamings.index','ID / Τίτλος / URL','id') !!}</th>
                            <th>Είδος</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.streamings.index','Reqs Purchase','requires_purchase') !!}</th>
                            <th>Notes</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.streamings.index','Δημιουργία','created_at') !!}</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($streamings as $streaming)
                            <tr>
                                <td>@if($streaming->published)
                                        <div class="text-center">
                                            <i class="icon wb-eye green-600" aria-hidden="true" title="Δημοσιευμένη"></i>
                                        </div>
                                    @else
                                        <div class="text-center">
                                            <i class="icon wb-eye-close red-600" aria-hidden="true" title="Αδημοσίευτη"></i>
                                        </div>
                                    @endif
                                </td>
                                <td>
                                    {!! link_to_route('uranus.streamings.edit', $streaming->id, $streaming->id) !!}: {!! link_to_route('uranus.streamings.edit', $streaming->title, $streaming->id) !!}
                                    /<br>
                                    {!! link_to_route('uranus.streamings.edit', $streaming->url, $streaming->id) !!}
                                </td>
                                <td>{{$streaming->getTypeOfVideo()}}</td>
                                <td>@if($streaming->requires_purchase)
                                        <strong>YES</strong>
                                    @else
                                        NO
                                    @endif
                                </td>
                                <td>{{ $streaming->notes }}</td>
                                <td>{{ $streaming->created_at }}</td>
                                @admincan('delete_plays')
                                <td>
                                    <a data-url="{!! route('uranus.streamings.destroy', $streaming->id) !!}" href="" class="deleteResource">
                                        <button type="button" class="btn btn-icon btn-danger btn-outline" title="Διαγραφή">
                                            <i class="fa fa-times" aria-hidden="true"></i></button>
                                    </a>
                                </td>
                                @endadmincan
                            </tr>
                        @empty
                            <p>Δε βρέθηκαν streaming</p>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
        <!-- End Panel Basic -->
        {!! $streamings->render() !!}
    </div>
</div>
<!-- End Page -->
@stop

@section('scripts')
    @parent
    {!! csrf_field() !!}
    <script src="{{ asset('js/admin/jquery.deleteResource.js') }}"></script>
@stop
