@extends('uranus::layout')
@section('head')
    <link type="text/css" rel="stylesheet" href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.10.2/themes/smoothness/jquery-ui.min.css" media="screen"/>
    @stop

@section('content')
            <!-- Page -->
    <div class="page animsition">
        <div class="page-header">
            <h1 class="page-title">Μήνυμα @if($contact->type == 'contact') επικοινωνίας @else συνεργασίας/διαφήμισης @endif από {!! $contact->name !!}</h1>
            <div class="page-header-actions">

            </div>
        </div>
        <div class="page-content">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-sm-6">
                        <!-- Panel Static Lables -->
                        <div class="panel">
                            <div class="panel-heading">
                                <h3 class="panel-title">Πληροφορίες Αποστολέα</h3>
                            </div>
                            <div class="panel-body container-fluid">
                                <div class="form-group row">
                                    <div class="col-sm-12">
                                        <div class="row">
                                            <b class="col-sm-5">Ονοματεπώνυμο</b>
                                            <span>{!! $contact->name !!}</span>
                                        </div>
                                        <div class="row">
                                            <b class="col-sm-5">Email</b>
                                            <span>{!! $contact->email !!}</span>
                                        </div>
                                        <div class="row">
                                            <b class="col-sm-6">user</b>
                                            <?php $user_item = \Packages\Neptune\app\Models\User::where('email', $contact->email)->first(); ?>
                                            <span class="col-sm-6">
                                                @if($user_item != null) <a target="_blank" href="{{ route('uranus.users.edit', $user_item->id) }}">{{ $user_item->id }}</a> @else - @endif
                                            </span>
                                        </div>
                                        <div class="row">
                                            <b class="col-sm-5">Τηλέφωνο</b>
                                            <span>{!! $contact->phone !!}</span>
                                        </div>
                                        <div class="row">
                                            <b class="col-sm-5">Created_at</b>
                                            <span>{!! $contact->created_at !!}</span>
                                        </div>
                                        <div class="row">
                                            <b class="col-sm-5">Updated_at</b>
                                            <span>{!! $contact->updated_at !!}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="panel">
                            <div class="panel-heading">
                                <h3 class="panel-title">Σχόλια unstage</h3>
                            </div>
                            <div class="panel-body container-fluid">
                                <div class="form-group row">
                                    <div class="col-sm-12">
                                        <div class="row">
                                            @include('uranus::messages.successfulSave')
                                            @include('uranus::errors.genericForm')
                                            <form method="post" action="{{ route('uranus.contacts.update',$contact->id) }}">
                                                {!! csrf_field() !!}
                                                {!! method_field('put') !!}
                                                <input type="hidden" name="contact_id" value="{!! $contact->id !!}"/>
                                                <div class="col-sm-12">
                                                    <div class="form-group row">
                                                        <div class="col-sm-12 col-md-6">
                                                            <label class="control-label">Είδος:</label>
                                                            <p>{{ $contact->type }}</p>
                                                        </div>
                                                        <div class="col-sm-12 col-md-6">
                                                            <label for="serviced" class="control-label">Serviced:</label>
                                                            <select name="serviced" id="serviced" class="form-control">
                                                                <option value="0" {{ $contact->serviced ? 'selected': '' }}>Οχι</option>
                                                                <option value="1" {{ $contact->serviced ? 'selected': '' }}>Ναι</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="form-group row">
                                                        <label for="notes" class="control-label">Σημειώσεις</label>
                                                        <textarea id="notes" class="form-control" name="notes" rows="4">{!! $contact->notes !!}</textarea>
                                                    </div>
                                                </div>
                                                <div class="col-sm-12">
                                                    <div class="form-group row">
                                                        <button type="submit" class="btn btn-block btn-success">Αποθήκευση</button>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- End Panel Static Lables -->
                    </div>
                    <div class="col-sm-6">
                        <!-- Panel Static Lables -->
                        <div class="panel">
                            <div class="panel-heading">
                                <h3 class="panel-title">Μήνυμα</h3>
                            </div>
                            <div class="panel-body container-fluid">
                                <div class="form-group row">
                                    <div class="col-sm-12">
                                        <div class="row">
                                            <p class="col-sm-5">{!! $contact->comment !!}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- End Panel Static Lables -->
                    </div>
                    <!-- End Panel Floating Lables -->
                </div>
            </div>
        </div>
    </div>
    <!-- End Page -->
@stop
@section('footer')
    @parent
    <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.10.2/jquery-ui.min.js" charset="UTF-8"></script>
    </script>
@stop
