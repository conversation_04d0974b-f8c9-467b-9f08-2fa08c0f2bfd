@extends('uranus::layout')
@section('head')
    <link type="text/css" rel="stylesheet"
          href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.10.2/themes/smoothness/jquery-ui.min.css"
          media="screen"/>
    @stop

    @section('content')
            <!-- Page -->
    <div class="page animsition">
        <div class="page-header">
            <div class="page-header-actions">
                <a href="{!! route('uranus.varieties.edit', $variety->id) !!}">
                    <button type="button" class="btn btn-floating btn-info">
                        <i class="icon wb-pencil" aria-hidden="true"></i></button>
                </a>
            </div>
        </div>
        <div class="page-content">
            <div class="container-fluid">
                @include('uranus::messages.successfulSave')
                @include('uranus::errors.genericForm')
                <div class="row">
                    <div class="col-sm-12">
                        <!-- Panel Static Lables -->
                        <div class="panel">
                            <div class="panel-heading">
                                <h4 class="panel-title">Σειρά ρόλων του variety {{ $variety->name }}</h4>
                            </div>
                            <div class="panel-body ">
                                <div class="container-fluid">
                                    @forelse($roles as $role)
                                        <div class="form-group row">
                                            <form action="{{ route('uranus.varieties.roles.update', $role->id) }}"
                                                  method="post">
                                                {!! csrf_field() !!}
                                                {!! method_field('put') !!}
                                                <div class="form-group row">
                                                    <div class="col-sm-12 col-md-12">
                                                        <label class="control-label"
                                                               for="role_{{ $role->id }}">{{ $role->description }}: 
                                                            <span>tinyInt (max 255)</span>
                                                        </label>
                                                    </div>
                                                    <div class="col-sm-12 col-md-9 col-lg-8">
                                                        <input type="text" class="form-control" name="sort_order_frontend"
                                                               id="role_{{ $role->id }}"
                                                               value="{{ $role->sort_order_frontend }}">
                                                        <input type="hidden" name="variety_id" value="{{ $variety->id }}"/>
                                                    </div>
                                                    <div class="col-sm-12 col-md-3 col-lg-2">
                                                        <button type="submit" class="btn btn-block btn-success">
                                                            Αποθήκευση
                                                        </button>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                    @empty
                                        Δε βρέθηκαν ρόλοι
                                    @endforelse
                                </div>
                            </div>
                        </div>
                        <!-- End Panel Static Lables -->
                    </div>
                    <!-- End Panel Floating Lables -->
                </div>
            </div>
        </div>
    </div>
    <!-- End Page -->
@stop
@section('footer')
    @parent
    <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.10.2/jquery-ui.min.js"
            charset="UTF-8"></script>
@stop
