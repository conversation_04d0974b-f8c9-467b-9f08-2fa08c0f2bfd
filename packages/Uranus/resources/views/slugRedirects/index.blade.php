@extends('uranus::layout')
@section('content')
    <!-- Page -->
    <div class="page animsition">
        <div class="page-header">
            <h1 class="page-title">Slug Redirects</h1>
            <div class="page-header-actions">
                @admincan('manage_pro')
                <a href="{!! route('uranus.slugRedirects.create') !!}">
                    <button type="button" class="btn btn-floating btn-danger">
                        <i class="icon wb-plus" aria-hidden="true"></i></button>
                </a>
                @endadmincan
            </div>
        </div>
        <div class="page-content">
            <!-- Panel Basic -->
            <div class="panel">
                <div class="panel-body">
                    {{--Search bar--}}
                    <form method="get" class="search_form" action="{{ route('uranus.slugRedirects.index') }}">
                        <input type="text" id="search_query" name="q" value="{{ request('q') }}">
                        <button type="submit">Αναζήτηση (old slug / new slug)</button>
                    </form>
                    <table class="table table-hover dataTable table-striped width-full" data-plugin=" ">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>{!! \App\Components\LinkTo::sortableRoute('uranus.slugRedirects.index','Old Slug','old_slug') !!}</th>
                                <th>{!! \App\Components\LinkTo::sortableRoute('uranus.slugRedirects.index','New Slug','new_slug') !!}</th>
                                <th>{!! \App\Components\LinkTo::sortableRoute('uranus.slugRedirects.index','Model Type','model_type') !!}</th>
                                <th>{!! \App\Components\LinkTo::sortableRoute('uranus.slugRedirects.index','Created at','created_at') !!}</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($slugRedirects as $slugRedirect)
                                <tr>
                                    <td>{{ $slugRedirect->id }}</td>
                                    <td><a target="_blank" href="{{ route('uranus.slugRedirects.edit', $slugRedirect->id) }}">{{ $slugRedirect->old_slug }}</a></td>
                                    <td><a target="_blank" href="{{ route('uranus.slugRedirects.edit', $slugRedirect->id) }}">{{ $slugRedirect->new_slug }}</a></td>
                                    <td>{{ $slugRedirect->model_type }}</td>
                                    <td>{{ $slugRedirect->created_at }}</td>
                                    @admincan('manage_pro')
                                    <td>
                                        <a href="{!! route('uranus.slugRedirects.edit', $slugRedirect->id) !!}">
                                            <button type="button" class="btn btn-icon btn-default btn-outline">
                                                <i class="icon wb-pencil" aria-hidden="true"></i></button>
                                        </a>
                                    </td>
                                    @endadmincan
                                    @admincan('delete_pro')
                                        <td>
                                            <a data-url="{!! route('uranus.slugRedirects.destroy', $slugRedirect->id) !!}" href="" class="deleteResource">
                                                <button type="button" class="btn btn-icon btn-danger btn-outline">
                                                    <i class="fa fa-times" aria-hidden="true"></i></button>
                                            </a>
                                        </td>
                                    @endadmincan
                                </tr>
                            @empty
                                <p>Δε βρέθηκαν slugRedirects</p>
                            @endforelse
                        </tbody>
                    </table>
                    {!! $slugRedirects->render() !!}
                </div>
            </div>
            <!-- End Panel Basic -->
        </div>
    </div>
    <!-- End Page -->
@stop
@section('scripts')
    @parent
    {!! csrf_field() !!}
    <script src="{{ asset('js/admin/jquery.deleteResource.js') }}"></script>
@stop
