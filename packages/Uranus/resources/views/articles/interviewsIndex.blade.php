@extends('uranus::layout')
@section('content')
    <!-- Page -->
    <div class="page animsition">
        <div class="page-header">
            <h1 class="page-title">Λίστα {{ $type }}</h1>
            <div class="page-header-actions">
                @admincan('manage_contributor_articles')
                @if($type == 'interviews')
                    <a href="{!! route('uranus.interviews.create') !!}">
                        <button type="button" class="btn btn-floating btn-danger">
                            <i class="icon wb-plus" aria-hidden="true"></i></button>
                    </a>
                @elseif($type == 'critiques')
                    <a href="{!! route('uranus.critiques.create') !!}">
                        <button type="button" class="btn btn-floating btn-danger">
                            <i class="icon wb-plus" aria-hidden="true"></i></button>
                    </a>
                @elseif($type == 'userposts')
                    <a href="{!! route('uranus.userposts.create') !!}">
                        <button type="button" class="btn btn-floating btn-danger">
                            <i class="icon wb-plus" aria-hidden="true"></i></button>
                    </a>
                @endif
                @endadmincan
            </div>
        </div>
        <div class="page-content">
            <!-- Panel Basic -->
            <div class="panel">
                <div class="panel-body">
                    {{--Search bar--}}
                    <?php $route_name = 'uranus.' . $type . '.index'; ?>
                    <form method="get" class="search_form" action="{{ route($route_name) }}">
                        <input type="text" id="search_query" name="q" value="{{ request('q') }}">
                        <button type="submit">Αναζήτηση</button>
                    </form>
                    <table class="table table-hover dataTable table-striped width-full" data-plugin=" ">
                        <thead>
                            <tr>
                                <th>{!! \App\Components\LinkTo::sortableRoute($route_name,'Δημ.','published') !!}</th>
                                <th>Τίτλος</th>
                                <th>Είδος</th>
                                <th>{!! \App\Components\LinkTo::sortableRoute($route_name,'Συντάκτης','author_id') !!}</th>
                                <th>{!! \App\Components\LinkTo::sortableRoute($route_name,'Ημ/νια πραγμ/σης','recorded_at') !!}</th>
                                <th>{!! \App\Components\LinkTo::sortableRoute($route_name,'Ημ/νια δημιουργίας','created_at') !!}</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($articles as $article)
                                <tr>
                                    <td>@if($article->published)
                                            <div class="text-center">
                                                <i class="icon wb-eye green-600" aria-hidden="true" title="Δημοσιευμένη"></i>
                                            </div>
                                        @else
                                            <div class="text-center">
                                                <i class="icon wb-eye-close red-600" aria-hidden="true" title="Μη δημοσιευμένη"></i>
                                            </div>
                                        @endif
                                    </td>
                                    <td>{!! link_to_route('uranus.articles.edit', $article->title, $article->id) !!}</td>
                                    <td>{!! $article->type !!}</td>
                                    <td>
                                        @if($article->author) {!! $article->author->fullName !!} @endif
                                        <br>
                                            @if(!$article->userAuthors->isEmpty()) <i>user</i> @else <i>no user</i> @endif
                                    </td>
                                    <td>{!! $article->recorded_at !!}</td>
                                    <td>{!! $article->created_at !!}</td>
                                    @admincan('manage_contributor_articles')
                                    <td>
                                        <a href="{!! route('uranus.articles.edit', $article->id) !!}">
                                            <button type="button" class="btn btn-icon btn-default btn-outline">
                                                <i class="icon wb-pencil" aria-hidden="true"></i></button>
                                        </a>
                                    </td>
                                    @endadmincan
                                    @admincan('delete_contributor_articles')
                                        <td>
                                            <a data-url="{!! route('uranus.articles.destroy', $article->id) !!}" href="" class="deleteResource">
                                                <button type="button" class="btn btn-icon btn-danger btn-outline">
                                                    <i class="fa fa-times" aria-hidden="true"></i></button>
                                            </a>
                                        </td>
                                    @endadmincan
                                </tr>
                            @empty
                                <p>Δε βρέθηκαν {{ $type }}</p>
                            @endforelse
                        </tbody>
                    </table>
                    {!! $articles->render() !!}
                </div>
            </div>
            <!-- End Panel Basic -->
        </div>
    </div>
    <!-- End Page -->
@stop
@section('scripts')
    @parent
    {!! csrf_field() !!}
    <script src="{{ asset('js/admin/jquery.deleteResource.js') }}"></script>
@stop
