@extends('uranus::layout')
@section('content')
    <!-- Page -->
    <div class="page animsition">
        <div class="page-header">
            <h1 class="page-title">Θεατρικά είδη</h1>
            <div class="page-header-actions">
                @admincan('manage_theatres')
                <a href="{!! route('uranus.genres.create') !!}">
                    <button type="button" class="btn btn-floating btn-danger">
                        <i class="icon wb-plus" aria-hidden="true"></i></button>
                </a>
                @endadmincan
            </div>
        </div>
        <div class="page-content">
            <!-- Panel Basic -->
            <div class="panel">
                <div class="panel-body">
                    <table class="table table-hover dataTable table-striped width-full" data-plugin=" ">
                        <thead>
                            <tr>
                                <th>{!! \App\Components\LinkTo::sortableRoute('uranus.genres.index','Όνομα','name') !!}</th>
                                <th># Supergenres</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($genres as $genre)
                                <tr>
                                    <td>{!! link_to_route('uranus.genres.edit', $genre->name, $genre->id) !!}</td>
                                    <td>{{ count($genre->supergenres) }}</td>
                                    @admincan('manage_theatres')
                                    <td>
                                        <a href="{!! route('uranus.genres.edit', $genre->id) !!}">
                                            <button type="button" class="btn btn-icon btn-default btn-outline">
                                                <i class="icon wb-pencil" aria-hidden="true"></i></button>
                                        </a>
                                    </td>
                                    @endadmincan
                                    @admincan('delete_theatres')
                                        <td>
                                            <a data-url="{!! route('uranus.genres.destroy', $genre->id) !!}" href="" class="deleteResource">
                                                <button type="button" class="btn btn-icon btn-danger btn-outline">
                                                    <i class="fa fa-times" aria-hidden="true"></i></button>
                                            </a>
                                        </td>
                                    @endadmincan
                                </tr>
                            @empty
                                <p>Δε βρέθηκαν θεατρικά είδη</p>
                            @endforelse
                        </tbody>
                    </table>
                    {!! $genres->render() !!}
                </div>
            </div>
            <!-- End Panel Basic -->
        </div>
    </div>
    <!-- End Page -->
@stop
@section('scripts')
    @parent
    {!! csrf_field() !!}
    <script src="{{ asset('js/admin/jquery.deleteResource.js') }}"></script>
@stop
