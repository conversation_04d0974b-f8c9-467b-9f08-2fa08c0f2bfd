@extends('uranus::layout')
@section('content')
        <!-- Page -->
<div class="page animsition">
    <div class="page-header">
        <h1 class="page-title">Δημιουργία νέου χρήστη</h1>
    </div>
    <div class="page-content">
        <div class="container-fluid">
            @include('uranus::messages.successfulSave')
            @include('uranus::errors.genericForm')
            <form action="{{ route('uranus.admins.store') }}" method="post">
                {!! csrf_field() !!}
                <div class="row">
                    <div class="col-sm-6">
                        <!-- Panel Static Lables -->
                        <div class="panel">
                            <div class="panel-body container-fluid">
                                <div class="form-group row">
                                    <div class="col-sm-12 col-md-6">
                                        <label for="username" class="control-label">Username*:</label>
                                        <input type="text" name="username" id="username" class="form-control" value="{{ old('username') }}">
                                    </div>
                                    <div class="col-sm-12 col-md-6">
                                        <label for="email" class="control-label">Email*:</label>
                                        <input type="text" name="email" id="email" class="form-control" value="{{ old('email') }}">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-12 col-md-6">
                                        <label for="password" class="control-label">Κωδικος*:</label>
                                        <input type="password" name="password" id="password" class="form-control" value="{{ old('password') }}">
                                    </div>
                                    <div class="col-sm-12 col-md-6">
                                        <label for="password_confirmation" class="control-label">Επαλήθευση Κωδικού*:</label>
                                        <input type="password" name="password_confirmation" id="password_confirmation" class="form-control">
                                    </div>
                                </div>
                                @admincan('manage_admins')
                                <div class="form-group row">
                                    <div class="col-sm-12 col-md-12">
                                        <label for="access_level_id" class="control-label">Ρόλος*:</label>
                                        <select name="access_level_id" id="access_level_id" class="form-control">
                                            @foreach($accessLevels as $accessLevel)
                                                <option value="{{ $accessLevel->id }}" {{old('accessLevel')?'selected':''}}>{{ $accessLevel->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                @endadmincan
                                <div class="form-group row">
                                    <div class="col-sm-12 col-md-6">
                                        <button type="submit" class="btn btn-block btn-success">Αποθήκευση</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- End Panel Static Labels -->
                    </div>
                </div>
            </form>

        </div>
    </div>
</div>
<!-- End Page -->
@stop