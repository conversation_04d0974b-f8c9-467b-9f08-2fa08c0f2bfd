<div class="panel-body">
    <ul class="list-group list-group-dividered list-group-full">
        @foreach($revisions as $revision)
            <li class="list-group-item">
                <div class="media">
                    {{--<div class="media-left">--}}
                    {{--<a class="avatar avatar-away" href="javascript:void(0)">--}}
                    {{--<img src="../../global/portraits/7.jpg" alt=""><i></i></a>--}}
                    {{--</div>--}}
                    <div class="media-body">
                        <h4 class="media-heading">
                            <small class="pull-right">{{ $revision->updated_at->diffForHumans() }}</small>
                            @if($revision->revisionAdmin)
                                @if($revision->key == 'created_at' && !$revision->old_value)
                                    Δημιουργήθηκε από
                                    <a href="{{ route('uranus.admins.edit',$revision->revisionAdmin->id) }}" class="grey-800">{{ $revision->revisionAdmin->name?:$revision->revisionAdmin->email }}</a>
                                @else
                                    <a href="{{ route('uranus.admins.edit',$revision->revisionAdmin->id) }}" class="grey-800">{{ $revision->revisionAdmin->name?:$revision->revisionAdmin->email }}</a>
                                @endif
                            @endif
                            άλλαξε το {{ $revision->key }}
                        </h4>
                        <small>{{ $revision->created_at->format('d/m/y H:i:s') }}</small>
                        @if($revision->key != 'created_at')
                            <div class="content well">
                                <p>
                                    Πριν:<br>
                                    <em> {{ $revision->old_value }}</em>
                                </p>
                                <p>
                                    Μετά:<br>
                                    <em> {{ $revision->new_value }}</em>
                                </p>
                            </div>
                        @endif
                    </div>
                </div>
            </li>
        @endforeach
    </ul>
    {!! $revisions->render() !!}
</div>