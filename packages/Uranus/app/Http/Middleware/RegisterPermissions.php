<?php

namespace Packages\Uranus\app\Http\Middleware;

use App\Models\Permission;
use Closure;
use Gate;
use Packages\Uranus\app\Models\Admin;

class RegisterPermissions
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // Only logged in admins have permissions
        if ( ! auth('admin')->check()) {
            return $next($request);
        }

        // Dynamically register permissions with Laravel's Gate.
        foreach ($this->getPermissions() as $permission) {
            Gate::define($permission->name, function (Admin $admin) use ($permission) {
                return $admin->hasPermission($permission);
            });
        }

        return $next($request);
    }

    /**
     * Get all permissions with access levels attached to them.
     *
     * @return \Illuminate\Database\Eloquent\Collection|\Illuminate\Support\Collection|static[]
     */
    private function getPermissions()
    {
        try {
            // todo store permissions in cache to avoid hitting database every time
            return Permission::with('accessLevels')->get();
        } catch (\Exception $e) {
            report($e);
            // Return an empty collection.
            // Necessary when running migration for the first time.
            return collect();
        }
    }
}
