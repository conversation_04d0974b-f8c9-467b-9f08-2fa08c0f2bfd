<?php namespace Packages\Uranus\app\Http\Requests;


class EnduserSlugifyRequest extends Request {

	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
            'username'             => 'required|alpha|unique:users',
		];
	}

}
