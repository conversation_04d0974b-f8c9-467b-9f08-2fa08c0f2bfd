<?php namespace Packages\Uranus\app\Http\Requests;

use Illuminate\Support\Facades\Config;

class PlayUpdateRequest extends Request
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'title'             => 'required',
            'year'              => 'required|numeric',
            'start_date'        => 'date_format:' . Config::get('app.carbon_format'),
            'end_date'          => 'date_format:' . Config::get('app.carbon_format'),
            'earnings'          => 'numeric',
            'production_cost'   => 'numeric',
            'duration'          => 'nullable|integer',
            'online_tickets_1'  => 'nullable|url',
            'online_tickets_2'  => 'nullable|url',
            'online_tickets_3'  => 'nullable|url',
        ];
    }

}
