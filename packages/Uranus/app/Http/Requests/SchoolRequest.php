<?php namespace Packages\Uranus\app\Http\Requests;


class SchoolRequest extends Request {

	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
            'name'          => 'required',
            'email'         => 'nullable|email',
            'second_email'  => 'nullable|email',
            'website'       => 'nullable|url',
            'facebook'      => 'nullable|url',
            'twitter'       => 'nullable|url',
		];
	}

}
