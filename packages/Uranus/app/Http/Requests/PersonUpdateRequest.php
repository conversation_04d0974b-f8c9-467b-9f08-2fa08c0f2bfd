<?php namespace Packages\Uranus\app\Http\Requests;

use Illuminate\Support\Facades\Config;

class PersonUpdateRequest extends Request
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            //			'first_name' => 'required',
            'last_name'     => 'required',
            'birthday'      => 'nullable|date_format:' . Config::get('app.carbon_format'),
            'death_day'     => 'nullable|date_format:' . Config::get('app.carbon_format'),
            'birth_year'    => 'nullable|integer',
            'height'        => 'nullable|integer',
            'weight'        => 'nullable|integer',
            'facebook'      => 'nullable|url',
            'facebook_page' => 'nullable|url',
            'instagram'     => 'nullable|url',
            'twitter'       => 'nullable|url',
            'linkedin'      => 'nullable|url',
            'imdb'          => 'nullable|url',
            'soundcloud'    => 'nullable|url',
            'somanybooks'   => 'nullable|url',
            'youtube'       => 'nullable|url',
        ];
    }

}
