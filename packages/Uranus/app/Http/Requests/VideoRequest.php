<?php namespace Packages\Uranus\app\Http\Requests;

use Illuminate\Support\Facades\Config;

class VideoRequest extends Request
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {

        return [
            'url'             => 'required|url|unique:videos',
        ];
    }

}
