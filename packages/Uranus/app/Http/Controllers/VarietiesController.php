<?php namespace Packages\Uranus\app\Http\Controllers;

use App\Models\Role;
use App\Models\Variety;
use Artesaos\SEOTools\Facades\SEOMeta;
use Illuminate\Http\Request;
use Packages\Uranus\app\Http\Requests\VarietyRequest;

class VarietiesController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     */
    public function index(Request $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'see_plays');

        $sortBy = $request->input('sortBy', 'name');
        $direction = $request->input('direction', 'asc');
        $page = $request->input('page', 1);
        $limit = config('uranus.limits.plays.index');
        $offset = ($page * $limit) - $limit;

        $varieties = Variety::orderBy($sortBy, $direction)
            ->offset($offset)
            ->limit($limit)
            ->paginate();

        $varieties->setPath($request->url())
            ->appends('sortBy', $sortBy)
            ->appends('direction', $direction);

        // seo title
        SEOMeta::setTitle('Index varieties');

        return view('uranus::varieties.index', compact('varieties'));
    }

    /**
     * Show the form for creating a new resource.
     *
     */
    public function create()
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_plays');

        // List of endeavours for autocomplete suggestions
        $varieties = Variety::select(['name'])->get();
        $varieties = $varieties->pluck('name')->all();

        // seo title
        SEOMeta::setTitle('New variety');

        return view('uranus::varieties.create', compact('varieties'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param VarietiesRequest $request
     */
    public function store(VarietyRequest $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_plays');

        $input = array_map('trim', $request->all());

        $variety = Variety::create($input);

        return redirect()->route('uranus.varieties.edit', [$variety->id])
                         ->with('success', 'Variety ' . $variety->id . ' added');
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param $id
     */
    public function edit($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_plays');

        $variety = Variety::where('id', $id)->firstOrFail();

        // seo title
        SEOMeta::setTitle('Edit variety ' . $variety->name);

        return view('uranus::varieties.edit', compact('variety'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param VarietyRequest $request
     * @param  int $id
     */
    public function update(VarietyRequest $request, $id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_plays');

        $input = array_map('trim', $request->all());

        $variety = Variety::findOrFail($id);

        $variety->update($input);

        return redirect()->route('uranus.varieties.edit', [$variety->id])
                         ->with('success', 'Variety ' . $variety->id . ' updated');
    }


    /**
     * Show the form for editing the specified resource.
     *
     * @param  int $id
     */
    public function editRoles($variety_id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_plays');

        $variety    = Variety::where('id', $variety_id)->firstOrFail();
        $roles      = $variety->roles()->orderBy('sort_order_frontend', 'asc')->get();

        return view('uranus::varieties.roles.edit', compact('variety', 'roles'));
    }


    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param  int $id
     */
    public function updateRoles(Request $request, $id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_plays');

        $role = Role::findOrFail($id);

        $role->sort_order_frontend = $request->input('sort_order_frontend');
        $role->save();

        return redirect()->route('uranus.varieties.roles.edit', $request->input('variety_id'))
            ->with('success', 'Η σειρά του ρόλου ' . $role->id . ' ενημερώθηκε');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int $id
     */
    public function destroy($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'delete_theatres');

        $deleted = Variety::destroy($id);

        $status = $deleted ? 1 : 0;
        $message = $deleted ? 'Το variety με id ' . $id . ' διαγράφηκε επιτυχώς' : 'Σφάλμα κατά τη διαγραφή του variety';

        return response()->json([
            'status'  => $status,
            'message' => $message,
        ]);

    }

}
