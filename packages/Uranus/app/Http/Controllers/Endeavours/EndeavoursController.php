<?php namespace Packages\Uranus\app\Http\Controllers\Endeavours;

use App\Models\Endeavour;
use App\Models\Theatre;
use App\Models\TvChannel;
use App\Models\Variety;
use App\Models\Tag;
use App\Services\Search\Text\TextSearchService;
use Artesaos\SEOTools\Facades\SEOMeta;
use Illuminate\Http\Request;
use Packages\Uranus\app\Http\Controllers\Controller;
use Packages\Uranus\app\Http\Requests\EndeavourRequest;
use Packages\Uranus\app\Http\Requests\EndeavourUpdateRequest;

class EndeavoursController extends Controller
{

    /**
     * @var TextSearchService
     */
    private $textSearch;

    public function __construct(TextSearchService $textSearch)
    {
        parent::__construct();

        $this->textSearch = $textSearch;
        $this->textSearch->setPreferredSearchLayer('eloquent');
    }

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     */
    public function index(Request $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'see_plays');

        // view variables
        $varieties = Variety::all();

        $sortBy = $request->input('sortBy', 'created_at');
        $direction = $request->input('direction', 'desc');
        $page = $request->input('page', 1);
        $limit = config('uranus.limits.plays.index');
        $offset = ($page * $limit) - $limit;

        $endeavours = Endeavour::orderBy($sortBy, $direction);

        $query_q = $request->query('q');
        if ( !empty($query_q) )
        {
            $endeavours->where('old_title', 'LIKE', '%' . $query_q . '%');
        }

        $query_variety = $request->query('variety');
        if( !empty($query_variety) )
        {
            $endeavours->where('variety_id', '=', $query_variety);
        }

        $endeavours = $endeavours->offset($offset)
            ->limit($limit)
            ->paginate();

        $endeavours->setPath($request->url());

        // append q query string if present
        if ( !empty($query_q) )
        {
            $endeavours->appends('q', $query_q);
        }

        // append variety query string if present
        if ( !empty($query_variety) )
        {
            $endeavours->appends('variety', $query_variety);
        }

        $endeavours->appends('sortBy', $sortBy)
            ->appends('direction', $direction);

        // seo title
        SEOMeta::setTitle('Index endeavours');

        return view('uranus::endeavours.index', [
            'endeavours'    => $endeavours,
            'varieties'     => $varieties,
        ]);
    }


    /**
     * Show the list of user saved plays
     *
     * @param Request $request
     * @return mixed
     * @throws AuthorizationException
     */
    public function indexUserSaved(Request $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'see_plays');

        $sortBy = $request->input('sortBy', 'updated_at');
        $direction = $request->input('direction', 'desc');
        $page = $request->input('page', 1);
        $limit = config('uranus.limits.plays.index');
        $offset = ($page * $limit) - $limit;

        // Init
        $endeavours = Endeavour::whereNotNull('user_id')
            ->orderBy($sortBy, $direction);

        // handle free text query string
        $query_q = $request->query('q');
        if ( !empty($query_q) )
        {
            // re initialize endeavours var in order to achieve the joins
            $endeavours = Endeavour::leftJoin('endeavour_translations', 'endeavours.id', '=', 'endeavour_translations.endeavour_id')
                ->select([
                    'endeavours.id',
//                    'endeavours.theatre_id',
                    'endeavours.user_id',
                    'endeavours.notes',
                    'endeavours.user_notes',
                    'endeavours.year',
                    'endeavours.published',
                    'endeavours.moderated',
                    'endeavours.updated_at',
                ])
                ->orderBy($sortBy, $direction)
                ->groupBy('endeavours.id');

            $endeavours->whereNotNull('endeavours.user_id')
                ->where(function ($query) use ($query_q) {
                    $query->orWhere('endeavours_translations.title', 'LIKE', '%' . $query_q . '%')
                        ->orWhere('endeavours.notes', 'LIKE', '%' . $query_q . '%')
                        ->orWhere('endeavours.user_notes', 'LIKE', '%' . $query_q . '%');
                });
        }

        // handle moderated query string
        $query_moderated = $request->query('moderated');
        if( !empty($query_moderated) )
        {
            if ($query_moderated == 'moderated')
            {
                $endeavours->where('moderated', true);
            }
            elseif ($query_moderated == 'unmoderated')
            {
                $endeavours->where('moderated', false);
            }
        }

        // handle published query string
        $query_published = $request->query('published');
        if( !empty($query_published) )
        {
            if ($query_published == 'published')
            {
                $endeavours->where('published', true);
            }
            elseif ($query_published == 'unpublished')
            {
                $endeavours->where('published', false);
            }
        }

        // handle user_id query string
        $query_user_id = $request->query('user_id');
        if( !empty($query_user_id) )
        {
            $endeavours->where('user_id', $query_user_id);
        }

        $endeavours = $endeavours
            ->offset($offset)
            ->limit($limit)
            ->paginate();

        $endeavours->setPath($request->url());

        // append q query string if present
        if ( !empty($query_q) )
        {
            $endeavours->appends('q', $query_q);
        }

        // append moderated query string if present
        if ( !empty($query_moderated) && ($query_moderated == 'moderated' || $query_moderated == 'unmoderated'))
        {
            $endeavours->appends('moderated', $query_moderated);
        }

        // append published query string if present
        if ( !empty($query_published) && ($query_published == 'published' || $query_published == 'unpublished'))
        {
            $endeavours->appends('published', $query_published);
        }

        // append user_id query string if present
        if ( !empty($query_user_id) )
        {
            $endeavours->appends('user_id', $query_user_id);
        }

        $endeavours->appends('sortBy', $sortBy)
            ->appends('direction', $direction);

        // load relations and extra data for all returned plays
        $endeavours->each(function($endeavour) {
            $endeavour->load([
//                'theatre',
                'user',
            ]);
        });

        // seo title
        SEOMeta::setTitle('Index user saved endeavours');

        return view('uranus::endeavours.index_user_saved', compact('endeavours'));
    }

    /**
     * Show the form for creating a new resource.
     *
     */
    public function create()
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_plays');

        // List of endeavours for autocomplete suggestions
        $endeavours = Endeavour::select(['old_title'])->get();
        $endeavours = $endeavours->pluck('old_title')->all();

        $varieties = Variety::orderBy('name', 'asc')->get();

        // seo title
        SEOMeta::setTitle('New endeavour');

        return view('uranus::endeavours.create', compact('endeavours', 'varieties'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param EndeavourRequest $request
     */
    public function store(EndeavourRequest $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_plays');

        $endeavour = new Endeavour;

        $data = [
            'old_title'     => $request['title'], // initially created nontranslatable in the endeavours table
            'year'          => $request['year'],
            'variety_id'    => $request['variety_id'],
            'el'            => [
                'title'         => $request['title'],
            ],
        ];

        $endeavour = $endeavour->create($data);

        return redirect()->route('uranus.endeavours.edit', [$endeavour->id])
                         ->with('success', 'Endeavour ' . $endeavour->id . ' added');
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param $id
     */
    public function edit($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_plays');

        $endeavour = Endeavour::where('id', $id)
            ->with([/*'filmGenres',*/ 'roles', /*'user'*/])
            ->firstOrFail();

        $data['varieties']      = Variety::orderBy('name', 'asc')->get();
        $data['tags']           = Tag::all();
        $data['theatres']       = Theatre::all();
        $data['tv_channels']    = TvChannel::orderBy('name', 'asc')->get();

        $endeavour->load([
            'roles.endeavourPeople' => function ($query) use ($endeavour) {
                $query->wherePivot('endeavour_id', '=', $endeavour->id);
            },
        ]);

        $data['endeavour'] = $endeavour;
//        $data['filmGenres'] = FilmGenre::all();

        $rolesWithoutActors = [];
        // Workaround to get list of roles for which this movie
        // doesn't have any person
        $rolesIdsWithActors = [];
        foreach ($data['endeavour']->roles as $endeavourRole)
        {
            $rolesIdsWithActors[] = $endeavourRole->id;
        }
        foreach ($endeavour->variety->roles as $role)
        {
            if ( ! in_array($role->id, $rolesIdsWithActors)) {
                $rolesWithoutActors[] = $role;
            }
        }

        $data['rolesWithoutActors'] = $rolesWithoutActors;

        // Images
        $data['resourceType']       = 'endeavour';
        $data['resourceTypeId']     = $id;

        // seo title
        SEOMeta::setTitle('Edit endeavour ' . $endeavour->title);

        return view('uranus::endeavours.edit', $data);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param EndeavourUpdateRequest $request
     * @param  int $id
     */
    public function update(EndeavourUpdateRequest $request, $id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_plays');

        $endeavour = Endeavour::findOrFail($id);

        $data = [
            'published'         => isset($request['published']) ? 1 : 0,
            'moderated'         => $request['moderated'],
            'featured'          => $request['featured'],
            'handpicked'        => $request['handpicked'],
            'notes'             => $request['notes'],
            'year'              => $request['year'],
            'start_date'        => $request['start_date'],
            'end_date'          => $request['end_date'],
            'variety_id'        => $request['variety_id'],
            'online_tickets_1'  => $request['online_tickets_1'],
            'online_tickets_2'  => $request['online_tickets_2'],
            'online_tickets_3'  => $request['online_tickets_3'],
            'ticket_price'      => $request['ticket_price'],
            'duration'          => $request['duration'],
            'tv_channel_id'     => $request['tv_channel_id'],
            'el'            => [
                'title'                 => $request['title'],
                'description'           => $request['description'],
                'synopsis'              => $request['synopsis'],
                'extra_info'            => $request['extra_info'],
                'extra_ongoing_info'    => $request['extra_ongoing_info'],
            ],
        ];

        $endeavour->update($data);

        // venues/theatres
        $endeavour->theatres()->sync(($request->input('theatre_list') ? : []));

        // tags
        $endeavour->tags()->sync(($request->input('tag_list') ? : []));

        return redirect()->route('uranus.endeavours.edit', [$endeavour->id])
                         ->with('success', 'Endeavour ' . $endeavour->id . ' updated');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int $id
     */
    public function destroy($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'delete_plays');

        $deleted = Endeavour::destroy($id);

        $status = $deleted ? 1 : 0;
        $message = $deleted ? 'Το endeavour με id ' . $id . ' διαγράφηκε επιτυχώς' : 'Σφάλμα κατά τη διαγραφή του endeavour';

        return response()->json([
            'status'  => $status,
            'message' => $message,
        ]);

    }


    public function popoverInfo(Request $request)
    {
        $endeavour = Endeavour::where('id', $request->get('id'))->with([/*'genres',*/ 'images', 'people'])->first();

        if ($endeavour)
        {
            return response()->json([
                'status'  => 1,
                'content' => view('uranus::endeavours._endeavourPopoverInfo', compact('endeavour'))->render(),
            ]);
        }

    }


    /*
     * Used to return endeavours for use in select2 dropdowns input
     */
    public function getEndeavourList(Request $request)
    {
        if ($request->has('searchEndeavours'))
        {
            $paginator = $this->textSearch->setQuery($request->input('query'))->endeavours();

            $endeavours = [];
            if ($paginator->endeavours)
            {
                $endeavourDTOS = $paginator->endeavours->all();
                foreach ($endeavourDTOS as $dto)
                {
                    $endeavours[$dto->title . ' (' . $dto->year .') (id: ' . $dto->id . ')'] = $dto->id;
                }
            }

            return $endeavours;
        }

    }

}
