<?php namespace Packages\Uranus\app\Http\Controllers\Endeavours;

use App\Models\Endeavour;
use App\Models\EndeavourPersonRole;
use App\Models\Role;
use Illuminate\Http\Request;
use Packages\Uranus\app\Http\Controllers\Controller;

class CharactersController extends Controller
{
    /**
     * Show the form for editing the specified resource.
     *
     * @param  int $id
     */
    public function edit($endeavour_id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_plays');

        $data['endeavour_id']       = $endeavour_id;
        // initialize var
        $data['characters_array']   = array();

        //get endeavour
        $endeavour = Endeavour::find($endeavour_id)->firstOrFail();

        // get all the roles of the variety that are characterable
        $characterable_roles    = $endeavour->variety->roles()->characterable()->get();

//        dd($characterable_roles);

        if(!$characterable_roles->isEmpty())
        {
            foreach($characterable_roles as $role)
                $data['characters_array'][$role->title] =
                    EndeavourPersonRole
                        ::select('endeavour_person_role.*')
                        ->where('endeavour_id', $endeavour_id)
                        ->join('people', 'endeavour_person_role.person_id', '=', 'people.id')
                        ->where('endeavour_person_role.role_id', $role->id)
                        ->with('person')
                        ->get();
        }

//        dd($data['characters_array']);

        return view('uranus::endeavours.characters.edit', $data);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param  int $id
     */
    public function update(Request $request, $id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_plays');

        $row = EndeavourPersonRole::findOrFail($id);

        $row->character = $request->input('character');
        $row->save();

        return redirect()->route('uranus.endeavours.characters.edit', $request->input('endeavour_id'))
                         ->with('success', 'Ο χαρακτήρας ' . $row->id . ' ενημερώθηκε');
    }
}
