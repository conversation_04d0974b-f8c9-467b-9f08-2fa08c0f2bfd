<?php namespace Packages\Uranus\app\Http\Controllers;

use App\Models\Person;
use Artesaos\SEOTools\Facades\SEOMeta;
use Illuminate\Http\Request;
use Packages\Neptune\app\Models\Subscription;

class SubscriptionsController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return Response
     */
    public function index()
    {
        $this->authorizeForUser(auth('admin')->user(), 'see_pro');

        $request = request();
        $sortBy = $request->input('sortBy', 'name');
        $direction = $request->input('direction', 'asc');
        $page = $request->input('page', 1);
        $limit = config('uranus.limits.genres.index');
        $offset = ($page * $limit) - $limit;

        $subscriptions = Subscription::orderBy($sortBy, $direction)
           ->offset($offset)
           ->limit($limit)
           ->paginate();

        $subscriptions->setPath($request->url())
               ->appends('sortBy', $sortBy)
               ->appends('direction', $direction);

        // seo title
        SEOMeta::setTitle('Index subscriptions');

        return view('uranus::subscriptions.index', compact('subscriptions'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return Response
     */
    public function create()
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_pro');

        // seo title
        SEOMeta::setTitle('New subscription');

        return view('uranus::subscriptions.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @return Response
     */
    public function store(Request $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_pro');

        $input = array_map('trim', $request->all());

        $subscription = Subscription::create($input);

        return redirect()->route('uranus.subscriptions.edit', [$subscription->id])
                         ->with('success', 'Subscription ' . $subscription->id . ' added');
    }


    /**
     * Show the form for editing the specified resource.
     *
     * @param $id
     * @return Response
     */
    public function edit($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_pro');

        $subscription = Subscription::findOrFail($id);

        $subscriptionPeople = [];

        // check whether the article is connected with people
        if ( ! empty($subscription->people()) )
        {
            foreach($subscription->people()->get() as $person)
            {
                $subscriptionPeople[$person->fullName] = $person->id;
            }
        }

        // seo title
        SEOMeta::setTitle('Edit subscription' . $subscription->name);

        return view('uranus::subscriptions.edit', compact('subscription', 'subscriptionPeople'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param $id
     * @return Response
     * @internal param $id
     * @internal param int $id
     */
    public function update(Request $request, $id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_pro');

        $input = array_map('trim', $request->all());

        $subscription = Subscription::findOrFail($id);

        $subscription->update($input);

        return redirect()->route('uranus.subscriptions.edit', [$subscription->id])
                         ->with('success', 'Subscription ' . $id . ' updated');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param $id
     * @return Response
     */
    public function destroy($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'delete_pro');

        $deleted = Subscription::destroy($id);

        $status = $deleted ? 1 : 0;
        $message = $deleted ? 'Το subscription με id ' . $id . ' διαγράφηκε επιτυχώς' : 'Σφάλμα κατά την διαγραφή του subscription';

        return response()->json([
            'status'  => $status,
            'message' => $message,
        ]);
    }

    /**
     * Attach a new person to a subscription
     *
     * @param Request $request
     */
    public function addPerson(Request $request)
    {
        $subscription_id = $request['subjectId'];
        $person_id  = $request['personId'];

        // find Subscription and person
        $subscription = Subscription::findOrFail($subscription_id);
        $person = Person::findOrFail($person_id);

        // Save subscription to person
        $person->subscription_id = $subscription->id;
        $person->save();
    }


    /**
     * Detach a person from a subscription
     *
     * @param Request $request
     */
    public function removePerson(Request $request)
    {
        if ($request->has('subjectId') && $request->has('personId'))
        {
            $subscription_id = $request['subjectId'];
            $person_id  = $request['personId'];

            // find Subscription and person
            $subscription = Subscription::findOrFail($subscription_id);
            $person = Person::findOrFail($person_id);

            // Save subscription to person
            $person->subscription_id = null;
            $person->save();
        }
    }
}
