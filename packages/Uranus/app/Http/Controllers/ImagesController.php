<?php namespace Packages\Uranus\app\Http\Controllers;

use App\Components\Sanitizer;
use App\Models\Image;
use App\Models\Movie;
use App\Models\Play;
use App\Models\TvShow;
use Artesaos\SEOTools\Facades\SEOMeta;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Packages\Uranus\app\Http\Requests\ImageDestroyRequest;
use Packages\Uranus\app\Http\Requests\ImageMainRequest;
use Packages\Uranus\app\Http\Requests\ImageTagsRequest;

class ImagesController extends Controller
{


    /**
     * Display a listing of the resource for a given attribute.
     *
     * @param Request $request
     * @return Response
     */
    public function index(Request $request)
    {
        if ($request->resourceType && $request->id) {

            $images = Image::where(Str::snake($request->resourceType) . '_id', '=', $request->id)->get();

            // Add accessor
            foreach ($images as $image) {
                $image->assetPath = unstageAsset('');
            }

            return $images;
        }
    }

    /**
     * Display a listing of the resource for moderation
     */
    public function indexForModeration(Request $request)
    {
        $play = $movie = $tvShow = null;

        $sortBy = $request->input('sortBy', 'updated_at');
        $direction = $request->input('direction', 'desc');
        $page = $request->input('page', 1);
        $limit = config('uranus.limits.schools.index');
        $offset = ($page * $limit) - $limit;

        $images = Image::orderBy($sortBy, $direction);

        // cater for type search filter (refers to moderated field)
        $query_type = $request->query('type');
        if ($query_type == 'moderated')
        {
            $images->where('moderated', true);
        }
        else if ($query_type == 'unmoderated')
        {
            $images->where('moderated', false);
        }

        // cater for tagged search filter (refers to people relationship)
        $query_tagged = $request->query('tagged');
        if ($query_tagged == 'tagged')
        {
            $images->has('people');
        }
        else if ($query_tagged == 'untagged')
        {
            $images->doesntHave('people');
        }

        // cater for related search filter (refers to one to many relationship via the relevant <model>_id field in images table))
        $query_related = $request->query('related');
        if ($query_related == 'person')
        {
            $images->has('person');
        }
        else if ($query_related == 'play')
        {
            $images->has('play');
        }
        else if ($query_related == 'movie')
        {
            $images->has('movie');
        }
        else if ($query_related == 'tvShow')
        {
            $images->has('tvShow');
        }
        else if ($query_related == 'theatre')
        {
            $images->has('theatre');
        }
        else if ($query_related == 'article')
        {
            $images->has('article');
        }
        else if ($query_related == 'festival')
        {
            $images->has('festival');
        }

        // cater for play_id search filter (refers to play relationship)
        $query_specific_play = $request->query('play_id');
        if ($query_specific_play)
        {
            $play = Play::find($query_specific_play);

            $images->whereHas('play', function (Builder $query) use ($query_specific_play) {
                $query->where('id', '=', $query_specific_play);
            });
        }

        // cater for movie_id search filter (refers to movie relationship)
        $query_specific_movie = $request->query('movie_id');
        if ($query_specific_movie)
        {
            $movie = Movie::find($query_specific_movie);

            $images->whereHas('movie', function (Builder $query) use ($query_specific_movie) {
                $query->where('id', '=', $query_specific_movie);
            });
        }

        // cater for tv_show_id search filter (refers to tvShow relationship)
        $query_specific_tv_show = $request->query('tv_show_id');
        if ($query_specific_tv_show)
        {
            $tvShow = TvShow::find($query_specific_tv_show);

            $images->whereHas('tvShow', function (Builder $query) use ($query_specific_tv_show) {
                $query->where('id', '=', $query_specific_tv_show);
            });
        }

        $images = $images->offset($offset)
            ->limit($limit)
            ->paginate();

        $images->setPath($request->url());

        // append type query string if present
        if ($query_type)
        {
            $images->appends('type', $query_type);
        }

        // append tagged query string if present
        if ($query_tagged)
        {
            $images->appends('tagged', $query_tagged);
        }

        // append related query string if present
        if ($query_related)
        {
            $images->appends('related', $query_related);
        }

        // append specific play query string if present
        if ($query_specific_play)
        {
            $images->appends('play_id', $query_specific_play);
        }

        // append specific movie query string if present
        if ($query_specific_movie)
        {
            $images->appends('movie_id', $query_specific_movie);
        }

        // append specific tv show query string if present
        if ($query_specific_tv_show)
        {
            $images->appends('tv_show_id', $query_specific_tv_show);
        }

        $images->appends('sortBy', $sortBy)
            ->appends('direction', $direction);

        // initialize var to hold tagged peeps fre images
        $imagePeople          = [];

        // loop through images to fetch tagged people
        // needed for the tagging autosuggests initialization
        foreach($images as $image)
        {
            // check whether the image is connected (tagged) with people
            if ( ! empty($image->people()) )
            {
                foreach($image->people()->get() as $person)
                {
                    $imagePeople[$image->id][$person->fullName] = $person->id;
                }
            }

        }

        // seo title
        SEOMeta::setTitle('Index images for moderation');

        return view('uranus::images.index', compact('images', 'imagePeople', 'play', 'movie', 'tvShow'));

    }
    /**
     * Display a listing of the resource for inspection related to pro upload
     */
    public function indexForInspection(Request $request)
    {

        $sortBy = $request->input('sortBy', 'updated_at');
        $direction = $request->input('direction', 'desc');
        $page = $request->input('page', 1);
        $limit = config('uranus.limits.schools.index');
        $offset = ($page * $limit) - $limit;

        $images = Image::whereNotNull('user_id')
            ->orderBy($sortBy, $direction);

        // cater for type search filter (refers to inspected field)
        $query_type = $request->query('type');
        if ($query_type == 'inspected')
        {
            $images->where('inspected', true);
        }
        else if ($query_type == 'uninspected')
        {
            $images->where('inspected', false);
        }

        // cater for decision search filter (refers to banned field)
        $query_decision = $request->query('decision');
        if ($query_decision == 'banned')
        {
            $images->where('banned', true);
        }
        else if ($query_decision == 'allowed')
        {
            $images->where('banned', false);
        }

        // cater for person_id search filter
        $query_person_id = $request->query('person_id');
        if ($query_person_id)
        {
            $images->where('person_id', '=', $query_person_id);
        }

        // cater for user_id search filter
        $query_user_id = $request->query('user_id');
        if ($query_user_id)
        {
            $images->where('user_id', '=', $query_user_id);
        }

        $images = $images->offset($offset)
            ->limit($limit)
            ->paginate();

        $images->setPath($request->url());

        // append type query string if present
        if ($query_type)
        {
            $images->appends('type', $query_type);
        }

        // append decision query string if present
        if ($query_decision)
        {
            $images->appends('decision', $query_decision);
        }

        // append person_id query string if present
        if ($query_person_id)
        {
            $images->appends('person_id', $query_person_id);
        }

        // append user_id query string if present
        if ($query_user_id)
        {
            $images->appends('user_id', $query_user_id);
        }

        $images->appends('sortBy', $sortBy)
            ->appends('direction', $direction);

        // seo title
        SEOMeta::setTitle('Index images for inspection');

        return view('uranus::images.index_inspection', compact('images'));

    }

    /**
     * Update the specified resource in storage (concerning moderation).
     *
     * @param $id
     * @return Response
     */
    public function updateForModeration(Request $request, $id)
    {
        $input = array_map('trim', $request->all());

        $image = Image::findOrFail($id);

        if( $input['moderated'] == 0 )
        {
            $image->moderated = false;
            $action_taken   = 'unmoderated';
        }
        else
        {
            $image->moderated = true;
            $action_taken   = 'moderated';
        }

        $image->save();

        return response()->json([
            'status'   => 'success',
            'moderated'  => $input['moderated'],
            'id' => $image->id
        ]);
    }


    /**
     * Update the specified resource in storage (concerning ban).
     *
     * @param $id
     * @return Response
     */
    public function updateForBan(Request $request, $id)
    {
        $input = array_map('trim', $request->all());

        $image = Image::findOrFail($id);

        if( $input['banned'] == 0 )
        {
            $image->banned = false;
            $action_taken   = 'allowed';
        }
        else
        {
            $image->banned = true;
            $action_taken   = 'banned';
        }

        // ...and set the inspected attribute as true, too (since it is inspected now that is decide)
        $image->inspected = true;

        $image->save();

        return response()->json([
            'status'   => 'success',
            'banned'  => $input['banned'],
            'id' => $image->id
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @return Response
     */
    public function destroy(ImageDestroyRequest $request)
    {
        $image = Image::find($request->id);

        $image->delete();

        return response()->json([
            'status'   => 'success',
            'message'  => 'Update successful.'
        ]);
    }

    /**
     * Set image as main and the one that used to be main (if exists) as normal
     *
     * @param $request
     */
    private function setMainImage($request)
    {
        $oldMainImage = Image::where(Str::snake($request->resourceType) . '_id', '=', $request->id)
                             ->where('main', '=', 1)
                             ->first();

        $newMainImage = Image::where(Str::snake($request->resourceType) . '_id', '=', $request->id ? $request->id : 0)
                             ->where('filename', '=', $request->name ? $request->name : $request->newMainImageFilename)
                             ->first();

        if ( ! $oldMainImage && $newMainImage) {
            $newMainImage->main = 1;
            $newMainImage->save();
        }

        if ($oldMainImage && $newMainImage && $oldMainImage != $newMainImage) {
            $oldMainImage->main = 0;
            $oldMainImage->save();
            $newMainImage->main = 1;
            $newMainImage->save();
        }
    }

    /**
     * @param $request
     */
    private function setTags($request, $credits = false)
    {
        if ($credits == true)
        {
            // the request is to add (people) credits to an image
            $input_array = [];
            // check whether we have people submitted
            if ($request->input('person_list'))
            {
                // format the array to be passed for syncing with the pivot value for role to be marked as credit
                foreach($request->input('person_list') as $person_id)
                {
                    $input_array[$person_id] = [
                        'role' => 'credit'
                    ];
                }
            }

            $image = Image::where(Str::snake($request->resourceType) . '_id', '=', $request->id)
                ->where('filename', '=', $request->imageToAddCredits)
                ->firstOrFail();

            $image->creditedPeople()->sync($input_array);
        }
        else
        {
            // the request is to add (people) tags to an image
            $input_array = [];
            // check whether we have people submitted
            if ($request->input('person_list'))
            {
                // format the array to be passed for syncing with the pivot value for role to be marked as tag
                foreach($request->input('person_list') as $person_id)
                {
                    $input_array[$person_id] = [
                        'role' => 'tag'
                    ];
                }
            }

            $image = Image::where(Str::snake($request->resourceType) . '_id', '=', $request->id)
                ->where('filename', '=', $request->imageToAddTags)
                ->firstOrFail();

            $image->taggedPeople()->sync($input_array);
        }

    }

    /**
     * Move file to storage
     *
     * @param $file
     * @param $request
     * @param $strayImage
     * @return Image
     */
    private function storeImage($file, $request)
    {
        $fileExtension = $file->guessExtension();

        $imageNameWithoutExtension = substr($file->getClientOriginalName(), 0,
            strrpos($file->getClientOriginalName(), "."));

        $image_name = Sanitizer::handle($imageNameWithoutExtension, true) . '-' . Str::random(6) . '.' . $fileExtension;
        $image_name = string_to_greeklish($image_name);

        // prepare the storage folder name
        $storageFolder = 'images/' . Str::plural(Str::camel($request->resourceType));

        // prepare path name
        $path = public_path($storageFolder);

        // move the file from the temporary uploaded location
        // to a more permanent one
        $file->move($path, $image_name);

        // image model actions
        $image_attributes = [
            'filename'                                 => $storageFolder . '/' . $image_name,
            Str::snake($request->resourceType) . '_id' => $request->id,
        ];

        // create new model
        $image = Image::create($image_attributes);

        if ($request->main_image) {
            $request->name = $image->filename;
            $this->setMainImage($request);
        }

        return $image;
    }


    /**
     * Attach a new person to an image
     *
     * @param Request $request
     */
    public function addPerson(Request $request)
    {
        $image_id = $request['imageId'];
        $person_id  = $request['personId'];

        // find image
        $image = Image::findOrFail($image_id);

        $image->people()->attach($person_id);
    }


    /**
     * Detach a person from an image
     *
     * @param Request $request
     */
    public function removePerson(Request $request)
    {
        if ($request->has('imageId') && $request->has('personId'))
        {
            $image_id = $request['imageId'];
            $person_id  = $request['personId'];

            // find image
            $image = Image::findOrFail($image_id);

            $image->people()->detach($person_id);
        }
    }

    /**
     * Tag people for image.
     *
     * @param ImageTagsRequest $request
     * @return JsonResponse
     */
    public function tag(ImageTagsRequest $request): JsonResponse
    {
        // Get image by filename
        $image = Image::find($request->get('id'));
        if($request->has('person_list'))
        {
            $tags = array_fill_keys($request->get('person_list'), ['role' => 'tag']);
            $image->taggedPeople()->sync($tags);
        }
        if($request->has('credit_list'))
        {
            $tags = array_fill_keys($request->get('credit_list'), ['role' => 'credit']);
            $image->creditedPeople()->sync($tags);
        }

        return response()->json([
            'status'   => 'success',
            'message'  => 'Update successful.'
        ]);
    }

    /**
     * Set image as main and the one that used to be main (if exists) as normal
     *
     * @param ImageMainRequest $request
     * @return JsonResponse
     */
    public function mainImage(ImageMainRequest $request): JsonResponse
    {
        // Update image
        $update_image = true;

        // Find old main image
        $oldMainImage = Image::where(Str::snake($request->resourceType) . '_id', '=', $request->resourceTypeId)
            ->where('main', '=', 1)
            ->first();

        if($oldMainImage)
        {
            if($oldMainImage->id !== $request->id)
            {
                // Update old
                $oldMainImage->main = 0;
                $oldMainImage->save();
            }
            else
            {
                $update_image = false;
            }
        }

        if($update_image)
        {
            // Update new
            $newMainImage = Image::find($request->id);
            $newMainImage->main = 1;
            $newMainImage->save();
        }

        return response()->json([
            'status'   => 'success',
            'message'  => 'Update successful.'
        ]);
    }
}
