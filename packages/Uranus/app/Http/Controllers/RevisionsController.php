<?php

namespace Packages\Uranus\app\Http\Controllers;

use App\Models\Festival;
use App\Models\Movie;
use App\Models\Person;
use App\Models\Play;
use App\Models\Revision;
use App\Models\Theatre;
use App\Models\TvShow;
use Carbon\Carbon;

class RevisionsController extends Controller
{

    public function __construct()
    {
        parent::__construct();

        Carbon::setLocale('el');
    }

    /**
     * @param $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     *
     * Shows revisions for given play
     */
    public function indexPlay($id)
    {
        $request = request();
        $sortBy = $request->input('sortBy', 'updated_at');
        $direction = $request->input('direction', 'desc');
        $page = $request->input('page', 1);
        $limit = config('uranus.limits.revisions.index');
        $offset = ($page * $limit) - $limit;

        $revisions = Revision::where('revisionable_type', Play::class)
                             ->where('revisionable_id', $id)
                             ->with('revisionAdmin')
                             ->offset($offset)
                             ->limit($limit)
                             ->orderBy($sortBy, $direction)
                             ->paginate($limit);

        $revisions->setPath($request->url())
                  ->appends('sortBy', $sortBy)
                  ->appends('direction', $direction);

        $play = Play::find($id);

        return view('uranus::revisions.indexPlay', compact('revisions', 'play'));
    }


    /**
     * @param $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     *
     * Shows revisions for given movie
     */
    public function indexMovie($id)
    {
        $request = request();
        $sortBy = $request->input('sortBy', 'updated_at');
        $direction = $request->input('direction', 'desc');
        $page = $request->input('page', 1);
        $limit = config('uranus.limits.revisions.index');
        $offset = ($page * $limit) - $limit;

        $revisions = Revision::where('revisionable_type', Movie::class)
                             ->where('revisionable_id', $id)
                             ->with('revisionAdmin')
                             ->offset($offset)
                             ->limit($limit)
                             ->orderBy($sortBy, $direction)
                             ->paginate($limit);

        $revisions->setPath($request->url())
                  ->appends('sortBy', $sortBy)
                  ->appends('direction', $direction);

        $movie = Movie::find($id);

        return view('uranus::revisions.indexMovie', compact('revisions', 'movie'));
    }


    /**
     * @param $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     *
     * Shows revisions for given tv show
     */
    public function indexTvShow($id)
    {
        $request = request();
        $sortBy = $request->input('sortBy', 'updated_at');
        $direction = $request->input('direction', 'desc');
        $page = $request->input('page', 1);
        $limit = config('uranus.limits.revisions.index');
        $offset = ($page * $limit) - $limit;

        $revisions = Revision::where('revisionable_type', TvShow::class)
                             ->where('revisionable_id', $id)
                             ->with('revisionAdmin')
                             ->offset($offset)
                             ->limit($limit)
                             ->orderBy($sortBy, $direction)
                             ->paginate($limit);

        $revisions->setPath($request->url())
                  ->appends('sortBy', $sortBy)
                  ->appends('direction', $direction);

        $tvShow = TvShow::find($id);

        return view('uranus::revisions.indexTvShow', compact('revisions', 'tvShow'));
    }


    /**
     * @param $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     *
     * Shows revisions for given person
     */
    public function indexPerson($id)
    {
        $request = request();
        $sortBy = $request->input('sortBy', 'updated_at');
        $direction = $request->input('direction', 'desc');
        $page = $request->input('page', 1);
        $limit = config('uranus.limits.revisions.index');
        $offset = ($page * $limit) - $limit;

        $revisions = Revision::where('revisionable_type', Person::class)
                             ->where('revisionable_id', $id)
                             ->with('revisionAdmin')
                             ->offset($offset)
                             ->limit($limit)
                             ->orderBy($sortBy, $direction)
                             ->paginate($limit);


        $revisions->setPath($request->url())
                  ->appends('sortBy', $sortBy)
                  ->appends('direction', $direction);

        $person = Person::find($id);

        return view('uranus::revisions.indexPerson', compact('revisions', 'person'));
    }


    /**
     * @param $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     *
     * Shows revisions for given theatre
     */
    public function indexTheatre($id)
    {
        $request = request();
        $sortBy = $request->input('sortBy', 'updated_at');
        $direction = $request->input('direction', 'desc');
        $page = $request->input('page', 1);
        $limit = config('uranus.limits.revisions.index');
        $offset = ($page * $limit) - $limit;

        $revisions = Revision::where('revisionable_type', Theatre::class)
                             ->where('revisionable_id', $id)
                             ->with('revisionAdmin')
                             ->offset($offset)
                             ->limit($limit)
                             ->orderBy($sortBy, $direction)
                             ->paginate($limit);

        $revisions->setPath($request->url())
                  ->appends('sortBy', $sortBy)
                  ->appends('direction', $direction);

        $theatre = Theatre::find($id);

        return view('uranus::revisions.indexTheatre', compact('revisions', 'theatre'));
    }


    /**
     * @param $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     *
     * Shows revisions for given festival
     */
    public function indexFestival($id)
    {
        $request = request();
        $sortBy = $request->input('sortBy', 'updated_at');
        $direction = $request->input('direction', 'desc');
        $page = $request->input('page', 1);
        $limit = config('uranus.limits.revisions.index');
        $offset = ($page * $limit) - $limit;

        $revisions = Revision::where('revisionable_type', Festival::class)
                             ->where('revisionable_id', $id)
                             ->with('revisionAdmin')
                             ->offset($offset)
                             ->limit($limit)
                             ->orderBy($sortBy, $direction)
                             ->paginate($limit);

        $revisions->setPath($request->url())
                  ->appends('sortBy', $sortBy)
                  ->appends('direction', $direction);

        Festival::find($id);

        return view('uranus::revisions.indexFestival', compact('revisions', 'festival'));
    }


}
