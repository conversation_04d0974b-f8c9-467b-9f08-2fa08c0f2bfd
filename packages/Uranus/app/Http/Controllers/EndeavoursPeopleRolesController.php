<?php

namespace Packages\Uranus\app\Http\Controllers;

use App\Models\EndeavourPersonRole;
use Illuminate\Http\Request;

class EndeavoursPeopleRolesController extends Controller
{


    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     */

    public function store(Request $request)
    {
        $data = [
            'role_id'       => $request['roleId'],
            'endeavour_id'  => $request['endeavourId'],
            'person_id'     => $request['personId'],
        ];

        EndeavourPersonRole::updateOrCreate($data);
    }


    /**
     * Remove the specified resource from storage.
     *
     * @param Request $request
     */

    public function destroy(Request $request)
    {
        if ($request->has('endeavourId') && $request->has('personId') && $request->has('roleId')) {
            EndeavourPersonRole::where('endeavour_id', $request['endeavourId'])
                          ->where('person_id', $request['personId'])
                          ->where('role_id', $request['roleId'])
                          ->firstOrFail()
                          ->delete();
        }
    }

}
