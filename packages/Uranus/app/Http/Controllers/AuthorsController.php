<?php namespace Packages\Uranus\app\Http\Controllers;

use App\Models\Author;
use Packages\Uranus\app\Http\Requests\AuthorRequest;

class AuthorsController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return Response
     */
    public function index()
    {
        $this->authorizeForUser(auth('admin')->user(), 'see_articles');

        $request = request();
        $sortBy = $request->input('sortBy', 'created_at');
        $direction = $request->input('direction', 'desc');
        $page = $request->input('page', 1);
        $limit = config('uranus.limits.authors.index');
        $offset = ($page * $limit) - $limit;

        $authors = Author::orderBy($sortBy, $direction)
                       ->offset($offset)
                       ->limit($limit)
                       ->paginate();

        $authors->setPath($request->url())
               ->appends('sortBy', $sortBy)
               ->appends('direction', $direction);

        return view('uranus::authors.index', compact('authors'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return Response
     */
    public function create()
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_articles');

        return view('uranus::authors.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param AuthorRequest $request
     * @return Response
     */
    public function store(AuthorRequest $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_articles');

        $input = array_map('trim', $request->all());

        $author = Author::create($input);

        return redirect()->route('uranus.authors.edit', [$author->id])
                         ->with('success', 'Author ' . $author->id . ' added');
    }


    /**
     * Show the form for editing the specified resource.
     *
     * @param $id
     * @return Response
     */
    public function edit($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_articles');

        $author = Author::findOrFail($id);

        return view('uranus::authors.edit', compact('author'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param AuthorRequest $request
     * @param $id
     * @return Response
     * @internal param $id
     * @internal param int $id
     */
    public function update(AuthorRequest $request, $id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_articles');

        $input = array_map('trim', $request->all());

        $author = Author::findOrFail($id);

        $author->update($input);

        return redirect()->route('uranus.authors.edit', [$author->id])
                         ->with('success', 'Author ' . $id . ' updated');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param $id
     * @return Response
     */
    public function destroy($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'delete_articles');

        $deleted = Author::destroy($id);

        $status = $deleted ? 1 : 0;
        $message = $deleted ? 'Ο αρθρογράφος με id ' . $id . ' διαγράφηκε επιτυχώς' : 'Σφάλμα κατά την διαγραφή του αρθρογράφου';

        return response()->json([
            'status'  => $status,
            'message' => $message,
        ]);
    }

}
