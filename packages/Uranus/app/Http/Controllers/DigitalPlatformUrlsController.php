<?php namespace Packages\Uranus\app\Http\Controllers;

use App\Models\DigitalPlatformUrl;
use App\Models\Endeavour;
use Artesaos\SEOTools\Facades\SEOMeta;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\Request;
use Packages\Uranus\app\Http\Requests\DigitalPlatformUrlRequest;

class DigitalPlatformUrlsController extends Controller
{

    /**
     * Show the form for creating a new resource.
     *
     * @param Request $request
     * @throws AuthorizationException
     */
    public function create(Request $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_plays');

        $endeavour = Endeavour::findOrFail($request->input('endeavour_id'));

        // seo title
        SEOMeta::setTitle('New digital platform url');

        return view('uranus::digital_platform_urls.create', compact('endeavour'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param DigitalPlatformUrlRequest $request
     * @throws AuthorizationException
     */
    public function store(DigitalPlatformUrlRequest $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_plays');

        $input = $request->validated();

//        dd($input);

        $dpu = DigitalPlatformUrl::create($input);

        return redirect()->route('uranus.endeavours.edit', [$dpu->endeavour_id])
                         ->with('success', 'Url ' . $dpu->id . ' added');
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param $id
     * @throws AuthorizationException
     */
    public function edit($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_plays');

        $dpu = DigitalPlatformUrl::where('id', $id)
                            ->firstOrFail();

        // seo title
        SEOMeta::setTitle('Edit digital platform url ' . $dpu->id);

        return view('uranus::digital_platform_urls.edit', compact('dpu'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param DigitalPlatformUrlRequest $request
     * @param int $id
     * @throws AuthorizationException
     */
    public function update(DigitalPlatformUrlRequest $request, $id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_plays');

        $input = array_map('trim', $request->validated());

        $dpu = DigitalPlatformUrl::findOrFail($id);

        $dpu->update($input);

        return redirect()->route('uranus.endeavours.edit', [$dpu->endeavour_id])
                         ->with('success', 'Url ' . $dpu->id . ' updated');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @throws AuthorizationException
     */
    public function destroy($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'delete_plays');

        $deleted = DigitalPlatformUrl::destroy($id);

        $status = $deleted ? 1 : 0;
        $message = $deleted ? 'Το Url με id ' . $id . ' διαγράφηκε επιτυχώς' : 'Σφάλμα κατά την διαγραφή του Url';

        return response()->json([
            'status'  => $status,
            'message' => $message,
        ]);

    }

}
