<?php namespace Packages\Uranus\app\Http\Controllers;

use App\Models\Person;
use App\Models\PersonEmail;
use Artesaos\SEOTools\Facades\SEOMeta;
use Illuminate\Http\Request;
use Packages\Uranus\app\Http\Requests\PersonEmailRequest;

class PersonEmailsController extends Controller
{

    /**
     * Show the form for creating a new resource.
     *
     * @param Request $request
     * @return Response
     */
    public function create(Request $request)
    {
        $this->authorizeFor<PERSON>ser(auth('admin')->user(), 'manage_people');

        $person = Person::findOrFail($request->input('person_id'));

        // seo title
        SEOMeta::setTitle('New person email');

        return view('uranus::personEmails.create', compact('person'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param PersonEmailRequest $request
     * @return Response
     */
    public function store(PersonEmailRequest $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_people');

        $input = $request->all();

        $email = PersonEmail::create($input);
     
        session()->flash('success', 'Email ' . $email->id . ' added');
        return redirect()->to(route('uranus.people.edit', [$email->person_id]) . '#crm');
        
        // return redirect()->route('uranus.people.edit', [$email->person_id])
        //                 ->with('success', 'Email ' . $email->id . ' added');
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param $id
     * @return Response
     */
    public function edit($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_people');

        $email = PersonEmail::where('id', $id)
                            ->with('person')
                            ->firstOrFail();

        // seo title
        SEOMeta::setTitle('Edit person email ' . $email->id);

        return view('uranus::personEmails.edit', compact('email'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param PersonEmailRequest $request
     * @param  int $id
     * @return Response
     */
    public function update(PersonEmailRequest $request, $id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_people');

        $input = $request->all();

        $email = PersonEmail::findOrFail($id);

        $email->update($input);

        session()->flash('success', 'Email ' . $email->id . ' updated');
        return redirect()->to(route('uranus.people.edit', [$email->person_id]) . '#crm');

        // return redirect()->route('uranus.people.edit', [$email->person_id])
        //                 ->with('success', 'Email ' . $email->id . ' updated');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int $id
     * @return Response
     */
    public function destroy($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'delete_people');

        $deleted = PersonEmail::destroy($id);

        $status = $deleted ? 1 : 0;
        $message = $deleted ? 'Το email με id ' . $id . ' διαγράφηκε επιτυχώς' : 'Σφάλμα κατά την διαγραφή του email';

        return response()->json([
            'status'  => $status,
            'message' => $message,
        ]);

    }

}
