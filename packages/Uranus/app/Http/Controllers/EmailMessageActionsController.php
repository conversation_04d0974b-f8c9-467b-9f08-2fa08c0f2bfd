<?php namespace Packages\Uranus\app\Http\Controllers;

use App\Models\EmailMessageAction;
use Artesaos\SEOTools\Facades\SEOMeta;
use Illuminate\Http\Request;

class EmailMessageActionsController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return Response
     */
    public function index(Request $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'see_contacts');

        $sortBy = $request->input('sortBy', 'date');
        $direction = $request->input('direction', 'desc');
        $page = $request->input('page', 1);
        $limit = config('uranus.limits.contacts.index');
        $offset = ($page * $limit) - $limit;

        $actions = EmailMessageAction::orderBy($sortBy, $direction);

        $query_action = $request->query('action');
        if ( !empty($query_action) )
        {
            if (in_array($query_action, ['send', 'open', 'delivery', 'click']))
            {
                $actions->where('action', $query_action);
            }
        }

        $actions = $actions->offset($offset)
            ->limit($limit)
            ->paginate();

        $actions->setPath($request->url());

        // append action query string if present
        if ( !empty($query_action) )
        {
            $actions->appends('action', $query_action);
        }

        $actions->appends('sortBy', $sortBy)
            ->appends('direction', $direction);

        // seo title
        SEOMeta::setTitle('Index email message actions');

        return view('uranus::emailMessageActions.index', compact('actions'));

    }

    /**
     * Show the specified resource.
     *
     * @param  int $id
     * @return Response
     */
    public function show($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_contacts');

        $action = EmailMessageAction::findOrFail($id);

        // seo title
        SEOMeta::setTitle('Preview email message action ' . $action->id);

        return view('uranus::emailMessageActions.show', compact('action'));
    }

}
