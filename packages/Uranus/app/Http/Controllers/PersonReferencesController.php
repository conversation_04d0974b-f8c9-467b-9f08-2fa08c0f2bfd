<?php namespace Packages\Uranus\app\Http\Controllers;

use App\Models\Person;
use App\Models\PersonReference;
use Artesaos\SEOTools\Facades\SEOMeta;
use Illuminate\Http\Request;
use Packages\Uranus\app\Http\Requests\PersonReferencesRequest;

class PersonReferencesController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return Response
     */
    public function index($person_id, Request $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'see_people');

        $person = Person::where('id', $person_id)
            ->with(['personReferences'])
            ->firstOrFail();

        $personReferences = $person->personReferences;

        // seo title
        SEOMeta::setTitle('Index person references');

        return view('uranus::personReferences.index', compact('personReferences', 'person'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @param Request $request
     * @return Response
     */
    public function create($person_id, Request $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_people');

        $person = Person::findOrFail($person_id);

        // seo title
        SEOMeta::setTitle('New person reference');

        return view('uranus::personReferences.create', compact('person'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param PersonReferencesRequest $request
     * @return Response
     */
    public function store($person_id, PersonReferencesRequest $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_people');

        $request->merge(['person_id' => $person_id]);

        $input = array_map('trim', $request->all());

        $personReference = PersonReference::create($input);

        return redirect()->route('uranus.personReferences.index', [$person_id])
                         ->with('success', 'Person reference ' . $personReference->id . ' added');
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param $id
     * @return Response
     */
    public function edit($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_people');

        $personReference = PersonReference::where('id', $id)
                        ->with('person')
                        ->firstOrFail();

        // seo title
        SEOMeta::setTitle('Edit person reference ' . $personReference->id);

        return view('uranus::personReferences.edit', compact('personReference'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param PersonReferencesRequest $request
     * @param  int $id
     * @return Response
     */
    public function update(PersonReferencesRequest $request, $id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_people');

        $input = $request->all();

        $personReference = PersonReference::with('person')->findOrFail($id);

        $personReference->update($input);

        return redirect()->route('uranus.personReferences.index', [$personReference->person->id])
                         ->with('success', 'Person reference ' . $personReference->id . ' ('. $personReference->title . ') updated');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int $id
     * @return Response
     */
    public function destroy($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'delete_people');

        $deleted = PersonReference::destroy($id);

        $status = $deleted ? 1 : 0;
        $message = $deleted ? 'Το person reference με id ' . $id . ' διαγράφηκε επιτυχώς' : 'Σφάλμα κατά τη διαγραφή του person reference';

        return response()->json([
            'status'  => $status,
            'message' => $message,
        ]);

    }

}
