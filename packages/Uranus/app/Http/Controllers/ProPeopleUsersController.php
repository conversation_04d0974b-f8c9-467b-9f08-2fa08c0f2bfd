<?php

namespace Packages\Uranus\app\Http\Controllers;

use App\Models\PersonUser;
use Illuminate\Http\Request;
use Packages\Neptune\app\Models\User;
use Packages\Uranus\app\Http\Requests\ProPersonUserRequest;

class ProPeopleUsersController extends Controller
{

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     */

    public function store(ProPersonUserRequest $request)
    {
        $data = [
            'person_id' => $request['person_id'],
            'user_id'   => $request['user_id'],
            'type'      => $request['type'],
        ];

        PersonUser::updateOrCreate($data);

        return redirect()->back()
            ->with('success', 'Person ' . $request['person_id'] . ' connected to User ' . $request['user_id'] . ' with type ' . $request['type']);
    }


    /**
     * Remove the specified resource from storage.
     *
     * @param Request $request
     */

    public function destroy($user_item_id, $person_id, Request $request)
    {
        $deleted = PersonUser::where('person_id', $person_id)
                      ->where('user_id', $user_item_id)
                      ->firstOrFail()
                      ->delete();

        $status = $deleted ? 1 : 0;
        $message = $deleted ? 'Ο συντελεστής με id ' . $person_id . ' και ο χρήστης με id ' . $user_item_id . ' αποσυνδέθηκαν επιτυχώς' : 'Σφάλμα κατά την αποσύνδεση του συντελεστή από το χρήστη';

        return response()->json([
            'status'  => $status,
            'message' => $message,
        ]);
    }

}
