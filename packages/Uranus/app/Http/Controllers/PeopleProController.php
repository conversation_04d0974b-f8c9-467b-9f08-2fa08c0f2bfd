<?php

namespace Packages\Uranus\app\Http\Controllers;

use App\Models\Image;
use App\Models\Person;
use App\Services\Search\Text\TextSearchService;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\Request;
use Artesaos\SEOTools\Facades\SEOMeta;

class PeopleProController extends Controller
{

    /**
     * @var TextSearchService
     */
    private $textSearch;


    public function __construct(TextSearchService $textSearch)
    {
        parent::__construct();

        $this->textSearch = $textSearch;
        $this->textSearch->setPreferredSearchLayer('eloquent');
    }


    /**
     * Display a listing of the people who are enabled in unstagePro.
     *
     * @param Request $request
     * @return Response
     * @throws AuthorizationException
     */
    public function index(Request $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'see_people');

        $sortBy = $request->input('sortBy', 'people.updated_at');
        $direction = $request->input('direction', 'desc');
        $page = $request->input('page', 1);
        $limit = config('uranus.limits.people.index');
        $offset = ($page * $limit) - $limit;

        // Init
        $people = Person::whereNotNull('subscription_id')
            ->orderBy($sortBy, $direction);

        $query_q = $request->query('q');
        if ( !empty($query_q) )
        {
            // re initialize people var in order to achieve the joins
            $people = Person::leftJoin('person_emails', 'people.id', '=', 'person_emails.person_id')
                ->leftJoin('people_translations', 'people.id', '=', 'people_translations.person_id')
                ->select(['people.id', 'people.updated_at'])
                ->orderBy($sortBy, $direction)
                ->groupBy('people.id');

            $people->whereNotNull('people.subscription_id')
                ->where(function ($query) use ($query_q) {
                    $query->orWhere('person_emails.email', 'LIKE', '%' . $query_q . '%')
                        ->orWhere('people_translations.first_name', 'LIKE', '%' . $query_q . '%')
                        ->orWhere('people_translations.last_name', 'LIKE', '%' . $query_q . '%')
                        ->orWhere('people.first_name', 'LIKE', '%' . $query_q . '%')
                        ->orWhere('people.last_name', 'LIKE', '%' . $query_q . '%')
                        ->orWhereRaw('concat_ws(" ",people_translations.first_name,people_translations.last_name)like \'%' . $query_q . '%\'')
                        ->orWhereRaw('concat_ws(" ",people.first_name,people.last_name)like \'%' . $query_q . '%\'');
                });
        }

        $people = $people
            ->offset($offset)
//            ->with('mainImage')
            ->limit($limit)
            ->paginate();

        $people->setPath($request->url());

        // append q query string if present
        if ( !empty($query_q) )
        {
            $people->appends('q', $query_q);
        }

        $people->appends('sortBy', $sortBy)
           ->appends('direction', $direction);

        // load relations and extra data for all returned peeps
        $people->each(function($person) {
            $person->load([
                'plays' => function ($query) {
                    $query->groupBy('id')
                        ->orderBy('start_date', 'DESC');
                },
                'movies' => function ($query) {
                    $query->groupBy('id')
                        ->orderBy('year', 'DESC');
                },
                'tvShows' => function ($query) {
                    $query->groupBy('id')
                        ->orderBy('year', 'DESC');
                },
            ]);

            // append custom attribute about user uploaded images
            // uploaded images count
            $person->user_images_count = $this->calculateUserImages($person);

            // uploaded plays count
            $person->user_plays_count = $this->calculateUserPlays($person);

            // uploaded videos count
            $person->user_videos_count = $this->calculateUserVideos($person);

            // all uploaded seminars count
            $person->user_all_seminars_count = $this->calculateUserSeminars($person, true);

            // banned uploaded seminars count
            $person->user_unbanned_seminars_count = $this->calculateUserSeminars($person, false);
        });

        // seo title
        SEOMeta::setTitle('Index pro enabled people');

        return view('uranus::people.index_proEnabled', compact('people'));

    }


    /**
     * Show the form for editing the pro users connected to this person.
     *
     * @param  int $id
     * @return Response
     */
    public function editProUsers($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_people');
        $person = Person::where('id', $id)
            ->firstOrFail();

        // seo title
        SEOMeta::setTitle('Edit pro users fer person ' . $person->id);

        return view('uranus::people.proUsers_index', compact('person'));
    }


    /**
     * Show the page with data about the pro data of this person.
     *
     * @param  int $id
     * @return Response
     */
    public function showProData($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_people');
        $person = Person::where('id', $id)
            ->firstOrFail();

        $simple_user_attributes = [
            'user_bio',
            'user_height',
            'user_eye_colour',
            'user_hair_colour',
            'user_acting_age_from',
            'user_acting_age_to',
            'user_facebook',
            'user_facebook_page',
            'user_instagram',
            'user_imdb',
            'user_website',
            'user_twitter',
            'user_youtube',
        ];

        $advanced_user_attributes = [];

        // uploaded plays count
        $advanced_user_attributes['uploaded_plays_count'] = $this->calculateUserPlays($person);

        // uploaded images count
        $advanced_user_attributes['uploaded_images_count'] = $this->calculateUserImages($person);

        // videos count
        $advanced_user_attributes['videos_count'] = $this->calculateUserVideos($person);

        // all seminars count
        $advanced_user_attributes['all_seminars_count'] = $this->calculateUserSeminars($person, true);

        // unbanned seminars count
        $advanced_user_attributes['unbanned_seminars_count'] = $this->calculateUserSeminars($person, false);

        // references count
        $advanced_user_attributes['references_count'] = $this->calculateUserReferences($person);

        // seo title
        SEOMeta::setTitle('Show pro data fer person ' . $person->id);

        return view('uranus::people.proData_show', compact('person', 'simple_user_attributes', 'advanced_user_attributes'));
    }


    /**
     * @param Person $person
     * @return mixed
     *
     * Returns the count of images of this person that have been uploaded by users
     */
    private function calculateUserPlays(Person $person)
    {
        // if we return the ->count() instead of get() and then pass to count
        // ...then we get wrong numbers
        return count($person->plays()
                ->whereNotNull('theatric_plays.user_id')
                ->groupBy('play_id')
                ->get());
    }


    /**
     * @param Person $person
     * @return mixed
     *
     * Returns the count of images of this person that have been uploaded by users
     */
    private function calculateUserImages(Person $person)
    {
        return Image::where('person_id', $person->id)
            ->whereNotNull('user_id')
            ->count();
    }


    /**
     * @param Person $person
     * @return mixed
     *
     * Returns the count of videos of this person that have been uploaded by users
     */
    private function calculateUserVideos(Person $person)
    {
        return $person->videos()
            ->whereNotNull('videos.user_id')
            ->count();
    }


    /**
     * @param Person $person
     * @param bool $all
     * @return mixed
     *
     * Returns the count of seminars of this person that have been uploaded by users
     */
    private function calculateUserSeminars(Person $person, $all = true)
    {
        if ($all)
        {
            // returns all seminars
            return $person->allSeminars()
                ->whereNotNull('seminars.user_id')
                ->count();
        }
        else
        {
            // returns the unbanned seminars only
            return $person->seminars()
                ->whereNotNull('seminars.user_id')
                ->count();
        }
    }


    /**
     * @param Person $person
     * @return mixed
     *
     * Returns the count of person references of this person that have been uploaded by users
     */
    private function calculateUserReferences(Person $person)
    {
        return $person->personReferences()
            ->whereNotNull('person_references.user_id')
            ->count();
    }
}
