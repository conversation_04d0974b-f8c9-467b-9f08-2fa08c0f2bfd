<?php

namespace Packages\Uranus\app\Http\Controllers;

use App\Models\Person;
use App\Trak\Calculators\People\InternalRatingPerson;
use App\Trak\Calculators\People\PersonQualityScore;
use Artesaos\SEOTools\Facades\SEOMeta;

class PeopleAnalyticsController extends Controller
{

    /**
     * Present lists with occurences of a person in relations with other models
     */
    public function castings(Person $person)
    {
        $person->load([
            'plays' => function ($query) {
                $query->groupBy('id')
                    ->orderBy('start_date', 'DESC');
            },
            'movies' => function ($query) {
                $query->groupBy('id')
                    ->orderBy('year', 'DESC');
            },
            'tvShows' => function ($query) {
                $query->groupBy('id')
                    ->orderBy('year', 'DESC');
            },
            'articles' => function ($query) {
                $query->groupBy('id')
                    ->orderBy('created_at', 'DESC');
            },
            'videos' => function ($query) {
                $query->groupBy('id')
                    ->orderBy('created_at', 'DESC');
            },
            'endeavours' => function ($query) {
                $query->groupBy('id')
                    ->orderBy('created_at', 'DESC');
            },
        ]);

        // seo title
        SEOMeta::setTitle('Castings ' . $person->fullName);

        return view('uranus::people.castings_index', compact('person'));
    }

    public function analytics($id)
    {
        $person = Person::findOrFail($id);

        // initialize ratings calculator
        $calculator = new InternalRatingPerson($person);
        // initialize quality calculator
        $quality_calculator = new PersonQualityScore($person);
        // calculate score
        $quality_calculator->calculateScore();

        // create specific attributes for this method
        $person->custom_visits_rating           = $calculator->calculateVisitsRating();
        $person->custom_recent_visits_rating    = $calculator->calculateRecentVisitsRating();
        $person->custom_followings_rating       = $calculator->calculateFollowingsRating();
        $person->custom_quality_score           = $quality_calculator->getQualityScore();
        $person->custom_quality_array           = $quality_calculator->getQualityArray();
        $person->custom_quality_breakdown       = $quality_calculator->getQualityBreakdown();

        // seo title
        SEOMeta::setTitle('Analytics ' . $person->fullName);

        return view('uranus::people.analytics_index', compact('person'));
    }
}
