<?php namespace Packages\Uranus\app\Http\Controllers;

use App\Models\Person;
use App\Models\PersonAlias;
use Artesaos\SEOTools\Facades\SEOMeta;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\Request;
use Packages\Uranus\app\Http\Requests\PersonAliasesRequest;

class PersonAliasesController extends Controller
{

    /**
     * Show the form for creating a new resource.
     *
     * @param Request $request
     * @return Response
     * @throws AuthorizationException
     */
    public function create(Request $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_people');

        $person = Person::findOrFail($request->input('person_id'));

        // seo title
        SEOMeta::setTitle('New person alias');

        return view('uranus::personAliases.create', compact('person'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param PersonAliasesRequest $request
     * @return Response
     * @throws AuthorizationException
     */
    public function store(PersonAliasesRequest $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_people');

        $input = array_map('trim', $request->all());

        $person_alias = PersonAlias::create($input);

        return redirect()->route('uranus.people.edit', [$person_alias->person_id])
                         ->with('success', 'person alias ' . $person_alias->id . ' added');
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param $id
     * @return Response
     * @throws AuthorizationException
     */
    public function edit($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_people');

        $person_alias = PersonAlias::where('id', $id)
                        ->with('person')
                        ->firstOrFail();

        // seo title
        SEOMeta::setTitle('Edit person alias ' . $person_alias->id);

        return view('uranus::personAliases.edit', compact('person_alias'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param PersonAliasesRequest $request
     * @param int $id
     * @return Response
     * @throws AuthorizationException
     */
    public function update(PersonAliasesRequest $request, $id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_people');

        $input = $request->all();

        $person_alias = PersonAlias::findOrFail($id);

        $person_alias->update($input);

        return redirect()->route('uranus.people.edit', [$person_alias->person_id])
                         ->with('success', 'person alias ' . $person_alias->id . ' updated');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return Response
     * @throws AuthorizationException
     */
    public function destroy($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'delete_people');

        $deleted = PersonAlias::destroy($id);

        $status = $deleted ? 1 : 0;
        $message = $deleted ? 'Το person alias με id ' . $id . ' διαγράφηκε επιτυχώς' : 'Σφάλμα κατά την διαγραφή του person alias';

        return response()->json([
            'status'  => $status,
            'message' => $message,
        ]);

    }

}
