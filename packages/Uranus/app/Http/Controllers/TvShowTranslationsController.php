<?php

namespace Packages\Uranus\app\Http\Controllers;

use App\Models\TvShow;
use Illuminate\Http\Request;
use Aws\Exception\AwsException;

class TvShowTranslationsController extends Controller
{
    /*
     * Holds the translation client object
     */
    protected $translationClient;

    /*
     * Holds the source language
     */
    protected $sourceLang;

    /*
     * Holds the target language
     */
    protected $targetLang;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->translationClient = new \Aws\Translate\TranslateClient([
            'region' => 'eu-west-1',
            'version' => '2017-07-01'
        ]);

        $this->sourceLang = 'el';
        $this->targetLang = 'en';
    }

    /**
     * Suggest translation for TV show title
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function suggest(Request $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_plays');

        $tvShow = TvShow::findOrFail($request->input('tvshow_id'));
        $field = $request->input('field', 'title');

        if (empty($tvShow->$field)) {
            return response()->json([
                'success' => false,
                'message' => 'No content to translate'
            ]);
        }

        // Strip HTML tags for translation
        $textToTranslate = strip_tags($tvShow->$field);

        if (empty($textToTranslate)) {
            return response()->json([
                'success' => false,
                'message' => 'No content to translate after removing HTML tags'
            ]);
        }

        // AWS imposes a request limit for 5000 bytes of translatable text
        if (mb_strlen($textToTranslate) >= 2000) {
            return response()->json([
                'success' => false,
                'message' => 'Content too long for translation'
            ]);
        }

        try {
            $result = $this->translationClient->translateText([
                'SourceLanguageCode' => $this->sourceLang,
                'TargetLanguageCode' => $this->targetLang,
                'Text' => $textToTranslate,
            ]);

            return response()->json([
                'success' => true,
                'translation' => $result->get('TranslatedText')
            ]);

        } catch (AwsException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Translation service error: ' . $e->getMessage()
            ]);
        }
    }
}
