<?php namespace Packages\Uranus\app\Http\Controllers;


use App\Models\ContactLead;
use Artesaos\SEOTools\Facades\SEOMeta;
use Illuminate\Http\Request;
use Packages\Uranus\app\Http\Requests\ContactRequest;

class ContactsController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @param ContactRequest $request
     * @return Response
     */
    public function index(ContactRequest $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'see_contacts');

        $request = request();
        $sortBy = $request->input('sortBy', 'created_at');
        $direction = $request->input('direction', 'desc');
        $page = $request->input('page', 1);
        $limit = config('uranus.limits.contacts.index');
        $offset = ($page * $limit) - $limit;

        // view variable to differentiate based on type
        $collaborations = false;

        $contacts = ContactLead::where('type', 'contact')
            ->orderBy($sortBy, $direction);

        $query_text = $request->query('q');
        if ($query_text !== null)
        {
            $contacts->where('notes', 'LIKE', '%' . $query_text . '%')
                ->orWhere('name', 'LIKE', '%' . $query_text . '%')
                ->orWhere('email', 'LIKE', '%' . $query_text . '%')
                ->orWhere('comment', 'LIKE', '%' . $query_text . '%');
        }

        $contacts = $contacts->offset($offset)
            ->limit($limit)
            ->paginate();

        $contacts->setPath($request->url());

        // append text query string if present
        if ($query_text !== null)
        {
            $contacts->appends('q', $query_text);
        }

        $contacts->appends('sortBy', $sortBy)
            ->appends('direction', $direction);

        // seo title
        SEOMeta::setTitle('Index contact leads');

        return view('uranus::contacts.index', compact('contacts', 'collaborations'));
    }


    /**
     * Display a listing of the resource.
     *
     * @param ContactRequest $request
     * @return Response
     */
    public function indexCollaborations(ContactRequest $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'see_contacts');

        $request = request();
        $sortBy = $request->input('sortBy', 'created_at');
        $direction = $request->input('direction', 'desc');
        $page = $request->input('page', 1);
        $limit = config('uranus.limits.contacts.index');
        $offset = ($page * $limit) - $limit;

        // view variable to differentiate based on type
        $collaborations = true;

        $contacts = ContactLead::where('type', 'collaboration')
            ->orderBy($sortBy, $direction);

        $query_text = $request->query('q');
        if ($query_text !== null)
        {
            $contacts->where('notes', 'LIKE', '%' . $query_text . '%')
                ->orWhere('name', 'LIKE', '%' . $query_text . '%')
                ->orWhere('email', 'LIKE', '%' . $query_text . '%')
                ->orWhere('comment', 'LIKE', '%' . $query_text . '%');
        }

        $contacts = $contacts->offset($offset)
            ->limit($limit)
            ->paginate();

        $contacts->setPath($request->url());

        // append text query string if present
        if ($query_text !== null)
        {
            $contacts->appends('q', $query_text);
        }

        $contacts->appends('sortBy', $sortBy)
            ->appends('direction', $direction);

        // seo title
        SEOMeta::setTitle('Index collaboration leads');

        return view('uranus::contacts.index', compact('contacts', 'collaborations'));
    }


    /**
     * Show the specified resource.
     *
     * @param  int $id
     * @return Response
     */
    public function show($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_contacts');

        $contact = ContactLead::findOrFail($id);

        // seo title
        SEOMeta::setTitle('Preview contact lead ' . $contact->id);

        return view('uranus::contacts.show', compact('contact'));
    }


    /**
     * Update the specified resource in storage.
     *
     * @param ContributionRequest $request
     * @param  int $id
     * @return Response
     */
    public function update(Request $request, $id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_contacts');

        $input = array_map('trim', $request->all());

        $contact = ContactLead::findOrFail($id);

        $contact->update($input);

        return redirect()->route('uranus.contacts.show', [$contact->id])
            ->with('success', 'Το μήνυμα ' . $contact->id . ' ενημερώθηκε');
    }


    /**
     * Remove the specified resource from storage.
     *
     * @param  int $id
     * @return Response
     */
    public function destroy($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'delete_contacts');

        $deleted = ContactLead::destroy($id);

        $status = $deleted ? 1 : 0;
        $message = $deleted ? 'Το μήνυμα με id ' . $id . ' διαγράφηκε επιτυχώς' : 'Σφάλμα κατά τη διαγραφή του μηνύματος';

        return response()->json([
            'status'  => $status,
            'message' => $message,
        ]);
    }
}
