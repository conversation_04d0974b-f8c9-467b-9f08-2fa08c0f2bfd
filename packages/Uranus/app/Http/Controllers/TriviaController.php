<?php namespace Packages\Uranus\app\Http\Controllers;

use App\Models\Person;
use App\Models\Trivia;
use Artesaos\SEOTools\Facades\SEOMeta;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\Request;
use Packages\Uranus\app\Http\Requests\TriviaRequest;

class TriviaController extends Controller
{

    /**
     * Show the form for creating a new resource.
     *
     * @param Request $request
     * @return Response
     * @throws AuthorizationException
     */
    public function create(Request $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_people');

        $person = Person::findOrFail($request->input('person_id'));

        // seo title
        SEOMeta::setTitle('New person trivia');

        return view('uranus::trivia.create', compact('person'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param TriviaRequest $request
     * @return Response
     * @throws AuthorizationException
     */
    public function store(TriviaRequest $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_people');

        $input = array_map('trim', $request->all());

        $trivia = Trivia::create($input);

        return redirect()->route('uranus.people.edit', [$trivia->person_id])
                         ->with('success', 'trivia ' . $trivia->id . ' added');
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param $id
     * @return Response
     * @throws AuthorizationException
     */
    public function edit($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_people');

        $trivia = Trivia::where('id', $id)
                        ->with('person')
                        ->firstOrFail();

        // seo title
        SEOMeta::setTitle('Edit person trivia ' . $trivia->id);

        return view('uranus::trivia.edit', compact('trivia'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param TriviaRequest $request
     * @param int $id
     * @return Response
     * @throws AuthorizationException
     */
    public function update(TriviaRequest $request, $id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_people');

        $input = $request->all();

        $trivia = Trivia::findOrFail($id);

        $trivia->update($input);

        return redirect()->route('uranus.people.edit', [$trivia->person_id])
                         ->with('success', 'trivia ' . $trivia->id . ' updated');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return Response
     * @throws AuthorizationException
     */
    public function destroy($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'delete_people');

        $deleted = Trivia::destroy($id);

        $status = $deleted ? 1 : 0;
        $message = $deleted ? 'Το trivia με id ' . $id . ' διαγράφηκε επιτυχώς' : 'Σφάλμα κατά την διαγραφή του trivia';

        return response()->json([
            'status'  => $status,
            'message' => $message,
        ]);

    }

}
