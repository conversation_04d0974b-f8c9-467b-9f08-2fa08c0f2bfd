<?php namespace Packages\Uranus\app\Http\Controllers;

use App\Models\Genre;
use Artesaos\SEOTools\Facades\SEOMeta;
use Packages\Uranus\app\Http\Requests\GenreRequest;

class GenresController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return Response
     */
    public function index()
    {
        $this->authorizeForUser(auth('admin')->user(), 'see_theatres');

        $request = request();
        $sortBy = $request->input('sortBy', 'updated_at');
        $direction = $request->input('direction', 'desc');
        $page = $request->input('page', 1);
        $limit = config('uranus.limits.genres.index');
        $offset = ($page * $limit) - $limit;

        $genres = Genre::orderBy($sortBy, $direction)
           ->with('supergenres')
           ->offset($offset)
           ->limit($limit)
           ->paginate();

        $genres->setPath($request->url())
               ->appends('sortBy', $sortBy)
               ->appends('direction', $direction);

        // seo title
        SEOMeta::setTitle('Index genres');

        return view('uranus::genres.index', compact('genres'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return Response
     */
    public function create()
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_theatres');

        // seo title
        SEOMeta::setTitle('New genre');

        return view('uranus::genres.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param GenreRequest $request
     * @return Response
     */
    public function store(GenreRequest $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_theatres');

        $input = array_map('trim', $request->all());

        $genre = Genre::create($input);

        return redirect()->route('uranus.genres.edit', [$genre->id])
                         ->with('success', 'Genre ' . $genre->id . ' added');
    }


    /**
     * Show the form for editing the specified resource.
     *
     * @param $id
     * @return Response
     */
    public function edit($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_theatres');

        $genre = Genre::findOrFail($id);

        // seo title
        SEOMeta::setTitle('Edit genre ' . $genre->name);

        return view('uranus::genres.edit', compact('genre'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param GenreRequest $request
     * @param $id
     * @return Response
     * @internal param $id
     * @internal param int $id
     */
    public function update(GenreRequest $request, $id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_theatres');

        $input = array_map('trim', $request->all());

        $genre = Genre::findOrFail($id);

        $genre->update($input);

        return redirect()->route('uranus.genres.edit', [$genre->id])
                         ->with('success', 'Genre ' . $id . ' updated');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param $id
     * @return Response
     */
    public function destroy($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'delete_theatres');

        $deleted = Genre::destroy($id);

        $status = $deleted ? 1 : 0;
        $message = $deleted ? 'Το είδος με id ' . $id . ' διαγράφηκε επιτυχώς' : 'Σφάλμα κατά την διαγραφή του είδους';

        return response()->json([
            'status'  => $status,
            'message' => $message,
        ]);
    }

}
