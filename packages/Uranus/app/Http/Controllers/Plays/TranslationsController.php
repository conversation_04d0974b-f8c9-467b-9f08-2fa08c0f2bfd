<?php namespace Packages\Uranus\app\Http\Controllers\Plays;

use Aws\Exception\AwsException;
use Illuminate\Http\Request;
use Packages\Uranus\app\Http\Controllers\Controller;

class TranslationsController extends Controller
{
    /*
     * Holds the translation client object
     */
    protected $translationClient;

    /*
     * Holds the source language
     */
    protected $sourceLang;

    /*
     * Holds the target language
     */
    protected $targetLang;

    /**
     * Translate title of play from el to en and suggest translation.
     *
     * @param Request $request
     * @return Response
     */
    public function suggest(Request $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_plays');

        $this->translationClient = new \Aws\Translate\TranslateClient([
//            'profile' => 'default',
            'region' => 'eu-west-1',
            'version' => '2017-07-01'
        ]);

        $this->sourceLang   = 'el';
        $this->targetLang   = 'en';

        $play = Play::findOrFail($request->input('play_id'));
        $field = $request->input('field', 'title');

        if (empty($play->$field)) {
            return response()->json([
                'success' => false,
                'message' => 'No content to translate'
            ]);
        }

        // Strip HTML tags for translation
        $textToTranslate = strip_tags($play->$field);

        if (empty($textToTranslate)) {
            return response()->json([
                'success' => false,
                'message' => 'No content to translate after removing HTML tags'
            ]);
        }

        // aws imposes a request limit for 5000 bytes of translatable text
        // TODO: calculate correctly the strlen on production
        if(mb_strlen($textToTranslate) >= 2000)
        {
            return response()->json([
                'success' => false,
                'message' => 'Content too long for translation'
            ]);
        }

        try {
            $result = $this->translationClient->translateText([
                'SourceLanguageCode' => $this->sourceLang,
//                'SourceLanguageCode' => 'auto',
                'TargetLanguageCode' => $this->targetLang,
                'Text' => $textToTranslate,
            ]);
//            dd($result->toArray()['TranslatedText']);
        }catch (AwsException $e) {
            // output error message if fails
            return response()->json([
                'success' => false,
                'message' => 'Translation service error: ' . $e->getMessage()
            ]);
        }

        // flag to check if the operation succeeded or not
        $translated = true;
        $translation = $result->get('TranslatedText');

        $status = $translated ? 1 : 0;
        $message = $translated ? 'Η μετάφραση της παράστασης δημιουργήθηκε. Μην ξεχάσεις να αποθηκεύσεις :)' : 'Σφάλμα κατά τη μετάφραση της παράστασης';

        return response()->json([
            'status'        => $status,
            'message'       => $message,
            'translation'   => $translation,
        ]);

    }
}
