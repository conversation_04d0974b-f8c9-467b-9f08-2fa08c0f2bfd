<?php namespace Packages\Uranus\app\Http\Controllers;

use App\Models\Role;
use App\Models\Variety;
use Artesaos\SEOTools\Facades\SEOMeta;
use Illuminate\Http\Request;
use Packages\Uranus\app\Http\Requests\RoleRequest;

class RolesController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     */
    public function index(Request $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'see_theatres');

        $sortBy = $request->input('sortBy', 'created_at');
        $direction = $request->input('direction', 'desc');
        $page = $request->input('page', 1);
        $limit = config('uranus.limits.roles.index');
        $offset = ($page * $limit) - $limit;

        $roles = Role::orderBy($sortBy, $direction);

        $query_q = $request->query('q');
        if ( !empty($query_q) )
        {
            $roles->where(function($query) use ($query_q) {
                $query->where('title', 'LIKE', '%' . $query_q . '%')
                    ->orWhere('description', 'LIKE', '%' . $query_q . '%');
            })
            ->get();
        }

        $query_characterable = $request->query('characterable');
        if( !empty($query_characterable) )
        {
            if ($query_characterable == 'yes')
            {
                $roles->characterable();
            }
            elseif ($query_characterable == 'no')
            {
                $roles->where('characterable', false);
            }
        }

        $roles = $roles->offset($offset)
            ->limit($limit)
            ->paginate();

        $roles->setPath($request->url());

        // append q query string if present
        if ( !empty($query_q) )
        {
            $roles->appends('q', $query_q);
        }

        // append characterable query string if present
        if ( !empty($query_characterable) )
        {
            $roles->appends('characterable', $query_characterable);
        }

        $roles->appends('sortBy', $sortBy)
            ->appends('direction', $direction);

        // seo title
        SEOMeta::setTitle('Index roles');

        return view('uranus::roles.index', compact('roles'));
    }

    /**
     * Show the form for creating a new resource.
     *
     */
    public function create()
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_theatres');

        // seo title
        SEOMeta::setTitle('New role');

        return view('uranus::roles.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param RoleRequest $request
     */
    public function store(RoleRequest $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_theatres');

        $input = $request->all();

        $input['title'] = string_to_greeklish($input['description']);

        $role = Role::create($input);

        return redirect()->route('uranus.roles.edit', [$role->id])
                         ->with('success', 'Role ' . $role->id . ' added');
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param $id
     */
    public function edit($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_theatres');

        $role = Role::with('varieties')
            ->findOrFail($id);

        $varieties = Variety::orderBy('name', 'asc')->get();

        // seo title
        SEOMeta::setTitle('Edit role ' . $role->title);

        return view('uranus::roles.edit', compact('role', 'varieties'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param RoleRequest $request
     * @param $id
     */
    public function update(RoleRequest $request, $id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_theatres');

        $role = Role::findOrFail($id);

        $role->update($request->input());

        // update the relationship with varieties
        $role->varieties()->sync(($request->input('varieties') ? : []));

        return redirect()->route('uranus.roles.edit', [$role->id])
                         ->with('success', 'Role ' . $id . ' updated');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param $id
     */
    public function destroy($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'delete_theatres');

        $deleted = Role::destroy($id);

        $status = $deleted ? 1 : 0;
        $message = $deleted ? 'Το επάγγελμα με id ' . $id . ' διαγράφηκε επιτυχώς' : 'Σφάλμα κατά την διαγραφή του επαγγέλματος';

        return response()->json([
            'status'  => $status,
            'message' => $message,
        ]);
    }

}
