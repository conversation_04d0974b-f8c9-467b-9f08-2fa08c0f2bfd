<?php namespace Packages\Uranus\app\Http\Controllers;

use App\Models\EmailMessage;
use App\Models\EmailMessageAction;
use Artesaos\SEOTools\Facades\SEOMeta;
use Illuminate\Http\Request;

class EmailMessagesController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return Response
     */
    public function index(Request $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'see_contacts');

        $sortBy = $request->input('sortBy', 'date');
        $direction = $request->input('direction', 'desc');
        $page = $request->input('page', 1);
        $limit = config('uranus.limits.contacts.index');
        $offset = ($page * $limit) - $limit;

        $messages = EmailMessage::orderBy($sortBy, $direction);

        $query_subject = $request->query('subject');
        if ( !empty($query_subject) )
        {
            $messages->where('subject', 'LIKE', '%' . $query_subject . '%');
        }

        $query_to = $request->query('to');
        if ( !empty($query_to) )
        {
            $messages->where('to', 'LIKE', '%' . $query_to . '%');
        }

        // cater for actions search filter (refers to actions relation)
        $query_actions = $request->query('actions');
        if ($query_actions == 'has')
        {
            $messages->has('actions');
        }
        else if ($query_actions == 'does_not_have')
        {
            $messages->doesnthave('actions');
        }

        $messages = $messages->offset($offset)
            ->limit($limit)
            ->paginate();

        $messages->setPath($request->url());

        // append subject query string if present
        if ( !empty($query_subject) )
        {
            $messages->appends('subject', $query_subject);
        }

        // append to query string if present
        if ( !empty($query_to) )
        {
            $messages->appends('to', $query_to);
        }

        // append actions query string if present
        if ($query_actions)
        {
            $messages->appends('actions', $query_actions);
        }

        $messages->appends('sortBy', $sortBy)
             ->appends('direction', $direction);

        // seo title
        SEOMeta::setTitle('Index email messages');

        return view('uranus::emailMessages.index', compact('messages'));

    }


    /**
     * Show the specified resource.
     *
     * @param  int $id
     * @return Response
     */
    public function show(Request $request, $id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_contacts');

        $message = EmailMessage::findOrFail($id);

        // and get the actions fer said message
        $sortBy = $request->input('sortBy', 'date');
        $direction = $request->input('direction', 'desc');
        $page = $request->input('page', 1);
        $limit = config('uranus.limits.contacts.index');
        $offset = ($page * $limit) - $limit;

        $actions = EmailMessageAction::where('email_message_id', $message->id)
            ->orderBy($sortBy, $direction)
            ->offset($offset)
            ->limit($limit)
            ->paginate();

        $actions->setPath($request->url())
            ->appends('sortBy', $sortBy)
            ->appends('direction', $direction);

        // seo title
        SEOMeta::setTitle('Preview email message ' . $message->id);

        return view('uranus::emailMessages.show', compact('message', 'actions'));
    }

}
