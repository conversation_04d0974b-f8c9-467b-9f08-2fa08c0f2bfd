<?php

namespace Packages\Uranus\app\Queries\People;


use App\Models\Person;
use App\Queries\BaseQuery as Query;

class PeopleWithBioQuery extends Query
{
    /**
     * Declare the body of this query
     *
     * @return mixed
     *
     */
    public static function body()
    {
        $request = request();
        $sortBy = $request->input('sortBy', 'updated_at');
        $direction = $request->input('direction', 'desc');
        $page = $request->input('page', 1);
        $limit = config('uranus.limits.people.index');
        $offset = ($page * $limit) - $limit;


        return Person::where(function ($query){
            $query->whereNotNull('bio')
                ->whereRaw('LENGTH(TRIM(people.bio)) > 0');
            })
            ->orWhere(function ($query){
                $query->whereNotNull('auto_biography_text')
                    ->whereRaw('LENGTH(TRIM(people.auto_biography_text)) > 0');
            })
             ->select([
                 'people.first_name',
                 'people.last_name',
                 'people.id',
                 'people.created_at',
             ])
             ->orderBy($sortBy, $direction)
             ->offset($offset)
             ->limit($limit)
             ->paginate($limit);

    }

    public static function count()
    {
        return Person::where(function ($query){
            $query->whereNotNull('bio')
                ->whereRaw('LENGTH(TRIM(people.bio)) > 0');
        })
            ->orWhere(function ($query){
                $query->whereNotNull('auto_biography_text')
                    ->whereRaw('LENGTH(TRIM(people.auto_biography_text)) > 0');
            })
            ->count();
    }

}
