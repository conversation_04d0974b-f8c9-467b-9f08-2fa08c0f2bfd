<?php

namespace Packages\Uranus\app\Queries\Plays;

use App\Models\Play;
use App\Queries\BaseQuery as Query;

class PlaysWithoutGenresQuery extends Query
{
    /**
     * Declare the body of this query
     *
     * @return mixed
     *
     */
    public static function body()
    {
        $request = request();
        $sortBy = $request->input('sortBy', 'updated_at');
        $direction = $request->input('direction', 'desc');
        $page = $request->input('page', 1);
        $limit = config('uranus.limits.plays.index');
        $offset = ($page * $limit) - $limit;

        return Play::whereNotIn('id', function ($query) {
            $query->select('theatric_play_id')
                  ->from('genre_theatric_play')
                  ->distinct();
        })
                   ->select([
                       'theatric_plays.title',
                       'theatric_plays.updated_at',
                       'theatric_plays.published',
                       'theatric_plays.id',
                   ])
                   ->orderBy($sortBy, $direction)
                   ->offset($offset)
                   ->limit($limit)
                   ->paginate($limit);

    }

    public static function count()
    {
        return Play::whereNotIn('id', function ($query) {
            $query->select('theatric_play_id')
                  ->from('genre_theatric_play')
                  ->distinct();
        })->count('theatric_plays.id');
    }

}
