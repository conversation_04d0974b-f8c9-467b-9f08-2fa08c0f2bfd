<?php

namespace App\Queries\People;

use App\Generators\PersonBioGenerator;
use App\Helpers\ContentSanitizer;
use App\Models\Person;
use App\Queries\BaseQuery as Query;

class ShowPersonQuery extends Query
{
    /**
     * Declare the body of this query
     *
     * @return mixed
     *
     */
    public static function body($slug)
    {
        $person = Person::where('slug', $slug)
                        ->with('ongoingPlays')
                        ->with('attendingPlays')
                        ->with('mainImage.creditedPeople')
                        ->with('restProfileImages.creditedPeople')
                        ->with('taggedImages.play')
                        ->with('taggedImages.movie')
                        ->with('taggedImages.tvShow.translations')
                        ->with('taggedImages.creditedPeople')
                        ->with('quotes')
                        ->with('trivia')
                        ->with('schools')
                        ->with('awards')
                        ->with('personReferences')
                        ->with('videos')
                        ->firstOrFail();

        // We will load roles and plays for these roles after the person model has been retrieved
        // We are doing lazy eager loading because we need person id for properly reading data
        // from person_play_role table


        // Archive & auto generates bio
        $person->load(['aggregateRoles.playsArchive' => function ($query) use ($person) {
            $query
                ->wherePivot('person_id', '=', $person->id);
        }, 'aggregateRoles.playsArchive.theatre', 'aggregateRoles.trendingPlays' => function ($query) use ($person) {
            $query
                ->wherePivot('person_id', '=', $person->id);
        },]);

        $person->load(['aggregateMovieRoles.moviesArchive' => function ($query) use ($person) {
            $query
                ->wherePivot('person_id', '=', $person->id);
        },'aggregateMovieRoles.trendingMovies' => function ($query) use ($person) {
            $query
                ->wherePivot('person_id', '=', $person->id);
        },]);

        $person->load(['aggregateEndeavourRoles.endeavoursArchive' => function ($query) use ($person) {
            $query
                ->wherePivot('person_id', '=', $person->id);
        },'aggregateEndeavourRoles.trendingEndeavours' => function ($query) use ($person) {
            $query
                ->wherePivot('person_id', '=', $person->id);
        },]);

        $person->load(['aggregateTvShowRoles.tvShowsArchive' => function ($query) use ($person) {
            $query
                ->wherePivot('person_id', '=', $person->id);
        }, 'aggregateTvShowRoles.tvShowsArchive.tvChannel', 'aggregateTvShowRoles.tvShowsArchive.translations', 'aggregateTvShowRoles.trendingTvShows' => function ($query) use ($person) {
            $query
                ->wherePivot('person_id', '=', $person->id);
        }, 'aggregateTvShowRoles.trendingTvShows.translations']);

        // Sidebar known for
        $person->load(['trendingPlays' => function ($query) use ($person) {
            $query
                ->wherePivot('person_id', '=', $person->id)
                ->groupBy('play_id')
            ;
        }, 'trendingPlays.roles' => function ($query) use ($person) {
            $query
                ->wherePivot('person_id', '=', $person->id)
                ->groupBy('play_id')
            ;
        }, 'trendingPlays.mainImage']);

        $person->load(['trendingMovies' => function ($query) use ($person) {
            $query
                ->wherePivot('person_id', '=', $person->id)
                ->groupBy('movie_id')
            ;
        }, 'trendingMovies.roles' => function ($query) use ($person) {
            $query
                ->wherePivot('person_id', '=', $person->id)
                ->groupBy('movie_id')
            ;
        }]);

        $person->load(['trendingEndeavours' => function ($query) use ($person) {
            $query
                ->wherePivot('person_id', '=', $person->id)
                ->groupBy('endeavour_id')
            ;
        }, 'trendingEndeavours.roles' => function ($query) use ($person) {
            $query
                ->wherePivot('person_id', '=', $person->id)
                ->groupBy('endeavour_id')
            ;
        }]);

        $person->load(['trendingTvShows' => function ($query) use ($person) {
            $query
                ->wherePivot('person_id', '=', $person->id)
                ->groupBy('tv_show_id')
            ;
        }, 'trendingTvShows.roles' => function ($query) use ($person) {
            $query
                ->wherePivot('person_id', '=', $person->id)
                ->groupBy('tv_show_id')
            ;
        }]);

        // todo move DTO creation outside of query
        // create PersonDTO
        $personDTO = $person;

        if ( ! $personDTO->bio )
        // if the bio of a person is not filled in we generate an automated one
        {
            $bioGenerator = new PersonBioGenerator($person);
            $personDTO->bio = $bioGenerator->generateBio();
        }

        // putting the presentable bio code here (leaving also the old bio code)
        // so as to not break somefin mistakingly
        // TODO: refactor it when we move the DTO creation out of the query class
        if ( $personDTO->user_bio )
        {
            // the presentable_bio attribute is created to hold the text we want to show in the frontend
            // Sanitize user_bio content to ensure consistent styling
            $personDTO->presentable_bio = ContentSanitizer::sanitizeUserBio($personDTO->user_bio);
        }
        else
        {
            $personDTO->presentable_bio = $personDTO->bio;
        }
        // presentable links
        $presentable_links = [
            'facebook'      => 'user_facebook',
            'facebook_page' => 'user_facebook_page',
            'instagram'     => 'user_instagram',
            'twitter'       => 'user_twitter',
            'youtube'       => 'user_youtube',
            'imdb'          => 'user_imdb',
            'website'       => 'user_website',
        ];

        foreach($presentable_links as $admin_edited => $user_edited)
        {
            $presentable_attribute = 'presentable_' . $admin_edited;
            $personDTO->$presentable_attribute = ! empty($personDTO->$user_edited) ? $personDTO->$user_edited : $personDTO->$admin_edited;
        }

        $personDTO->allImages = array_merge($personDTO->mainImage ? [$personDTO->mainImage] : [],
            $personDTO->restProfileImages->all(), $personDTO->taggedImages->all());

        return $personDTO;
    }
}
