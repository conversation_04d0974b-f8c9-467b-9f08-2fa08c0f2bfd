<?php

namespace App\Queries\TvShows;

use App\Models\TvShow;
use App\Queries\BaseQuery as Query;

class ShowTvShowQuery extends Query
{
    /**
     * Declare the body of this query
     *
     * @return mixed
     *
     */
    public static function body($slug)
    {
        $tvShow = TvShow::where('slug', $slug)
                    ->where('published', true)
                    ->with('translations')
                    ->with('tvChannel')
                    ->with('mainImage')
                    ->with('images')
                    ->with('images.taggedPeople')
                    ->with('images.creditedPeople')
                    ->with('directors.mainImage')
                    ->with('actors.mainImage')
                    ->with('filmGenres')
//                    ->with('criticReviews')
//                    ->with('reviews')
                    ->firstOrFail();

        // We will load roles and people for these roles after the movie model has been retrieved
        // We are doing lazy eager loading because we need movie id for properly reading data
        // from movie_person_role table
        $tvShow->load([
            'roles.tvShowPeople' => function ($query) use ($tvShow) {
                $query
                    ->whereNotIn('person_role_tv_show.role_id', ['1', '2'])
                    ->wherePivot('tv_show_id', '=', $tvShow->id);
            },
        ]);

        return $tvShow;
    }

}
