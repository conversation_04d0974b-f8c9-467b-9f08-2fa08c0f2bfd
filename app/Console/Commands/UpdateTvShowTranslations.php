<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\TvShow;
use App\Jobs\UpdateTvShowTranslation;
use Illuminate\Foundation\Bus\DispatchesJobs;

class UpdateTvShowTranslations extends Command
{
    use DispatchesJobs;
    
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tvshow-translations:update';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Translates TV show data automatically';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // fetch some TV shows
        $tvShows = TvShow::notTranslatedIn('en')
            ->take(40)
            ->get();

        foreach($tvShows as $tvShow)
        {
            $this->dispatch(new UpdateTvShowTranslation($tvShow));
        }
        
        $this->info('Dispatched ' . $tvShows->count() . ' TV show translation jobs.');
    }
}
