<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\TvShow;

class FindMissingTvShowTranslations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tvshow-translations:find-missing {--locale=en} {--export}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Find TV shows with missing translations';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $locale = $this->option('locale');
        $export = $this->option('export');
        
        $this->info("Finding TV shows missing {$locale} translations...");
        
        $missingTranslations = TvShow::notTranslatedIn($locale)->get();
        $totalTvShows = TvShow::count();
        $translatedCount = $totalTvShows - $missingTranslations->count();
        
        $this->info("Results:");
        $this->info("- Total TV Shows: {$totalTvShows}");
        $this->info("- Translated ({$locale}): {$translatedCount}");
        $this->info("- Missing translations: {$missingTranslations->count()}");
        $this->info("- Translation coverage: " . round(($translatedCount / $totalTvShows) * 100, 2) . "%");
        
        if ($export && $missingTranslations->count() > 0) {
            $filename = storage_path("app/missing_tvshow_translations_{$locale}_" . date('Y-m-d_H-i-s') . ".csv");
            
            $file = fopen($filename, 'w');
            fputcsv($file, ['ID', 'Title (Greek)', 'Synopsis (Greek)', 'Year', 'Created At']);
            
            foreach ($missingTranslations as $tvShow) {
                fputcsv($file, [
                    $tvShow->id,
                    $tvShow->translate('el')->title ?? 'N/A',
                    substr($tvShow->translate('el')->synopsis ?? 'N/A', 0, 100) . '...',
                    $tvShow->year,
                    $tvShow->created_at
                ]);
            }
            
            fclose($file);
            $this->info("Exported missing translations to: {$filename}");
        }
        
        if ($missingTranslations->count() > 0) {
            $this->info("\nFirst 10 TV shows missing {$locale} translations:");
            $this->table(
                ['ID', 'Title (Greek)', 'Year'],
                $missingTranslations->take(10)->map(function ($tvShow) {
                    return [
                        $tvShow->id,
                        $tvShow->translate('el')->title ?? 'N/A',
                        $tvShow->year
                    ];
                })->toArray()
            );
        }
        
        return 0;
    }
}
