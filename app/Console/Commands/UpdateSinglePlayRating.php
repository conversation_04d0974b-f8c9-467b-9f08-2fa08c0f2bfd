<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\Play;
use App\Jobs\CalculateInternalPlayRating;
use Illuminate\Foundation\Bus\DispatchesJobs;

class UpdateSinglePlayRating extends Command
{
    use DispatchesJobs;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'play-ratings:update-one {play_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Calculates the internal play rating for a specific play';


    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $play = Play::find($this->argument('play_id'));

        if (! empty($play))
        {
            $this->dispatch(new CalculateInternalPlayRating($play));
        }
    }
}
