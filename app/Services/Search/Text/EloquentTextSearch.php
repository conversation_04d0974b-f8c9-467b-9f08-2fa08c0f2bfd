<?php

namespace App\Services\Search\Text;

use App\Components\Suggestion;
use App\Models\Endeavour;
use App\Models\EndeavourTranslation;
use App\Models\Person;
use App\Models\PersonTranslation;
use App\Models\Play;
use App\Models\PlayTranslation;
use App\Models\Movie;
use App\Models\School;
use App\Models\TvShow;
use App\Models\Theatre;
use App\Models\TheatreTranslation;
use App\Models\TvShowRating;
use App\Services\Search\Text\Contracts\TextSearchInterface;
use App\Transformers\Suggestions\EloquentTransformer;
use Illuminate\Support\Facades\Config;


class EloquentTextSearch implements TextSearchInterface
{
    /**
     * @var Person
     */
    private $person;
    /**
     * @var Theatre
     */
    private $theatre;
    /**
     * @var Play
     */
    private $play;
    /**
     * @var Movie
     */
    private $movie;
    /**
     * @var TvShow
     */
    private $tvShow;
    /**
     * @var Endeavour
     */
    private $endeavour;
    /**
     * @var School
     */
    private $school;
    /*
     * Extra relations to load with models
     */
    public $withRelations;


    public function __construct(Person $person, Theatre $theatre, Play $play, Movie $movie, TvShow $tvShow, Endeavour $endeavour, School $school)
    {
        $this->person = $person;
        $this->theatre = $theatre;
        $this->play = $play;
        $this->movie = $movie;
        $this->tvShow = $tvShow;
        $this->endeavour = $endeavour;
        $this->school = $school;
    }

    /**
     * {@inheritdoc}
     */
    public function getSuggestions($query, $limit)
    {
        $results['plays'] = $this->playsBaseQuery($query)
                                 ->with('mainImage')
                                 ->orderBy('title')
                                 ->select(['theatric_plays.title', 'theatric_plays.id', 'theatric_plays.slug'])
                                 ->limit($limit)
                                 ->get()
                                 ->all();

        $results['people'] = $this->peopleBaseQuery($query)
                                  ->with('mainImage')
                                  ->orderBy('last_name')
                                  ->select([
                                      'people.first_name',
                                      'people.last_name',
                                      'people.id',
                                      'people.slug',
                                      'people.sex',
                                  ])
                                  ->limit($limit)
                                  ->get()
                                  ->all();

        $results['theatres'] = $this->theatresBaseQuery($query)
                                    ->orderBy('name')
                                    ->select(['theatres.name', 'theatres.id', 'theatres.slug'])
                                    ->limit($limit)
                                    ->get()
                                    ->all();

        $formattedResults = [];
        foreach ($results as $modelName => $suggestions) {
            $formattedResults[$modelName] = [];
            foreach ($suggestions as $model) {
                $suggestion = $this->createSuggestion();
                if ($model instanceof Person) {
                    $suggestion->text = $model->fullName;
                }
                elseif ($model instanceof Play) {
                    $suggestion->text = $model->title;
                }
                elseif ($model instanceof Theatre) {
                    $suggestion->text = $model->name;
                }
                else {
                    $suggestion->text = null;
                }
                $suggestion->subtitle = null;
                $suggestion->imageUrl = $model->suggestionThumbnail(200, null);
                $suggestion->score = 0;
                $suggestion->model = $modelName;
                $suggestion->slug = isset($model->slug) ? route($modelName . '.show', $model->slug) : null;
                $formattedResults[$modelName][] = $suggestion;
            }
        }

        return (new EloquentTransformer($formattedResults))->handle();
    }

    /**
     * @return Suggestion
     */
    private function createSuggestion()
    {
        return new Suggestion();
    }

    /**
     * {@inheritdoc}
     */
    public function getAllByText($query, $page, $limit)
    {
        return [
            'people'        => $this->getPeopleByText($query, $page, $limit),
            'plays'         => $this->getPlaysByText($query, $page, $limit),
            'movies'        => $this->getMoviesByText($query, $page, $limit),
            'tvShows'       => $this->getTvShowsByText($query, $page, $limit),
            'theatres'      => $this->getTheatresByText($query, $page, $limit),
            'endeavours'    => $this->getEndeavoursByText($query, $page, $limit),
            'schools'       => $this->getSchoolsByText($query, $page, $limit),
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function getPeopleByText($query, $page, $limit)
    {
        if ( ! $limit) {
            $limit = Config::get('limit.textSearch.all.people');
        }
        $offset = ($page * $limit) - $limit;

        $query = $this->peopleBaseQuery($query)
            ->with('mainImage')
            ->orderBy('last_name')
            ->orderBy('id', 'desc')
            ->offset($offset)
            ->limit($limit);

        if ($this->withRelations) {
            $query->with($this->withRelations);
        }

        $people = $query->get();

        return [
            'results'     => $people,
            'perPage'     => $limit,
            'currentPage' => $page,
        ];
    }

    private function peopleBaseQuery($query)
    {
        $builder = $this->person
            ->orWhere('first_name', 'LIKE', '%' . $query . '%')
            ->orWhere('last_name', 'LIKE', '%' . $query . '%')
            ->orWhereRaw('concat_ws(" ",first_name,last_name)like \'%' . $query . '%\'');

        if (strlen($query) == mb_strlen($query, 'utf-8')) {
            $greekQuery = greeklish_to_greek($query);
            $builder->orWhere('first_name', 'LIKE', '%' . $greekQuery . '%')
                    ->orWhere('last_name', 'LIKE', '%' . $greekQuery . '%')
                    ->orWhereRaw('concat_ws(" ",first_name,last_name)like \'%' . $greekQuery . '%\'');
        }

        // Find people from translated fields
        $translatedData = $this->peopleTranslationsBaseQuery($query);
        if(!empty($translatedData))
        {
            $builder->orWhereIn('id', $translatedData);
        }

        return $builder;
    }

    private function peopleTranslationsBaseQuery($query)
    {
        $builder = PersonTranslation::
            orWhere('first_name', 'LIKE', '%' . $query . '%')
            ->orWhere('last_name', 'LIKE', '%' . $query . '%')
            ->orWhereRaw('concat_ws(" ",first_name,last_name)like \'%' . $query . '%\'')
            ->groupBy('person_id');

        if (strlen($query) == mb_strlen($query, 'utf-8')) {
            $greekQuery = greeklish_to_greek($query);
            $builder->orWhere('first_name', 'LIKE', '%' . $greekQuery . '%')
                    ->orWhere('last_name', 'LIKE', '%' . $greekQuery . '%')
                    ->orWhereRaw('concat_ws(" ",first_name,last_name)like \'%' . $greekQuery . '%\'');
        }

        return $builder->get()->pluck('person_id');
    }

    public function countPeopleByText($query)
    {
        return $this->peopleBaseQuery($query)->count();
    }

    /**
     * {@inheritdoc}
     */
    public function getTheatresByText($query, $page, $limit)
    {
        if ( ! $limit) {
            $limit = Config::get('limit.textSearch.all.theatres');
        }
        $offset = ($page * $limit) - $limit;

        $query = $this->theatresBaseQuery($query)
            ->orderBy('name')
            ->orderBy('id', 'desc')
            ->offset($offset)
            ->limit($limit);

        if ($this->withRelations) {
            $query->with($this->withRelations);
        }
        $theatres = $query->get();

        return [
            'results'     => $theatres,
            'perPage'     => $limit,
            'currentPage' => $page,
        ];
    }

    private function theatresBaseQuery($query)
    {
        $builder = $this->theatre->where('name', 'LIKE', '%' . $query . '%');

        if (strlen($query) == mb_strlen($query, 'utf-8')) {
            $greekQuery = greeklish_to_greek($query);
            $builder->orWhere('name', 'LIKE', '%' . $greekQuery . '%');
        }

        // Find theatres from translated fields
        $translatedData = $this->theatreTranslationsBaseQuery($query);
        if(!empty($translatedData))
        {
            $builder->orWhereIn('id', $translatedData);
        }

        return $builder;
    }

    private function theatreTranslationsBaseQuery($query)
    {
        $builder = TheatreTranslation::where('name', 'LIKE', '%' . $query . '%')->groupBy('theatre_id');

        if (strlen($query) == mb_strlen($query, 'utf-8')) {
            $greekQuery = greeklish_to_greek($query);
            $builder->orWhere('name', 'LIKE', '%' . $greekQuery . '%');
        }

        return $builder->get()->pluck('theatre_id');
    }

    public function countTheatresByText($query)
    {
        return $this->theatresBaseQuery($query)->count();
    }

    /**
     * {@inheritdoc}
     */
    public function getPlaysByText($query, $page, $limit)
    {
        if ( ! $limit) {
            $limit = Config::get('limit.textSearch.all.plays');
        }
        $offset = ($page * $limit) - $limit;

        $query = $this->playsBaseQuery($query)
            ->with(['mainImage', 'theatre'])
            ->orderBy('title')
            ->orderBy('id', 'desc')
            ->offset($offset)
            ->limit($limit);

        if ($this->withRelations) {
            $query->with($this->withRelations);
        }
        $plays = $query->get();

        return [
            'results'     => $plays,
            'perPage'     => $limit,
            'currentPage' => $page,
        ];
    }

    private function playsBaseQuery($query)
    {
        $builder = $this->play->where('title', 'LIKE', '%' . $query . '%');

        if (strlen($query) == mb_strlen($query, 'utf-8')) {
            $greekQuery = greeklish_to_greek($query);
            $builder->orWhere('title', 'LIKE', '%' . $greekQuery . '%');
        }

        // Find plays from translated fields
        $translatedData = $this->playsTranslationsBaseQuery($query);

        if(!empty($translatedData))
        {
            $builder->orWhereIn('id', $translatedData);
        }

        return $builder;
    }

    private function playsTranslationsBaseQuery($query)
    {
        $builder = PlayTranslation::where('title', 'LIKE', '%' . $query . '%')->groupBy('theatric_play_id');

        if (strlen($query) == mb_strlen($query, 'utf-8')) {
            $greekQuery = greeklish_to_greek($query);
            $builder->orWhere('title', 'LIKE', '%' . $greekQuery . '%');
        }

        return $builder->get()->pluck('theatric_play_id');
    }

    public function countPlaysByText($query)
    {
        return $this->playsBaseQuery($query)->count();
    }

    /**
     * {@inheritdoc}
     */
    public function getMoviesByText($query, $page, $limit)
    {
        if ( ! $limit) {
            $limit = Config::get('limit.textSearch.all.plays');
        }
        $offset = ($page * $limit) - $limit;

        $query = $this->moviesBaseQuery($query)
//                      ->with(['mainImage'])
            ->orderBy('title')
            ->orderBy('id', 'desc')
            ->offset($offset)
            ->limit($limit);

        if ($this->withRelations) {
            $query->with($this->withRelations);
        }
        $movies = $query->get();

        return [
            'results'     => $movies,
            'perPage'     => $limit,
            'currentPage' => $page,
        ];
    }

    private function moviesBaseQuery($query)
    {
        $builder = $this->movie->where('title', 'LIKE', '%' . $query . '%');

        if (strlen($query) == mb_strlen($query, 'utf-8')) {
            $greekQuery = greeklish_to_greek($query);
            $builder->orWhere('title', 'LIKE', '%' . $greekQuery . '%');
        }

        return $builder;
    }

    public function countMoviesByText($query)
    {
        return $this->moviesBaseQuery($query)->count();
    }

    /**
     * {@inheritdoc}
     */
    public function getEndeavoursByText($query, $page, $limit)
    {
        if ( ! $limit ) {
            $limit = Config::get('limit.textSearch.all.plays');
        }
        $offset = ($page * $limit) - $limit;

        $query = $this->endeavoursBaseQuery($query)
//                      ->with(['mainImage'])
            ->orderBy('old_title')
            ->orderBy('id', 'desc')
            ->offset($offset)
            ->limit($limit);

        if ($this->withRelations) {
            $query->with($this->withRelations);
        }
        $endeavours = $query->get();

        return [
            'results'     => $endeavours,
            'perPage'     => $limit,
            'currentPage' => $page,
        ];
    }

    private function endeavoursBaseQuery($query)
    {
        $builder = $this->endeavour->where('old_title', 'LIKE', '%' . $query . '%');

        if (strlen($query) == mb_strlen($query, 'utf-8')) {
            $greekQuery = greeklish_to_greek($query);
            $builder->orWhere('old_title', 'LIKE', '%' . $greekQuery . '%');
        }

        // Find endeavours from translated fields
        $translatedData = $this->endeavoursTranslationsBaseQuery($query);
        if(!empty($translatedData))
        {
            $builder->orWhereIn('id', $translatedData);
        }

        return $builder;
    }

    private function endeavoursTranslationsBaseQuery($query)
    {
        $builder = EndeavourTranslation::
        orWhere('title', 'LIKE', '%' . $query . '%')
            ->groupBy('endeavour_id');

        if (strlen($query) == mb_strlen($query, 'utf-8')) {
            $greekQuery = greeklish_to_greek($query);
            $builder->orWhere('title', 'LIKE', '%' . $greekQuery . '%');
        }

        return $builder->get()->pluck('endeavour_id');
    }

    public function countEndeavoursByText($query)
    {
        return $this->endeavoursBaseQuery($query)->count();
    }

    /**
     * {@inheritdoc}
     */
    public function getSchoolsByText($query, $page, $limit)
    {
        if ( ! $limit ) {
            $limit = Config::get('limit.textSearch.all.plays');
        }
        $offset = ($page * $limit) - $limit;

        $query = $this->schoolsBaseQuery($query)
//                      ->with(['mainImage'])
            ->orderBy('name')
            ->orderBy('id', 'desc')
            ->offset($offset)
            ->limit($limit);

        if ($this->withRelations) {
            $query->with($this->withRelations);
        }
        $schools = $query->get();

        return [
            'results'     => $schools,
            'perPage'     => $limit,
            'currentPage' => $page,
        ];
    }

    private function schoolsBaseQuery($query)
    {
        $builder = $this->school->where('name', 'LIKE', '%' . $query . '%');

        if (strlen($query) == mb_strlen($query, 'utf-8')) {
            $greekQuery = greeklish_to_greek($query);
            $builder->orWhere('name', 'LIKE', '%' . $greekQuery . '%');
        }

        return $builder;
    }

    public function countSchoolsByText($query)
    {
        return $this->schoolsBaseQuery($query)->count();
    }

    /**
     * {@inheritdoc}
     */
    public function getTvShowsByText($query, $page, $limit)
    {
        if ( ! $limit) {
            $limit = Config::get('limit.textSearch.all.plays');
        }
        $offset = ($page * $limit) - $limit;

        $query = $this->tvShowsBaseQuery($query)
            ->with(['mainImage', 'translations'])
            ->offset($offset)
            ->limit($limit);

        if ($this->withRelations) {
            $query->with($this->withRelations);
        }
        $tvShows = $query->get();

        return [
            'results'     => $tvShows,
            'perPage'     => $limit,
            'currentPage' => $page,
        ];
    }

    private function tvShowsBaseQuery($query)
    {
        // Find TV shows from translated fields
        $translatedData = $this->tvShowsTranslationsBaseQuery($query);

        $builder = $this->tvShow;

        if(!empty($translatedData))
        {
            $builder = $builder->whereIn('id', $translatedData);
        }
        else
        {
            // If no translations found, return empty result
            $builder = $builder->whereRaw('1 = 0');
        }

        return $builder;
    }

    private function tvShowsTranslationsBaseQuery($query)
    {
        $builder = \App\Models\TvShowTranslation::where('title', 'LIKE', '%' . $query . '%')
            ->groupBy('tv_show_id');

        if (strlen($query) == mb_strlen($query, 'utf-8')) {
            $greekQuery = greeklish_to_greek($query);
            $builder->orWhere('title', 'LIKE', '%' . $greekQuery . '%');
        }

        return $builder->get()->pluck('tv_show_id');
    }

    public function countTvShowsByText($query)
    {
        return $this->tvShowsBaseQuery($query)->count();
    }


}
