<?php

namespace App\Helpers;

class ContentSanitizer
{
    /**
     * Sanitize HTML content to remove custom styling and restrict to platform-allowed elements
     *
     * @param string $content
     * @return string
     */
    public static function sanitizeHtml($content)
    {
        if (empty($content)) {
            return '';
        }

        // Use DOMDocument for robust HTML parsing
        $dom = new \DOMDocument('1.0', 'UTF-8');
        
        // Suppress warnings for malformed HTML
        libxml_use_internal_errors(true);
        
        // Load HTML with UTF-8 encoding
        $dom->loadHTML('<?xml encoding="UTF-8">' . $content, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
        
        // Clear libxml errors
        libxml_clear_errors();
        
        $xpath = new \DOMXPath($dom);
        
        // Remove all style attributes
        $elementsWithStyle = $xpath->query('//*[@style]');
        foreach ($elementsWithStyle as $element) {
            $element->removeAttribute('style');
        }
        
        // Remove all class attributes (except specific allowed ones if needed)
        $elementsWithClass = $xpath->query('//*[@class]');
        foreach ($elementsWithClass as $element) {
            $element->removeAttribute('class');
        }
        
        // Remove unwanted attributes
        $unwantedAttributes = ['id', 'onclick', 'onload', 'onerror', 'background', 'bgcolor', 'color', 'face', 'size'];
        foreach ($unwantedAttributes as $attr) {
            $elementsWithAttr = $xpath->query('//*[@' . $attr . ']');
            foreach ($elementsWithAttr as $element) {
                $element->removeAttribute($attr);
            }
        }
        
        // Remove data-* attributes
        $allElements = $xpath->query('//*');
        foreach ($allElements as $element) {
            $attributesToRemove = [];
            foreach ($element->attributes as $attribute) {
                if (strpos($attribute->name, 'data-') === 0) {
                    $attributesToRemove[] = $attribute->name;
                }
            }
            foreach ($attributesToRemove as $attrName) {
                $element->removeAttribute($attrName);
            }
        }
        
        // Define allowed HTML tags
        $allowedTags = ['p', 'br', 'strong', 'b', 'em', 'i', 'u', 'ul', 'ol', 'li', 'h4', 'h5', 'h6', 'a'];
        
        // Remove disallowed tags but keep their content
        $allElements = $xpath->query('//*');
        $elementsToReplace = [];
        
        foreach ($allElements as $element) {
            if (!in_array(strtolower($element->tagName), $allowedTags)) {
                $elementsToReplace[] = $element;
            }
        }
        
        // Replace disallowed elements with their content
        foreach ($elementsToReplace as $element) {
            $fragment = $dom->createDocumentFragment();
            while ($element->firstChild) {
                $fragment->appendChild($element->firstChild);
            }
            $element->parentNode->replaceChild($fragment, $element);
        }
        
        // Clean up links - only allow safe href attributes
        $links = $xpath->query('//a');
        foreach ($links as $link) {
            $href = $link->getAttribute('href');
            
            // Remove all attributes first
            $attributesToRemove = [];
            foreach ($link->attributes as $attribute) {
                $attributesToRemove[] = $attribute->name;
            }
            foreach ($attributesToRemove as $attrName) {
                $link->removeAttribute($attrName);
            }
            
            // Add back only safe href
            if ($href && (strpos($href, 'http://') === 0 || strpos($href, 'https://') === 0 || strpos($href, 'mailto:') === 0)) {
                $link->setAttribute('href', $href);
                $link->setAttribute('target', '_blank');
                $link->setAttribute('rel', 'noopener noreferrer');
            } else {
                // Remove invalid links but keep content
                $fragment = $dom->createDocumentFragment();
                while ($link->firstChild) {
                    $fragment->appendChild($link->firstChild);
                }
                $link->parentNode->replaceChild($fragment, $link);
            }
        }
        
        // Get the cleaned HTML
        $cleanedContent = '';
        foreach ($dom->childNodes as $node) {
            $cleanedContent .= $dom->saveHTML($node);
        }
        
        // Remove XML encoding prefix if present
        $cleanedContent = preg_replace('/^<\?xml[^>]*>/', '', $cleanedContent);
        
        // Remove empty paragraphs
        $cleanedContent = preg_replace('/<p[^>]*>\s*<\/p>/', '', $cleanedContent);
        
        // Normalize whitespace
        $cleanedContent = preg_replace('/\s+/', ' ', $cleanedContent);
        
        return trim($cleanedContent);
    }

    /**
     * Sanitize user bio content specifically
     * This method can be extended with bio-specific rules if needed
     *
     * @param string $content
     * @return string
     */
    public static function sanitizeUserBio($content)
    {
        return self::sanitizeHtml($content);
    }
}
