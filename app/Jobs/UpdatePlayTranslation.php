<?php

namespace App\Jobs;

use Aws\Exception\AwsException;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Models\Play;

class UpdatePlayTranslation implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /*
     * Holds the play object
     */
    protected $play;

    /*
     * Holds the translation client object
     */
    protected $translationClient;

    /*
     * Holds the source language
     */
    protected $sourceLang;

    /*
     * Holds the target language
     */
    protected $targetLang;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Play $play)
    {
        $this->play = $play;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->translationClient = new \Aws\Translate\TranslateClient([
//            'profile' => 'default',
            'region' => 'eu-west-1',
            'version' => '2017-07-01'
        ]);

        $this->sourceLang   = 'el';
        $this->targetLang   = 'en';

        // translate the title
        $this->performTranslation('title');
        // translate the synopsis
        $this->performTranslation('synopsis');
        // translate the storyline
        $this->performTranslation('storyline');
        // translate the extra_info
        $this->performTranslation('extra_info');
        // translate the extra_ongoing_info
        $this->performTranslation('extra_ongoing_info');

        $this->play->save();
    }

    /**
     * @param $field
     * Performs the translation of the given field of a play
     */
    private function performTranslation($field)
    {
        if ( ! empty($this->play->$field) )
        {
            // Strip HTML tags for translation
            $textToTranslate = strip_tags($this->play->$field);

            if (empty($textToTranslate)) {
                return;
            }

            // aws imposes a request limit for 5000 bytes of translatable text
            // TODO: calculate correctly the strlen on production
            if(mb_strlen($textToTranslate) >= 2000)
            {
                return;
            }

//            echo 'Play id: ' . $this->play->id . "\n";
//            echo 'Field: ' . $field . "\n";
//            echo 'Field byte count: ' . mb_strlen($this->play->$field) . "\n";
//            echo 'Text to translate:<sot>' . $textToTranslate . "<eot>\n";

            try {
                $result = $this->translationClient->translateText([
                    'SourceLanguageCode' => $this->sourceLang,
//                'SourceLanguageCode' => 'auto',
                    'TargetLanguageCode' => $this->targetLang,
                    'Text' => $textToTranslate,
                ]);
//            dd($result->toArray()['TranslatedText']);
            }catch (AwsException $e) {
                // output error message if fails
                echo 'play id: ' . $this->play->id . "\n";
                echo $e->getMessage();
                echo "\n";
            }

            // save the translated first name
            $this->play->translateOrNew('en')->$field = $result->get('TranslatedText');
        }

    }
}
