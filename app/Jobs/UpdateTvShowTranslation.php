<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Models\TvShow;
use Aws\Exception\AwsException;

class UpdateTvShowTranslation implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /*
     * Holds the TV show object
     */
    protected $tvShow;

    /*
     * Holds the translation client object
     */
    protected $translationClient;

    /*
     * Holds the source language
     */
    protected $sourceLang;

    /*
     * Holds the target language
     */
    protected $targetLang;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(TvShow $tvShow)
    {
        $this->tvShow = $tvShow;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->translationClient = new \Aws\Translate\TranslateClient([
//            'profile' => 'default',
            'region' => 'eu-west-1',
            'version' => '2017-07-01'
        ]);

        $this->sourceLang   = 'el';
        $this->targetLang   = 'en';

        // translate the title
        $this->performTranslation('title');
        // translate the synopsis
        $this->performTranslation('synopsis');

        $this->tvShow->save();
    }

    /**
     * @param $field
     * Performs the translation of the given field of a TV show
     */
    private function performTranslation($field)
    {
        if ( ! empty($this->tvShow->$field) )
        {
            // Strip HTML tags for translation
            $textToTranslate = strip_tags($this->tvShow->$field);

            if (empty($textToTranslate)) {
                return;
            }

            // aws imposes a request limit for 5000 bytes of translatable text
            // TODO: calculate correctly the strlen on production
            if(mb_strlen($textToTranslate) >= 2000)
            {
                return;
            }

            try {
                $result = $this->translationClient->translateText([
                    'SourceLanguageCode' => $this->sourceLang,
//                'SourceLanguageCode' => 'auto',
                    'TargetLanguageCode' => $this->targetLang,
                    'Text' => $textToTranslate,
                ]);
            }catch (AwsException $e) {
                // output error message if fails
                echo 'TV show id: ' . $this->tvShow->id . "\n";
                echo $e->getMessage();
                echo "\n";
                return;
            }

            // save the translated field
            $this->tvShow->translateOrNew('en')->$field = $result->get('TranslatedText');
        }
    }
}
